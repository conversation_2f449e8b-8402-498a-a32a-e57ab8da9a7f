
name: Sync to Azure DevOps
on:
  push:
    branches:
      - main
      - develop
jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
            
      - name: Mirror to Azure DevOps
        run: |
          git remote add azure https://a.badr:<EMAIL>/laplace-software/Access%20Management%20System/_git/Access%20Management%20System
          git push --mirror azure
        env:
          AZURE_USER: ${{ secrets.AZURE_USER }}
          AZURE_PAT: ${{ secrets.AZURE_PAT }}
 
 
