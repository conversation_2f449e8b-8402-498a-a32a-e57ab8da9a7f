_pycache__/
__pycache__/
*.py[cod]

/ams_suprema/logs/
/prometheus_exporter

!/ks_dashboard_ninja/static/description/
/ks_dashboard_ninja/static/description/*
!/ks_dashboard_ninja/static/description/css/
!/ks_dashboard_ninja/static/description/js/
!/ks_dashboard_ninja/static/description/icon.png

!/ks_dashboard_ninja/static/description/images
/ks_dashboard_ninja/static/description/images/*
!/ks_dashboard_ninja/static/description/images/icons/



# C extensions
*.so

# my custom logs
logs/
cert/

# Distribution / packaging
.Python
env/
bin/
build/
develop-eggs/
dist/
eggs/
lib64/
parts/
sdist/
var/

*.egg-info/
.installed.cfg
*.egg
*.eggs
AUTHORS
ChangeLog

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.cache
nosetests.xml
coverage.xml

# Translations
*.mo

# Pycharm
.idea

# Django stuff:
*.log

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

# Rope
.ropeproject

# Sphinx documentation
docs/_build/

# Backup files
*~
*.swp

# Mac files
.DS_Store

# file generated when running tests
.cprofile_*
node_modules
package-lock.json

# Windows
desktop.ini
*.ini


