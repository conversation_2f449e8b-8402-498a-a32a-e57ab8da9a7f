
### Introduction

This document provides an overview of the component diagram for all custom modules within the system..

- **ams_base**:
  This module serves as a shared foundation among all custom modules. All modules are required to depend on `ams_base`. The core model within this module is `ams_base.abstract_model`, which serves as the base model for all other models across the modules.

- **ams_biostar**:
  Within the `ams_bs` module, the focus lies on device management, user enrollment, event logging, and communication with API. It encapsulates the business logic required for efficient management of devices used within the system.

- **ams_vm**:
- within the `ams_vm` module visitor management with access control and visitor registration management. It provides a secure way to manage visitor access and registration within the system.

### Component Diagram:
this diagram represent modules and dependencies
```mermaid
graph BT;
    ams_base[ams_base <br>Base Module]
    ams_bs[ams_bs <br> Biostar Access Control]
    ams[ams Core <br>  Access Management System]
    ams_vm[ams_vm <br> Visitor Management]
    ams_mep[ams_mep <br> Ministry of Economy and Planning <br> Cutomization]
    hr[Employees Management]
  
    ams_base --> hr
    ams --> ams_base
    ams_bs --> ams
    ams_vm --> ams
    ams_mep --> ams_vm
    

     
     
 
     
    
     
    
