


### Access Group
    An access group consists of access levels, which specify the accessible doors for specific schedules. Each user can belong to maximum 16 access groups. To assign access groups to users, you have to do the followings;
    
        1- Make schedules.
        2- Make doors.
        3- Make access levels using the schedules and doors.
        4- Make access groups using the access levels.
        5- Assign access groups to users using User.SetAccessGroup or User.SetAccessGroupMulti.


```mermaid
classDiagram
    class BaseAbstractModel {
        - _name = 'ams_base.abstract_model'
        - company_id : Many2One
    }
    
    class APIBaseModel {
        - _name = 'ams_base.api_model'
        - _inherit = 'ams_base.abstract_model'
        - name : Char
        - description : Char
        - record_id :Integer 
        - synced : Boolean
        - last_sync_date : DateTime
        - is_predefined : Boolean
        
        - error : Boolean
        - error_msg : Char
        - response_code : Char
    }

    class BaseAMSUser {
        - _name = 'ams_base.user'
        - _inherit = 'ams_base.api_model'
        - user_type : Selection(employee, visitor)
        - enroll_number : Integer
        - start_datetime : DateTime
        - end_datetime : DateTime
        
        + action_link_users()
        + action_unlink_users()
    }
    class BaseDevice {
        - _name = 'ams_base.device'
        - _inherit = 'ams_base.api_model'
        - ip : Char
        - port : Integer
        - device_serial : Char
        - state : Char
        - last_log_id : Integer
        - last_log_date : DateTime
        - activate : Boolean
        - log_active : Boolean

    }
    class BaseAccessGroup {
        - _name = 'ams_base.access_group'
        - _inherit = 'ams_base.api_model'
        
        + action_open_devices_tree_wizard()
        + action_open_users_tree_wizard()
        + action_open_device_action_views()
        + action_apply_ac_group_config()
        + action_delete_ac_group()
      
        
    }
    class BaseAPIClient {
        - _name = 'ams_base.api_client'
    }
    class BaseEventLog {
        - _name = 'ams_base.event_log'
        - _inherit = 'ams_base.api_model'
         - device_serial : Char
        - event_datetime : Datetime
        - log_date : Datetime
        - event_id : Char
        - event_code : Integer
        - code_name : Char
        - sub_code : Integer
        - executed_datetime : Datetime
        - has_image : Boolean
        - temperature : Float
        - timestamp : Char
        - description : Char
        - state : Selection(pending, executed)
        - enroll_number : Char
        - enroll_name : Char
        - is_biometric : Boolean
        
    }
    class BaseBiostarAPIClient {
        - _name = 'ams_base.biostar_api_client'
        - _inherit = 'ams_base.api_client'
    }
    class BaseZKAPIClient {
        - _name = 'ams_base.zk_api_client'
        - _inherit = 'ams_base.api_client'
    }
    
    class BaseCard {
        - _name = 'ams_base.card'
        - _inherit = 'ams_base.api_model'
        - card_number : char
        - display_card_id : Char
        - status : Char
        - state : char
        - is_blocked : Boolean
        - is_assigned : Boolean
        - mobile_card: Boolean
        - issue_count : Integer
        - card_slot : Integer
        - card_mask : Char
        
    }
    
    class Card {
        - _name = 'ams.card'
        - _inherit = 'ams_base.card'
        - card_type_id : Many2One
        - user_id : Many2One
    }
    
    class DeviceGroup {
        - _name = 'ams.device_group'
        - _inherit = 'ams_base.api_model'
        - parent_id : Many2One
        - device_ids : One2Many
    }
    class AMSUserGroup {
        - _name = 'ams.user_group'
        - _inherit = 'ams_base.api_model'
        - parent_id : Many2One
        - user_ids : One2Many
    }
    class AMSUser {
        - _name = 'ams.user'
        - _inherit = 'ams_base.user'
        - device_group_id : Many2One
        - user_group_id : Many2One
        + action_link_users()
        + action_unlink_users()
    }
    class Device {
        - _name = 'ams.device'
        - _inherit = 'ams_base.device'
        - device_group_id : Many2One
    }
    class Schedule {
        - _name = 'ams.schedule'
        - _inherit = 'ams_base.api_model'
    }
    
    class DoorGroup{
        - _name = 'ams.door_group'
        - _inherit = 'ams_base.api_model'
        - door_ids : One2Many
    }
    class Door{
        - _name = 'ams.door'
        - _inherit = 'ams_base.api_model'
        - door_group_id : Many2One
    }
    class DoorSchedule {
        - _name = 'ams.door_schedule'
        - _inherit = 'ams_base.api_model'
        - name : Char // computed
        - door_id : Many2One
        - schedule_id : Many2One
        - door_record_id: Integer
        - schedule_record_id: Integer
    }
    
    class AccessLevel {
        - _name = 'ams.access_level'
        - _inherit = 'ams_base.api_model'
        - door_schedule_ids : One2Many
        - ac_group_id : Many2One
    }
    class AccessGroup {
        - _name = 'ams.access_group'
        - _inherit = 'ams_base.access_group'
        - ac_levels_ids :Many2Many
        - device_ids : Many2Many
        - group_users_ids : One2many(AccessGroupUser)
       
    }
    class AccessGroupUser {
        - user_id : Many2One
        - ac_group_id : Many2One
        - config_applied : Boolean
    }
    class EventLog {
        - _name = 'ams.event_log'
        - _inherit = 'ams_base.event_log'
    }
    
    class CardType{
        - _name = 'ams.card_type'
        - _inherit = 'ams_base.api_model'
         - type : Char
        - mode : Char
       
    }
    
    BaseAbstractModel <|--  APIBaseModel :_inherit
    APIBaseModel <|--  BaseAccessGroup :_inherit
    APIBaseModel <|--  BaseAMSUser :_inherit
    APIBaseModel <|--  BaseDevice :_inherit
    
    BaseAccessGroup <|--  AccessGroup :_inherit
    BaseDevice <|--  Device: _inherit
    BaseAMSUser <|--  AMSUser :_inherit
    
   
    APIBaseModel <|--  DeviceGroup :_inherit
    APIBaseModel <|--  AMSUserGroup :_inherit
    
    APIBaseModel <|--  DoorGroup :_inherit
    APIBaseModel <|--  Door :_inherit
    APIBaseModel <|--  Schedule :_inherit
    APIBaseModel <|--  DoorSchedule :_inherit
    APIBaseModel <|--  AccessLevel :_inherit
    
    APIBaseModel <|--  Card :_inherit
    APIBaseModel <|--  CardType :_inherit
    
    APIBaseModel <|--  BaseEventLog :_inherit
    BaseEventLog <|--  EventLog :_inherit
    
    BaseAPIClient <|--  BaseBiostarAPIClient :_inherit
    BaseAPIClient <|--  BaseZKAPIClient :_inherit
    
    AccessGroup *--* Device
    AccessGroup --* AccessGroupUser
    AccessGroupUser --o AMSUser
    AccessGroup *--* AccessLevel
    AccessLevel --* DoorSchedule
    DoorSchedule --o Schedule
    DoorSchedule --o Door

 ```

