


### Biostar Access Control
    An access group consists of access levels, which specify the accessible doors for specific schedules. Each user can belong to maximum 16 access groups. To assign access groups to users, you have to do the followings;
    
        1- Make schedules.
        2- Make doors.
        3- Make access levels using the schedules and doors.
        4- Make access groups using the access levels.
        5- Assign access groups to users using User.SetAccessGroup or User.SetAccessGroupMulti.


```mermaid
classDiagram
    class DeviceGroup {
        - _name = 'ams_bs.device_group'
        - _inherit = 'ams_base.api_model'
        - parent_id : Many2One
        - device_ids : One2Many
    }
    class AMSUserGroup {
        - _name = 'ams_bs.user_group'
        - _inherit = 'ams_base.api_model'
        - parent_id : Many2One
        - user_ids : One2Many
    }
    class AMSUser {
        - _name = 'ams_bs.user'
        - _inherit = 'ams_base.user'
        - device_group_id : Many2One
        - user_group_id : Many2One
        + action_link_users()
        + action_unlink_users()
    }
    class Device {
        - _name = 'ams_bs.device'
        - _inherit = 'ams_base.device'
        - device_group_id : Many2One
    }
    class Schedule {
        - _name = 'ams_bs.schedule'
        - _inherit = 'ams_base.api_model'
    }
    
    class DoorGroup{
        - _name = 'ams_bs.door'
        - _inherit = 'ams_base.api_model'
        - door_ids : One2Many
    }
    class Door{
        - _name = 'ams_bs.door'
        - _inherit = 'ams_base.api_model'
        - door_group_id : Many2One
    }
    class DoorSchedule {
        - _name = 'ams_bs.door_schedule'
        - _inherit = 'ams_base.api_model'
        - name : Char // computed
        - door_id : Many2One
        - schedule_id : Many2One
        - door_record_id: Integer
        - schedule_record_id: Integer
    }
    
    class AccessLevel {
        - _name = 'ams_bs.access_level'
        - _inherit = 'ams_base.api_model'
        - door_schedule_ids : One2Many
        - ac_group_ids : Many2Many
    }
    class AccessGroup {
        - _name = 'ams_bs.access_group'
        - _inherit = 'ams_base.access_group'
        - ac_levels_ids :Many2Many
        - device_ids : Many2Many
        - group_users_ids : One2many(AccessGroupUser)
       
    }
    class AccessGroupUser {
        - user_id : Many2One
        - ac_group_id : Many2One
        - config_applied : Boolean
    }
    class EventLog {
        - _name = 'ams_bs.event_log'
        - _inherit = 'ams_base.event_log'
    }
    
    class CardType {
        - _name = 'ams_bs.card_type'
        - _inherit = 'ams_base.api_model'
        - type : Char
        - mode : Char
    }
    
  
    class Card {
        - _name = 'ams_bs.card'
        - _inherit = 'ams_base.api_model'
        - card_number : char
        - display_card_id : Char
        - status : Char
        - state : char
        - is_blocked : Boolean
        - is_assigned : Boolean
        - mobile_card: Boolean
        - issue_count : Integer
        - card_slot : Integer
        - card_mask : Char
        - card_type_id : Many2One
        - user_id : Many2One
    }
    
    BaseAbstractModel <|--  APIBaseModel :_inherit
    APIBaseModel <|--  BaseAccessGroup :_inherit
    APIBaseModel <|--  BaseAMSUser :_inherit
    APIBaseModel <|--  BaseDevice :_inherit
    
    BaseAccessGroup <|--  AccessGroup :_inherit
    BaseDevice <|--  Device: _inherit
    BaseAMSUser <|--  AMSUser :_inherit
    
    
    APIBaseModel <|--  DeviceGroup :_inherit
    APIBaseModel <|--  AMSUserGroup :_inherit
    
    APIBaseModel <|--  CardType :_inherit
    APIBaseModel <|--  Card :_inherit
    
    APIBaseModel <|--  DoorGroup :_inherit
    APIBaseModel <|--  Door :_inherit
    APIBaseModel <|--  Schedule :_inherit
    APIBaseModel <|--  DoorSchedule :_inherit
    APIBaseModel <|--  AccessLevel :_inherit
    
   
    APIBaseModel <|--  BaseEventLog :_inherit
    BaseEventLog <|--  EventLog :_inherit
    
    AccessGroup *--* Device
    AccessGroup --* AccessGroupUser
    AccessGroupUser --o AMSUser
    AccessGroup *--* AccessLevel
    AccessLevel --* DoorSchedule
    DoorSchedule --o Schedule
    DoorSchedule --o Door
   


 ```

