


### Biostar API Client



```mermaid
classDiagram
   
    class APIBaseModel {
        - _name = 'ams_base.api_model'
        - bs_device_api:env['ams_bs.biostar_device_api_client']// Propery
        - bs_user_api:env['ams_bs.biostar_user_api_client']// Propery
        - bs_ac_api:env['ams_bs.biostar_ac_api_client']// Propery
    }
    class BaseAPIClient {
        - _name = 'ams_base.api_client'
        + not_implemented_response(method_name='')
        + is_success_response(response)
        + _handle_request_exceptions(exception, response)
        + get_response_dict(response)

        
        
    }
    
    class BaseBiostarAPIClient {
        - _name = 'ams_base.biostar_api_client'
        - _inherit = 'ams_base.api_client'
        
        is_success_response(response)
        + token_key() //@property
        + token() //@property
        + get_default_headers(token)
        + login()
        
        //not implemented methods
        + get_user_groups()
        + get_user_group()
        + get_users()
        + get_user()
        
        + get_device_groups()
        + get_device_group()
        + get_devices()
        + get_device()
        + get_events()
        
        + get_ac_doors()
        + get_ac_door()
        + get_ac_schedules()
        + get_ac_schedule()
        + get_ac_levels()
        + get_ac_level()
        + get_ac_groups()
        + get_ac_group()
        
    }
    
    class BiostarDeviceAPIClient {
        - _name = 'ams_bs.device_api_client'
        - _inherit = 'ams_base.biostar_api_client'
        
        + get_device_groups()
        + get_device_group()
        + get_devices()
        + get_device()
        + get_events()
    }
    class BiostarUserAPIClient {
        - _name = 'ams_bs.device_api_client'
        - _inherit = 'ams_base.biostar_api_client'
        
        + get_user_groups()
        + get_user_group()
        + get_users()
        + get_user()
    }
    class BiostarACAPIClient {
        - _name = 'ams_bs.ac_api_client'
        - _inherit = 'ams_base.biostar_api_client'
        
        + get_ac_doors()
        + get_ac_door()
        + get_ac_schedules()
        + get_ac_schedule()
        + get_ac_levels()
        + get_ac_level()
        + get_ac_groups()
        + get_ac_group()
    }
    

    BaseAPIClient <|-- BaseBiostarAPIClient   :_inherit
    BaseBiostarAPIClient <|--  BiostarDeviceAPIClient :_inherit
    BaseBiostarAPIClient <|--  BiostarUserAPIClient :_inherit
    BaseBiostarAPIClient <|--  BiostarACAPIClient :_inherit

  
%%    AccessGroupUser --o AMSUser
%%    AccessGroup *--* AccessLevel
%%    AccessLevel --* DoorSchedule


 ```

