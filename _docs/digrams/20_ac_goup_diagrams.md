


### Access Group
    An access group consists of access levels, which specify the accessible doors for specific schedules. Each user can belong to maximum 16 access groups. To assign access groups to users, you have to do the followings;
    
        1- Make schedules.
        2- Make doors.
        3- Make access levels using the schedules and doors.
        4- Make access groups using the access levels.
        5- Assign access groups to users using User.SetAccessGroup or User.SetAccessGroupMulti.
    
    In addition to doors, you can use access groups to limit access to specific floors in a lift. In this case, floor levels, which specify the accessible floors for specific schedules, should be defined.

    Symbols Mermaid syntax:
    -->: Represents a unidirectional association from one class to another.
    <--: Represents a unidirectional association in the reverse direction.
    --: Represents a bidirectional association, indicating that both classes are associated with each other.
    -|>: Represents a generalization relationship, showing inheritance where the subclass extends the superclass.
    ..|>: Represents a realization relationship, indicating that a class implements an interface.
    -.-: Represents a dependency relationship, where one class depends on another.
    --*: Represents a one-to-many association, indicating that one instance of a class is associated with multiple instances of another class.
    --o: Represents a many-to-one association, indicating that multiple instances of one class are associated with one instance of another class.
    --: Represents a many-to-many association, indicating that multiple instances of one class are associated with multiple instances of another class


```mermaid
classDiagram
    class ACBaseModel {
    }

    class BaseDevicePort {
    }
    class ExitButton {
    }
    class Relay {
    }
    class Sensor {
    }
    class Device {
        - exit_buttons_ids:One2many
        - relays_ids:One2many
        - sensors_ids:One2many
        + action_link_ac_group()
        + action_unlink_ac_group()
    }
    class DoorSchedule {
    - door_id : Many2One
    - schedule_id : Many2One
    
    }
    class AccessGroup {
        - ac_levels_ids :Many2Many
        - device_ids : Many2Many
        - users_ids : One2many - GroupUser
        + action_open_devices_tree_wizard()
        + action_open_users_tree_wizard()
        + action_open_device_action_views()
        + action_apply_ac_group_config()
        + action_delete_ac_group()
    }
    class AccessGroupUser {
        - user_id : Many2One
        - ac_group_id : Many2One
        - config_applied : Boolean
    }
    class AccessLevel {
    }
    class Schedule {
    }
    class Door{
        - entry_device_id : Many2One
        - exit_device_id : Many2One
        - relay_id : Many2One
        - sensor_id : Many2One
        - exit_button_id : Many2One
         
        + action_add_door()
        + action_delete_door()
    }
    class SupremaUser {
        +action_link_users()
        +action_unlink_users()
    }
    
    
    
    BaseAbstractModel <|--  ACBaseModel
    ACBaseModel <|--  BaseDevicePort 
    
    BaseDevicePort <|-- ExitButton
    BaseDevicePort <|-- Relay
    BaseDevicePort <|-- Sensor
    
    AccessGroup -- Device
    AccessGroup --* AccessGroupUser
    AccessGroupUser --o SupremaUser
    AccessGroup -- AccessLevel
    AccessLevel --* DoorSchedule
    DoorSchedule --o Schedule
    DoorSchedule --o Door
    
    Device --* Sensor
    Device --* Relay
    Device --* ExitButton
    
    Door --o Device
 ```

### AC group apply config
    how trasaction workflow should be trandfered when applying config Ac group config
```mermaid
sequenceDiagram
    participant A as AC Group <BR> (UI-XML)
    participant B  as AC Group  <BR> (Model)
    participant C  as Device Actions  <BR> (Model)
    participant D  as Suprema User  <BR> (Model)
    participant E  as Suprema Client <BR> (API-GSDK)
    
  A->>B: action_apply_ac_group_config()
  
    B->>B: _link_ac_group() 
        B-->>B: _prepare_ac_group_dict() 
        B->>E: add_ac_group()
        
    B->>B: _unlink_ac_group()
    B-->>B: _delete_ac_config()
        B->>E: delete_ac_group()
        
    B->>C: _create_device_action()
    C->>C: action_execute()
         alt action_type='add'
            C->>D: enroll_user()
                D->>E: enroll_user()
                D->>E: set_card()
                D->>E: set_finger()
                D->>E: set_face()
                D->>E: set_ac_groups()
        else if action_type='delete'
            C->>D: delete_user()
                D->>E: delete_user()
        else if action_type='unlink'
            C->>D: unlink_user()
                D->>E: set_ac_groups()
        end
    
   
```