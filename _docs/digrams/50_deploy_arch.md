graph TB
    direction TB
    subgraph "External Access"
        Client[Client Browser]
        BioStar[BioStar 2 Server]
    end

    subgraph "Docker Server Environment"
        Portainer[Portainer CE]
        NPM[Nginx Proxy Manager]
        subgraph "Application Layer"
            Odoo[Odoo 18 Container]
            LDAP[LDAP Integration]
        end
        subgraph "Database Layer"
            Postgres[(PostgreSQL DB)]
            PGAdmin[pgAdmin4]
        end
    end

    subgraph "Internal Services"
        AD[Active Directory]
        SMTP[SMTP Server]
    end

    %% External Connections
    Client -->|HTTPS 443| NPM
    BioStar -->|API| Odoo

    %% DMZ to Docker
    NPM -->|Port 8069| Odoo

    %% Internal Connections
    Odoo --> Postgres
    Odoo --> SMTP
    PGAdmin --> Postgres

    %% Management
    Portainer -.->|Manages| Odoo
    Portainer -.->|Manages| Postgres
    Portainer -.->|Manages| PGAdmin

    classDef external fill:#f9f,stroke:#333,stroke-width:2px
    classDef docker fill:#b9e0ff,stroke:#333,stroke-width:2px
    classDef db fill:#85C1E9,stroke:#333,stroke-width:2px
    classDef internal fill:#ABEBC6,stroke:#333,stroke-width:2px
    classDef proxy fill:#F8C471,stroke:#333,stroke-width:2px

    class Client,BioStar external
    class Odoo,LDAP,Portainer,NPM docker
    class Postgres,PGAdmin db
    class AD,SMTP internal
