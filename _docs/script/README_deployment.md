# MEP18 Deployment Export

This directory contains scripts for creating clean deployment packages for the MEP18 branch.

## Scripts Overview

### 1. Main MEP18 Export (`export_mep18_deployment.sh`)

Exports core MEP18 modules from the main repository.

### 2. THP Modules Export (`export_mep18_thp_deployment.sh`)

Exports additional THP modules from the `addons_thp` repository.

## Quick Usage

### Export Main MEP18 Modules

```bash
# Navigate to the script directory
cd /Users/<USER>/Documents/Laplace/Projects/odoo18/addons_ams/_docs/script/

# Run the main MEP18 export script
./export_mep18_deployment.sh
```

### Export THP Modules

```bash
# Navigate to the script directory
cd /Users/<USER>/Documents/Laplace/Projects/odoo18/addons_ams/_docs/script/

# Run the THP modules export script
./export_mep18_thp_deployment.sh

/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_ams/_docs/script/export_mep18_deployment.sh

/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_ams/_docs/script/export_mep18_thp_deployment.sh
```

### Export Both (Complete Deployment)

```bash
# Run both scripts for a complete deployment package
./export_mep18_deployment.sh
./export_mep18_thp_deployment.sh
```

## What Each Script Does

### Main MEP18 Script (`export_mep18_deployment.sh`)

Creates a deployment package containing the core MEP18 folders:

- `ams_base/`
- `ams/`
- `ams_bs/`
- `ams_vm/`
- `ams_mep/`
- `docker/`

### THP Modules Script (`export_mep18_thp_deployment.sh`)

Creates a deployment package containing selected THP modules:

- `addons_thp/lp/lp_auto_refresh/`
- `addons_thp/lp/lp_hr_ldap/`
- `addons_thp/muk_web_theme/`
- `addons_thp/oca/auth_saml/`

## Key Features

✅ **Automatic exclusion of unwanted files:**

- Temporary files
- Cached files
- Files listed in `.gitignore`
- Untracked files
- Build artifacts

✅ **Only tracked/committed files are included**

✅ **No manual folder deletion required**

✅ **Timestamped output files**

## Output

Both scripts create ZIP files in `/Users/<USER>/Documents/Laplace/mep18_deployment/` (under the Laplace folder) with the following formats:

### Main MEP18 Script Output

```
mep18_release_YYYYMMDD_HHMMSS.zip
```

### THP Modules Script Output

```
mep18_thp_release_YYYYMMDD_HHMMSS.zip
```

### Complete Deployment Structure

After running both scripts, you'll have:

- Core MEP18 modules in `mep18_release_*.zip`
- Additional THP modules in `mep18_thp_release_*.zip`
- Both packages can be deployed together for a complete MEP18 installation

## Requirements

### For Main MEP18 Script

- Must be run from the script directory (`addons_ams/_docs/script/`)
- Git repository must be available in the main project
- `zip` command must be installed
- **Output Location**: `/Users/<USER>/Documents/Laplace/mep18_deployment/` (under Laplace folder)

### For THP Modules Script

- Must be run from the script directory (`addons_ams/_docs/script/`)
- Git repository must be available in the `addons_thp` directory
- `zip` command must be installed
- `addons_thp` repository must exist at `../../addons_thp`

## Troubleshooting

### Main MEP18 Script Issues

If the main script fails:

1. Ensure you're in the correct directory (`addons_ams/_docs/script/`)
2. Check that you have write permissions to the output directory
3. Verify that the `mep18` branch exists and is accessible

### THP Modules Script Issues

If the THP script fails:

1. Ensure you're in the correct directory (`addons_ams/_docs/script/`)
2. Verify that `addons_thp` directory exists at `../../addons_thp`
3. Check that the `mep18` branch exists in the THP repository
4. Ensure you have write permissions to the output directory
5. Verify that the specified THP modules exist in the repository

## Manual Verification

### Verify Main MEP18 Package

To verify the contents of the main MEP18 package:

```bash
# List all contents
unzip -l /Users/<USER>/Documents/Laplace/mep18_deployment/mep18_release_*.zip | head -20

# See only top-level folders
unzip -l /Users/<USER>/Documents/Laplace/mep18_deployment/mep18_release_*.zip | grep -E '^\s+0\s+.*\s+[^/]+/$' | awk '{print $NF}' | sort
```

### Verify THP Modules Package

To verify the contents of the THP modules package:

```bash
# List all contents
unzip -l /Users/<USER>/Documents/Laplace/mep18_deployment/mep18_thp_release_*.zip | head -20

# See THP module structure
unzip -l /Users/<USER>/Documents/Laplace/mep18_deployment/mep18_thp_release_*.zip | grep "addons_thp/" | head -10
```

### Verify Both Packages

To get a quick overview of both packages:

```bash
# List all ZIP files in deployment directory
ls -la /Users/<USER>/Documents/Laplace/mep18_deployment/mep18_*.zip

# Quick size comparison
du -h /Users/<USER>/Documents/Laplace/mep18_deployment/mep18_*.zip
```
