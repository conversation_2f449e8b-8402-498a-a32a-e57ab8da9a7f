#!/bin/bash

# MEP18 Deployment Export Script
# This script exports only the required folders for MEP18 deployment
# avoiding the need to manually delete unwanted folders
#
# IMPORTANT: git archive automatically excludes:
# - Temporary files
# - Cached files
# - Files listed in .gitignore
# - Untracked files
# Only committed/tracked files are included in the export

set -e  # Exit on any error

# Configuration
BRANCH="main"
OUTPUT_DIR="../../../../mep18_deployment"
ARCHIVE_NAME="mep18_release_$(date +%Y%m%d_%H%M%S).zip"

# Required folders for MEP18 deployment
FOLDERS=(
    "ams_base/"
    "ams/"
    "ams_bs/"
    "ams_vm/"
    "ams_mep/"
    "docker/"
)

echo "=== MEP18 Deployment Export ==="
echo "Branch: $BRANCH"
echo "Output: $OUTPUT_DIR/$ARCHIVE_NAME"
echo "Required folders: ${FOLDERS[*]}"
echo

# Ensure we're on the correct branch
echo "Checking current branch..."
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "$BRANCH" ]; then
    echo "Switching to $BRANCH branch..."
    git checkout $BRANCH
fi

# Create output directory if it doesn't exist
echo "Creating output directory: $OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR"

# Get absolute path for output directory
OUTPUT_DIR=$(cd "$OUTPUT_DIR" && pwd)

# Create temporary directory for staging
TEMP_DIR=$(mktemp -d)
echo "Using temporary directory: $TEMP_DIR"

# Export each required folder using git archive
# Note: git archive only exports tracked files, automatically excluding:
# - .gitignore entries, temp files, cache files, build artifacts
# - Untracked files and directories
echo "Exporting required folders (tracked files only)..."
for folder in "${FOLDERS[@]}"; do
    echo "  - Exporting $folder (excluding temp/cache/gitignore files)"
    git archive --format=tar HEAD "$folder" | tar -x -C "$TEMP_DIR"
done

# Create the deployment archive
echo "Creating deployment archive..."
cd "$TEMP_DIR"
zip -r "$OUTPUT_DIR/$ARCHIVE_NAME" .

# Cleanup
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo
echo "=== Export Complete ==="
echo "Deployment package created: $OUTPUT_DIR/$ARCHIVE_NAME"
echo "Package contains only the required MEP18 folders:"
for folder in "${FOLDERS[@]}"; do
    echo "  - $folder"
done
echo
echo "You can now deploy this clean package without manual folder deletion."