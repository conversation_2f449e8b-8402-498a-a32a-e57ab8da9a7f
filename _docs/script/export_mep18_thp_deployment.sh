#!/bin/bash

# MEP18 THP Modules Deployment Export Script
# This script exports only the required THP modules for MEP18 deployment
# from the addons_thp repository while maintaining directory structure
#
# IMPORTANT: git archive automatically excludes:
# - Temporary files
# - Cached files
# - Files listed in .gitignore
# - Untracked files
# Only committed/tracked files are included in the export

set -e  # Exit on any error

# Configuration
BRANCH="main"
# Get the script directory and calculate paths relative to it
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
OUTPUT_DIR="$PROJECT_ROOT/../../mep18_deployment"
ARCHIVE_NAME="mep18_thp_release_$(date +%Y%m%d_%H%M%S).zip"
THP_REPO_PATH="$PROJECT_ROOT/addons_thp"

# Required THP modules for MEP18 deployment
THP_MODULES=(
    "lp/lp_auto_refresh/"
    "lp/lp_hr_ldap/"
    "muk_web_theme/"
    "oca/auth_saml/"
)

echo "=== MEP18 THP Modules Deployment Export ==="
echo "Branch: $BRANCH"
echo "Output: $OUTPUT_DIR/$ARCHIVE_NAME"
echo "THP Repository: $THP_REPO_PATH"
echo "Required THP modules: ${THP_MODULES[*]}"
echo

# Change to THP repository directory
echo "Changing to THP repository directory..."
cd "$THP_REPO_PATH"

# Check if this is a git repository
echo "Checking if THP directory is a git repository..."
if git rev-parse --git-dir > /dev/null 2>&1; then
    echo "Git repository detected. Checking current branch..."
    CURRENT_BRANCH=$(git branch --show-current)
    if [ "$CURRENT_BRANCH" != "$BRANCH" ]; then
        echo "Switching to $BRANCH branch..."
        git checkout $BRANCH
    fi
    USE_GIT=true
else
    echo "Not a git repository. Using direct file copying..."
    USE_GIT=false
fi

# Create output directory if it doesn't exist
echo "Creating output directory: $OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR"

# Get absolute path for output directory
OUTPUT_DIR=$(cd "$OUTPUT_DIR" && pwd)

# Create temporary directory for staging
TEMP_DIR=$(mktemp -d)
echo "Using temporary directory: $TEMP_DIR"

# Create addons_thp directory structure in temp
mkdir -p "$TEMP_DIR/addons_thp"

# Export each required THP module
if [ "$USE_GIT" = true ]; then
    # Use git archive for git repositories (excludes untracked files)
    echo "Exporting required THP modules using git archive (tracked files only)..."
    for module in "${THP_MODULES[@]}"; do
        echo "  - Exporting addons_thp/$module (excluding temp/cache/gitignore files)"
        
        # Check if the module path exists in the repository
        if [ -d "$module" ]; then
            git archive --format=tar HEAD "$module" | tar -x -C "$TEMP_DIR/addons_thp"
        else
            echo "    WARNING: Module path '$module' not found in repository"
        fi
    done
else
    # Use direct file copying for non-git directories
    echo "Copying required THP modules directly..."
    for module in "${THP_MODULES[@]}"; do
        echo "  - Copying addons_thp/$module"
        
        # Check if the module path exists
        if [ -d "$module" ]; then
            # Create parent directories if needed
            mkdir -p "$TEMP_DIR/addons_thp/$(dirname "$module")"
            # Copy the module directory
            cp -r "$module" "$TEMP_DIR/addons_thp/$module"
        else
            echo "    WARNING: Module path '$module' not found"
        fi
    done
fi

# Create the deployment archive
echo "Creating THP deployment archive..."
cd "$TEMP_DIR"
zip -r "$OUTPUT_DIR/$ARCHIVE_NAME" .

# Cleanup
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo
echo "=== THP Export Complete ==="
echo "THP deployment package created: $OUTPUT_DIR/$ARCHIVE_NAME"
echo "Package contains only the required THP modules with directory structure:"
for module in "${THP_MODULES[@]}"; do
    echo "  - addons_thp/$module"
done
echo
echo "You can now deploy this clean THP package alongside the main MEP18 deployment."