System Deployment Requirements for Visitor Management System

1. Infrastructure Overview

- Operating System: Ubuntu 22.04 LTS (stable, secure, and optimized for Docker-based deployment).
- Platform: Odoo 18 Community Edition (including custom modules for visitor management and Suprema BioStar 2 API integration).
- Containerization: Docker-based ecosystem with tools like Portainer CE, Nginx Proxy Manager (NPM), PostgreSQL, and pgAdmin4.
- Integration Requirements:
  - Suprema BioStar 2 API for biometric access control.
  - Email notifications through an internal SMTP server.
  - LDAP/Active Directory authentication for user management within the organization.

2. Server Specifications
   Production Server

- CPU:
  - Minimum: 8 Cores
  - Recommended: 16 Cores (to handle peak traffic and biometric integration load)
- RAM:
  - Minimum: 16 GB
  - Recommended: 32 GB (for PostgreSQL, Odoo operations, and email/LDAP synchronization).
- Storage:
  - Minimum: 500 GB SSD
  - Recommended: 1 TB SSD (to manage visitor logs, employee records, database backups, and application storage).
  - Backup Storage: Additional 2 TB HDD for periodic automated backups and log retention.
- Network:
  - Gigabit Ethernet with a static IP address.
  - Firewall configured to permit internal access to Suprema BioStar 2 server API and other dependent services.

Staging Server

Used for testing new features and updates before production deployment:

- CPU: 4 Cores
- RAM: 8 GB
- Storage: 250 GB SSD

3. Software and Tools Setup

- Docker Components:
  - Portainer CE: For container management.
  - Nginx Proxy Manager (NPM): For reverse proxy management, SSL configuration, and load balancing.
  - Containers:
    - PostgreSQL: Database container for Odoo with optimized resource limits and auto-scaling capabilities.
    - pgAdmin4: Administrative tool for managing and monitoring the PostgreSQL instance.
    - Odoo 18 Community: Application container hosting the customized visitor management modules.
    - LDAP Integration Add-on: Container or service configured to support authentication with Active Directory.
- Security:
  - SSL certificates for secure communication (TLS 1.2+).
  - Internal firewalls configured with access controls for Suprema BioStar 2 API endpoints.
  - Regular vulnerability assessments and OS patching.

4. Network Architecture

- Deploy in a private subnet within the organization’s network, ensuring the server remains securely isolated while maintaining access to the BioStar 2 server for API integration.
- Use Nginx Proxy Manager for handling:
  - Odoo access via a domain name (e.g., visitors.ministry.gov.sa).
  - SSL termination and forwarding to containerized services.
- Ports:
  - Postgres: 5432 (internal only)
  - Odoo: 8069 (via Nginx Proxy Manager, SSL termination applied)
  - Suprema BioStar API: Accessible over internal subnets only.
  - LDAP: Ports 389/636 (secure) for authentication synchronization.

5. Email System Configuration

- Email Server: Use an internal organization-hosted SMTP server for outbound email settings.
- Configure Odoo outgoing email settings for notifications and alerts:
  - Outgoing SMTP Settings:
    - Server: smtp.ministry.gov.sa
    - Port: 587 (TLS)
    - Authentication: Enabled

6. Active Directory (LDAP) Configuration

- Leverage the Odoo LDAP Integration module for syncing with the organization's Active Directory.
- Details:
  - LDAP Host: ldap.ministry.gov.sa
  - LDAP Port: 389 or 636 (for secure LDAP)
  - Base DN: Example: dc=ministry,dc=gov,dc=sa
  - Bind DN: Service account with limited privileges to query AD.
  - User Filter: (&(objectClass=user)(!(userAccountControl:1.2.840.113556.1.4.803:=2)))

7. Backup and Disaster Recovery

- Daily Backups:
  - Database: Automated PostgreSQL dumps stored in the backup storage (2 TB HDD).
  - Files: Tarball snapshots of Odoo add-ons and attachments.
  - Remote sync: Backup data securely transferred to a secondary offsite location for redundancy.
- Monitoring:
  - Utilize tools like Prometheus + Grafana for real-time resource and service monitoring.
  - Configure alerts for CPU, memory spikes, or network disruptions.

8. Scalability and High Availability

- Use Docker Swarm or Kubernetes (depending on organizational policy) for container orchestration.
- For redundancy:
  - Deploy Odoo containers with horizontal scaling.
  - Use PostgreSQL clustering or a failover replica.
- Set up a secondary backup Suprema BioS[system_requirement.md](system_requirement.md)tar 2 API integration endpoint (if supported).
