Here’s an optimized version of your setup() method that fixes blurry text issues in Leaflet.js, improves tile rendering quality, and ensures smooth map interaction:

🔹 Key Improvements:
	1.	Enabled Retina Tiles (detectRetina: true) for high-DPI displays.
	2.	Set image-rendering: crisp-edges; for sharp text rendering.
	3.	Disabled CSS transforms (transform: none !important;) to prevent blurry labels.
	4.	**Forced pixel-perfect rendering (image-rendering: pixelated;).
	5.	Fixed maxBounds to prevent invalid lat/lon issues.

✅ Updated Code

setup() {
    this.leafletMap = null;
    this.markers = [];
    this.polylines = [];
    this.mapContainerRef = useRef("mapContainer");
    this.state = useState({
        closedGroupIds: [],
        expendedPinList: false,
    });
    this.nextId = 1;

    useEffect(() => {
        // Initialize Leaflet Map
        this.leafletMap = L.map(this.mapContainerRef.el, {
            preferCanvas: false, // Ensure crisp vector rendering
            zoomSnap: 0.5, // Prevent fractional zoom (avoids blurring)
            maxBounds: [
                [90, -180], // Top-Left (Lat, Lng)
                [-90, 180], // Bottom-Right (Lat, Lng)
            ],
        });

        // Set attribution
        this.leafletMap.attributionControl.setPrefix(
            '<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">Leaflet</a>'
        );

        // High-Quality Tile Layer (Retina Support)
        L.tileLayer(this.apiTilesRoute, {
            attribution: mapTileAttribution,
            tileSize: 256, // Standard tile size for better rendering
            detectRetina: true, // Enables high-DPI tiles
            minZoom: 2,
            maxZoom: 19,
            id: "mapbox/streets-v11",
            accessToken: this.props.model.metaData.mapBoxToken,
        }).addTo(this.leafletMap);

        // Apply CSS fixes for sharper rendering
        this.fixMapRenderingIssues();
    }, []);

    useEffect(() => {
        this.updateMap();
    });

    this.pinListRef = useRef("pinList");

    useSortable({
        enable: () => this.props.model.canResequence,
        ref: this.pinListRef,
        elements: ".o-map-renderer--pin-located",
        handle: ".o_row_handle",
        onDrop: async (params) => {
            const rowId = parseInt(params.element.dataset.id);
            const previousRowId = parseInt(params.previous?.dataset?.id) || null;
            await this.props.model.resequence(rowId, previousRowId);
        },
    });

    onWillUpdateProps(this.onWillUpdateProps);
    onWillUnmount(this.onWillUnmount);
},

// 🔹 Fix blurry text by overriding CSS transforms & forcing high-quality rendering
fixMapRenderingIssues() {
    L.DomUtil.setTransform = function (el, offset) {
        el.style.transform = `translate(${offset.x}px, ${offset.y}px)`;
    };

    const style = document.createElement("style");
    style.innerHTML = `
        .leaflet-tile {
            image-rendering: crisp-edges;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: pixelated;
        }
        .leaflet-pane,
        .leaflet-map-pane,
        .leaflet-tile-pane {
            transform: none !important;
        }
    `;
    document.head.appendChild(style);
}

🔹 What’s Fixed?

✅ Blurry text in Leaflet map (due to CSS transforms).
✅ Improved tile rendering for high-DPI screens (Retina).
✅ Crisp markers and labels using CSS overrides.
✅ Fixed maxBounds issues (previously incorrect).

Your map should now render sharp text & high-quality tiles. 🚀 Let me know if you need further refinements! 😃