
from odoo import models, api
class ResUsers(models.Model):
    _inherit = 'res.users'

    @api.model
    def _remove_technical_features_group(self, groups):
        """Remove Technical Features (base.group_no_one) if the user is not an admin."""
        tech_features_group = self.env.ref('base.group_no_one', raise_if_not_found=False)
        admin_group = self.env.ref('base.group_system', raise_if_not_found=False)

        # Ensure `groups` list excludes Technical Features for non-admin users
        if tech_features_group and admin_group:
            groups = [g for g in groups if g != tech_features_group.id or self.has_group('base.group_system')]
        return groups

    def write(self, values):
        if 'groups_id' in values:
            # Extract groups to be assigned or unassigned
            operation, _, group_ids = values['groups_id'][0]  # format [(operation, group_id, group_ids)]
            if operation == 6:  # Replace all groups
                values['groups_id'][0][2] = self._remove_technical_features_group(group_ids)
            elif operation in [4, 5]:  # Add/Remove groups
                groups_to_update = values['groups_id'][0][2]
                if groups_to_update:
                    values['groups_id'][0][2] = self._remove_technical_features_group(groups_to_update)

        return super(ResUsers, self).write(values)

    def _post_login(self):
        """Ensure the group is removed during login for non-admin users."""
        tech_features_group = self.env.ref('base.group_no_one', raise_if_not_found=False)
        if tech_features_group and not self.has_group('base.group_system') and tech_features_group in self.groups_id:
            self.groups_id -= tech_features_group