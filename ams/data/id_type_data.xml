<odoo>
    <data noupdate="1">
         <!-- Predefined data -->

        <!-- Record for National ID -->
        <record id="ams_id_type_predefined_national_id" model="ams.id_type">
            <field name="name">National ID</field>
            <field name="is_predefined">True</field>
        </record>

        <!-- Record for Resident ID -->
        <record id="ams_id_type_predefined_resident_id" model="ams.id_type">
            <field name="name">Resident ID</field>
            <field name="is_predefined">True</field>
        </record>

        <!-- Record for Passport -->
        <record id="ams_id_type_predefined_passport" model="ams.id_type">
            <field name="name">Passport</field>
            <field name="is_predefined">True</field>
        </record>

        <record id="ams_id_type_other_id" model="ams.id_type">
            <field name="name">Other ID</field>
            <field name="is_predefined">True</field>
        </record>
    </data>
</odoo>
