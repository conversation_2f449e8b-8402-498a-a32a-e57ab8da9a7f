# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ams
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-28 17:37+0000\n"
"PO-Revision-Date: 2025-01-28 17:37+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: ams
#: model:ir.model,name:ams.model_ams_user
msgid "AMS User"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_building
msgid "Building"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.building_action
#: model:ir.ui.menu,name:ams.building_menu
#: model:ir.model.fields,field_description:ams.field_ams_access_group__building_id
msgid "Buildings"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,help:ams.building_action
msgid "Create a building"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,help:ams.building_action
msgid "Create building to organize access groups by location"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__api_type
msgid "API Type"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__absent_time_criteria
msgid "Absent Time Criteria"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule__purpose_type__ac
#: model:ir.model.fields.selection,name:ams.selection__ams_time_unit__purpose_type__ac
msgid "Access Control"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_access_group
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__ac_group_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__ac_group_id
#: model:ir.model.fields,field_description:ams.field_ams_access_level__ac_group_id
msgid "Access Group"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_access_group_user
#: model:ir.model,name:ams.model_ams_access_group_user_group
msgid "Access Group User"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.access_group_action
#: model:ir.model.fields,field_description:ams.field_ams_user__ac_group_ids
#: model:ir.ui.menu,name:ams.access_group_menu
msgid "Access Groups"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_access_level
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__ac_level_id
msgid "Access Level"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.access_level_action
#: model:ir.model.fields,field_description:ams.field_ams_access_group__ac_levels_ids
#: model:ir.ui.menu,name:ams.access_level_menu
msgid "Access Levels"
msgstr ""

#. module: ams
#: model:ir.ui.menu,name:ams.top_menu
msgid "Access Management"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_access_level__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_card__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_card_type__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_device__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_device_group__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_door__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_door_group__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_event_log__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_id_type__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_schedule__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_user__message_needaction
#: model:ir.model.fields,field_description:ams.field_ams_user_group__message_needaction
msgid "Action Needed"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_device__activate
#: model:ir.model.fields,field_description:ams.field_ams_user__activate
msgid "Activate"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_level__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_card__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_card_type__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_device__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_device_group__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_door__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_door_group__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_event_log__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_id_type__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_user__activity_ids
#: model:ir.model.fields,field_description:ams.field_ams_user_group__activity_ids
msgid "Activities"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_access_level__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_card__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_card_type__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_device__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_device_group__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_door__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_door_group__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_event_log__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_id_type__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_schedule__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_user__activity_exception_decoration
#: model:ir.model.fields,field_description:ams.field_ams_user_group__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_access_level__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_card__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_card_type__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_device__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_device_group__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_door__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_door_group__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_event_log__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_id_type__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_schedule__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_user__activity_state
#: model:ir.model.fields,field_description:ams.field_ams_user_group__activity_state
msgid "Activity State"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_access_level__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_card__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_card_type__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_device__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_device_group__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_door__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_door_group__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_event_log__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_id_type__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_schedule__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_user__activity_type_icon
#: model:ir.model.fields,field_description:ams.field_ams_user_group__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule__purpose_type__all
#: model:ir.model.fields.selection,name:ams.selection__ams_time_unit__purpose_type__all
msgid "All"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_time_unit__apply_min_max
msgid "Apply Min Checkin Time & Max Checkout Time"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__apply_min_max
msgid "Apply Min Max"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_access_level__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_card__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_card_type__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_device__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_device_group__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_door__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_door_group__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_event_log__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_id_type__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_schedule__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_user__message_attachment_count
#: model:ir.model.fields,field_description:ams.field_ams_user_group__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: ams
#: model_terms:ir.ui.view,arch_db:ams.ams_base_card_view_form
#: model_terms:ir.ui.view,arch_db:ams.event_log_view_form
msgid "Basic Info"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_bs_biostar_ac_api_client
msgid "Biostar AC API Client"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_base_biostar_api_client
msgid "Biostar API Client"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_bs_biostar_device_api_client
msgid "Biostar Device API Client"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_bs_biostar_user_api_client
msgid "Biostar User API Client"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_card
msgid "Card"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card__card_mask
msgid "Card Mask"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card__card_number
msgid "Card Number"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card__card_slot
msgid "Card Slot"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_card_type
#: model:ir.model.fields,field_description:ams.field_ams_card__card_type_id
#: model:ir.ui.menu,name:ams.card_type_menu
msgid "Card Type"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.card_type_action
msgid "Card Types"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.card_action
#: model:ir.model.fields,field_description:ams.field_ams_user__card_ids
#: model:ir.ui.menu,name:ams.card_menu
#: model_terms:ir.ui.view,arch_db:ams.user_view_form
msgid "Cards"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__code_name
msgid "Code Name"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__color
msgid "Color"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__company_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__company_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__company_id
#: model:ir.model.fields,field_description:ams.field_ams_access_level__company_id
#: model:ir.model.fields,field_description:ams.field_ams_card__company_id
#: model:ir.model.fields,field_description:ams.field_ams_card_type__company_id
#: model:ir.model.fields,field_description:ams.field_ams_device__company_id
#: model:ir.model.fields,field_description:ams.field_ams_device_group__company_id
#: model:ir.model.fields,field_description:ams.field_ams_door__company_id
#: model:ir.model.fields,field_description:ams.field_ams_door_group__company_id
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__company_id
#: model:ir.model.fields,field_description:ams.field_ams_event_log__company_id
#: model:ir.model.fields,field_description:ams.field_ams_id_type__company_id
#: model:ir.model.fields,field_description:ams.field_ams_schedule__company_id
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__company_id
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__company_id
#: model:ir.model.fields,field_description:ams.field_ams_user__company_id
#: model:ir.model.fields,field_description:ams.field_ams_user_group__company_id
#: model:ir.ui.menu,name:ams.company_menu
msgid "Company"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__config_applied
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__config_applied
msgid "Configuration Applied"
msgstr ""

#. module: ams
#: model:ir.ui.menu,name:ams.config_menu
msgid "Configurations"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.door_action
msgid "Create a door"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.door_group_action
msgid "Create a door group"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.card_action
msgid "Create a model"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.schedule_action
msgid "Create a schedule"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.schedule_day_action
msgid "Create a schedule_day"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.access_group_action
#: model_terms:ir.actions.act_window,help:ams.device_action
#: model_terms:ir.actions.act_window,help:ams.device_group_action
#: model_terms:ir.actions.act_window,help:ams.event_log_action
#: model_terms:ir.actions.act_window,help:ams.user_action
#: model_terms:ir.actions.act_window,help:ams.user_group_action
msgid "Create access group"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.access_level_action
msgid "Create access level"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.access_group_action
#: model_terms:ir.actions.act_window,help:ams.device_action
#: model_terms:ir.actions.act_window,help:ams.device_group_action
#: model_terms:ir.actions.act_window,help:ams.event_log_action
#: model_terms:ir.actions.act_window,help:ams.user_action
#: model_terms:ir.actions.act_window,help:ams.user_group_action
msgid "Create an access group"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.access_level_action
msgid "Create an access level"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.card_type_action
msgid "Create card_type"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.door_action
msgid "Create door"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.door_group_action
msgid "Create door group"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.card_action
msgid "Create model"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.schedule_action
msgid "Create schedule"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.schedule_day_action
msgid "Create schedule_day"
msgstr ""

#. module: ams
#: model_terms:ir.actions.act_window,help:ams.time_unit_action
msgid "Create shift unit"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_access_level__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_card__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_card_type__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_device__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_device_group__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_door__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_door_group__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_event_log__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_id_type__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_schedule__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_user__create_uid
#: model:ir.model.fields,field_description:ams.field_ams_user_group__create_uid
msgid "Created by"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__create_date
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__create_date
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__create_date
#: model:ir.model.fields,field_description:ams.field_ams_access_level__create_date
#: model:ir.model.fields,field_description:ams.field_ams_card__create_date
#: model:ir.model.fields,field_description:ams.field_ams_card_type__create_date
#: model:ir.model.fields,field_description:ams.field_ams_device__create_date
#: model:ir.model.fields,field_description:ams.field_ams_device_group__create_date
#: model:ir.model.fields,field_description:ams.field_ams_door__create_date
#: model:ir.model.fields,field_description:ams.field_ams_door_group__create_date
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__create_date
#: model:ir.model.fields,field_description:ams.field_ams_event_log__create_date
#: model:ir.model.fields,field_description:ams.field_ams_id_type__create_date
#: model:ir.model.fields,field_description:ams.field_ams_schedule__create_date
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__create_date
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__create_date
#: model:ir.model.fields,field_description:ams.field_ams_user__create_date
#: model:ir.model.fields,field_description:ams.field_ams_user_group__create_date
msgid "Created on"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_schedule__cycle_days
#: model_terms:ir.ui.view,arch_db:ams.schedule_view_form
msgid "Cycle Days"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule__schedule_type__daily
msgid "Daily"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_schedule__day_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__week_day
msgid "Day"
msgstr ""

#. module: ams
#: model_terms:ir.ui.view,arch_db:ams.schedule_view_form
msgid "Days"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_schedule__default_time_unit_id
#: model_terms:ir.ui.view,arch_db:ams.schedule_view_form
msgid "Default Time Unit"
msgstr ""

#. module: ams
#: model:ir.ui.menu,name:ams.menu_hr_department
msgid "Departments"
msgstr ""

#. module: ams
#: model_terms:ir.ui.view,arch_db:ams.ams_base_card_view_list
msgid "Desc"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__description
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__description
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__description
#: model:ir.model.fields,field_description:ams.field_ams_access_level__description
#: model:ir.model.fields,field_description:ams.field_ams_card__description
#: model:ir.model.fields,field_description:ams.field_ams_card_type__description
#: model:ir.model.fields,field_description:ams.field_ams_device__description
#: model:ir.model.fields,field_description:ams.field_ams_device_group__description
#: model:ir.model.fields,field_description:ams.field_ams_door__description
#: model:ir.model.fields,field_description:ams.field_ams_door_group__description
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__description
#: model:ir.model.fields,field_description:ams.field_ams_event_log__description
#: model:ir.model.fields,field_description:ams.field_ams_schedule__description
#: model:ir.model.fields,field_description:ams.field_ams_user__description
#: model:ir.model.fields,field_description:ams.field_ams_user_group__description
msgid "Description"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_device
#: model:ir.ui.menu,name:ams.device_menu
msgid "Device"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_device_group
#: model:ir.model.fields,field_description:ams.field_ams_device__device_group_id
#: model:ir.model.fields,field_description:ams.field_ams_user__device_group_id
#: model:ir.ui.menu,name:ams.device_group_menu
msgid "Device Group"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.device_group_action
msgid "Device Groups"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_device__device_serial
#: model:ir.model.fields,field_description:ams.field_ams_event_log__device_serial
msgid "Device Serial"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.device_action
#: model:ir.model.fields,field_description:ams.field_ams_access_group__device_ids
#: model:ir.model.fields,field_description:ams.field_ams_device_group__device_ids
#: model:ir.ui.menu,name:ams.device_config_menu
#: model_terms:ir.ui.view,arch_db:ams.device_group_view_form
msgid "Devices"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card__display_card_id
msgid "Display Card Id"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__display_name
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__display_name
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__display_name
#: model:ir.model.fields,field_description:ams.field_ams_access_level__display_name
#: model:ir.model.fields,field_description:ams.field_ams_card__display_name
#: model:ir.model.fields,field_description:ams.field_ams_card_type__display_name
#: model:ir.model.fields,field_description:ams.field_ams_device__display_name
#: model:ir.model.fields,field_description:ams.field_ams_device_group__display_name
#: model:ir.model.fields,field_description:ams.field_ams_door__display_name
#: model:ir.model.fields,field_description:ams.field_ams_door_group__display_name
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__display_name
#: model:ir.model.fields,field_description:ams.field_ams_event_log__display_name
#: model:ir.model.fields,field_description:ams.field_ams_id_type__display_name
#: model:ir.model.fields,field_description:ams.field_ams_schedule__display_name
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__display_name
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__display_name
#: model:ir.model.fields,field_description:ams.field_ams_user__display_name
#: model:ir.model.fields,field_description:ams.field_ams_user_group__display_name
msgid "Display Name"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_door
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__door_id
msgid "Door"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_door_group
#: model:ir.model.fields,field_description:ams.field_ams_door__door_group_id
msgid "Door Group"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.door_group_action
#: model:ir.ui.menu,name:ams.door_group_menu
msgid "Door Groups"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__door_record_id
msgid "Door Record"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_door_schedule
msgid "Door Schedule"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_level__door_schedule_ids
#: model_terms:ir.ui.view,arch_db:ams.access_level_view_form
msgid "Door Schedules"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.door_action
#: model:ir.model.fields,field_description:ams.field_ams_door_group__door_ids
#: model:ir.ui.menu,name:ams.door_menu
#: model_terms:ir.ui.view,arch_db:ams.door_group_view_form
msgid "Doors"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__duration
msgid "Duration"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_user__email
msgid "Email"
msgstr ""

#. module: ams
#: model:ir.ui.menu,name:ams.menu_hr_employee
msgid "Employees"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_user__end_datetime
msgid "End Datetime"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__end_limit
msgid "End Limit"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__end_time
msgid "End Time"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_time_unit__end_time
msgid "End time of shift"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__enroll_name
msgid "Enroll Name"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__enroll_number
#: model:ir.model.fields,field_description:ams.field_ams_user__enroll_number
msgid "Enroll Number"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__error
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__error
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__error
#: model:ir.model.fields,field_description:ams.field_ams_access_level__error
#: model:ir.model.fields,field_description:ams.field_ams_card__error
#: model:ir.model.fields,field_description:ams.field_ams_card_type__error
#: model:ir.model.fields,field_description:ams.field_ams_device__error
#: model:ir.model.fields,field_description:ams.field_ams_device_group__error
#: model:ir.model.fields,field_description:ams.field_ams_door__error
#: model:ir.model.fields,field_description:ams.field_ams_door_group__error
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__error
#: model:ir.model.fields,field_description:ams.field_ams_event_log__error
#: model:ir.model.fields,field_description:ams.field_ams_schedule__error
#: model:ir.model.fields,field_description:ams.field_ams_user__error
#: model:ir.model.fields,field_description:ams.field_ams_user_group__error
msgid "Error"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_access_level__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_card__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_card_type__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_device__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_device_group__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_door__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_door_group__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_event_log__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_schedule__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_user__error_msg
#: model:ir.model.fields,field_description:ams.field_ams_user_group__error_msg
msgid "Error Message"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__event_code
msgid "Event Code"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__event_datetime
msgid "Event DateTime"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__event_id
msgid "Event ID"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_event_log
#: model:ir.ui.menu,name:ams.event_log_menu
msgid "Event Log"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.event_log_action
msgid "Event Logs"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__executed_datetime
msgid "Executed DateTime"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_time_unit__unit_type__flexible
msgid "Flexible"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_level__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_card__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_card_type__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_device__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_device_group__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_door__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_door_group__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_event_log__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_id_type__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_user__message_follower_ids
#: model:ir.model.fields,field_description:ams.field_ams_user_group__message_follower_ids
msgid "Followers"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_level__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_card__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_card_type__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_device__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_device_group__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_door__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_door_group__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_event_log__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_id_type__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_user__message_partner_ids
#: model:ir.model.fields,field_description:ams.field_ams_user_group__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_access_group__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_access_group_user__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_access_group_user_group__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_access_level__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_card__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_card_type__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_device__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_device_group__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_door__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_door_group__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_door_schedule__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_event_log__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_id_type__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_schedule__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_schedule_day__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_time_unit__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_user__activity_type_icon
#: model:ir.model.fields,help:ams.field_ams_user_group__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule_day__week_day__7
msgid "Friday"
msgstr ""

#. module: ams
#: model_terms:ir.ui.view,arch_db:ams.schedule_view_form
msgid "Generate Days"
msgstr ""

#. module: ams
#: model_terms:ir.ui.view,arch_db:ams.ams_base_card_view_search
#: model_terms:ir.ui.view,arch_db:ams.schedule_day_view_search
msgid "Group By"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__group_users_ids
msgid "Group Users"
msgstr ""

#. module: ams
#: model:ir.ui.menu,name:ams.menu_hr_root
msgid "HR"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__has_image
msgid "Has Image"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__has_message
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__has_message
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__has_message
#: model:ir.model.fields,field_description:ams.field_ams_access_level__has_message
#: model:ir.model.fields,field_description:ams.field_ams_card__has_message
#: model:ir.model.fields,field_description:ams.field_ams_card_type__has_message
#: model:ir.model.fields,field_description:ams.field_ams_device__has_message
#: model:ir.model.fields,field_description:ams.field_ams_device_group__has_message
#: model:ir.model.fields,field_description:ams.field_ams_door__has_message
#: model:ir.model.fields,field_description:ams.field_ams_door_group__has_message
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__has_message
#: model:ir.model.fields,field_description:ams.field_ams_event_log__has_message
#: model:ir.model.fields,field_description:ams.field_ams_id_type__has_message
#: model:ir.model.fields,field_description:ams.field_ams_schedule__has_message
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__has_message
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__has_message
#: model:ir.model.fields,field_description:ams.field_ams_user__has_message
#: model:ir.model.fields,field_description:ams.field_ams_user_group__has_message
msgid "Has Message"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__id
#: model:ir.model.fields,field_description:ams.field_ams_access_level__id
#: model:ir.model.fields,field_description:ams.field_ams_card__id
#: model:ir.model.fields,field_description:ams.field_ams_card_type__id
#: model:ir.model.fields,field_description:ams.field_ams_device__id
#: model:ir.model.fields,field_description:ams.field_ams_device_group__id
#: model:ir.model.fields,field_description:ams.field_ams_door__id
#: model:ir.model.fields,field_description:ams.field_ams_door_group__id
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__id
#: model:ir.model.fields,field_description:ams.field_ams_event_log__id
#: model:ir.model.fields,field_description:ams.field_ams_id_type__id
#: model:ir.model.fields,field_description:ams.field_ams_schedule__id
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__id
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__id
#: model:ir.model.fields,field_description:ams.field_ams_user__id
#: model:ir.model.fields,field_description:ams.field_ams_user_group__id
msgid "ID"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_id_type
#: model:ir.model.fields,field_description:ams.field_res_partner__id_type_id
#: model:ir.model.fields,field_description:ams.field_res_users__id_type_id
msgid "ID Type"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_device__ip
msgid "IP Address"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_access_level__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_card__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_card_type__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_device__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_device_group__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_door__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_door_group__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_event_log__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_id_type__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_schedule__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_user__activity_exception_icon
#: model:ir.model.fields,field_description:ams.field_ams_user_group__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_access_group__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_access_group_user__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_access_group_user_group__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_access_level__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_card__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_card_type__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_device__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_device_group__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_door__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_door_group__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_door_schedule__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_event_log__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_id_type__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_schedule__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_schedule_day__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_time_unit__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_user__activity_exception_icon
#: model:ir.model.fields,help:ams.field_ams_user_group__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_access_group__message_needaction
#: model:ir.model.fields,help:ams.field_ams_access_group_user__message_needaction
#: model:ir.model.fields,help:ams.field_ams_access_group_user_group__message_needaction
#: model:ir.model.fields,help:ams.field_ams_access_level__message_needaction
#: model:ir.model.fields,help:ams.field_ams_card__message_needaction
#: model:ir.model.fields,help:ams.field_ams_card_type__message_needaction
#: model:ir.model.fields,help:ams.field_ams_device__message_needaction
#: model:ir.model.fields,help:ams.field_ams_device_group__message_needaction
#: model:ir.model.fields,help:ams.field_ams_door__message_needaction
#: model:ir.model.fields,help:ams.field_ams_door_group__message_needaction
#: model:ir.model.fields,help:ams.field_ams_door_schedule__message_needaction
#: model:ir.model.fields,help:ams.field_ams_event_log__message_needaction
#: model:ir.model.fields,help:ams.field_ams_id_type__message_needaction
#: model:ir.model.fields,help:ams.field_ams_schedule__message_needaction
#: model:ir.model.fields,help:ams.field_ams_schedule_day__message_needaction
#: model:ir.model.fields,help:ams.field_ams_time_unit__message_needaction
#: model:ir.model.fields,help:ams.field_ams_user__message_needaction
#: model:ir.model.fields,help:ams.field_ams_user_group__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_access_group__message_has_error
#: model:ir.model.fields,help:ams.field_ams_access_group__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_access_group_user__message_has_error
#: model:ir.model.fields,help:ams.field_ams_access_group_user__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_access_group_user_group__message_has_error
#: model:ir.model.fields,help:ams.field_ams_access_group_user_group__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_access_level__message_has_error
#: model:ir.model.fields,help:ams.field_ams_access_level__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_card__message_has_error
#: model:ir.model.fields,help:ams.field_ams_card__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_card_type__message_has_error
#: model:ir.model.fields,help:ams.field_ams_card_type__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_device__message_has_error
#: model:ir.model.fields,help:ams.field_ams_device__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_device_group__message_has_error
#: model:ir.model.fields,help:ams.field_ams_device_group__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_door__message_has_error
#: model:ir.model.fields,help:ams.field_ams_door__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_door_group__message_has_error
#: model:ir.model.fields,help:ams.field_ams_door_group__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_door_schedule__message_has_error
#: model:ir.model.fields,help:ams.field_ams_door_schedule__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_event_log__message_has_error
#: model:ir.model.fields,help:ams.field_ams_event_log__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_id_type__message_has_error
#: model:ir.model.fields,help:ams.field_ams_id_type__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_schedule__message_has_error
#: model:ir.model.fields,help:ams.field_ams_schedule__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_schedule_day__message_has_error
#: model:ir.model.fields,help:ams.field_ams_schedule_day__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_time_unit__message_has_error
#: model:ir.model.fields,help:ams.field_ams_time_unit__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_user__message_has_error
#: model:ir.model.fields,help:ams.field_ams_user__message_has_sms_error
#: model:ir.model.fields,help:ams.field_ams_user_group__message_has_error
#: model:ir.model.fields,help:ams.field_ams_user_group__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__index
msgid "Index"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card__is_assigned
msgid "Is Assigned"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__is_biometric
msgid "Is Biometric"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card__is_blocked
msgid "Is Blocked"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__is_day_off
msgid "Is Day Off"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_access_level__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_card__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_card_type__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_device__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_device_group__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_door__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_door_group__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_event_log__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_id_type__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_schedule__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_user__message_is_follower
#: model:ir.model.fields,field_description:ams.field_ams_user_group__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__is_overnight
msgid "Is Overnight"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_access_level__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_card__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_card_type__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_device__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_device_group__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_door__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_door_group__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_event_log__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_id_type__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_schedule__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_user__is_predefined
#: model:ir.model.fields,field_description:ams.field_ams_user_group__is_predefined
msgid "Is Predefined"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__is_units_mixed_types
msgid "Is Units Mixed Types"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__is_units_overlapped
msgid "Is Units Overlapped"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__is_visitor_group
msgid "Is Visitor Group"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card__issue_count
msgid "Issue Count"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_device__last_log_date
msgid "Last Log Date"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_device__last_log_id
msgid "Last Log ID"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_access_level__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_card__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_card_type__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_device__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_device_group__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_door__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_door_group__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_event_log__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_schedule__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_user__last_sync_date
#: model:ir.model.fields,field_description:ams.field_ams_user_group__last_sync_date
msgid "Last Sync Date"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_access_level__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_card__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_card_type__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_device__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_device_group__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_door__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_door_group__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_event_log__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_id_type__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_schedule__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_user__write_uid
#: model:ir.model.fields,field_description:ams.field_ams_user_group__write_uid
msgid "Last Updated by"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__write_date
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__write_date
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__write_date
#: model:ir.model.fields,field_description:ams.field_ams_access_level__write_date
#: model:ir.model.fields,field_description:ams.field_ams_card__write_date
#: model:ir.model.fields,field_description:ams.field_ams_card_type__write_date
#: model:ir.model.fields,field_description:ams.field_ams_device__write_date
#: model:ir.model.fields,field_description:ams.field_ams_device_group__write_date
#: model:ir.model.fields,field_description:ams.field_ams_door__write_date
#: model:ir.model.fields,field_description:ams.field_ams_door_group__write_date
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__write_date
#: model:ir.model.fields,field_description:ams.field_ams_event_log__write_date
#: model:ir.model.fields,field_description:ams.field_ams_id_type__write_date
#: model:ir.model.fields,field_description:ams.field_ams_schedule__write_date
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__write_date
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__write_date
#: model:ir.model.fields,field_description:ams.field_ams_user__write_date
#: model:ir.model.fields,field_description:ams.field_ams_user_group__write_date
msgid "Last Updated on"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_device__log_active
msgid "Log Active"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__log_date
msgid "Log Date"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__max_checkout_time
msgid "Max Checkout Time"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_time_unit__start_limit
msgid "Max allowed checkin time when shift is flexible"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_time_unit__end_limit
msgid "Max allowed checkout time when Time is flexible"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_time_unit__max_checkout_time
msgid "Maximum time allowed to checkout after end of Time"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_access_level__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_card__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_card_type__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_device__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_device_group__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_door__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_door_group__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_event_log__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_id_type__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_schedule__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_user__message_has_error
#: model:ir.model.fields,field_description:ams.field_ams_user_group__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_level__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_card__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_card_type__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_device__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_device_group__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_door__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_door_group__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_event_log__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_id_type__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_user__message_ids
#: model:ir.model.fields,field_description:ams.field_ams_user_group__message_ids
msgid "Messages"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__min_checkin_time
msgid "Min Check-in Time"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_time_unit__min_checkin_time
msgid "Minimum time allowed to checkin before start of Time"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card__mobile_card
msgid "Mobile Card"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card_type__mode
msgid "Mode"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule_day__week_day__3
msgid "Monday"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_access_level__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_card__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_card_type__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_device__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_device_group__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_door__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_door_group__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_event_log__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_id_type__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_schedule__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_user__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_user_group__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__name
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__name
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__name
#: model:ir.model.fields,field_description:ams.field_ams_access_level__name
#: model:ir.model.fields,field_description:ams.field_ams_card__name
#: model:ir.model.fields,field_description:ams.field_ams_card_type__name
#: model:ir.model.fields,field_description:ams.field_ams_device__name
#: model:ir.model.fields,field_description:ams.field_ams_device_group__name
#: model:ir.model.fields,field_description:ams.field_ams_door__name
#: model:ir.model.fields,field_description:ams.field_ams_door_group__name
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__name
#: model:ir.model.fields,field_description:ams.field_ams_event_log__name
#: model:ir.model.fields,field_description:ams.field_ams_id_type__name
#: model:ir.model.fields,field_description:ams.field_ams_schedule__name
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__name
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__name
#: model:ir.model.fields,field_description:ams.field_ams_user__name
#: model:ir.model.fields,field_description:ams.field_ams_user_group__name
msgid "Name"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_access_level__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_card__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_card_type__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_device__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_device_group__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_door__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_door_group__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_event_log__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_id_type__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_schedule__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_user__activity_date_deadline
#: model:ir.model.fields,field_description:ams.field_ams_user_group__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_access_level__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_card__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_card_type__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_device__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_device_group__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_door__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_door_group__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_event_log__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_id_type__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_schedule__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_user__activity_summary
#: model:ir.model.fields,field_description:ams.field_ams_user_group__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_access_level__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_card__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_card_type__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_device__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_device_group__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_door__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_door_group__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_event_log__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_id_type__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_schedule__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_user__activity_type_id
#: model:ir.model.fields,field_description:ams.field_ams_user_group__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_time_unit__unit_type__normal
msgid "Normal"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_access_level__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_card__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_card_type__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_device__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_device_group__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_door__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_door_group__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_event_log__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_id_type__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_schedule__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_user__message_needaction_counter
#: model:ir.model.fields,field_description:ams.field_ams_user_group__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_access_level__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_card__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_card_type__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_device__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_device_group__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_door__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_door_group__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_event_log__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_id_type__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_schedule__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_user__message_has_error_counter
#: model:ir.model.fields,field_description:ams.field_ams_user_group__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_access_group__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_access_group_user__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_access_group_user_group__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_access_level__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_card__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_card_type__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_device__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_device_group__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_door__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_door_group__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_door_schedule__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_event_log__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_id_type__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_schedule__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_schedule_day__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_time_unit__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_user__message_needaction_counter
#: model:ir.model.fields,help:ams.field_ams_user_group__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_access_group__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_access_group_user__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_access_group_user_group__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_access_level__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_card__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_card_type__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_device__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_device_group__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_door__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_door_group__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_door_schedule__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_event_log__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_id_type__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_schedule__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_schedule_day__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_time_unit__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_user__message_has_error_counter
#: model:ir.model.fields,help:ams.field_ams_user_group__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_time_unit__unit_type__open
msgid "Open"
msgstr ""

#. module: ams
#: model:ir.ui.menu,name:ams.op_menu
msgid "Operations"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_access_level__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_card__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_card_type__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_device__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_device_group__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_door__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_door_group__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_event_log__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_schedule__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_user__origin_id
#: model:ir.model.fields,field_description:ams.field_ams_user_group__origin_id
msgid "Origin ID"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_device_group__parent_id
#: model:ir.model.fields,field_description:ams.field_ams_user_group__parent_id
msgid "Parent Group"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_res_partner
msgid "Partner"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_device__port
msgid "Port"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_schedule__purpose_type
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__purpose_type
#: model_terms:ir.ui.view,arch_db:ams.schedule_view_form
#: model_terms:ir.ui.view,arch_db:ams.schedule_view_list
#: model_terms:ir.ui.view,arch_db:ams.time_unit_view_form
#: model_terms:ir.ui.view,arch_db:ams.time_unit_view_list
msgid "Purpose Type"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__record_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__record_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__record_id
#: model:ir.model.fields,field_description:ams.field_ams_access_level__record_id
#: model:ir.model.fields,field_description:ams.field_ams_card__record_id
#: model:ir.model.fields,field_description:ams.field_ams_card_type__record_id
#: model:ir.model.fields,field_description:ams.field_ams_device__record_id
#: model:ir.model.fields,field_description:ams.field_ams_device_group__record_id
#: model:ir.model.fields,field_description:ams.field_ams_door__record_id
#: model:ir.model.fields,field_description:ams.field_ams_door_group__record_id
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__record_id
#: model:ir.model.fields,field_description:ams.field_ams_event_log__record_id
#: model:ir.model.fields,field_description:ams.field_ams_schedule__record_id
#: model:ir.model.fields,field_description:ams.field_ams_user__record_id
#: model:ir.model.fields,field_description:ams.field_ams_user_group__record_id
msgid "Record ID"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_time_unit__duration
msgid "Required time to complete Time Unit"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__response_code
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__response_code
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__response_code
#: model:ir.model.fields,field_description:ams.field_ams_access_level__response_code
#: model:ir.model.fields,field_description:ams.field_ams_card__response_code
#: model:ir.model.fields,field_description:ams.field_ams_card_type__response_code
#: model:ir.model.fields,field_description:ams.field_ams_device__response_code
#: model:ir.model.fields,field_description:ams.field_ams_device_group__response_code
#: model:ir.model.fields,field_description:ams.field_ams_door__response_code
#: model:ir.model.fields,field_description:ams.field_ams_door_group__response_code
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__response_code
#: model:ir.model.fields,field_description:ams.field_ams_event_log__response_code
#: model:ir.model.fields,field_description:ams.field_ams_schedule__response_code
#: model:ir.model.fields,field_description:ams.field_ams_user__response_code
#: model:ir.model.fields,field_description:ams.field_ams_user_group__response_code
msgid "Response Code"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_access_level__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_card__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_card_type__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_device__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_device_group__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_door__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_door_group__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_event_log__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_id_type__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_schedule__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_user__activity_user_id
#: model:ir.model.fields,field_description:ams.field_ams_user_group__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_access_level__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_card__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_card_type__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_device__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_device_group__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_door__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_door_group__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_event_log__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_id_type__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_schedule__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_user__message_has_sms_error
#: model:ir.model.fields,field_description:ams.field_ams_user_group__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule_day__week_day__1
msgid "Saturday"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_schedule
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__schedule_id
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__schedule_id
#: model_terms:ir.ui.view,arch_db:ams.schedule_day_view_search
msgid "Schedule"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_schedule_day
msgid "Schedule Day"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.schedule_day_action
#: model_terms:ir.ui.view,arch_db:ams.schedule_day_view_list
msgid "Schedule Days"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__schedule_record_id
msgid "Schedule Record"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_schedule__schedule_type
#: model_terms:ir.ui.view,arch_db:ams.schedule_view_form
#: model_terms:ir.ui.view,arch_db:ams.schedule_view_list
msgid "Schedule Type"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.schedule_action
#: model:ir.ui.menu,name:ams.schedule_menu
msgid "Schedules"
msgstr ""

#. module: ams
#: model_terms:ir.ui.view,arch_db:ams.time_unit_view_form
msgid "Setting"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_user__start_datetime
msgid "Start Datetime"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__start_limit
msgid "Start Limit"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__start_time
msgid "Start Time"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_time_unit__start_time
msgid "Start time of shift"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card__state
#: model:ir.model.fields,field_description:ams.field_ams_device__state
#: model:ir.model.fields,field_description:ams.field_ams_event_log__state
msgid "State"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card__status
msgid "Status"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_access_group__activity_state
#: model:ir.model.fields,help:ams.field_ams_access_group_user__activity_state
#: model:ir.model.fields,help:ams.field_ams_access_group_user_group__activity_state
#: model:ir.model.fields,help:ams.field_ams_access_level__activity_state
#: model:ir.model.fields,help:ams.field_ams_card__activity_state
#: model:ir.model.fields,help:ams.field_ams_card_type__activity_state
#: model:ir.model.fields,help:ams.field_ams_device__activity_state
#: model:ir.model.fields,help:ams.field_ams_device_group__activity_state
#: model:ir.model.fields,help:ams.field_ams_door__activity_state
#: model:ir.model.fields,help:ams.field_ams_door_group__activity_state
#: model:ir.model.fields,help:ams.field_ams_door_schedule__activity_state
#: model:ir.model.fields,help:ams.field_ams_event_log__activity_state
#: model:ir.model.fields,help:ams.field_ams_id_type__activity_state
#: model:ir.model.fields,help:ams.field_ams_schedule__activity_state
#: model:ir.model.fields,help:ams.field_ams_schedule_day__activity_state
#: model:ir.model.fields,help:ams.field_ams_time_unit__activity_state
#: model:ir.model.fields,help:ams.field_ams_user__activity_state
#: model:ir.model.fields,help:ams.field_ams_user_group__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__sub_code
msgid "Sub Code"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule_day__week_day__2
msgid "Sunday"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__synced
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__synced
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__synced
#: model:ir.model.fields,field_description:ams.field_ams_access_level__synced
#: model:ir.model.fields,field_description:ams.field_ams_card__synced
#: model:ir.model.fields,field_description:ams.field_ams_card_type__synced
#: model:ir.model.fields,field_description:ams.field_ams_device__synced
#: model:ir.model.fields,field_description:ams.field_ams_device_group__synced
#: model:ir.model.fields,field_description:ams.field_ams_door__synced
#: model:ir.model.fields,field_description:ams.field_ams_door_group__synced
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__synced
#: model:ir.model.fields,field_description:ams.field_ams_event_log__synced
#: model:ir.model.fields,field_description:ams.field_ams_schedule__synced
#: model:ir.model.fields,field_description:ams.field_ams_user__synced
#: model:ir.model.fields,field_description:ams.field_ams_user_group__synced
msgid "Synced"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule__purpose_type__ta
#: model:ir.model.fields.selection,name:ams.selection__ams_time_unit__purpose_type__ta
msgid "TA"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__temperature
msgid "Temperature"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule_day__week_day__6
msgid "Thursday"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_time_unit
#: model:ir.ui.menu,name:ams.time_unit_menu
#: model_terms:ir.ui.view,arch_db:ams.time_unit_view_list
msgid "Time Unit"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.time_unit_action
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__time_units_ids
msgid "Time Units"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_event_log__timestamp
msgid "Timestamp"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule_day__week_day__4
msgid "Tuesday"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_card_type__type
msgid "Type"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_access_group__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_access_group_user__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_access_group_user_group__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_access_level__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_card__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_card_type__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_device__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_device_group__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_door__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_door_group__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_door_schedule__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_event_log__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_id_type__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_schedule__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_schedule_day__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_time_unit__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_user__activity_exception_decoration
#: model:ir.model.fields,help:ams.field_ams_user_group__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__unit_type
msgid "Unit Type"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__user_id
#: model:ir.model.fields,field_description:ams.field_ams_card__user_id
#: model:ir.ui.menu,name:ams.user_menu
msgid "User"
msgstr ""

#. module: ams
#: model:ir.model,name:ams.model_ams_user_group
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__user_group_id
#: model:ir.model.fields,field_description:ams.field_ams_user__user_group_id
#: model:ir.ui.menu,name:ams.user_group_menu
msgid "User Group"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.user_group_action
#: model:ir.model.fields,field_description:ams.field_ams_access_group__user_groups_ids
#: model_terms:ir.ui.view,arch_db:ams.access_group_view_form
msgid "User Groups"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_user__user_type
msgid "User Type"
msgstr ""

#. module: ams
#: model:ir.actions.act_window,name:ams.user_action
#: model:ir.model.fields,field_description:ams.field_ams_user_group__user_ids
#: model_terms:ir.ui.view,arch_db:ams.access_group_view_form
#: model_terms:ir.ui.view,arch_db:ams.user_group_view_form
msgid "Users"
msgstr ""

#. module: ams
#: model:ir.model.fields,field_description:ams.field_ams_access_group__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_group_user_group__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_access_level__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_card__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_card_type__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_device__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_device_group__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_door__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_door_group__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_door_schedule__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_event_log__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_id_type__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_schedule_day__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_time_unit__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_user__website_message_ids
#: model:ir.model.fields,field_description:ams.field_ams_user_group__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_access_group__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_access_group_user__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_access_group_user_group__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_access_level__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_card__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_card_type__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_device__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_device_group__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_door__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_door_group__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_door_schedule__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_event_log__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_id_type__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_schedule__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_schedule_day__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_time_unit__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_user__website_message_ids
#: model:ir.model.fields,help:ams.field_ams_user_group__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule_day__week_day__5
msgid "Wednesday"
msgstr ""

#. module: ams
#: model_terms:ir.ui.view,arch_db:ams.schedule_day_view_search
msgid "Week Day"
msgstr ""

#. module: ams
#: model:ir.model.fields.selection,name:ams.selection__ams_schedule__schedule_type__weekly
msgid "Weekly"
msgstr ""

#. module: ams
#: model:ir.model.fields,help:ams.field_ams_time_unit__absent_time_criteria
msgid ""
"make employee absent if working time less than this time (hours:minutes)"
msgstr ""
