# -*- coding: utf-8 -*-
import json

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.modules import get_module_resource


class BaseBiostarAPIClient(models.AbstractModel):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.biostar_api_client'
    _inherit = 'ams_base.api_client'
    _description = 'Base Biostar API Client'
    # endregion


    # region ---------------------- TODO[IMP]: Properties -------------------------------------
    @property
    def response_key(self):
        return 'Response'

    @property
    def response_code_key(self):
        return 'code'

    @property
    def response_message_key(self):
        return 'message'

    @property
    def response_result_key(self):
        return 'response_result'
    # endregion


