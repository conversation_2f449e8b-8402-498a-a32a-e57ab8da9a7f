# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class AccessGroup(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams.access_group'
    _description = "Access Group"
    _inherit = 'ams_base.access_group'
    _rec_names_search = ['name', 'floor_name']
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    floor_name = fields.Char(string="Floor Name", translate=True, help="Name of the floor for this access group")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    building_id = fields.Many2one('ams.building', string="Building", tracking=True)
    ac_levels_ids = fields.Many2many('ams.access_level', string="Access Levels")
    device_ids = fields.Many2many('ams.device', string="Devices")
    group_users_ids = fields.One2many('ams.access_group_user', 'ac_group_id', string="Group Users")
    user_groups_ids = fields.One2many('ams.access_group_user_group','ac_group_id',string="User Groups")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('floor_name', 'name')
    def _compute_display_name(self):
        for record in self:
            # Prioritize floor_name if assigned, otherwise use name field
            record.display_name = record.floor_name or record.name
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_link_to_building(self):
        """Link selected access groups to the building from context"""
        building_id = self.env.context.get('building_id_to_link')
        
        if not building_id:
            raise UserError('No building specified for linking.')
        
        # Check if any of the selected records are already linked
        # already_linked = self.filtered('building_id')
        # if already_linked:
        #     linked_names = ', '.join(already_linked.mapped('name'))
        #     raise UserError(f'The following access groups are already linked to buildings: {linked_names}')
        
        # Link all selected access groups to the building in batch
        self.write({'building_id': building_id})
        
        # Return action to close the popup/wizard
        return {'type': 'ir.actions.act_window_close'}
    
    def action_mark_as_visitor_group(self):
        """Mark selected access groups as visitor groups"""
        self.write({'is_visitor_group': True})
        return {'type': 'ir.actions.act_window_close'}
    
    def action_remove_visitor_group(self):
        """Remove visitor group status from selected access groups"""
        self.write({'is_visitor_group': False})
        return {'type': 'ir.actions.act_window_close'}
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
