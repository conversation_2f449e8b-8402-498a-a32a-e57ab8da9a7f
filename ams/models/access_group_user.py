# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class AccessGroupUser(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams.access_group_user'
    _inherit = 'ams_base.api_model_chatter'
    _description = "Access Group User"

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    config_applied = fields.<PERSON><PERSON><PERSON>(string="Configuration Applied", default=False)
    # endregion

    # region  Special
    # endregion

    # region  Relational
    user_id = fields.Many2one('ams.user', string="User", required=True)
    ac_group_id = fields.Many2one('ams.access_group', string="Access Group", required=True)
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
