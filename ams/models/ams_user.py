# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
import logging
from odoo.addons.ams_bs.api.dto_response import BaseResponse, Response

_logger = logging.getLogger(__name__)


class AMSUser(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams.user'
    _description = "AMS User"
    _inherit = 'ams_base.user'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    device_group_id = fields.Many2one('ams.device_group', string="Device Group")
    user_group_id = fields.Many2one('ams.user_group', string="User Group", ondelete='restrict')
    ac_group_ids = fields.Many2many('ams.access_group', string="Access Groups")
    card_ids = fields.One2many('ams.card', 'user_id', string="Cards")

    bs_fp1_template0 = fields.Text(string="Fingerprint 1 Template 0")
    bs_fp1_template1 = fields.Text(string="Fingerprint 1 Template 1")
    bs_fp2_template0 = fields.Text(string="Fingerprint 2 Template 0")
    bs_fp2_template1 = fields.Text(string="Fingerprint 2 Template 1")

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def create(self, vals):

        if isinstance(vals, list):
            for val in vals:
                if val.get('enroll_number', 0) == 0:
                    val['enroll_number'] = int(self.env['ir.sequence'].next_by_code('ams.seq_user') or 0)
        else:
            if vals.get('enroll_number', 0) == 0:
                vals['enroll_number'] = int(self.env['ir.sequence'].next_by_code('ams.seq_user') or 0)

        res = super(AMSUser, self).create(vals)
        # self.send_auto_refresh()
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------

    def action_link_users(self):
        # Logic to link users
        pass

    def action_unlink_users(self):
        # Logic to unlink users
        pass

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
