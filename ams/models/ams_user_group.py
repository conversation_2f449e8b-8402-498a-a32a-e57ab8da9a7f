# -*- coding: utf-8 -*-


from odoo import api, fields, models,_
from odoo.exceptions import UserError, ValidationError


class AMSUserGroup(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams.user_group'
    _description = "User Group"
    _inherit = 'ams_base.api_model_chatter'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    parent_id = fields.Many2one('ams.user_group', string="Parent Group")
    user_ids = fields.One2many('ams.user', 'user_group_id', string="Users")
    group_tag_id = fields.Many2one('hr.employee.category', string="User Group Tag")






    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def create(self, vals_list):
        records = super(AMSUserGroup, self).create(vals_list)
        for rec in records:
            if rec.group_tag_id:
                if rec.group_tag_id.name:
                    rec.group_tag_id.name = rec.name
            else:
                category = self.env['hr.employee.category'].search([('name', '=', rec.name)], limit=1)
                if not category:
                    category = self.env['hr.employee.category'].create({'name': rec.name})
                rec.group_tag_id = category  # Assign found or created category
        return records

    def write(self, vals):
        # Ensure vals is a list of dictionaries
        if isinstance(vals, list):
            for single_vals in vals:
                self.write(single_vals)  # Recursively call write for each dict in list
            return True
        result = super(AMSUserGroup, self).write(vals)
        if 'name' in vals:
            for rec in self:
                if rec.group_tag_id:
                    if rec.group_tag_id.name:
                        rec.group_tag_id.name = rec.name
                else:
                    category = self.env['hr.employee.category'].search([('name', '=', rec.name)], limit=1)
                    if not category:
                        category = self.env['hr.employee.category'].create({'name': rec.name})
                    rec.group_tag_id = category
        return result
