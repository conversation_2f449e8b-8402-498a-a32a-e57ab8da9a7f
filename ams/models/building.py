# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class Building(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams.building'
    _description = "Building"
    _inherit = 'ams_base.abstract_model'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(string="Name", required=True, tracking=True)
    map_url = fields.Char(string="Map URL", help="Google Maps or other map service URL for this building location")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    ac_groups_ids = fields.One2many('ams.access_group', 'building_id', string="Access Groups")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_open_access_group_link(self):
        """Open the access group link view to link unlinked access groups to this building"""
        self.ensure_one()
        return {
            'name': 'Link Access Groups to Building',
            'type': 'ir.actions.act_window',
            'res_model': 'ams.access_group',
            'view_mode': 'list',
            'view_id': self.env.ref('ams.building_access_group_link_view_list').id,
            'target': 'new',
            'context': {
                'building_id_to_link': self.id,
                'default_building_id': self.id,
                'building_id': self.id,
                'building_name': self.name,
            },
            'domain': [],
        }
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion