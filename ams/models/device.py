# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError



class Device(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams.device'
    _description = "Device"
    _inherit = ['ams_base.device']
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    device_group_id = fields.Many2one('ams.device_group', string="Device Group")
    device_type_id = fields.Many2one('ams.device_type', string="Device Type")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------


    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
