from odoo import api, fields, models

class DeviceType(models.Model):
    _name = 'ams.device_type'
    _description = 'Device Type'
    _inherit = 'ams_base.api_model_chatter'



 # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    input_port_num = fields.Integer(string='Input Port Number')
    output_port_num = fields.Integer(string='Output Port Number')
    relay_num = fields.Integer(string='Relay Number')
    enable_face = fields.<PERSON><PERSON><PERSON>(string='Enable Fase')
    enable_fingerprint = fields.Boolean(string='Enable Fingerprint')
    enable_card = fields.Boolean(string='Enable Card')
    enable_wifi = fields.Boolean(string='Enable WiFi')
    # endregion

    # region  Special
    # endregion

    # region  Relational

    # endregion

    # region  Computed
    # endregion
