from odoo import models, fields


class IDType(models.Model):
    # region ---------------------- [IMP]: Private Attributes --------------------------------
    _name = "ams.id_type"
    _description = "ID Type"
    _inherit = ["ams_base.abstract_model", "mail.thread", "mail.activity.mixin"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ----------------------[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(string='Name')
    is_predefined = fields.<PERSON><PERSON><PERSON>(string='Is Predefined', default=False)
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
