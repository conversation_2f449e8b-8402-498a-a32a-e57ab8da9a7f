# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class ResPartner(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "res.partner"
    _inherit = "res.partner"
    _description = "Partner"

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    @api.model
    def _get_default_id_type(self):
        return self.env['ams.id_type'].search([('name', 'ilike', 'National ID')], limit=1).id
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic

    # endregion

    # region  Special
    # endregion

    # region  Relational
    id_type_id = fields.Many2one('ams.id_type', string="ID Type", required=True, default=lambda self: self._get_default_id_type())


    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
