# -*- coding: utf-8 -*-

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.addons.ams_base.helper.helper import *


class Schedule(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams.schedule'
    _description = "Schedule"
    _inherit = 'ams_base.api_model_chatter'
    _inherits = {'resource.calendar': 'resource_calendar_id'}
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    cycle_days = fields.Integer(tracking=True)
    schedule_type = fields.Selection(selection=[('daily', 'Daily'), ('weekly', 'Weekly')], default='weekly',
                                     required=True, tracking=True)
    activate = fields.Boolean(default=False)
    is_readonly = fields.Boolean(compute='_compute_is_readonly', store=True)
    default_time_unit_id = fields.Many2one('ams.time_unit', string='Default Time Unit')
    purpose_type = fields.Selection(selection=[('ac', 'Access Control'), ('ta', 'TA'), ('all', 'All')], default='all')
    resource_calendar_id = fields.Many2one('resource.calendar', string='Schedule Working Time', auto_join=True,
                                           index=True, ondelete="cascade")

    # endregion

    # region  Special
    # endregion

    # region  Relational
    day_ids = fields.One2many('ams.schedule_day', 'schedule_id')

    # endregion
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('activate', 'schedule_type', 'day_ids')
    def _compute_is_readonly(self):
        for record in self:
            record.is_readonly = (
                    record.activate and
                    record.schedule_type == 'weekly' and
                    bool(record.day_ids)
            )

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_generate_days(self):
        """
        Generate schedule days based on the schedule type and cycle_days.
        - If the schedule type is 'weekly', generate days for the week (Saturday to Friday).
        - If the schedule type is 'daily', generate days based on the cycle_days value.
        """
        self.ensure_one()  # Ensure the method is called on a single record
        if not self.default_time_unit_id:
            raise ValidationError("Default Time Unit is required for generating schedule days.")

        schedule_days = []

        if self.schedule_type == 'weekly':
            # Generate days for the week (Saturday to Friday)
            for day in WEEK_DAYS:
                schedule_days.append({
                    'week_day': day[0],
                    'index': day[0],
                    'schedule_id': self.id,
                    'is_day_off': day[1] in ['Friday', 'Saturday'],
                    'time_units_ids': self.default_time_unit_id if day[1] not in ['Friday', 'Saturday'] else [],
                })

        elif self.schedule_type == 'daily':
            # Generate days based on cycle_days
            if not self.cycle_days or self.cycle_days <= 0:
                raise ValidationError("Cycle Days must be a positive number for daily schedules.")
            for index in range(1, self.cycle_days + 1):
                schedule_days.append({
                    'index': index,
                    'schedule_id': self.id,
                    'time_units_ids': self.default_time_unit_id,

                })
        else:
            raise ValidationError("Unsupported schedule type.")

        if schedule_days:
            # Clear existing schedule days
            self.day_ids.unlink()
            # Create new schedule days
            self.env['ams.schedule_day'].create(schedule_days)

    def action_update_working_schedule(self):
        for schedule in self:
            # Step 1: Ensure a linked resource calendar exists
            if not schedule.resource_calendar_id:
                raise ValidationError("No resource calendar linked to this schedule.")

            # Step 2: Update the resource calendar with schedule details
            schedule.resource_calendar_id.write({
                'name': schedule.name,  # Ensure name is synced
            })

            # Step 3: Remove old attendance records for this schedule
            existing_attendance = self.env['resource.calendar.attendance'].search([
                ('calendar_id', '=', schedule.resource_calendar_id.id)
            ])
            existing_attendance.unlink()

            # Step 4: Create new attendance records based on schedule days
            for day in schedule.day_ids:
                if day.is_day_off:
                    continue  # Skip off days

                for time_unit in day.time_units_ids:
                    self.env['resource.calendar.attendance'].create({
                        'calendar_id': schedule.resource_calendar_id.id,
                        'dayofweek': str(to_day_index_iso(day.index)),  # Ensure day mapping  ddad
                        'hour_from': time_unit.start_time,  # Time unit fields
                        'hour_to': time_unit.end_time,
                        'name': f"{schedule.name} - {day.week_day} ({time_unit.start_time}-{time_unit.end_time})"
                    })

    def action_activate(self):
        for record in self:
            record.activate = True

    def action_reset(self):
        for record in self:
            record.activate = False

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
