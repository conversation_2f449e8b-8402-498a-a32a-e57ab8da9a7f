# -*- coding: utf-8 -*-
from odoo.addons.ams_base.helper.helper import WEEK_DAYS
from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class ScheduleDay(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams.schedule_day'
    _description = "Schedule Day"
    _inherit = ["ams_base.abstract_model", "mail.thread", "mail.activity.mixin"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(compute="_compute_name", store=True)
    week_day = fields.Selection(WEEK_DAYS, string='Day')
    index = fields.Integer()
    
    # endregion

    # region  Special
    # endregion

    # region  Relational
    is_day_off = fields.Boolean(compute="_compute_is_day_off", store=True, readonly=False)
    is_units_overlapped = fields.Boolean(compute="_compute_is_units_overlapped", store=True)
    is_units_mixed_types = fields.Boolean(compute="_compute_is_units_mixed_types", store=True)
    # endregion

    # region  Special
    # endregion

    # region  Relational
    schedule_id = fields.Many2one('ams.schedule', ondelete='cascade', )
    time_units_ids = fields.Many2many('ams.time_unit')

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('index', 'week_day', 'schedule_id', 'schedule_id.schedule_type')
    def _compute_name(self):
        for record in self:
            if record.schedule_id and record.schedule_id.schedule_type == 'weekly':
                week_day_dict = dict(WEEK_DAYS)
                record.name = week_day_dict.get(record.week_day, f"Day {record.index}")
            else:
                record.name = f"Day {record.index}"

    @api.depends('time_units_ids')
    def _compute_is_day_off(self):
        for record in self:
            if record.time_units_ids:
                record.is_day_off = False
            else:
                record.is_day_off = True

    @api.depends('time_units_ids')
    def _compute_is_units_mixed_types(self):
        for record in self:
            if record.time_units_ids:
                unit_types = [unit.unit_type for unit in record.time_units_ids]
                if len(set(unit_types)) > 1:
                    record.is_units_mixed_types = True
                else:
                    record.is_units_mixed_types = False

    @api.depends('time_units_ids')
    def _compute_is_units_overlapped(self):
        """
        Check for time overlaps in  units assigned to this day.
        """
        for record in self:
            units = record.time_units_ids
            for unit1 in units:
                for unit2 in units:
                    if unit1 != unit2:  # Avoid comparing the same unit with itself
                        if self._is_time_overlap(unit1, unit2):
                            record.is_units_overlapped = True
                            return
                        else:
                            record.is_units_overlapped = False
                    else:
                        record.is_units_overlapped = False

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # Constraints
    @api.constrains('is_units_overlapped')
    def _check_is_units_overlap(self):
        for record in self:
            if record.is_units_overlapped:
                raise ValidationError("Time units assigned to this day have overlapping time ranges.")

    @api.onchange('is_day_off')
    def _onchange_day_off(self):
        for record in self:
            if record.is_day_off:
                record.time_units_ids = False
            else:
                record.time_units_ids = record.schedule_id.default_time_unit_id

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _is_time_overlap(self, unit1, unit2):
        """
        Check if two units have a time overlap.
        """
        # Handle 'open' type units
        if unit1.unit_type == 'open' or unit2.unit_type == 'open':
            return True

        # get the start and end times for the first unit unit
        start1 = unit1.min_checkin_time
        end1 = unit1.max_checkout_time

        # get the start and end times for the second  unit
        start2 = unit2.min_checkin_time
        end2 = unit2.max_checkout_time

        # Handle overnight units
        if unit1.is_overnight:
            end1 += 24
        if unit2.is_overnight:
            end2 += 24

        # Check overlap
        return start1 < end2 and start2 < end1
    # endregion