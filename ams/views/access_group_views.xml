<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: access_group_view_form-->
    <record id="access_group_view_form" model="ir.ui.view">
        <field name="name">ams.access_group.form</field>
        <field name="model">ams.access_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='basic_info']" position="after">
                <page name="group_users" string="Users">
                    <field name="group_users_ids">
                        <list editable="bottom">
                            <field name="user_id"/>
                            <field name="config_applied" optional="hide"/>
                            <field name="ac_group_id" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
            <xpath expr="//page[@name='group_users']" position="after">
                <page name="user_groups" string="User Groups">
                    <field name="user_groups_ids">
                        <list editable="bottom">
                            <field name="user_group_id"/>
                            <field name="config_applied" optional="hide"/>
                            <field name="ac_group_id" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
            <xpath expr="//group[@name='basic_info_full_width']" position="inside">
                <field name="floor_name"/>
                <field name="building_id"/>
                <field name="ac_levels_ids" widget="many2many_tags"/>
                <field name="device_ids" widget="many2many_tags" groups="base.group_no_one"/>
                <field name="is_visitor_group"/>
                <field name="api_type"/>
                 <field name="api_type" readonly="0" groups="base.group_no_one"/>


            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: access_group_view_list-->
    <record id="access_group_view_list" model="ir.ui.view">
        <field name="name">ams.access_group.list</field>
        <field name="model">ams.access_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="inside">
                <header>
                    <button name="action_mark_as_visitor_group" type="object" string="Mark As Visitor Group" 
                            class="btn-secondary" 
                            confirm="Mark selected access groups as visitor groups?"/>
                    <button name="action_remove_visitor_group" type="object" string="Remove Visitor Group" 
                            class="btn-secondary" 
                            confirm="Remove visitor group status from selected access groups?"/>
                </header>
                <field name="floor_name" optional="show"/>
                <field name="building_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: access_group_view_search-->
    <record id="access_group_view_search" model="ir.ui.view">
        <field name="name">ams.access_group.search</field>
        <field name="model">ams.access_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="floor_name"/>
                <field name="building_id"/>
                <group expand="0" string="Group By">
                    <filter string="Floor Name" name="group_by_floor_name" domain="[]" context="{'group_by': 'floor_name'}"/>
                    <filter string="Building" name="group_by_building" domain="[]" context="{'group_by': 'building_id'}"/>
                </group>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: access_group_link_view_list-->
    <record id="building_access_group_link_view_list" model="ir.ui.view">
        <field name="name">ams.building_access_group.link.list</field>
        <field name="model">ams.access_group</field>
        <field name="arch" type="xml">
            <list create="0" delete="0">
                <header>
                    <button name="action_link_to_building" type="object" string="Link Selected" 
                            class="btn-primary" 
                            confirm="Link selected access groups to the building?"/>
                    
                    <button name="action_mark_as_visitor_group" type="object" string="Mark As Visitor Group" 
                            class="btn-secondary" 
                            confirm="Mark selected access groups as visitor groups?"/>
                    <button name="action_remove_visitor_group" type="object" string="Remove Visitor Group" 
                            class="btn-secondary" 
                            confirm="Remove visitor group status from selected access groups?"/>
              
                </header>
                <field name="name"/>
                <field name="floor_name" optional="show"/>
                <field name="building_id" optional="show"/>
                <field name="company_id" optional="hide"/>
                <field name="is_visitor_group" optional="show"/>
                <field name="synced" optional="show"/>
                
            </list>
        </field>
    </record>

    <!--TODO[IMP]: building_access_group_link_action-->
    <record id="building_access_group_link_action" model="ir.actions.act_window">
        <field name="name">Link Access Groups to Building</field>
        <field name="res_model">ams.access_group</field>
        <field name="view_mode">list</field>
        <field name="view_id" ref="building_access_group_link_view_list"/>
        <field name="target">new</field>
        <field name="context">{
            'building_id_to_link': active_id,
            'default_building_id': active_id,
            'building_id': active_id,
            'building_name': active_record and active_record.name or '',
        }</field>
        <field name="domain">[('building_id', '=', False)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No unlinked access groups found
            </p>
            <p>
                All access groups are already linked to buildings.
            </p>
        </field>
    </record>

    <!--TODO[IMP]: access_group_action-->
    <record id="access_group_action" model="ir.actions.act_window">
        <field name="name">Access Groups</field>
        <field name="res_model">ams.access_group</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an access group
            </p>
            <p>
                Create access group
            </p>
        </field>
    </record>
</odoo>
