<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: access_level_view_form-->
    <record id="access_level_view_form" model="ir.ui.view">
        <field name="name">ams.access_level.form</field>
        <field name="model">ams.access_level</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                 <field name="ac_group_id"/>
            </field>

<!--            <xpath expr="//notebook" position="inside">-->
            <xpath expr="//page[@name='basic_info']" position="replace">
                <page name="door_schedules" string="Door Schedules">
                    <field name="door_schedule_ids">
                        <list editable="bottom">
                            <field name="door_id" />
                            <field name="schedule_id"/>
                             <field name="name" optional="hide"/>
                            <field name="door_record_id" optional="hide"/>
                            <field name="schedule_record_id" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: access_level_view_list-->
    <record id="access_level_view_list" model="ir.ui.view">
        <field name="name">ams.access_level.list</field>
        <field name="model">ams.access_level</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="inside">
                <field name="ac_group_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: access_level_view_search-->
    <record id="access_level_view_search" model="ir.ui.view">
        <field name="name">ams.access_level.search</field>
        <field name="model">ams.access_level</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="ac_group_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: access_level_action-->
    <record id="access_level_action" model="ir.actions.act_window">
        <field name="name">Access Levels</field>
        <field name="res_model">ams.access_level</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an access level
            </p>
            <p>
                Create access level
            </p>
        </field>
    </record>
</odoo>
