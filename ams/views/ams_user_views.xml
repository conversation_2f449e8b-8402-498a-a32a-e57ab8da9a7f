<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: user_view_form-->
    <record id="user_view_form" model="ir.ui.view">
        <field name="name">ams.user.form</field>
        <field name="model">ams.user</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="description" position="replace">
                <field name="user_group_id"/>
                <field name="enroll_number"/>
                <field name="email"/>
                <field name="device_group_id" invisible="1"/>
            </field>
            <field name="synced" position="before">
                <field name="activate"/>
            </field>

            <xpath expr="//group[@name='basic_info_col1']" position="inside">
                <field name="user_type" widget="selection"/>
                <field name="start_datetime"/>
                <field name="end_datetime"/>
            </xpath>

            <xpath expr="//group[@name='basic_info_full_width']" position="inside">
                <field name="ac_group_ids" widget="many2many_tags" />

            </xpath>

             <xpath expr="//page[@name='technical']" position="before">
                <page name="card_info" string="Cards">
                    <field name="card_ids" readonly="1">
                            <list editable="bottom">
                             <field name="card_number" optional="show"/>
                                 <field name="name" optional="hide"/>
                                <field name="card_type_id" optional="hide"/>
                             </list>
                        </field>
                </page>
            </xpath>

        </field>
    </record>

    <!--TODO[IMP]: user_view_list-->
    <record id="user_view_list" model="ir.ui.view">
        <field name="name">ams.user.list</field>
        <field name="model">ams.user</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="device_group_id"/>
                <field name="enroll_number" optional="show"/>
                <field name="start_datetime" optional="show"/>
                <field name="end_datetime" optional="show"/>
                <field name="activate" optional="show"/>
            </field>

        </field>
    </record>

    <!--TODO[IMP]: user_view_search-->
    <record id="user_view_search" model="ir.ui.view">
        <field name="name">ams.user.search</field>
        <field name="model">ams.user</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
        <field name="name" position="after">
                <field name="device_group_id"/>
                <field name="enroll_number"/>
            </field>
        </field>
    </record>

    <!--TODO[IMP]: user_action-->
    <record id="user_action" model="ir.actions.act_window">
        <field name="name">Users</field>
        <field name="res_model">ams.user</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an access group
            </p>
            <p>
                Create access group
            </p>
        </field>
    </record>
</odoo>
