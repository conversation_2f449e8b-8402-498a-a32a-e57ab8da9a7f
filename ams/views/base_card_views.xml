<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <card_name> _view_form-->
    <record id="ams_base_card_view_form" model="ir.ui.view">
        <field name="name">ams_base.card.form</field>
        <field name="model">ams_base.card</field>
        <field name="arch" type="xml">
            <form>
                <header>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="card_number"/>
                        </group>
                        <group>
                            <field name="state"/>
                            <field name="is_assigned"/>

                        </group>
                    </group>
                    <notebook>
                        <page string="Basic Info">
                            <group>
                                <group>
                                    <field name="display_card_id"/>
                                    <field name="card_slot"/>
                                    <field name="card_mask"/>
                                    <field name="mobile_card"/>
                                </group>
                                <group>
                                    <field name="is_blocked"/>
                                    <field name="issue_count"/>
                                    <field name="status"/>
                                    <field name="record_id"/>

                                </group>
                            </group>

                        </page>
                    </notebook>

                </sheet>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: <card> _view_list-->
    <record id="ams_base_card_view_list" model="ir.ui.view">
        <field name="name">ams_base.card.list</field>
        <field name="model">ams_base.card</field>
        <field name="arch" type="xml">
            <list string="Desc">
                <field name="name"/>
                <field name="card_number"/>
                <field name="display_card_id" optional="hide"/>
                <field name="card_slot" optional="hide"/>
                <field name="card_mask" optional="hide"/>
                <field name="mobile_card" optional="hide"/>
                <field name="status" optional="hide"/>
                <field name="state" optional="show"/>
                 <field name="is_blocked" optional="hide"/>
                <field name="issue_count" optional="hide"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: <card> _view_search-->
    <record id="ams_base_card_view_search" model="ir.ui.view">
        <field name="name">ams_base.card.search</field>
        <field name="model">ams_base.card</field>
        <field name="arch" type="xml">
            <search>
                <field name="card_number"/>
                <field name="name"/>
                <field name="record_id"/>
                <!--<field name="living_area" filter_domain="[('living_area', '>=', self)]"/>-->
                <!--<filter string="Available" name="available" domain="[('state', 'in', ('new', 'offer_received'))]"/>-->
                <group expand="1" string="Group By">
                    <!--<filter string="Postcode" name='postcode' context="{'group_by':'postcode'}"/>-->
                </group>
            </search>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_kanban-->
    <record id="ams_base_card_view_kanban" model="ir.ui.view">
        <field name="name">ams_base.card.kanban</field>
        <field name="model">ams_base.card</field>
        <field name="arch" type="xml">
            <kanban default_group_by="field_name" records_draggable="0">
                <!--<field name="state"/>-->
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click">
                            <div>
                                <strong class="o_kanban_record_title">
                                    <!--<field name="name"/>-->
                                </strong>
                            </div>
                            <!--<div>
                                Expected Price: <field name="expected_price"/>
                            </div>
                            <div t-if="record.state.raw_value == 'offer_received'">
                                Best Offer: <field name="best_price"/>
                            </div>
                            <div t-if="record.selling_price.raw_value">
                                Selling Price: <field name="selling_price"/>
                            </div>
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>-->
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

</odoo>
