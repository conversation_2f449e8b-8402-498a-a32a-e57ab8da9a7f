<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: building_view_form-->
    <record id="building_view_form" model="ir.ui.view">
        <field name="name">ams.building.form</field>
        <field name="model">ams.building</field>
        <field name="arch" type="xml">
            <form string="Building">
                <sheet>
                    <group>
                        <group name="basic_info">
                            <field name="name"/>
                            <field name="company_id" groups="base.group_multi_company" />
                        </group>
                    </group>
                    <notebook>
                        <page name="access_groups" string="Access Groups">
                            <div class="oe_button_box" name="button_box">
                                <button name="action_open_access_group_link" type="object" 
                                        string="Link Access Groups" 
                                        class="btn-primary"/>
                            </div>
                            <field name="ac_groups_ids" readonly="1">
                                <list editable="bottom">
                                    <field name="name"/>
                                    <field name="is_visitor_group"/>
                                    <field name="api_type"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: building_view_list-->
    <record id="building_view_list" model="ir.ui.view">
        <field name="name">ams.building.list</field>
        <field name="model">ams.building</field>
        <field name="arch" type="xml">
            <list string="Buildings">
                <field name="name"/>
                <!-- <field name="company_id" groups="base.group_multi_company"/> -->
                <!-- <field name="ac_groups_ids" widget="many2many_tags"/> -->
            </list>
        </field>
    </record>

    <!--TODO[IMP]: building_view_search-->
    <record id="building_view_search" model="ir.ui.view">
        <field name="name">ams.building.search</field>
        <field name="model">ams.building</field>
        <field name="arch" type="xml">
            <search string="Buildings">
                <field name="name"/>
                <field name="company_id"/>
                <group expand="0" string="Group By">
                    <filter string="Company" name="group_by_company" domain="[]" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!--TODO[IMP]: building_action-->
    <record id="building_action" model="ir.actions.act_window">
        <field name="name">Buildings</field>
        <field name="res_model">ams.building</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a building
            </p>
            <p>
                Create building to organize access groups by location
            </p>
        </field>
    </record>
</odoo>