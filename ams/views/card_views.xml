<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]:  card_view_form-->
    <record id="card_view_form" model="ir.ui.view">
        <field name="name">Cards</field>
        <field name="model">ams.card</field>
        <field name="inherit_id" ref="ams_base_card_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="card_number" position="after">
                <field name="card_type_id"/>
                <field name="user_id"/>
            </field>
        </field>
    </record>


    <!--TODO[IMP]:  card_view_list-->
    <record id="card_view_list" model="ir.ui.view">
        <field name="name">Cards</field>
        <field name="model">ams.card</field>
        <field name="inherit_id" ref="ams_base_card_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="card_number" position="after">
                <field name="card_type_id" optional="show"/>
                <field name="user_id" optional="show"/>
            </field>
        </field>
    </record>


    <!--TODO[IMP]: card_view_search-->
    <record id="card_view_search" model="ir.ui.view">
        <field name="name">Cards</field>
        <field name="model">ams.card</field>
        <field name="inherit_id" ref="ams_base_card_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="attributes">
            </xpath>
        </field>
    </record>


    <!--TODO[IMP]: <model_name> _action-->
    <record id="card_action" model="ir.actions.act_window">
        <field name="name">Cards</field>
        <field name="res_model">ams.card</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>
</odoo>
