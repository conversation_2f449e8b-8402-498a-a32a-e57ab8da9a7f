<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: device_group_view_form-->
    <record id="device_group_view_form" model="ir.ui.view">
        <field name="name">ams.device_group.form</field>
        <field name="model">ams.device_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="parent_id"/>
            </field>
            <xpath expr="//page[@name='basic_info']" position="replace">
                <page name="device" string="Devices">
                    <field name="device_ids">
                        <list>
                            <field name="name"/>
                            <field name="device_group_id" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: device_group_view_list-->
    <record id="device_group_view_list" model="ir.ui.view">
        <field name="name">ams.device_group.list</field>
        <field name="model">ams.device_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="inside">
                <field name="parent_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: device_group_view_search-->
    <record id="device_group_view_search" model="ir.ui.view">
        <field name="name">ams.device_group.search</field>
        <field name="model">ams.device_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="parent_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: device_group_action-->
    <record id="device_group_action" model="ir.actions.act_window">
        <field name="name">Device Groups</field>
        <field name="res_model">ams.device_group</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an access group
            </p>
            <p>
                Create access group
            </p>
        </field>
    </record>
</odoo>
