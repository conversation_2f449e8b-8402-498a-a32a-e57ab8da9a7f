<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: device_view_form-->
    <record id="device_view_form" model="ir.ui.view">
        <field name="name">ams.device.form</field>
        <field name="model">ams.device</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="device_group_id"/>
                <field name="device_type_id"/>
            </field>

            <xpath expr="//group[@name='basic_info_col1']" position="inside">
                <field name="ip"/>
                <field name="port"/>
                <field name="device_serial"/>
                <field name="state"/>
            </xpath>
            <xpath expr="//group[@name='basic_info_col2']" position="inside">
                <field name="last_log_id"/>
                <field name="last_log_date"/>
                <field name="activate"/>
                <field name="log_active"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: device_view_list-->
    <record id="device_view_list" model="ir.ui.view">
        <field name="name">ams.device.list</field>
        <field name="model">ams.device</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="device_group_id"/>
                <field name="state"/>
                <field name="device_serial" optional="hide"/>
                <field name="ip" optional="hide"/>

            </field>
        </field>
    </record>

    <!--TODO[IMP]: device_view_search-->
    <record id="device_view_search" model="ir.ui.view">
        <field name="name">ams.device.search</field>
        <field name="model">ams.device</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="device_group_id"/>
                <field name="state"/>
                <field name="device_serial"/>
                <field name="ip"/>

            </field>
        </field>
    </record>

    <!--TODO[IMP]: device_action-->
    <record id="device_action" model="ir.actions.act_window">
        <field name="name">Devices</field>
        <field name="res_model">ams.device</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an access group
            </p>
            <p>
                Create access group
            </p>
        </field>
    </record>


</odoo>
