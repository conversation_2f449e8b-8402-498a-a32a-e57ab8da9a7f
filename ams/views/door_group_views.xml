<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: door_group_view_form-->
    <record id="door_group_view_form" model="ir.ui.view">
        <field name="name">ams.door_group.form</field>
        <field name="model">ams.door_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='basic_info']" position="replace">
                <page name="doors" string="Doors">
                    <field name="door_ids">
                        <list>
                            <field name="name"/>
                            <field name="description" optional="hide"/>
                            <field name="record_id" optional="hide"/>
                            <field name="synced" optional="hide"/>
                            <field name="last_sync_date" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_group_view_list-->
    <record id="door_group_view_list" model="ir.ui.view">
        <field name="name">ams.door_group.list</field>
        <field name="model">ams.door_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="inside">
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_group_view_search-->
    <record id="door_group_view_search" model="ir.ui.view">
        <field name="name">ams.door_group.search</field>
        <field name="model">ams.door_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="door_ids"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_group_action-->
    <record id="door_group_action" model="ir.actions.act_window">
        <field name="name">Door Groups</field>
        <field name="res_model">ams.door_group</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a door group
            </p>
            <p>
                Create door group
            </p>
        </field>
    </record>
</odoo>
