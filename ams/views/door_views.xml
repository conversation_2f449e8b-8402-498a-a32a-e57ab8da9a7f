<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: door_view_form-->
    <record id="door_view_form" model="ir.ui.view">
        <field name="name">ams.door.form</field>
        <field name="model">ams.door</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="door_group_id"/>
            </field>
        </field>
    </record>

    <!--TODO[IMP]: door_view_list-->
    <record id="door_view_list" model="ir.ui.view">
        <field name="name">ams.door.list</field>
        <field name="model">ams.door</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="door_group_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_view_search-->
    <record id="door_view_search" model="ir.ui.view">
        <field name="name">ams.door.search</field>
        <field name="model">ams.door</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="door_group_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_action-->
    <record id="door_action" model="ir.actions.act_window">
        <field name="name">Doors</field>
        <field name="res_model">ams.door</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a door
            </p>
            <p>
                Create door
            </p>
        </field>
    </record>
</odoo>
