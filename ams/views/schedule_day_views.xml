<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: schedule_day_view_form-->
    <record id="schedule_day_view_form" model="ir.ui.view">
        <field name="name">ams.schedule_day.form</field>
        <field name="model">ams.schedule_day</field>
        <field name="arch" type="xml">
            <form>
                <header>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="week_day"/>
                            <field name="schedule_id"/>
                            <field name="time_units_ids" widget="many2many_tags"/>
                        </group>
                        <group>
                            <field name="is_day_off" widget="boolean_toggle"/>
                            <field name="is_units_overlapped" readonly="1"/>
                            <field name="is_units_mixed_types" readonly="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: schedule_day_view_list-->
    <record id="schedule_day_view_list" model="ir.ui.view">
        <field name="name">ams.schedule_day.tree</field>
        <field name="model">ams.schedule_day</field>
        <field name="arch" type="xml">
            <list string="Schedule Days">
                <field name="name"/>
                <field name="week_day"/>
                <field name="index"/>
                <field name="schedule_id"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: schedule_day_view_search-->
    <record id="schedule_day_view_search" model="ir.ui.view">
        <field name="name">ams.schedule_day.search</field>
        <field name="model">ams.schedule_day</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="week_day"/>
                <field name="schedule_id"/>
                <group expand="1" string="Group By">
                    <filter string="Schedule" name="schedule" context="{'group_by': 'schedule_id'}"/>
                    <filter string="Week Day" name="week_day" context="{'group_by': 'week_day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!--TODO[IMP]: schedule_day_action-->
    <record id="schedule_day_action" model="ir.actions.act_window">
        <field name="name">Schedule Days</field>
        <field name="res_model">ams.schedule_day</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a schedule_day
            </p>
            <p>
                Create schedule_day
            </p>
        </field>
    </record>
</odoo>
