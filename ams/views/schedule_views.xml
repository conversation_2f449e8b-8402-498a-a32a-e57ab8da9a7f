<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: schedule_view_form-->
    <record id="schedule_view_form" model="ir.ui.view">
        <field name="name">ams.schedule.form</field>
        <field name="model">ams.schedule</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button string="Generate Days" type="object" name="action_generate_days" class="btn-primary"/>
            </xpath>

            <xpath expr="//field[@name='description']" position="after">
                <field name="schedule_type" string="Schedule Type"/>
                <field name="cycle_days" string="Cycle Days" invisible="schedule_type=='weekly'"/>
            </xpath>
            <xpath expr="//field[@name='last_sync_date']" position="after">
                <field name="default_time_unit_id" string="Default Time Unit"/>
                <field name="purpose_type" string="Purpose Type" groups="base.group_no_one"/>
            </xpath>
            <xpath expr="//page[@name='basic_info']" position="replace">
                <page name="days" string="Days">
                    <field name="day_ids" string="Days">
                        <list editable="bottom">
                            <field name="name"/>
                            <field name="week_day" optional="hide"/>
                            <field name="is_day_off" optional="show" widget="boolean_toggle"/>
                            <field name="time_units_ids" widget="many2many_tags"/>
                            <field name="index" optional="hide"/>
                            <field name="is_units_overlapped" optional="hide"/>
                            <field name="is_units_mixed_types" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: schedule_view_list-->
    <record id="schedule_view_list" model="ir.ui.view">
        <field name="name">ams.schedule.list</field>
        <field name="model">ams.schedule</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="inside">
                <field name="schedule_type" string="Schedule Type"/>
                <field name="purpose_type" string="Purpose Type" groups="base.group_no_one"/>
                <field name="default_time_unit_id"/>

            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: schedule_view_search-->
    <record id="schedule_view_search" model="ir.ui.view">
        <field name="name">ams.schedule.search</field>
        <field name="model">ams.schedule</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">

            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: schedule_action-->
    <record id="schedule_action" model="ir.actions.act_window">
        <field name="name">Schedules</field>
        <field name="res_model">ams.schedule</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'default_purpose_type': 'ac', 'search_default_available': 1}</field>
        <field name="domain">[('purpose_type', 'in', ['ac', 'all'])]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a schedule
            </p>
            <p>
                Create schedule
            </p>
        </field>
    </record>
</odoo>
