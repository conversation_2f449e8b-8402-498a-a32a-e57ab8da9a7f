# Language Toggle Widget Implementation

This document describes the implementation of the language toggle widget in the AMS Base module.

## Overview

The language toggle widget is a systray component that allows users to quickly switch between Arabic and English languages in the Odoo interface. It appears in the top header beside the username.

## Features

- **Quick Toggle**: Switch between Arabic (ar_001) and English (en_US) with a single click
- **Visual Feedback**: Shows current language and loading state
- **Responsive Design**: Works on desktop and mobile devices
- **RTL Support**: Proper right-to-left layout for Arabic
- **Error Handling**: Graceful fallback if the controller fails

## Implementation Details

### Files Created/Modified

1. **JavaScript Component**
   - `static/src/lang_toggle_component/language_toggle_systray.js` - Main component logic
   - Registers with Odoo's systray registry
   - Handles language switching via RPC calls

2. **XML Template**
   - `static/src/lang_toggle_component/language_toggle_systray.xml` - Component template
   - Bootstrap dropdown with language options

3. **CSS Styles**
   - `static/src/lang_toggle_component/language_toggle.css` - Component styling
   - Responsive design and RTL support

4. **Python Controller**
   - `controllers/language_controller.py` - Backend API
   - Handles language switching and validation

5. **Translations**
   - `i18n/ar_001.po` - Arabic translations for error messages

### Technical Architecture

#### Frontend (JavaScript)
- Uses Owl framework components
- Integrates with Odoo's service system (orm, action)
- Manages local state for current language and loading status
- Provides fallback mechanism if controller fails

#### Backend (Python)
- HTTP controller with JSON endpoints
- Validates language codes against available languages
- Updates user preferences and session context
- Returns structured responses with success/error status

#### Styling (CSS)
- Follows Odoo's design patterns
- Responsive breakpoints for mobile devices
- Dark mode support
- RTL layout support for Arabic

## Usage

### For Users
1. Look for the globe icon in the top header (systray area)
2. Click the dropdown to see current language
3. Click on the alternative language to switch
4. The page will reload with the new language applied

### For Developers

#### Testing
Run the test function in browser console:
```javascript
testLanguageToggle()
```

#### Customization
To modify supported languages, update the `toggleLanguage()` method in the JavaScript component:
```javascript
const targetLang = this.isArabic ? 'en_US' : 'ar_001';
```

#### Adding More Languages
1. Extend the component logic to handle multiple languages
2. Update the template to show more options
3. Modify the controller to support additional language codes

## Configuration

### Prerequisites
- Both Arabic (ar_001) and English (en_US) languages must be installed in Odoo
- Users must have appropriate permissions to modify their language preferences

### Installation
1. Install/upgrade the `ams_base` module
2. Refresh the browser to load new assets
3. The language toggle should appear in the systray automatically

## Troubleshooting

### Common Issues

1. **Component not visible**
   - Check if module is properly installed
   - Verify assets are loaded in browser developer tools
   - Ensure user has proper permissions

2. **Language switch not working**
   - Check browser console for JavaScript errors
   - Verify both languages are installed and active
   - Test the controller endpoint manually

3. **Styling issues**
   - Clear browser cache
   - Check for CSS conflicts with other modules
   - Verify responsive breakpoints

### Debug Mode
When accessing Odoo with `?debug=1`, the test script will automatically run and log component status to the console.

## Security Considerations

- Language switching only affects the current user's preferences
- Controller validates language codes against available languages
- No sensitive data is exposed through the API
- Standard Odoo authentication is required

## Performance

- Minimal impact on page load (small JavaScript/CSS footprint)
- RPC calls are asynchronous and don't block UI
- Component uses efficient state management
- CSS is optimized for fast rendering

## Future Enhancements

- Support for more than two languages
- Remember language preference per session
- Integration with user profile settings
- Keyboard shortcuts for language switching
- Animation effects for smoother transitions