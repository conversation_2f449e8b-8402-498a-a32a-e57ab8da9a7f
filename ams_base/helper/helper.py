import datetime

from dateutil.relativedelta import relativedelta
from odoo.tools import date_utils
import pytz

WEEK_DAYS = [('1', 'Saturday'), ('2', 'Sunday'), ('3', 'Monday'), ('4', 'Tuesday'),
             ('5', 'Wednesday'), ('6', 'Thursday'), ('7', 'Friday')]
WEEK_DAYS_LIST = ['Saturday', 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']


def to_day_index_iso(day_number: int) -> int:
    """
    Converts a custom week day number (1 = Saturday, ..., 7 = Friday)
    to ISO 8601 standard (1 = Monday, ..., 7 = Sunday).
    """
    return ((day_number - 3) % 7)


def convert_to_time_object(float_time):
    """Convert float_time to Time"""
    time = datetime.time(hour=int(float_time), minute=int(
        round((float_time - int(float_time)) * 60)))
    return time


def convert_to_float_time(time):
    float_time = round(float(str(time.hour + time.minute / 60)), 2)
    return float_time


def convert_to_str_time_format(float_time):
    """covert float time to time format then to str"""
    time = str(int(float_time)) + ':' + str(int(round((float_time - int(float_time)) * 60)))
    return time


def convert_time_to_float(time_str):
    hours, minutes = map(int, time_str.split(":"))
    return hours + minutes / 60


def get_minutes(float_time):
    """Takes the minutes from float_time then convert it to time minutes. ex: 1.5 -> 30 min"""
    return int(round((float_time - int(float_time)) * 60))


def get_total_minutes(float_time):
    """Takes the minutes from float_time then convert it to time minutes. ex: 1.5 -> 90 min"""
    return int(round((float_time - int(float_time)) * 60)) + int(float_time) * 60


def get_float_min(min):
    """convert the int minutes to float time hours. ex: 15 -> 0.25 """
    return round(min / 60, 2)


def get_diff(time, minutes):
    result = convert_to_float_time(datetime.time(hour=int(time), minute=int(
        round((time - int(time)) * 60)))) - get_float_min(minutes)
    if result < 0:
        result = 24 + result
    return result


def get_sum(time, minutes):
    result = convert_to_float_time(datetime.time(hour=int(time), minute=int(
        round((time - int(time)) * 60)))) + get_float_min(minutes)
    if result >= 24:
        result = result - 24
    return result


def compine_date_time(date, float_time):
    return datetime.datetime(year=date.year, month=date.month, day=date.day,
                             hour=int(float_time), minute=get_minutes(float_time))


def convert_to_utc(log_datetime, timezone):
    new_datetime = pytz.timezone(timezone).localize(log_datetime).astimezone(pytz.utc)
    new_datetime = datetime.datetime(year=new_datetime.year, month=new_datetime.month, day=new_datetime.day,
                                     hour=new_datetime.hour, minute=new_datetime.minute)
    return new_datetime
