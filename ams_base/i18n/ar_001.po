# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ams_base
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-23 08:18+0000\n"
"PO-Revision-Date: 2025-02-23 08:18+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.api_model_view_form
msgid "<strong>Error! </strong>"
msgstr ""

#. module: ams_base
#: model:ir.module.category,name:ams_base.ams_group_category
msgid "AMS Category"
msgstr ""

#. module: ams_base
#: model:res.groups,name:ams_base.ams_group_manager
msgid "AMS Manager"
msgstr ""

#. module: ams_base
#: model:res.groups,name:ams_base.ams_group_readonly
msgid "AMS ReadOnly"
msgstr ""

#. module: ams_base
#: model:res.groups,name:ams_base.ams_group_user
msgid "AMS User"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__api_type
msgid "API Type"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_company__absent_color
msgid "Absent Color"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.ams_employee_view_form
#: model_terms:ir.ui.view,arch_db:ams_base.res_user_inherit_view_form
msgid "Access Management"
msgstr ""

#. module: ams_base
#: model:ir.module.category,description:ams_base.ams_group_category
msgid "Access Management Category"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__message_needaction
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__message_needaction
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__message_needaction
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__message_needaction
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__message_needaction
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__message_needaction
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__message_needaction
msgid "Action Needed"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_activate_model__activate
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activate
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activate
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activate
msgid "Activate"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_activate_model__activate_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activate_date
msgid "Activate date"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_activate_model__activate_uid
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activate_uid
msgid "Activate uid"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__active
msgid "Active"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__activity_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__activity_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__activity_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activity_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activity_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__activity_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activity_ids
msgid "Activities"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__activity_state
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__activity_state
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__activity_state
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activity_state
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activity_state
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__activity_state
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activity_state
msgid "Activity State"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__activity_type_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__activity_type_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__activity_type_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activity_type_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activity_type_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__activity_type_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_users__level__administrator
msgid "Administrator"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_company__visitor_visibility__all
msgid "All"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.api_model_view_list
msgid "Api Model"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_api_model_chatter
msgid "Api Model Chatter"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__approval_uid
msgid "Approval Manager"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__state__approved
msgid "Approved"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__ar_name
msgid "Arabic Name"
msgstr ""

#. module: ams_base
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_code_model_ar_name_unique
msgid "Arabic Name already exists !"
msgstr " الاسم العربي موجود بالفعل!"

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__ar_name
msgid "Arabic name"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__message_attachment_count
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__message_attachment_count
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__message_attachment_count
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__message_attachment_count
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__message_attachment_count
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__message_attachment_count
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: ams_base
#: code:addons/ams_base/controllers/language_controller.py
msgid "Language code \"%s\" is not available."
msgstr "رمز اللغة \"%s\" غير متاح."

#. module: ams_base
#: code:addons/ams_base/controllers/language_controller.py
msgid "Language switched successfully."
msgstr "تم تغيير اللغة بنجاح."

#. module: ams_base
#: code:addons/ams_base/controllers/language_controller.py
msgid "Failed to switch language: %s"
msgstr "فشل في تغيير اللغة: %s"

#. module: ams_base
#: code:addons/ams_base/controllers/language_controller.py
msgid "Failed to get available languages: %s"
msgstr "فشل في الحصول على اللغات المتاحة: %s"

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_company__attend_color
msgid "Attend Color"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_company__sync_option__auto
msgid "Auto Mapping"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_res_company__sync_option
msgid "Auto option will create records in ams base"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_user
msgid "Base AMS User"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_api_client
msgid "Base API Client"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_access_group
msgid "Base Access Group"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_card
msgid "Base Card"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_device
msgid "Base Device"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_event_log
msgid "Base Event Log"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_request
msgid "Base Request"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_zk_api_client
msgid "Base ZK API Client"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.api_model_view_form
msgid "Basic Info"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_api_model
msgid "Biostar Base API Model"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__state__cancelled
msgid "Cancelled"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__card_mask
msgid "Card Mask"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__card_number
msgid "Card Number"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__card_slot
msgid "Card Slot"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.api_model_view_form
msgid "Close"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__code
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__code
msgid "Code"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__code_name
msgid "Code Name"
msgstr ""

#. module: ams_base
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_code_model_code_unique
msgid "Code already exists !"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__commit
msgid "Comments"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_res_company
#: model:ir.model.fields,field_description:ams_base.field_ams_base_abstract_model__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_activate_model__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_name_model__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__company_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__company_id
msgid "Company"
msgstr " الشركة"

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.hr_department_inherit_view_form
#: model_terms:ir.ui.view,arch_db:ams_base.hr_department_inherit_view_list
msgid "Compute Dept Depths"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__state__confirmed
msgid "Confirmed"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_follow_up_model__last_dept_depth
#: model:ir.model.fields,help:ams_base.field_ams_base_request__last_dept_depth
#: model:ir.model.fields,help:ams_base.field_res_users__last_dept_depth
msgid "Contain hierarchy  depth of last department assigned to employee"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_follow_up_model__dept_depth
#: model:ir.model.fields,help:ams_base.field_ams_base_request__dept_depth
msgid "Contain hierarchy of department depth when create record"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_follow_up_model__create_employee_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__create_employee_id
msgid "Create By Employee"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_company__follow_up_option__create_dept
msgid "Create Department"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_company__visitor_visibility__create_department
msgid "Created Department"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_company__visitor_visibility__create_user
msgid "Created User"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__create_uid
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__create_uid
#: model:ir.model.fields,field_description:ams_base.field_ams_base_name_model__create_uid
msgid "Created by"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__create_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__create_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_name_model__create_date
msgid "Created on"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__date
msgid "Date"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_company__dayoff_color
msgid "Dayoff Color"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_activate_model__deactivate_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__deactivate_date
msgid "Deactivate date"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_activate_model__deactivate_uid
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__deactivate_uid
msgid "Deactivate uid"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_hr_department
#: model:ir.model.fields,field_description:ams_base.field_ams_base_follow_up_model__department_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__department_id
msgid "Department"
msgstr "القسم"

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_follow_up_model__dept_depth
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__dept_depth
#: model:ir.model.fields,field_description:ams_base.field_hr_department__dept_depth
msgid "Department Depth"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__description
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__description
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__description
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__description
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__description
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__description
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__description
msgid "Description"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_hr_employee__enroll_number
#: model_terms:ir.ui.view,arch_db:ams_base.ams_employee_view_form
msgid "Device Enroll Number"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__device_serial
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__device_serial
msgid "Device Serial"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__display_card_id
msgid "Display Card Id"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__display_name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__display_name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_name_model__display_name
msgid "Display Name"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__duration
msgid "Duration"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_request__duration
msgid "Duration in hours"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__email
msgid "Email"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__email_state
msgid "Email State"
msgstr ""

#. module: ams_base
#: model:ir.actions.act_window,name:ams_base.employee_action
#: model:ir.model,name:ams_base.model_hr_employee
#: model:ir.model.fields,field_description:ams_base.field_ams_base_follow_up_model__employee_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__employee_id
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_user__user_type__employee
#: model:ir.model.fields.selection,name:ams_base.selection__ams_bs_user__user_type__employee
#: model:ir.model.fields.selection,name:ams_base.selection__ams_user__user_type__employee
msgid "Employee"
msgstr "الموظف"

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.ams_employee_view_form
msgid "Employee Access Management"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_hr_employee__employee_number
msgid "Employee No"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_follow_up_model__department_id
#: model:ir.model.fields,help:ams_base.field_ams_base_request__department_id
msgid "Employee department on record creation"
msgstr " "

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.ams_employee_view_form
msgid "Employee's Name"
msgstr ""

#. module: ams_base
#: model:ir.actions.act_window,name:ams_base.ams_employee_view_list
msgid "Employees"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__en_name
msgid "En Name"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__end_date
msgid "End Date"
msgstr ""

#. module: ams_base
#. odoo-python
#: code:addons/addons_ams/ams_base/models/base_request.py:0
#: code:addons/ams_base/models/base_request.py:0
msgid "End Date must be greater than Start Date."
msgstr " تاريخ الانتهاء يجب ان يكون اكبر من تاريخ البدء."

#. module: ams_base
#. odoo-python
#: code:addons/addons_ams/ams_base/models/base_request.py:0
#: code:addons/ams_base/models/base_request.py:0
msgid "End Date must be in the future."
msgstr " تاريخ الانتهاء يجب ان يكون في المستقبل."

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__end_datetime
msgid "End Datetime"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__en_name
msgid "English Name"
msgstr ""

#. module: ams_base
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_code_model_en_name_unique
msgid "English Name already exists !"
msgstr " الاسم الانجليزي موجود بالفعل !"

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__enroll_name
msgid "Enroll Name"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_hr_employee__enroll_number
msgid "Enroll No"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__enroll_number
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__enroll_number
msgid "Enroll Number"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__error
msgid "Error"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__error_msg
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__error_msg
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__error_msg
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__error_msg
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__error_msg
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__error_msg
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__error_msg
#: model_terms:ir.ui.view,arch_db:ams_base.api_model_view_form
msgid "Error Message"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__event_code
msgid "Event Code"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__event_datetime
msgid "Event DateTime"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__event_id
msgid "Event ID"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_event_log__state__executed
#: model:ir.model.fields.selection,name:ams_base.selection__ams_bs_event_log__state__executed
#: model:ir.model.fields.selection,name:ams_base.selection__ams_event_log__state__executed
msgid "Executed"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__executed_datetime
msgid "Executed DateTime"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__email_state__failed
msgid "Failed"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_partner__sex__female
msgid "Female"
msgstr " انثى"

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__status__finished
msgid "Finished"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_hr_employee__follow_up_level
#: model:ir.model.fields,field_description:ams_base.field_res_users__level
msgid "Flow-Up Level"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_follow_up_model
msgid "Follow Up Model"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_company__follow_up_option
msgid "Follow Up Option"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__message_follower_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__message_follower_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__message_follower_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__message_follower_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__message_follower_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__message_follower_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__message_follower_ids
msgid "Followers"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__message_partner_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__message_partner_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__message_partner_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__message_partner_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__message_partner_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__message_partner_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_access_group__activity_type_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_api_model_chatter__activity_type_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_card__activity_type_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__activity_type_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_device__activity_type_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_event_log__activity_type_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_user__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_res_company__visitor_visibility
msgid ""
"Give access for visitor records, All users can see all records if all is "
"selected, Only the user who created the record can see it if created user is"
" selected, Users in the same department as the creator can see the record if"
" created department is selected"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_res_company__follow_up_option
msgid ""
"Give access records which are created with employee department or last "
"department assigned to employee"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.api_model_view_search
msgid "Group By"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__has_image
msgid "Has Image"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__has_message
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__has_message
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__has_message
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__has_message
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__has_message
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__has_message
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__has_message
msgid "Has Message"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_name_model__id
msgid "ID"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_partner__id_number
#: model:ir.model.fields,field_description:ams_base.field_res_users__id_number
msgid "ID Number"
msgstr " رقم الهوية"

#. module: ams_base
#: model:ir.model.constraint,message:ams_base.constraint_res_partner_unique_id_number
msgid "ID Number must be unique!"
msgstr " يجب أن يكون رقم الهوية فريد"

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__ip
msgid "IP Address"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__activity_exception_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__activity_exception_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__activity_exception_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activity_exception_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activity_exception_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__activity_exception_icon
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_access_group__activity_exception_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_api_model_chatter__activity_exception_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_card__activity_exception_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__activity_exception_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_device__activity_exception_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_event_log__activity_exception_icon
#: model:ir.model.fields,help:ams_base.field_ams_base_user__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_access_group__message_needaction
#: model:ir.model.fields,help:ams_base.field_ams_base_api_model_chatter__message_needaction
#: model:ir.model.fields,help:ams_base.field_ams_base_card__message_needaction
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__message_needaction
#: model:ir.model.fields,help:ams_base.field_ams_base_device__message_needaction
#: model:ir.model.fields,help:ams_base.field_ams_base_event_log__message_needaction
#: model:ir.model.fields,help:ams_base.field_ams_base_user__message_needaction
msgid "If checked, new messages require your attention."
msgstr " إذا تم تحديد هذا الحقل، سيطلب الرسائل الجديدة المراجعة منك."

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_access_group__message_has_error
#: model:ir.model.fields,help:ams_base.field_ams_base_access_group__message_has_sms_error
#: model:ir.model.fields,help:ams_base.field_ams_base_api_model_chatter__message_has_error
#: model:ir.model.fields,help:ams_base.field_ams_base_api_model_chatter__message_has_sms_error
#: model:ir.model.fields,help:ams_base.field_ams_base_card__message_has_error
#: model:ir.model.fields,help:ams_base.field_ams_base_card__message_has_sms_error
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__message_has_error
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__message_has_sms_error
#: model:ir.model.fields,help:ams_base.field_ams_base_device__message_has_error
#: model:ir.model.fields,help:ams_base.field_ams_base_device__message_has_sms_error
#: model:ir.model.fields,help:ams_base.field_ams_base_event_log__message_has_error
#: model:ir.model.fields,help:ams_base.field_ams_base_event_log__message_has_sms_error
#: model:ir.model.fields,help:ams_base.field_ams_base_user__message_has_error
#: model:ir.model.fields,help:ams_base.field_ams_base_user__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr " إذا تم تحديد هذا الحقل، يوجد بعض الرسائل تحتوي على خطأ في تسليمها."

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__is_assigned
msgid "Is Assigned"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__is_biometric
msgid "Is Biometric"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__is_blocked
msgid "Is Blocked"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__is_event
msgid "Is Event"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__message_is_follower
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__message_is_follower
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__message_is_follower
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__message_is_follower
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__message_is_follower
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__message_is_follower
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__is_predefined
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__is_predefined
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__is_predefined
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__is_predefined
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__is_predefined
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__is_predefined
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__is_predefined
msgid "Is Predefined"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__is_visitor_group
msgid "Is Visitor Group"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__issue_count
msgid "Issue Count"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_follow_up_model__last_department_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__last_department_id
#: model:ir.model.fields.selection,name:ams_base.selection__res_company__follow_up_option__last_dept
msgid "Last Department"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_follow_up_model__last_dept_depth
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__last_dept_depth
#: model:ir.model.fields,field_description:ams_base.field_res_users__last_dept_depth
msgid "Last Department Depth"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__last_log_date
msgid "Last Log Date"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__last_log_id
msgid "Last Log ID"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__last_sync_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__last_sync_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__last_sync_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__last_sync_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__last_sync_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__last_sync_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__last_sync_date
msgid "Last Sync Date"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__write_uid
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__write_uid
#: model:ir.model.fields,field_description:ams_base.field_ams_base_name_model__write_uid
msgid "Last Updated by"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__write_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__write_date
#: model:ir.model.fields,field_description:ams_base.field_ams_base_name_model__write_date
msgid "Last Updated on"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_follow_up_model__last_department_id
#: model:ir.model.fields,help:ams_base.field_ams_base_request__last_department_id
msgid "Last department assign to employee"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__log_active
msgid "Log Active"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__log_date
msgid "Log Date"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_hr_employee__user_name
msgid "Login"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_partner__sex__male
msgid "Male"
msgstr " ذكر"

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_users__level__manager
msgid "Manager"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_company__sync_option__manual
msgid "Manual Mapping"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__message_has_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__message_has_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__message_has_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__message_has_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__message_has_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__message_has_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__message_ids
msgid "Messages"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__mobile_card
msgid "Mobile Card"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.api_model_view_form
msgid "More Info"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__schedule_type__multiple
#: model:ir.model.fields.selection,name:ams_base.selection__ams_vm_base_request__schedule_type__multiple
#: model:ir.model.fields.selection,name:ams_base.selection__ams_vm_invitation__schedule_type__multiple
#: model:ir.model.fields.selection,name:ams_base.selection__ams_vm_visit__schedule_type__multiple
msgid "Multiple Days"
msgstr " ايام متعددة"

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_name_model__name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__name
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__name
msgid "Name"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_partner__nationality
#: model:ir.model.fields,field_description:ams_base.field_res_users__nationality
msgid "Nationality"
msgstr "الجنسية"

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__need_approval
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__state__need_approval
msgid "Need Approval"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__activity_date_deadline
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__activity_summary
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__activity_summary
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__activity_summary
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activity_summary
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activity_summary
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__activity_summary
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__activity_type_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__activity_type_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__activity_type_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activity_type_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activity_type_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__activity_type_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__message_needaction_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__message_needaction_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__message_needaction_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__message_needaction_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__message_needaction_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__message_needaction_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__message_has_error_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__message_has_error_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__message_has_error_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__message_has_error_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__message_has_error_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__message_has_error_counter
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_access_group__message_needaction_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_api_model_chatter__message_needaction_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_card__message_needaction_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__message_needaction_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_device__message_needaction_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_event_log__message_needaction_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_user__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_access_group__message_has_error_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_api_model_chatter__message_has_error_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_card__message_has_error_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__message_has_error_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_device__message_has_error_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_event_log__message_has_error_counter
#: model:ir.model.fields,help:ams_base.field_ams_base_user__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__origin_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__origin_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__origin_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__origin_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__origin_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__origin_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__origin_id
msgid "Origin ID"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_partner__sex__other
msgid "Other"
msgstr "اخرى"

#. module: ams_base
#: model:ir.model,name:ams_base.model_res_partner
msgid "Partner"
msgstr "جهة الاتصال"

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_event_log__state__pending
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__email_state__pending
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__state__pending
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__status__pending
#: model:ir.model.fields.selection,name:ams_base.selection__ams_bs_event_log__state__pending
#: model:ir.model.fields.selection,name:ams_base.selection__ams_event_log__state__pending
msgid "Pending"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__port
msgid "Port"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__record_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__record_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__record_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__record_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__record_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__record_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__record_id
msgid "Record ID"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__state__rejected
msgid "Rejected"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.ams_employee_view_form
msgid "Related User"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__request_date
msgid "Request Date"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__request_uid
msgid "Requested By"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__response_uid
msgid "Responded By"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__response_code
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__response_code
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__response_code
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__response_code
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__response_code
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__response_code
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__response_code
msgid "Response Code"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__response_date
msgid "Response Date"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__activity_user_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__activity_user_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__activity_user_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__activity_user_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__activity_user_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__activity_user_id
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__status__running
msgid "Running"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__message_has_sms_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__message_has_sms_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__message_has_sms_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__message_has_sms_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__message_has_sms_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__message_has_sms_error
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__schedule_type
msgid "Schedule Type"
msgstr " نوع الزيارة"

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__email_state__sent
msgid "Sent"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_partner__sex
#: model:ir.model.fields,field_description:ams_base.field_res_users__sex
msgid "Sex"
msgstr "الجنس"

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_request__schedule_type__single
#: model:ir.model.fields.selection,name:ams_base.selection__ams_vm_base_request__schedule_type__single
#: model:ir.model.fields.selection,name:ams_base.selection__ams_vm_invitation__schedule_type__single
#: model:ir.model.fields.selection,name:ams_base.selection__ams_vm_visit__schedule_type__single
msgid "Single Day"
msgstr "يوم واحد"

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__active
msgid ""
"Special field used to hide record , when execute action  archive  and active"
" value = False"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__start_date
msgid "Start Date"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__start_datetime
msgid "Start Datetime"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__state
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__state
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__state
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__state
msgid "State"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__status
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__status
msgid "Status"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_access_group__activity_state
#: model:ir.model.fields,help:ams_base.field_ams_base_api_model_chatter__activity_state
#: model:ir.model.fields,help:ams_base.field_ams_base_card__activity_state
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__activity_state
#: model:ir.model.fields,help:ams_base.field_ams_base_device__activity_state
#: model:ir.model.fields,help:ams_base.field_ams_base_event_log__activity_state
#: model:ir.model.fields,help:ams_base.field_ams_base_user__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__sub_code
msgid "Sub Code"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_request__subject
msgid "Subject"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__res_users__level__supervisor
msgid "Supervisor"
msgstr ""

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_access_group__api_type__suprema
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_access_group__api_type__suprema
msgid "Suprema"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_company__sync_option
msgid "Sync Option"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__synced
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model__synced
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__synced
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__synced
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__synced
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__synced
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__synced
msgid "Synced"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.ams_employee_view_form
msgid "Tags"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.api_model_view_form
msgid "Technical Info"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__temperature
msgid "Temperature"
msgstr ""

#. module: ams_base
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_request_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_vm_base_request_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_vm_invitation_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_vm_visit_unique_name
msgid "The name must be unique!"
msgstr " الاسم يجب ان يكون فريد ! "

#. module: ams_base
#: model:ir.model.constraint,message:ams_base.constraint_ams_access_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_access_group_user_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_access_group_user_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_access_level_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_access_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_api_model_chatter_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_api_model_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_card_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_device_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_event_log_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_base_user_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_access_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_access_group_user_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_access_group_user_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_access_level_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_card_type_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_card_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_device_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_device_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_door_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_door_schedule_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_door_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_event_log_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_schedule_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_user_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_bs_user_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_card_type_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_card_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_device_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_device_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_door_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_door_schedule_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_door_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_event_log_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_schedule_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_user_group_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_ams_user_unique_name
#: model:ir.model.constraint,message:ams_base.constraint_res_company_unique_name
msgid "The name must be unique."
msgstr " الاسم يجب ان يكون فريد. "

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__timestamp
msgid "Timestamp"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_access_group__activity_exception_decoration
#: model:ir.model.fields,help:ams_base.field_ams_base_api_model_chatter__activity_exception_decoration
#: model:ir.model.fields,help:ams_base.field_ams_base_card__activity_exception_decoration
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__activity_exception_decoration
#: model:ir.model.fields,help:ams_base.field_ams_base_device__activity_exception_decoration
#: model:ir.model.fields,help:ams_base.field_ams_base_event_log__activity_exception_decoration
#: model:ir.model.fields,help:ams_base.field_ams_base_user__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__activate
msgid ""
"Used to deactivate record without  hide from screen,  when value = False,"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_hr_employee__user_name
msgid "Used to log into the system"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_res_users
#: model:ir.model.fields.selection,name:ams_base.selection__res_users__level__user
msgid "User"
msgstr "المستخدم"

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__user_type
msgid "User Type"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_company__vacation_color
msgid "Vacation Color"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_partner__is_visitor
#: model:ir.model.fields,field_description:ams_base.field_res_users__is_visitor
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_user__user_type__visitor
#: model:ir.model.fields.selection,name:ams_base.selection__ams_bs_user__user_type__visitor
#: model:ir.model.fields.selection,name:ams_base.selection__ams_user__user_type__visitor
msgid "Visitor"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_res_company__visitor_visibility
msgid "Visitor Visibility"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,field_description:ams_base.field_ams_base_access_group__website_message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_api_model_chatter__website_message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_card__website_message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_code_model__website_message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_device__website_message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_event_log__website_message_ids
#: model:ir.model.fields,field_description:ams_base.field_ams_base_user__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_access_group__website_message_ids
#: model:ir.model.fields,help:ams_base.field_ams_base_api_model_chatter__website_message_ids
#: model:ir.model.fields,help:ams_base.field_ams_base_card__website_message_ids
#: model:ir.model.fields,help:ams_base.field_ams_base_code_model__website_message_ids
#: model:ir.model.fields,help:ams_base.field_ams_base_device__website_message_ids
#: model:ir.model.fields,help:ams_base.field_ams_base_event_log__website_message_ids
#: model:ir.model.fields,help:ams_base.field_ams_base_user__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: ams_base
#. odoo-python
#: code:addons/addons_ams/ams_base/models/base_api_model.py:0
#: code:addons/ams_base/models/base_api_model.py:0
msgid "You cannot delete records that are synced."
msgstr "لا يمكن حذف السجلات المتزامنة."

#. module: ams_base
#: model:ir.model.fields.selection,name:ams_base.selection__ams_access_group__api_type__zk
#: model:ir.model.fields.selection,name:ams_base.selection__ams_base_access_group__api_type__zk
msgid "ZKTeco"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_abstract_model
msgid "ams_base.abstract_model"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_activate_model
msgid "ams_base.activate_model"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_code_model
msgid "ams_base.code_model"
msgstr ""

#. module: ams_base
#: model:ir.model,name:ams_base.model_ams_base_name_model
msgid "ams_base.name_model"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.ams_employee_view_form
msgid "e.g. EMP12345"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.ams_employee_view_form
msgid "e.g. Sales Manager"
msgstr ""

#. module: ams_base
#: model_terms:ir.ui.view,arch_db:ams_base.ams_employee_view_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: ams_base
#: model:ir.model.fields,help:ams_base.field_ams_base_follow_up_model__employee_id
#: model:ir.model.fields,help:ams_base.field_ams_base_request__employee_id
msgid "this record owned to this employee as follow up"
msgstr ""
