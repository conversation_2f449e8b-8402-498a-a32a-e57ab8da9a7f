import json

from odoo import models
import inspect
import requests
from requests.adapters import HTT<PERSON><PERSON>pter
import ssl

import logging

_logger = logging.getLogger('Client-API')
# 'http': 'http://localhost:8080',
PROXIES = {
    'https': 'http://127.0.0.1:5555'
}
DISABLE_SSL_WARNINGS = True  # TODO: for developer mode usages
ENABLE_PROXY = False


class NoHostnameVerificationAdapter(HTTPAdapter):
    """this class for developer mode only not production
     to Override the default behavior to not verify the hostname"""

    def init_poolmanager(self, *args, **kwargs):
        # Override the default behavior to not verify the hostname
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        kwargs['ssl_context'] = context

        return super().init_poolmanager(*args, **kwargs)


class BaseAPIClient(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.api_client'
    _description = 'Base API Client'

    # endregion

    # region Properties -----------------------------------------------------------------
    @property
    def session(self):
        # TODO: enable reusable session instead of creating session every time
        api_session = requests.Session()
        api_session.verify = False  # Disable SSL verification for all requests in this session
        if ENABLE_PROXY:
            api_session.proxies.update(PROXIES)  # Apply proxy settings to all requests

        if DISABLE_SSL_WARNINGS:
            # Mount adapters to handle both HTTP and HTTPS
            api_session.mount('http://', NoHostnameVerificationAdapter())
            api_session.mount('https://', NoHostnameVerificationAdapter())

        return api_session

    @property
    def logger(self):
        return _logger

    @property
    def response_key(self):
        return 'response'

    @property
    def response_code_key(self):
        return 'response_code'

    @property
    def response_message_key(self):
        return 'response_message'

    @property
    def response_result_key(self):
        return 'first_element'

    # @property
    # def api_base_url(self):
    #     return self.env['ir.config_parameter'].sudo().get_param('mdu_controller.api_url')
    #
    # @property
    # def api_username(self):
    #     return self.env['ir.config_parameter'].sudo().get_param('mdu_controller.username')
    #
    # @property
    # def api_password(self):
    #     return self.env['ir.config_parameter'].sudo().get_param('mdu_controller.password')

    def not_implemented_response(self, method_name=''):
        return {
            'response_code': '0',
            'response_status_code': '200',
            'response_message': f'Method [{method_name}] Not Implemented in this controller {self._name}',
            'response_result': ''
        }

    def is_success_response(self, response):
        ...

    @property
    def END_POINTS(self):
        return {
            "HOME": "/home",
            "UPLOAD_FILE": "/upload-tone/",
            "GET_SOUND_LIST": "/tones/",
            "MANAGE_TONES": "/manage_tones",
            "REMOVE_TONE": "/remove-tone",
            "PLAY_TONE": "/play-tone/",
            "LIST_DEVICES": "/devices/",
            "GET_CONFIG": "/get-config",
            "GET_SCHEDULE_CONFIG": "/get-schedule-config",
            "SAVE_SCHEDULE_CONFIG": "/save-schedule-config",
            "GET_SCHEDULE_STATUS": "/scheduler-status",
            "SAVE_CONFIG": "/save-config",
            "START_SCHEDULE": "/start-scheduler",
            "RELOAD_SCHEDULE_CONFIG": "/reload-scheduler-config",
            "STOP_SCHEDULE": "/stop-scheduler",
            "IS_ALIVE": "/is-alive",
            "GET_FILES": "/files",

        }

    def _handle_request_exceptions(self, exception, response=None):
        """Centralize exception handling for API requests."""
        response_status_code = response.status_code if response else 500
        response_code = "0"
        response_message = "An unexpected error occurred."
        response_result = None

        api_server = 'API Server'  # sound player API (Client App)
        if isinstance(exception, requests.exceptions.ConnectionError) or response_status_code == 404:
            response_message = f"Connection error: Unable to connect to the {api_server}."
        elif isinstance(exception, requests.exceptions.Timeout):
            response_message = "Timeout error: The request timed out."
        elif isinstance(exception, requests.exceptions.HTTPError):
            response_message = f"HTTP error occurred: {exception}"
        elif isinstance(exception, requests.exceptions.RequestException):
            response_message = f"An error occurred during the request: {exception}"
        elif isinstance(exception, ValueError):
            response_message = "The (Client App) returned an invalid Value."
        elif isinstance(exception, json.JSONDecodeError):
            response_message = f"Error parsing response: {api_server} returned an invalid JSON."

        return {
            'response_code': response_code,
            'response_status_code': response_status_code,
            'response_message': response_message,
            'response_result': response_result
        }

    def get_response_dict(self, response):
        try:
            response_json = response.json()
            response_obj = response_json.get(self.response_key, '')
            response_result_json = response.json()
            is_success_response = self.is_success_response(response_obj)
            return {
                'response_code': response_obj.get(self.response_code_key, ''),
                'response_status_code': response.status_code,
                'response_message': response_obj.get(self.response_message_key, ''),
                'response_result': response_json.get(self.response_result_key, '')  # first element if no error
            }

        except Exception as e:
            return self._handle_request_exceptions(e, response)

    # endregion]

    # region Helper Methods -----------------------------------------------------
    def convert_to_boolean(self, value):
        """ Convert JSON value to boolean"""
        if isinstance(value, str):
            if value.lower() == 'true':
                return True
            elif value.lower() == 'false':
                return False
        return bool(value)

    def get_field_value(self, json_data, field_path):
        """
        Retrieve the value of a nested field in a JSON object

        Args:
            json_data (dict): The JSON data as a dictionary.
            field_path (str): The path of the nested field, e.g., 'field1.field2.field3.field4'.

        Returns:
            The value of the nested field if it exists, otherwise None.
        """
        fields = field_path.split('.')
        current_level = json_data

        try:
            for field in fields:
                current_level = current_level[field]
            return current_level
        except (KeyError, TypeError):
            return None
    # endregion
