# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class ActivateModel(models.AbstractModel):
    _name = "ams_base.activate_model"
    _description = "ams_base.activate_model"
    _inherit = "ams_base.abstract_model"
    _abstract = True

    activate = fields.Boolean(string="Activate", default=True, help="")
    activate_date = fields.Datetime(string="Activate date", help="")
    deactivate_date = fields.Datetime(string="Deactivate date", help="")

    deactivate_uid = fields.Many2one(comodel_name="res.users", string="Deactivate uid", help="")
    activate_uid = fields.Many2one(comodel_name="res.users", string="Activate uid", help="")

    def action_deactivate(self, ):
        self.deactivate_uid = self.env.user.id
        self.deactivate_date = fields.Datetime.now()
        self.activate = False

    def action_activate(self, ):
        self.activate_uid = self.env.user.id
        self.activate_date = fields.Datetime.now()
        self.activate = True
