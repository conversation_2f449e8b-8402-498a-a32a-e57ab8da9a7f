# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

import logging

_logger = logging.getLogger('AMS')


class BaseAbstractModel(models.AbstractModel):
    _name = "ams_base.abstract_model"
    _description = "ams_base.abstract_model"
    _abstract = True

    company_id = fields.Many2one(comodel_name="res.company", string="Company", help="")

    def _get_tz(self):
        # TODO add configuration
        return self.env.user.tz or 'Asia/Riyadh'

    @property
    def logger(self):
        return _logger

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def call_model_method_safely(self, model, method_name, *args, **kwargs):
        method = getattr(model, method_name, None)

        if not callable(method):
            raise AttributeError(f"'{method_name}' is not a callable method on model {model._name}")

        return method(*args, **kwargs)

    def send_auto_refresh(self):
        """auto refresh for treeview send trigger to bus channel 'auto_refresh' exist in module lp_auto_refresh
         so to work properly need install module lp_auto_refresh manually"""
        for record in self:
            record.env['bus.bus']._sendone('auto_refresh', 'auto_refresh',
                                           {'model': record._name, 'id': record.id})

    # endregion

    def _format_time(self, value):

        """Format float time as HH:MM for error messages."""

        hours = int(value)

        minutes = int((value % 1) * 60)

        return f"{hours:02d}:{minutes:02d}"

    def _validate_time_field(self, value, field_name):

        """Validate time fields are between 0.0 and 23:59."""

        if value is not False and not (0.0 <= value < 24):
            raise ValidationError(

                _("'%s' must be between 00:00 and 23:59. Given: %s")

                % (field_name, self._format_time(value))

            )

        return True

    @api.model
    def _get_time_fields(self):
        """Return list of float fields that represent time
        Override this in child models to specify time fields to validate"""
        return []

    @api.constrains(lambda self: self._get_time_fields())
    def _check_time_fields(self):
        """Centralized validation for all time fields"""
        for record in self:
            for field_name in record._get_time_fields():
                value = record[field_name]
                record._validate_time_field(value, field_name)
