# -*- coding: utf-8 -*-
from collections import defaultdict

from odoo import api, fields, models
from odoo.api import model


class BaseAccessGroup(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.access_group'
    _inherit = 'ams_base.api_model_chatter'
    _description = 'Base Access Group'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    is_visitor_group = fields.Boolean(string="Is Visitor Group", default=False)
    api_type = fields.Selection([('suprema', 'Suprema'), ('zk', 'ZKTeco')], string="API Type", readonly=True)

    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_open_devices_tree_wizard(self):
        pass

    def action_open_users_tree_wizard(self):
        pass

    def action_open_device_action_views(self):
        pass

    def action_apply_ac_group_config(self):
        pass

    def action_delete_ac_group(self):
        pass

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # Method to return grouping based on API Type
    def get_grouped_by_api_type(self):
        grouped_data = defaultdict(list)
        # records = self.search([])  # Fetch all records. Adjust the domain if needed.
        for record in self:
            grouped_data[record.api_type].append(record)
        return dict(grouped_data)

    # endregion
