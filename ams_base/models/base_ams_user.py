# -*- coding: utf-8 -*-
from datetime import datetime, timedelta
from odoo import api, fields, models


class BaseAMSUser(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.user'
    _inherit = 'ams_base.api_model_chatter'
    _description = 'Base AMS User'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    user_type = fields.Selection([('employee', 'Employee'), ('visitor', 'Visitor')], string='User Type')
    enroll_number = fields.Integer(string='Enroll Number', tracking=True, default=0, copy=False)
    start_datetime = fields.Datetime(
        string='Start Datetime',
        default=lambda self: fields.Datetime.start_of(datetime.now(), 'year')
    )
    end_datetime = fields.Datetime(
        string='End Datetime',
        default=lambda self: fields.Datetime.end_of(datetime.now() + timedelta(days=365 * 10), 'year')
        # 10 years from now
    )
    activate = fields.Boolean(string='Activate', default=True)
    email = fields.Char()

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_sync_users(self):
        pass

    def action_link_users(self):
        pass

    def action_unlink_users(self):
        pass
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
