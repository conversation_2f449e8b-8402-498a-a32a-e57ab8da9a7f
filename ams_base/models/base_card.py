# -*- coding: utf-8 -*-


from odoo import api, fields, models


class BaseCard(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_base.card"
    _description = "Base Card"
    _inherit = 'ams_base.api_model_chatter'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion]

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    card_number = fields.Char(string='Card Number')
    display_card_id = fields.Char(string='Display Card Id')
    status = fields.Char(string='Status')
    state = fields.Char(string='State')  # TODO selection
    is_blocked = fields.Bo<PERSON>an(string='Is Blocked')
    is_assigned = fields.Boolean(string='Is Assigned')
    mobile_card = fields.Boolean(string='Mobile Card')
    issue_count = fields.Integer(string='Issue Count')
    card_slot = fields.Integer(string='Card Slot')
    card_mask = fields.Char(string='Card Mask')

    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
