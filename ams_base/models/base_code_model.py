# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class BaseCodeModel(models.Model):
    _name = "ams_base.code_model"
    _description = "ams_base.code_model"
    _inherit = ["ams_base.activate_model", "mail.thread", "mail.activity.mixin"]

    _sql_constraints = [
        ('code_unique', 'unique (code)', "Code already exists !"),
        ('ar_name_unique', 'unique (ar_name)', "Arabic Name already exists !"),
        ('en_name_unique', 'unique (en_name)', "English Name already exists !")]

    name = fields.Char(default='Name', readonly=True, compute="_compute_name", search="_search_name")
    code = fields.Char(string="Code", help="Code", tracking=True)
    ar_name = fields.Char(string="Arabic name", help="Arabic Name", required=True, tracking=True)
    en_name = fields.Char(string="En Name", help="English Name", required=True, tracking=True)
    activate = fields.Boolean(string="Activate", default=True, tracking=True,
                              help="Used to deactivate record without  hide from screen,  when value = False,")
    active = fields.Boolean(string="Active", default=True, tracking=True,
                            help="Special field used to hide record , when execute action  archive  and active value "
                                 "= False")

    @api.depends_context('lang', )
    @api.depends("ar_name", "en_name")
    def _compute_name(self):
        language = self.env.lang
        for record in self:
            name = record.en_name
            if language:
                if language.startswith('ar'):
                    name = record.ar_name
                else:
                    name = record.en_name

            record.name = name

    def _search_display_name(self, operator, value):
        # todo : filter based on input
        """
        filter names which matches the user input to help in search by name as a computed field
        """
        result = self.search(['|', '|',
                              ('ar_name', operator, value),
                              ('en_name', operator, value),
                              ('code', operator, value)])
        return [('id', 'in', result.ids)]

    def _search_name(self, operator, value):
        """
        filter names which matches the user input to help in search by name as a computed field
        """
        result = self.search(['|', '|',
                              ('ar_name', operator, value),
                              ('en_name', operator, value),
                              ('code', operator, value)])
        return [('id', 'in', result.ids)]

    def copy(self, default=None):
        """
        override the copy method to add some character to the unique fields in order to avoid the redundancy
        which obstruct the duplication action.
        """
        default = dict(default or {})
        added_char = "_Copy(%s)" % self.id
        default.update({
            'code': self.code + added_char,
            'ar_name': self.ar_name + added_char,
            'en_name': self.en_name + added_char,
        })
        return super(BaseCodeModel, self).copy(default)
