# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class BaseDevice(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.device'
    _inherit = 'ams_base.api_model_chatter'
    _description = 'Base Device'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    ip = fields.Char(string='IP Address')
    port = fields.Integer(string='Port')
    device_serial = fields.Char(string='Device Serial')
    # state = fields.Char(string='State')
    state = fields.Selection(selection=[('1', 'Online'), ('0', 'Offline'),('2', 'Sync Error')], default='0')

    last_log_id = fields.Integer(string='Last Log ID')
    last_log_date = fields.Datetime(string='Last Log Date')
    activate = fields.Boolean(string='Activate')
    log_active = fields.Boolean(string='Log Active')

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_sync_devices(self):
        pass
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
