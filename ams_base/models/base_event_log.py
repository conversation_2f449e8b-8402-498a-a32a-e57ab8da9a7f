# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class BaseEventLog(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.event_log'
    _inherit = 'ams_base.api_model_chatter'
    _description = 'Base Event Log'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    device_serial = fields.Char(string="Device Serial")
    event_datetime = fields.Datetime(string="Event DateTime")
    log_date = fields.Datetime(string="Log Date")
    event_id = fields.Char(string="Event ID")
    event_code = fields.Integer(string="Event Code")
    code_name = fields.Char(string="Code Name")
    sub_code = fields.Integer(string="Sub Code")
    executed_datetime = fields.Datetime(string="Executed DateTime")
    has_image = fields.Boolean(string="Has Image")
    temperature = fields.Float(string="Temperature")
    timestamp = fields.Char(string="Timestamp")
    description = fields.Char(string="Description")
    state = fields.Selection([('pending', 'Pending'), ('executed', 'Executed')], string="State")
    enroll_number = fields.Char(string="Enroll Number")
    enroll_name = fields.Char(string="Enroll Name")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    is_punch_log = fields.Boolean(compute="_compute_is_punch_log" , store=True)

    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('event_code')
    def _compute_is_punch_log(self):
        """
        Compute whether the event is a punch log based on its event code.
        Sets is_punch_log to True if event_code is a success code.
        """
        for record in self:
            record.is_punch_log = self._is_success_code(record.event_code)
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _create_punch_log(self, enroll_number, device_serial, log_date):
        """
        implementation will be in ta module and called from ta_suprema
        """
        ...

    @staticmethod
    def _is_success_code(event_code):
        """
        Check if the event code falls within success code ranges.
        Args:
            event_code (int): The event code to check
        Returns:
            bool: True if event_code is in success ranges, False otherwise
        """
        if not event_code:
            return False

        success_code_ranges = [
            (4096, 4140),     # VERIFY_SUCCESS and variants
            (4864, 4872),     # IDENTIFY_SUCCESS
            (4608, 4652),     # VERIFY_DURESS
            (5376, 5384),     # IDENTIFY_DURESS
            (5632, 5632),     # DUAL_AUTH_SUCCESS (single code)
        ]

        for start, end in success_code_ranges:
            if start <= event_code <= end:
                return True
        return False
    # endregion
