# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class HrDepartment(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "hr.department"
    _description = "Department"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    dept_depth = fields.Char(string='Department Depth', compute='_compute_dept_depth', store=True)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('parent_id')
    def _compute_dept_depth(self):
        for department in self:
            # If there's no parent, set dept_depth to the department's ID
            if not department.parent_id:
                department.dept_depth = str(department.id)
            else:
                if not department.parent_id.dept_depth:
                    department.dept_depth = False
                else:
                    # Concatenate parent dept_depth with current dept's ID to build hierarchy
                    department.dept_depth = department.parent_id.dept_depth + '.' + str(department.id)

    # Method to recalculate dept_depth for all departments
    def compute_all_dept_depths(self):
        for department in self:
            department._compute_dept_depth()
        return True

    @property
    def dept_depth_ids(self):
        return [int(id) for id in self.dept_depth.split('.') if id not in [False, 'False', 'false', '']]

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _domain_follow_up(self):
        """ """

        # Determine user access level
        user = self.env.user

        current_user_department = user.department_id

        if user._is_admin() or user.level == 'administrator':
            return []

        if not current_user_department:
            return [('id', '=', -1)]

        if user.level in ['user', 'supervisor']:
            return [('id', '=', current_user_department.id)]

        elif user.level == 'manager':
            return ['|', ('id', 'child_of', current_user_department.id), ('id', '=', current_user_department.id)]

    # endregion
