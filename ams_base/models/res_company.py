# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class ResCompany(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "res.company"
    _inherit = "res.company"
    _description = "Company"

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    dayoff_color = fields.Char()
    vacation_color = fields.Char()
    absent_color = fields.Char()
    attend_color = fields.Char()

    sync_option = fields.Selection([('manual', 'Manual Mapping'), ('auto', 'Auto Mapping')],
                                   default='auto', string="Sync Option",
                                   help="Auto option will create records in ams base")

    follow_up_option = fields.Selection([('create_dept', 'Create Department'),
                                         ('last_dept', 'Last Department')],
                                        default='last_dept', string="Follow Up Option",
                                        help="Give access records which are created with employee department or last department assigned to employee")

    visitor_visibility = fields.Selection(
        [('all', 'All'), ('create_user', 'Created User'), ('create_department', 'Created Department')],
        default='create_user', string="Visitor Visibility",
        help="Give access for visitor records, All users can see all records if all is selected, Only the user who created the record can see it if created user is selected, Users in the same department as the creator can see the record if created department is selected")
    #
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
