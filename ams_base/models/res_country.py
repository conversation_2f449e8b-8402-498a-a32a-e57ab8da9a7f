# -*- coding: utf-8 -*-
from odoo import api, fields, models, _

class ResCountry(models.Model):
    _inherit = 'res.country'

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=None):
        """Override name_search to load all countries without limit but still enable search"""
        args = args or []

        # If there's a search term, filter by it
        if name:
            # Search for countries that start with the search term
            # Use 'ilike' with a '%' wildcard at the end only
            domain = ['|', ('name', '=ilike', name + '%'), ('code', '=ilike', name + '%')]
            args = args + domain

        # If the context has 'load_all_countries', ignore the limit
        if self._context.get('load_all_countries'):
            limit = None

        # Use the standard search but without limit if specified
        return super(ResCountry, self).name_search(name=name, args=args, operator=operator, limit=limit)
