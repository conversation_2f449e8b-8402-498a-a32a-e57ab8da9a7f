# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class ResPartner(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "res.partner"
    _inherit = "res.partner"
    _description = "Partner"
    _sql_constraints = [
        ('unique_id_number', 'unique(id_number)', 'ID Number must be unique!')
    ]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    @api.model
    def _get_default_country(self):
        return self.env['res.country'].search([('code', '=', 'SA')], limit=1).id
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    sex = fields.Selection([
        ('male', 'Male'),
        ('female', 'Female')
    ], string="Gender" , default='male')
    id_number = fields.Char(string="ID Number")

    # endregion

    # region  Special
    # endregion

    # region  Relational
    nationality = fields.Many2one('res.country', string="Nationality", default=lambda self: self._get_default_country())
    is_visitor = fields.Boolean(string="Visitor")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
