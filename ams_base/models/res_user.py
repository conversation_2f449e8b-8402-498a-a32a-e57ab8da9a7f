# -*- coding: utf-8 -*-


from odoo import models, fields, api, _
from odoo.exceptions import UserError


class ResUser(models.Model):
    """Predefined Odoo module addons/base/res_users"""

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "res.users"

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic

    level = fields.Selection(
        [
            ('user', 'User'),
            ('supervisor', 'Supervisor'),
            ('manager', 'Manager'),
            ('administrator', 'Administrator'),
        ],
        string="Flow-Up Level",
        default='user',
        required=True,
    )
    last_dept_depth = fields.Char(string='Last Department Depth', index=True,
                                  related='department_id.dept_depth', store=True,
                                  help="Contain hierarchy  depth of last department assigned to employee")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
