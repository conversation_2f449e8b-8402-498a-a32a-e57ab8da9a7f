<?xml version="1.0" encoding="utf-8"?>
<odoo>
     <record id="ams_group_category" model="ir.module.category" >
        <field name="name">AMS Category</field>
        <field name="description">Access Management Category</field>
        <field name="sequence">10</field>
    </record>

    <record id="ams_group_manager" model="res.groups">
        <field name="name">AMS Manager</field>
        <field name="category_id" ref="ams_group_category"/>
    </record>

    <record id="ams_group_user" model="res.groups">
        <field name="name">AMS User</field>
        <field name="category_id" ref="ams_group_category"/>
    </record>

    <record id="ams_group_readonly" model="res.groups">
        <field name="name">AMS ReadOnly</field>
        <field name="category_id" ref="ams_group_category"/>
    </record>

     <record id="base.menu_management" model="ir.ui.menu">
            <field name="groups_id" eval="[(6, 0, [ref('base.group_system')])]"/>
     </record>
   <record id="hr.menu_hr_root" model="ir.ui.menu">
            <field name="groups_id" eval="[(6, 0, [ref('base.group_system')])]"/>
        </record>
</odoo>
