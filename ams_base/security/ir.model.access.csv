id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink

access_abstract_model_admin,access_abstract_model_admin,model_ams_base_abstract_model,base.group_system,1,1,1,1
access_abstract_model_manager,access_abstract_model_manager,model_ams_base_abstract_model,ams_group_manager,1,1,1,1
access_abstract_model_user,access_abstract_model_user,model_ams_base_abstract_model,ams_group_user,1,1,1,0
access_abstract_model_readonly,access_abstract_model_readonly,model_ams_base_abstract_model,ams_group_readonly,1,0,0,0

access_base_access_group_admin,access_base_access_group_admin,model_ams_base_access_group,base.group_system,1,1,1,1
access_base_access_group_manager,access_base_access_group_manager,model_ams_base_access_group,ams_group_manager,1,1,1,1
access_base_access_group_user,access_base_access_group_user,model_ams_base_access_group,ams_group_user,1,1,1,0
access_base_access_group_readonly,access_base_access_group_readonly,model_ams_base_access_group,ams_group_readonly,1,0,0,0

access_base_ams_user_admin,access_base_ams_user_admin,model_ams_base_user,base.group_system,1,1,1,1
access_base_ams_user_manager,access_base_ams_user_manager,model_ams_base_user,ams_group_manager,1,1,1,1
access_base_ams_user_user,access_base_ams_user_user,model_ams_base_user,ams_group_user,1,1,1,0
access_base_ams_user_readonly,access_base_ams_user_readonly,model_ams_base_user,ams_group_readonly,1,0,0,0

access_base_api_client_admin,access_base_api_client_admin,model_ams_base_api_client,base.group_system,1,1,1,1
access_base_api_client_manager,access_base_api_client_manager,model_ams_base_api_client,ams_group_manager,1,1,1,1
access_base_api_client_user,access_base_api_client_user,model_ams_base_api_client,ams_group_user,1,1,1,0
access_base_api_client_readonly,access_base_api_client_readonly,model_ams_base_api_client,ams_group_readonly,1,0,0,0

access_base_api_model_admin,access_base_api_model_admin,model_ams_base_api_model,base.group_system,1,1,1,1
access_base_api_model_manager,access_base_api_model_manager,model_ams_base_api_model,ams_group_manager,1,1,1,1
access_base_api_model_user,access_base_api_model_user,model_ams_base_api_model,ams_group_user,1,1,1,0
access_base_api_model_readonly,access_base_api_model_readonly,model_ams_base_api_model,ams_group_readonly,1,0,0,0

access_base_device_admin,access_base_device_admin,model_ams_base_device,base.group_system,1,1,1,1
access_base_device_manager,access_base_device_manager,model_ams_base_device,ams_group_manager,1,1,1,1
access_base_device_user,access_base_device_user,model_ams_base_device,ams_group_user,1,1,1,0
access_base_device_readonly,access_base_device_readonly,model_ams_base_device,ams_group_readonly,1,0,0,0

access_base_event_log_admin,access_base_event_log_admin,model_ams_base_event_log,base.group_system,1,1,1,1
access_base_event_log_manager,access_base_event_log_manager,model_ams_base_event_log,ams_group_manager,1,1,1,1
access_base_event_log_user,access_base_event_log_user,model_ams_base_event_log,ams_group_user,1,1,1,0
access_base_event_log_readonly,access_base_event_log_readonly,model_ams_base_event_log,ams_group_readonly,1,0,0,0

access_base_zk_api_client_admin,access_base_zk_api_client_admin,model_ams_base_zk_api_client,base.group_system,1,1,1,1
access_base_zk_api_client_manager,access_base_zk_api_client_manager,model_ams_base_zk_api_client,ams_group_manager,1,1,1,1
access_base_zk_api_client_user,access_base_zk_api_client_user,model_ams_base_zk_api_client,ams_group_user,1,1,1,0
access_base_zk_api_client_readonly,access_base_zk_api_client_readonly,model_ams_base_zk_api_client,ams_group_readonly,1,0,0,0

access_base_request_admin,access_base_request_admin,model_ams_base_request,base.group_system,1,1,1,1
access_base_request_manager,access_base_request_manager,model_ams_base_request,ams_group_manager,1,1,1,1
access_base_request_user,access_base_request_user,model_ams_base_request,ams_group_user,1,1,1,0
access_base_request_readonly,access_base_request_readonly,model_ams_base_request,ams_group_readonly,1,0,0,0

access_card_group_admin,access_card_group_admin,model_ams_base_card,base.group_system,1,1,1,1
access_card_group_manager,access_card_group_manager,model_ams_base_card,ams_group_manager,1,1,1,1
access_card_group_user,access_card_group_user,model_ams_base_card,ams_group_user,1,1,1,0
access_card_group_readonly,access_card_group_readonly,model_ams_base_card,ams_group_readonly,1,0,0,0

access_employee_group_admin,access_employee_group_admin,model_hr_employee,base.group_system,1,1,1,1
access_employee_group_manager,access_employee_group_manager,model_hr_employee,ams_group_manager,1,1,1,1
access_employee_group_user,access_employee_group_user,model_hr_employee,ams_group_user,1,1,1,0
access_employee_group_readonly,access_employee_group_readonly,model_hr_employee,ams_group_readonly,1,0,0,0

access_name_model_admin,access_name_model_admin,model_ams_base_name_model,base.group_system,1,1,1,1
access_name_model_manager,access_name_model_manager,model_ams_base_name_model,ams_group_manager,1,1,1,1
access_name_model_user,access_name_model_user,model_ams_base_name_model,ams_group_user,1,1,1,0
access_name_model_readonly,access_name_model_readonly,model_ams_base_name_model,ams_group_readonly,1,0,0,0

access_code_model_admin,access_code_model_admin,model_ams_base_code_model,base.group_system,1,1,1,1
access_code_model_manager,access_code_model_manager,model_ams_base_code_model,ams_group_manager,1,1,1,1
access_code_model_user,access_code_model_user,model_ams_base_code_model,ams_group_user,1,1,1,0
access_code_model_readonly,access_code_model_readonly,model_ams_base_code_model,ams_group_readonly,1,0,0,0
