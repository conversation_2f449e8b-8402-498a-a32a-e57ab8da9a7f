# Language Toggle Component - Changelog

All notable changes to the Language Toggle Component will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2024-01-XX

### Added
- **Organized Component Structure**: Consolidated all language toggle assets into `lang_toggle_component/` folder
- **Comprehensive Documentation**: Added detailed README.md and technical documentation
- **Reactive Flag Display**: Flag now updates automatically based on `isArabic` getter
- **Enhanced Error Handling**: Multiple fallback strategies for page reload
- **Cross-Tab Synchronization**: Language changes sync across browser tabs using localStorage
- **Event-Driven Architecture**: Implemented MutationObserver, storage events, and custom events
- **Success Rate Monitoring**: Dynamic switching tracks success rate for optimization
- **Timeout Detection**: Page reload timeout detection with user notifications
- **Safe Property Assignment**: Property descriptor checking to avoid read-only errors

### Changed
- **Template Logic**: Updated flag display from hardcoded `state.currentLang === 'ar_001'` to reactive `isArabic` getter
- **Language Detection**: Enhanced priority-based language detection with localStorage support
- **Component Architecture**: Refactored from polling-based to event-driven reactive updates
- **File Organization**: Moved from scattered files to organized component structure

### Fixed
- **Arabic Language Support**: Fixed handling of both `ar_001` and `ar-001` formats
- **Display Logic**: Corrected flag and text synchronization issues
- **Page Reload**: Resolved reload functionality with multiple fallback strategies
- **Property Errors**: Fixed "Cannot set property lang" errors with safe assignment
- **Memory Leaks**: Proper cleanup of event listeners on component destruction

### Technical Improvements
- **Performance**: Eliminated setInterval polling in favor of reactive updates
- **Reliability**: Multiple reload strategies with comprehensive error handling
- **Maintainability**: Organized file structure with clear separation of concerns
- **Debugging**: Enhanced console logging for better troubleshooting
- **Documentation**: Comprehensive technical and user documentation

## [1.0.0] - 2024-01-XX (Initial Implementation)

### Added
- **Basic Language Toggle**: Initial implementation of Arabic/English language switching
- **Systray Integration**: Component registration in Odoo's systray
- **RPC Communication**: Backend language switching via `/ams_base/switch_language` endpoint
- **Basic UI**: Flag and text display for language indication
- **CSS Styling**: Basic component styling with RTL support
- **Template Structure**: XML template for component rendering

### Features
- Simple click-to-switch functionality
- Basic flag display (US/Saudi Arabia)
- Text display in both languages
- Page reload after language change
- Integration with Odoo's user preferences

---

## Migration Guide

### From v1.0.0 to v2.0.0

#### File Structure Changes

**Old Structure:**
```
static/src/
├── css/language_toggle.css
├── js/language_toggle_systray.js
├── xml/language_toggle_systray.xml
└── svg/
    ├── sa.svg
    └── us.svg
```

**New Structure (Simplified):**
```
static/src/lang_toggle_component/
├── language_toggle.css
├── language_toggle_systray.js
├── language_toggle_systray.xml
├── sa.svg
├── us.svg
├── TECHNICAL.md
├── CHANGELOG.md
└── README.md
```

#### Code Changes

**Template Updates:**
```xml
<!-- Old -->
<img t-att-src="state.currentLang === 'ar_001' ? '/ams_base/static/src/svg/us.svg' : '/ams_base/static/src/svg/sa.svg'" />

<!-- New -->
<img t-att-src="isArabic ? '/ams_base/static/src/svg/us.svg' : '/ams_base/static/src/svg/sa.svg'" />
```

**JavaScript Enhancements:**
- Added event-driven architecture
- Enhanced error handling
- Improved language detection
- Added reactive state management

#### Breaking Changes

1. **File Paths**: Update any hardcoded references to old file paths
2. **Template Logic**: Flag display now uses `isArabic` getter instead of direct state comparison
3. **Event Handling**: Component now uses multiple event listeners instead of polling

#### Upgrade Steps

1. **Update Module**: Upgrade the `ams_base` module
2. **Clear Cache**: Clear browser cache and Odoo assets
3. **Restart Server**: Restart Odoo server to load new assets
4. **Test Functionality**: Verify language switching works correctly
5. **Check Console**: Monitor browser console for any errors

---

## Development Notes

### Version 2.0.0 Development Focus

1. **Code Organization**: Consolidated scattered files into a cohesive component structure
2. **Documentation**: Added comprehensive documentation for maintainability
3. **Reliability**: Enhanced error handling and fallback mechanisms
4. **Performance**: Optimized reactive updates and eliminated polling
5. **User Experience**: Improved feedback and error notifications

### Testing Checklist

- [ ] Language switching works in both directions
- [ ] Flag updates correctly with language changes
- [ ] Cross-tab synchronization functions properly
- [ ] Page reload fallbacks work when dynamic switching fails
- [ ] Error notifications appear for failed operations
- [ ] Component cleans up properly on destruction
- [ ] RTL/LTR document direction updates correctly
- [ ] localStorage synchronization works across tabs

### Known Issues

- **Browser Compatibility**: Some older browsers may not support all features
- **Network Issues**: RPC failures may require manual page refresh
- **Cache Issues**: Browser cache may need clearing after updates

### Future Roadmap

- **Multi-language Support**: Extend beyond Arabic/English
- **User Preferences**: Remember user language preferences
- **Accessibility**: Enhanced ARIA support and keyboard navigation
- **Performance**: Further optimizations for large applications
- **Testing**: Automated testing suite implementation