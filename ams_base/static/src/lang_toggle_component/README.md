# Language Toggle Component

A comprehensive language switching component for Odoo that provides seamless bilingual support between Arabic and English.

## Overview

The Language Toggle Component is a systray widget that allows users to quickly switch between Arabic (`ar_001`) and English (`en_US`) languages in the Odoo interface. It features reactive UI updates, robust error handling, and multiple fallback mechanisms.

## Features

- **Reactive UI**: Flag and text automatically update based on current language
- **Robust Language Detection**: Multiple fallback sources for language detection
- **Event-Driven Architecture**: Uses MutationObserver, storage events, and custom events
- **Cross-Tab Synchronization**: Language changes sync across browser tabs
- **Multiple Reload Strategies**: Comprehensive fallback mechanisms for page reload
- **RTL/LTR Support**: Automatic document direction switching
- **Error Handling**: Graceful degradation with user notifications

## File Structure

```
lang_toggle_component/
├── language_toggle_systray.js        # Main component logic
├── language_toggle.css               # Component styling
├── language_toggle_systray.xml       # Component template
├── sa.svg                            # Saudi Arabia flag
├── us.svg                            # United States flag
├── TECHNICAL.md                      # Technical documentation
├── CHANGELOG.md                      # Version history
└── README.md                         # This file
```

## Technical Architecture

### Component Structure

- **Framework**: Owl Component (Odoo 18)
- **Services**: ORM, Action, Notification
- **State Management**: Reactive state with `useState`
- **Event Handling**: Multiple listener types for comprehensive reactivity

### Language Detection Priority

1. Session user context (`session.user_context.lang`)
2. User object (`user.lang`)
3. Global session info (`window.odoo.__session_info__.user_context.lang`)
4. Local storage (`localStorage.getItem('odoo_user_lang')`)
5. Document language (`document.documentElement.lang`)
6. Default fallback (`en_US`)

### Event Listeners

1. **MutationObserver**: Watches document language attribute changes
2. **Storage Events**: Listens for cross-tab language changes
3. **Custom Events**: Responds to `odoo:languageChanged` events

## Usage

### For Users

1. Locate the language toggle button in the top header (systray area)
2. The button displays:
   - Current language text ("English" or "العربية")
   - Corresponding flag (US flag for Arabic interface, Saudi flag for English interface)
3. Click the button to switch languages
4. The page will reload with the new language applied

### For Developers

#### Integration

The component is automatically registered in the systray:

```javascript
registry.category("systray").add(
    "ams_base.LanguageToggleSystray",
    {
        Component: LanguageToggleSystray,
    },
    { sequence: 100 }
);
```

#### Customization

**Adding More Languages:**

1. Extend the `isArabic` getter to handle additional languages
2. Update the `displayText` getter for new language pairs
3. Modify the `toggleLanguage` method for multi-language support
4. Add corresponding flag SVG files

**Custom Styling:**

Modify `language_toggle.css` to customize appearance:

```css
.o_language_toggle_btn {
    /* Custom button styles */
}

.o_flag_icon {
    /* Custom flag styles */
}
```

## API Reference

### Methods

#### `getCurrentLanguage()`
Returns the current language code with priority-based detection.

#### `toggleLanguage()`
Switches between Arabic and English languages with comprehensive error handling.

#### `tryDynamicLanguageSwitch(targetLang)`
Attempts dynamic language switching without page reload.

#### `forcePageReload()`
Implements multiple reload strategies with timeout detection.

#### `updateLanguageReferences(targetLang)`
Updates all language references across the application.

### Properties

#### `isArabic` (getter)
Returns `true` if current language is Arabic (handles both `ar_001` and `ar-001` formats).

#### `displayText` (getter)
Returns the text to display based on current language.

### State

```javascript
this.state = {
    currentLang: string,  // Current language code
    isLoading: boolean    // Loading state during language switch
}
```

## Error Handling

### Graceful Degradation

1. **RPC Failure**: Falls back to direct ORM calls
2. **Dynamic Switch Failure**: Falls back to page reload
3. **Reload Failure**: Multiple reload strategies with user notifications
4. **Property Access Errors**: Safe property assignment with descriptor checking

### User Notifications

- Success notifications for successful language switches
- Warning notifications for reload issues
- Error notifications with manual refresh instructions

## Performance Considerations

- **Efficient Event Handling**: Proper cleanup of event listeners
- **Minimal DOM Manipulation**: Reactive updates only when necessary
- **Optimized Reload**: Dynamic switching attempts before falling back to reload
- **Success Rate Monitoring**: Tracks dynamic switching success for optimization

## Browser Compatibility

- Modern browsers with ES6+ support
- MutationObserver API support
- LocalStorage support
- Custom Events support

## Security

- Safe property assignment with descriptor checking
- Input validation for language codes
- XSS protection through proper escaping
- No sensitive data exposure in localStorage

## Troubleshooting

### Common Issues

1. **Language not switching**: Check browser console for RPC errors
2. **Flag not updating**: Ensure template uses `isArabic` getter
3. **Cross-tab sync issues**: Verify localStorage access permissions
4. **Reload failures**: Check for browser popup blockers

### Debug Mode

The component includes extensive console logging for debugging:

```javascript
console.log('=== Language Toggle Started ===');
console.log('Current state:', { /* state info */ });
```

## Contributing

1. Follow Odoo coding standards
2. Add comprehensive error handling
3. Include console logging for debugging
4. Update documentation for new features
5. Test across different browsers and scenarios

## License

This component is part of the AMS Base module and follows the same licensing terms as Odoo.