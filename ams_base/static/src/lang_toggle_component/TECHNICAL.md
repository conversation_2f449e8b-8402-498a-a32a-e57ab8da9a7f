# Language Toggle Component - Technical Documentation

## Architecture Deep Dive

### Component Lifecycle

```javascript
setup() {
    // Service initialization
    this.orm = useService('orm');
    this.actionService = useService('action');
    this.notification = useService('notification');
    
    // State management
    this.state = useState({
        currentLang: this.getCurrentLanguage(),
        isLoading: false
    });
    
    // Lifecycle hooks
    onMounted(() => {
        this.syncLanguageState();
        this.setupLanguageChangeListeners();
    });
    
    onWillDestroy(() => {
        this.cleanupLanguageChangeListeners();
    });
}
```

### Event-Driven Architecture

#### 1. MutationObserver
Monitors changes to the document's `lang` attribute:

```javascript
this.documentObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
            this.handleLanguageChange();
        }
    });
});
```

#### 2. Storage Events
Listens for cross-tab language changes:

```javascript
this.storageListener = (event) => {
    if (event.key === 'odoo_user_lang') {
        this.handleLanguageChange();
    }
};
window.addEventListener('storage', this.storageListener);
```

#### 3. Custom Events
Responds to application-wide language change events:

```javascript
this.languageChangeListener = (event) => {
    if (event.detail && event.detail.newLanguage) {
        this.handleLanguageChange();
    }
};
document.addEventListener('odoo:languageChanged', this.languageChangeListener);
```

### Language Detection Algorithm

```javascript
getCurrentLanguage() {
    let currentLang = null;
    
    // Priority 1: Session user context
    if (session.user_context?.lang) {
        currentLang = session.user_context.lang;
    }
    // Priority 2: User object
    else if (user.lang) {
        currentLang = user.lang;
    }
    // Priority 3: Global session info
    else if (window.odoo?.__session_info__?.user_context?.lang) {
        currentLang = window.odoo.__session_info__.user_context.lang;
    }
    // Priority 4: LocalStorage
    else {
        try {
            currentLang = localStorage.getItem('odoo_user_lang');
        } catch (e) {
            console.warn('Could not access localStorage:', e);
        }
    }
    
    // Priority 5: Document language or default
    if (!currentLang) {
        currentLang = document.documentElement.lang || 'en_US';
    }
    
    // Normalize Arabic language codes
    if (currentLang?.toLowerCase().startsWith('ar')) {
        currentLang = currentLang.includes('-') ? currentLang : currentLang.replace('_', '-');
    }
    
    return currentLang;
}
```

### Dynamic Language Switching

#### Success Rate Calculation

```javascript
async tryDynamicLanguageSwitch(targetLang) {
    let successCount = 0;
    let totalAttempts = 0;
    
    // Attempt 1: Document direction
    try {
        const isRTL = targetLang.startsWith('ar');
        document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
        document.body.classList.toggle('o_rtl', isRTL);
        successCount++;
    } catch (error) {
        console.warn('Failed to update document direction:', error);
    }
    totalAttempts++;
    
    // Attempt 2: Translation reload
    try {
        if (window.odoo?.__session_info__?.cache_hashes) {
            const translationsUrl = `/web/webclient/translations/${window.odoo.__session_info__.cache_hashes.translations}?lang=${targetLang}`;
            const response = await fetch(translationsUrl);
            if (response.ok) successCount++;
        }
    } catch (error) {
        console.warn('Failed to reload translations:', error);
    }
    totalAttempts++;
    
    // ... more attempts
    
    const successRate = successCount / totalAttempts;
    return successRate >= 0.5; // 50% success threshold
}
```

### Reload Strategies

#### Multiple Fallback Methods

```javascript
forcePageReload() {
    // Timeout detection
    const reloadTimeout = setTimeout(() => {
        this.notification.add(
            'Page reload is taking longer than expected. Please manually refresh the page.',
            { type: 'warning', sticky: true }
        );
    }, 5000);
    
    // Strategy 1: Standard reload
    try {
        if (window.location?.reload) {
            window.location.reload(true);
            return;
        }
    } catch (error) {
        console.warn('window.location.reload() failed:', error);
    }
    
    // Strategy 2: URL redirect with timestamp
    try {
        if (window.location?.href) {
            const currentUrl = window.location.href;
            const separator = currentUrl.includes('?') ? '&' : '?';
            window.location.href = currentUrl + separator + '_reload=' + Date.now();
            return;
        }
    } catch (error) {
        console.warn('window.location.href redirect failed:', error);
    }
    
    // Strategy 3: Replace method
    try {
        if (window.location?.replace) {
            const currentUrl = window.location.href;
            const separator = currentUrl.includes('?') ? '&' : '?';
            window.location.replace(currentUrl + separator + '_reload=' + Date.now());
            return;
        }
    } catch (error) {
        console.warn('window.location.replace() failed:', error);
    }
    
    // Strategy 4: History API
    try {
        if (window.history?.go) {
            window.history.go(0);
            return;
        }
    } catch (error) {
        console.warn('window.history.go() failed:', error);
    }
    
    // Final fallback: User notification
    clearTimeout(reloadTimeout);
    this.notification.add(
        'Language changed successfully, but automatic page refresh failed. Please manually refresh the page (F5 or Ctrl+R) to see the changes.',
        { type: 'warning', sticky: true, title: 'Manual Refresh Required' }
    );
}
```

### Error Handling Patterns

#### Safe Property Assignment

```javascript
updateLanguageReferences(targetLang) {
    // Safe user.lang assignment
    try {
        if (user && typeof user.lang !== 'undefined' && Object.getOwnPropertyDescriptor(user, 'lang')?.set) {
            user.lang = targetLang;
        } else {
            console.warn('user.lang is read-only, skipping direct assignment');
        }
    } catch (error) {
        console.warn('Could not update user.lang:', error.message);
    }
    
    // Update other references
    if (session.user_context) {
        session.user_context.lang = targetLang;
    }
    
    if (window.odoo?.__session_info__?.user_context) {
        window.odoo.__session_info__.user_context.lang = targetLang;
    }
    
    this.state.currentLang = targetLang;
    document.documentElement.lang = targetLang;
}
```

### Performance Optimizations

#### Event Listener Cleanup

```javascript
cleanupLanguageChangeListeners() {
    if (this.documentObserver) {
        this.documentObserver.disconnect();
    }
    if (this.storageListener) {
        window.removeEventListener('storage', this.storageListener);
    }
    if (this.languageChangeListener) {
        document.removeEventListener('odoo:languageChanged', this.languageChangeListener);
    }
}
```

#### Reactive State Management

```javascript
get isArabic() {
    const lang = this.state.currentLang.toLowerCase();
    return lang.startsWith('ar_') || lang.startsWith('ar-');
}

get displayText() {
    return this.isArabic ? 'English' : 'العربية';
}
```

### Template Integration

#### Reactive Flag Display

```xml
<img class="o_flag_icon" 
     t-att-src="isArabic ? '/ams_base/static/src/svg/us.svg' : '/ams_base/static/src/svg/sa.svg'"
     width="20" height="14" alt="Flag"/>
<span class="o_language_text" t-esc="displayText"/>
```

### CSS Architecture

#### Responsive Design

```css
.o_language_toggle_btn {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: none;
    color: #ffffff;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
    white-space: nowrap;
    cursor: pointer;
    min-height: 40px;
}

.o_language_content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.o_flag_icon {
    flex-shrink: 0;
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}
```

#### RTL Support

```css
[dir="rtl"] .o_language_content {
    flex-direction: row-reverse;
}

[dir="rtl"] .o_language_text {
    margin-right: 0;
    margin-left: 8px;
}
```

### Testing Considerations

#### Unit Test Structure

```javascript
// Test language detection priority
test('getCurrentLanguage respects priority order', () => {
    // Mock different language sources
    // Assert correct priority is followed
});

// Test reactive updates
test('isArabic getter updates reactively', () => {
    // Change state.currentLang
    // Assert isArabic updates correctly
});

// Test error handling
test('handles RPC failures gracefully', () => {
    // Mock RPC failure
    // Assert fallback behavior
});
```

### Debugging Tools

#### Console Logging

```javascript
console.log('=== Language Toggle Started ===');
console.log('Current state:', {
    currentLang: this.state.currentLang,
    isArabic: this.isArabic,
    userLang: user.lang,
    sessionLang: session.user_context?.lang,
    documentLang: document.documentElement.lang
});
```

#### Success Rate Monitoring

```javascript
const successRate = successCount / totalAttempts;
console.log(`Dynamic switching success rate: ${successCount}/${totalAttempts} (${Math.round(successRate * 100)}%)`);
```

### Security Considerations

1. **Input Validation**: Language codes are validated before processing
2. **XSS Prevention**: All user inputs are properly escaped
3. **Property Safety**: Safe property assignment with descriptor checking
4. **Storage Security**: No sensitive data stored in localStorage

### Browser Compatibility Matrix

| Feature | Chrome | Firefox | Safari | Edge |
|---------|--------|---------|--------|---------|
| MutationObserver | ✅ | ✅ | ✅ | ✅ |
| Custom Events | ✅ | ✅ | ✅ | ✅ |
| LocalStorage | ✅ | ✅ | ✅ | ✅ |
| Fetch API | ✅ | ✅ | ✅ | ✅ |
| ES6+ Features | ✅ | ✅ | ✅ | ✅ |

### Future Enhancements

1. **Multi-language Support**: Extend beyond Arabic/English
2. **Language Preferences**: User-specific language preferences
3. **Accessibility**: Enhanced ARIA support
4. **Performance**: Virtual DOM optimizations
5. **Testing**: Automated browser testing