/* Language Toggle Systray Component Styles */
.o_language_toggle_systray {
    position: relative;
    display: inline-block;
}

.o_language_toggle_btn {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: none;
    color: #ffffff;
    text-decoration: none;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
    white-space: nowrap;
    cursor: pointer;
    min-height: 40px;

}

.o_language_toggle_btn:hover,
.o_language_toggle_btn:focus {
}

.o_language_toggle_btn:active {
    transform: translateY(0);
    background: rgba(255, 255, 255, 0.1);
}

.o_language_toggle_btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.o_language_content {
    display: flex;
    align-items: center;
    gap: 4px;
}

.o_flag_icon {
    width: 30px;
    height: 22px;
}

.o_language_toggle_btn:hover .o_flag_icon {
    
}

.o_language_text {
    font-weight: inherit;
    font-size: 13px;
    color: inherit;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Loading State */
.o_language_toggle_systray.o_loading .o_language_toggle_btn {
    opacity: 0.7;
    cursor: wait;
}

/* RTL Support */
[dir="rtl"] .o_language_content {
    flex-direction: row-reverse;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .o_language_toggle_btn:hover,
    .o_language_toggle_btn:focus {
        background-color: rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }
    
    .o_flag_icon {
        box-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .o_language_toggle_btn {
        padding: 6px 8px;
    }
    
    .o_language_content {
        gap: 6px;
    }
    
    .o_flag_icon {
        width: 16px;
        height: 11px;
    }
    
    .o_language_text {
        font-size: 12px;
    }
}

/* Animation for language switch */
@keyframes languageSwitch {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(0.95); }
    100% { opacity: 1; transform: scale(1); }
}

.o_language_toggle_systray.o_loading .o_language_content {
    animation: languageSwitch 0.6s ease-in-out;
}