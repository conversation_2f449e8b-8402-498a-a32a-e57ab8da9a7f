/** @odoo-module **/

import { Component, useState, onMounted, onWill<PERSON><PERSON>roy } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { user } from "@web/core/user";
import { session } from "@web/session";
import { rpc } from "@web/core/network/rpc";

export class LanguageToggleSystray extends Component {
    static template = "ams_base.LanguageToggleSystray";
    static props = {};

    setup() {
        this.orm = useService('orm');
        this.actionService = useService('action');
        this.notification = useService('notification');
        
        this.state = useState({
            currentLang: this.getCurrentLanguage(),
            isLoading: false
        });
        
        // Initialize language state on mount
        onMounted(() => {
            this.syncLanguageState();
            this.setupLanguageChangeListeners();
        });
        
        // Clean up listeners on destroy
        onWillDestroy(() => {
            this.cleanupLanguageChangeListeners();
        });
    }
    
    getCurrentLanguage() {
        // Priority order: session user_context > user object > session info > localStorage > document lang > default
        let currentLang = null;
        
        if (session.user_context && session.user_context.lang) {
            currentLang = session.user_context.lang;
        } else if (user.lang) {
            currentLang = user.lang;
        } else if (window.odoo && window.odoo.__session_info__ && window.odoo.__session_info__.user_context && window.odoo.__session_info__.user_context.lang) {
            currentLang = window.odoo.__session_info__.user_context.lang;
        } else {
            // Check localStorage for cross-tab synchronization
            try {
                const storedLang = localStorage.getItem('odoo_user_lang');
                if (storedLang) {
                    currentLang = storedLang;
                }
            } catch (e) {
                // Could not access localStorage
            }
        }
        
        // Fallback to document language or default
        if (!currentLang) {
            currentLang = document.documentElement.lang || 'en_US';
        }
        
        // Normalize language code format (handle both ar_001 and ar-001)
        if (currentLang && currentLang.toLowerCase().startsWith('ar')) {
            // Ensure consistent format for Arabic
            currentLang = currentLang.includes('-') ? currentLang : currentLang.replace('_', '-');
        }
        
        return currentLang;
    }
    
    setupLanguageChangeListeners() {
        // Listen for language changes in the user object
        this.originalUserLang = user.lang;
        
        // Create a mutation observer to watch for document language changes
        this.documentObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
                    this.handleLanguageChange();
                }
            });
        });
        
        this.documentObserver.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['lang']
        });
        
        // Listen for storage events (in case language is changed in another tab)
        this.storageListener = (event) => {
            if (event.key === 'odoo_user_lang') {
                this.handleLanguageChange();
            }
        };
        window.addEventListener('storage', this.storageListener);
        
        // Listen for custom language change events
        this.languageChangeListener = (event) => {
            if (event.detail && event.detail.newLanguage) {
                this.handleLanguageChange();
            }
        };
        document.addEventListener('odoo:languageChanged', this.languageChangeListener);
    }
    
    cleanupLanguageChangeListeners() {
        if (this.documentObserver) {
            this.documentObserver.disconnect();
        }
        if (this.storageListener) {
            window.removeEventListener('storage', this.storageListener);
        }
        if (this.languageChangeListener) {
            document.removeEventListener('odoo:languageChanged', this.languageChangeListener);
        }
    }
    
    handleLanguageChange() {
        const newLang = this.getCurrentLanguage();
        if (this.state.currentLang !== newLang) {
            this.state.currentLang = newLang;
        }
    }
    
    syncLanguageState() {
        // Ensure the component state reflects the actual current language
        const currentLang = this.getCurrentLanguage();
        if (this.state.currentLang !== currentLang) {
            this.state.currentLang = currentLang;
        }
    }

    get isArabic() {
        // Handle both ar_001 and ar-001 formats
        const lang = this.state.currentLang.toLowerCase();
        return lang.startsWith('ar_') || lang.startsWith('ar-');
    }

    get displayText() {
        // If current language is Arabic (ar_001 or ar-001), show English text
        // If current language is English, show Arabic text
        return this.isArabic ? 'English' : 'العربية';
    }

    async toggleLanguage() {
        if (this.state.isLoading) {
            return;
        }
        
        this.state.isLoading = true;
        
        try {
            // Determine target language based on current language
            const targetLang = this.isArabic ? 'en_US' : 'ar_001';
            
            // Call the controller to switch language
            const result = await rpc('/ams_base/switch_language', {
                lang_code: targetLang
            });
            
            if (result && result.success) {
                // Update all language references immediately
                this.updateLanguageReferences(targetLang);
                
                // Emit language change event
                this.emitLanguageChangeEvent(targetLang);
                
                // Try dynamic language switching without reload
                if (await this.tryDynamicLanguageSwitch(targetLang)) {
                    this.notification.add('Language switched successfully', { type: 'success' });
                    this.forcePageReload();
                } else {
                    // Fallback to page reload if dynamic switching fails
                    this.forcePageReload();
                }
            } else {
                // Fallback to direct ORM call
                await this.orm.write('res.users', [user.userId], {
                    lang: targetLang
                });
                
                // Update all language references immediately
                this.updateLanguageReferences(targetLang);
                
                // Emit language change event
                this.emitLanguageChangeEvent(targetLang);
                
                // Try dynamic language switching without reload
                if (await this.tryDynamicLanguageSwitch(targetLang)) {
                    this.notification.add('Language switched successfully', { type: 'success' });
                } else {
                    // Fallback to page reload if dynamic switching fails
                    this.forcePageReload();
                }
            }
            
        } catch (error) {
            // Try fallback approach
            try {
                const targetLang = this.isArabic ? 'en_US' : 'ar_001';
                await this.orm.write('res.users', [user.userId], {
                    lang: targetLang
                });
                
                // Update all language references immediately
                this.updateLanguageReferences(targetLang);
                
                // Emit language change event
                this.emitLanguageChangeEvent(targetLang);
                
                // Fallback to page reload
                this.forcePageReload();
            } catch (fallbackError) {
                // Fallback language switch also failed
            }
        } finally {
            this.state.isLoading = false;
        }
    }
    
    async tryDynamicLanguageSwitch(targetLang) {
        
        try {
            let successCount = 0;
            let totalAttempts = 0;
            
            // Update document direction for RTL/LTR
            try {
                const isRTL = targetLang.startsWith('ar');
                document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
                document.body.classList.toggle('o_rtl', isRTL);
                successCount++;
            } catch (error) {
                // Failed to update document direction
            }
            totalAttempts++;
            
            // Try to reload translations dynamically
            try {
                if (window.odoo && window.odoo.__session_info__ && window.odoo.__session_info__.cache_hashes) {
                    const translationsUrl = `/web/webclient/translations/${window.odoo.__session_info__.cache_hashes.translations}?lang=${targetLang}`;
                    const response = await fetch(translationsUrl);
                    if (response.ok) {
                        successCount++;
                    }
                }
            } catch (error) {
                // Failed to reload translations
            }
            totalAttempts++;
            
            // Try to reload menus with new language
            try {
                if (window.odoo && window.odoo.reloadMenus) {
                    await window.odoo.reloadMenus();
                    successCount++;
                }
            } catch (error) {
                // Failed to reload menus
            }
            totalAttempts++;
            
            // Try to trigger a global re-render
            try {
                // Dispatch a custom event that other components can listen to
                const rerenderEvent = new CustomEvent('odoo:forceRerender', {
                    detail: { language: targetLang },
                    bubbles: true
                });
                document.dispatchEvent(rerenderEvent);
                successCount++;
            } catch (error) {
                // Failed to dispatch re-render event
            }
            totalAttempts++;
            
            // Try to refresh the current action/view
            try {
                if (this.actionService && this.actionService.doAction) {
                    const currentAction = this.actionService.currentController?.action;
                    if (currentAction) {
                        // Refresh current view without changing the action
                        await this.actionService.doAction(currentAction, {
                            clearBreadcrumbs: false,
                            stackPosition: 'replaceCurrentAction'
                        });
                        successCount++;
                    }
                }
            } catch (error) {
                // Failed to refresh current action
            }
            totalAttempts++;
            
            // Consider dynamic switching successful if at least half of the operations succeeded
            const successRate = successCount / totalAttempts;
            
            if (successRate >= 0.5) {
                return true;
            } else {
                return false;
            }
            
        } catch (error) {
            return false;
        }
    }
    
    emitLanguageChangeEvent(targetLang) {
        // Emit custom event for other components to listen to
        const languageChangeEvent = new CustomEvent('odoo:languageChanged', {
            detail: { 
                newLanguage: targetLang,
                previousLanguage: this.state.currentLang 
            },
            bubbles: true
        });
        document.dispatchEvent(languageChangeEvent);
        
        // Store language preference in localStorage for cross-tab sync
        try {
            localStorage.setItem('odoo_user_lang', targetLang);
        } catch (e) {
            // Could not store language preference
        }
    }
    
    forcePageReload() {
        
        // Set a timeout to detect if reload is stuck
        const reloadTimeout = setTimeout(() => {
            this.notification.add(
                'Page reload is taking longer than expected. Please manually refresh the page.',
                { 
                    type: 'warning',
                    sticky: true
                }
            );
        }, 5000); // 5 second timeout
        
        // Try multiple reload methods in order of preference
        try {
            // Method 1: Standard reload
            if (window.location && window.location.reload) {
                window.location.reload(true); // Force reload from server
                return;
            }
        } catch (error) {
            // window.location.reload() failed
        }
        
        try {
            // Method 2: Redirect to current URL
            if (window.location && window.location.href) {
                const currentUrl = window.location.href;
                // Add timestamp to force reload
                const separator = currentUrl.includes('?') ? '&' : '?';
                window.location.href = currentUrl + separator + '_reload=' + Date.now();
                return;
            }
        } catch (error) {
            // window.location.href redirect failed
        }
        
        try {
            // Method 3: Use replace method
            if (window.location && window.location.replace) {
                const currentUrl = window.location.href;
                // Add timestamp to force reload
                const separator = currentUrl.includes('?') ? '&' : '?';
                window.location.replace(currentUrl + separator + '_reload=' + Date.now());
                return;
            }
        } catch (error) {
            // window.location.replace() failed
        }
        
        try {
            // Method 4: Use history API to navigate
            if (window.history && window.history.go) {
                window.history.go(0);
                return;
            }
        } catch (error) {
            // window.history.go() failed
        }
        
        // Clear the timeout since we're showing the manual message
        clearTimeout(reloadTimeout);
        
        // Method 5: Last resort - show manual reload message
        this.notification.add(
            'Language changed successfully, but automatic page refresh failed. Please manually refresh the page (F5 or Ctrl+R) to see the changes.',
            { 
                type: 'warning',
                sticky: true,
                title: 'Manual Refresh Required'
            }
        );
        
        // Also try to show a browser alert as additional fallback
        try {
            setTimeout(() => {
                if (confirm('Language changed successfully. The page needs to be refreshed to apply changes. Click OK to refresh now.')) {
                    // Try one more time with a simple reload
                    window.location.reload();
                }
            }, 1000);
        } catch (error) {
            // Could not show confirmation dialog
        }
    }
    
    updateLanguageReferences(targetLang) {
        // Try to update user object safely (avoid read-only property error)
        try {
            if (user && typeof user.lang !== 'undefined' && Object.getOwnPropertyDescriptor(user, 'lang')?.set) {
                user.lang = targetLang;
            }
        } catch (error) {
            // Could not update user.lang
        }
        
        // Update session user_context
        if (session.user_context) {
            session.user_context.lang = targetLang;
        }
        
        // Update global session info
        if (window.odoo && window.odoo.__session_info__ && window.odoo.__session_info__.user_context) {
            window.odoo.__session_info__.user_context.lang = targetLang;
        }
        
        // Update component state
        this.state.currentLang = targetLang;
        
        // Update document language attribute
        document.documentElement.lang = targetLang;
    }
}

// Register the component in the systray
registry.category("systray").add(
    "ams_base.LanguageToggleSystray",
    {
        Component: LanguageToggleSystray,
    },
    { sequence: 100 } // Position it at the end of the systray to avoid hiding messages
);