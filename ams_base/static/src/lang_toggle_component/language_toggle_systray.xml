<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="ams_base.LanguageToggleSystray">
        <div class="o_language_toggle_systray" 
             t-att-class="{ 'o_loading': state.isLoading }">
            <button class="btn btn-link o_language_toggle_btn"
                    type="button"
                    t-on-click="toggleLanguage"
                    t-att-disabled="state.isLoading"
                    title="Switch Language">
                <div class="o_language_content">
                    <img class="o_flag_icon" 
                         t-att-src="isArabic ? '/ams_base/static/src/lang_toggle_component/us.svg' : '/ams_base/static/src/lang_toggle_component/sa.svg'"
                          alt="Flag"/>
                    <span class="o_language_text" t-esc="displayText"/>
                </div>
                <i t-if="state.isLoading" class="fa fa-spinner fa-spin ms-2" aria-hidden="true"/>
            </button>
        </div>
    </t>
</templates>