<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: api_model_view_form-->
    <record id="api_model_view_form" model="ir.ui.view">
        <field name="name">ams_base.api_model.form</field>
        <field name="model">ams_base.api_model</field>
        <field name="arch" type="xml">
            <form>
                <header>
                </header>
                <sheet>
                    <div class="alert alert-danger" role="alert" invisible="not error">
                        <strong>Error! </strong> <field name="error_msg" readonly="1"/>
                        <button name="action_reset_error" icon="fa-close" type="object" class="btn btn-link mx-5" title="Close" />
                    </div>
                    <group>
                        <group name="group1">
                            <field name="name"/>
                            <field name="description"/>
                        </group>
                        <group name="group2">
                            <field name="synced"/>
                            <field name="last_sync_date"/>
                            <field name="need_to_sync"/>

                        </group>
                    </group>
                    <notebook>
                        <page name="basic_info" string="Basic Info">
                            <group name="basic_info_main_group">
                                <group name="basic_info_col1">

                                </group>
                                <group name="basic_info_col2">
                                </group>
                            </group>
                            <group name="basic_info_full_width">

                            </group>
                        </page>
                        <page name="more_info" string="More Info" invisible="1">
                            <group>
                                <group>
                                </group>

                                <group>
                                </group>
                            </group>
                        </page>
                        <page name="technical" string="Technical Info" groups="base.group_no_one">
                            <group name="row1">
                                <group name="technical_col1">
                                    <field name="record_id"/>
                                    <field name="is_predefined"/>

                                </group>
                                <group name="technical_col2">
                                    <field name="synced" readonly="0"/>

                                </group>
                            </group>
                        </page>
                        <page name="handle_error" string="Error Message" invisible="not error"  >
                            <group>
                                <field name="error"/>
                                <field name="error_msg" class="text-danger"/>
                                <field name="response_code"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>

            </form>
        </field>
    </record>

    <!--TODO[IMP]: api_model_view_list-->
    <record id="api_model_view_list" model="ir.ui.view">
        <field name="name">ams_base.api_model.tree</field>
        <field name="model">ams_base.api_model</field>
        <field name="arch" type="xml">

            <list string="Api Model">
                 <header>
                     <button name="action_unsync"   string="Unsync"    type="object"
                             class="btn btn-outline-danger" title="Unsync From Api" groups="base.group_no_one"  />
                </header>
                <field name="name" decoration-danger="need_to_sync == True"
                  decoration-success="need_to_sync == False"/>
                <field name="description" optional="hide"/>
                <field name="record_id" optional="hide"/>
                <field name="synced" optional="hide"/>
                <field name="need_to_sync"/>
                <field name="last_sync_date" optional="hide"/>
                <field name="error" optional="hide"/>
                <field name="response_code" optional="hide"/>
                <field name="company_id" optional="hide"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: api_model_view_search-->
    <record id="api_model_view_search" model="ir.ui.view">
        <field name="name">ams_base.api_model.search</field>
        <field name="model">ams_base.api_model</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="description"/>
                <field name="record_id"/>
                <field name="synced"/>
                <field name="last_sync_date" filter_domain="[(last_sync_date,'=',self)]"/>
                <field name="error"/>
                <filter string="Need to Sync" name="need_to_sync" domain="[('need_to_sync', '=', True)]"/>
                <group expand="1" string="Group By">
                    <!--<filter string="Postcode" name='postcode' context="{'group_by':'postcode'}"/>-->

                </group>
            </search>
        </field>
    </record>

<!--    Create server action to call action_unsync()-->
<!--    <record model="ir.actions.server" id="action_server_unsync">-->
<!--        <field name="name">Unsync</field>-->
<!--        <field name="model_id" ref="model_ams_base_api_model"/>-->
<!--        <field name="state">code</field>-->
<!--        <field name="groups_id" eval="[(4, ref('base.group_no_one'))]"/>-->
<!--        <field name="code">-->
<!--            records.sudo().action_unsync()-->
<!--        </field>-->
<!--    </record>-->


</odoo>
