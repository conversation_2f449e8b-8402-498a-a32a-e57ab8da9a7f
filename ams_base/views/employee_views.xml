<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: employee_view_form-->
    <record id="ams_employee_view_form" model="ir.ui.view">
        <field name="name">Employee Access Management Form</field>
        <field name="model">hr.employee</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <form string="Employee Access Management">
                <header>
                </header>
                <sheet>
                    <div class="oe_title mw-75 ps-0 pe-2">
                        <h1 class="d-flex flex-row align-items-center">
                            <div invisible="not user_id" class="me-2">
                                <widget name="hr_employee_chat" invisible="not context.get('chat_icon')"/>
                            </div>
                            <field name="name" placeholder="Employee's Name" required="True"
                                   style="font-size: min(4vw, 2.6rem);"/>
                        </h1>

                    </div>
                    <group>
                        <group>
                            <!--                            <field name="name" string="Employee Name" placeholder="e.g. <PERSON>" required="1"/>-->
                            <field name="work_email" widget="email" placeholder="e.g. <EMAIL>"/>
                            <field name="work_phone" widget="phone"/>
                            <field name="mobile_phone" widget="phone"/>
                            <field name="category_ids" widget="many2many_tags"
                                   options="{'color_field': 'color', 'no_create_edit': True}" placeholder="Tags"
                                   groups="hr.group_hr_user"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="company_country_id" invisible="1"/>
                            <field name="company_country_code" invisible="1"/>
                        </group>
                        <group>
                            <field name="department_id"/>
                            <field name="job_id"
                                   context="{'default_no_of_recruitment': 0, 'default_is_favorite': False}"
                                   placeholder="e.g. Sales Manager"/>
                            <field name="parent_id" widget="many2one_avatar_user"/>
                            <field name="coach_id" widget="many2one_avatar_user"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="access_management" string="Access Management">
                            <group>
                                <group name="access_col1" >
                                    <field name="follow_up_level" widget="selection"/>
                                    <field name="user_id" string="Related User"/>
                                    <field name="employee_number" placeholder="e.g. 12345"/>
                                    <field name="enroll_number" placeholder="Device Enroll Number"/>
                                </group>
                                <group name="access_col2">

                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: employee_view_list-->
    <record id="ams_employee_view_list" model="ir.actions.act_window">
        <field name="name">Employees</field>
        <field name="res_model">hr.employee</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!--TODO[IMP]: employee_tree_inherit-->
    <record id="ams_employee_tree_inherit" model="ir.ui.view">
        <field name="name">hr.employee.tree.inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="action_reset_host_work_phone" 
                        string="Reset Host Work Phone" 
                        type="object" 
                        groups="base.group_no_one"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: employee_action-->
    <record id="employee_action" model="ir.actions.act_window">
        <field name="name">Employee</field>
        <field name="res_model">hr.employee</field>
        <field name="view_mode">form</field>
        <!--<field name="view_id" ref="employee_access_management_view_form"/>-->
        <field name="context">{'search_default_available': 1,'apply_followup_domain':1}</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'list', 'view_id': ref('hr.view_employee_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('ams_employee_view_form')})]"/>
        <field name="target">current</field>
    </record>
</odoo>