<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: res_user_view_form-->
    <record id="hr_department_inherit_view_form" model="ir.ui.view">
        <field name="name">hr.department.inherit.form</field>
        <field name="model">hr.department</field>
         <field name="priority" eval="10"/>
        <field name="inherit_id" ref="hr.view_department_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form/sheet" position="before">
                <header>
                    <button name="compute_all_dept_depths" type="object" string="Compute Dept Depths" class="btn-primary" groups="base.group_no_one"/>
                </header>
            </xpath>
            <xpath expr="//field[@name='color']" position="after">
                <field name="dept_depth" optional="hide" groups="base.group_no_one"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: res_user_view_list-->
      <record id="hr_department_inherit_view_list" model="ir.ui.view">
        <field name="name">hr.department.inherit.tree</field>
        <field name="model">hr.department</field>
         <field name="priority" eval="10"/>
        <field name="inherit_id" ref="hr.view_department_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//list" position="inside">
                <header>
                    <button name="compute_all_dept_depths" type="object" string="Compute Dept Depths" class="btn-primary" groups="base.group_no_one"/>
                </header>
            </xpath>
            <xpath expr="//field[@name='color']" position="after">
                     <field name="dept_depth" groups="base.group_no_one"/>
            </xpath>
        </field>
    </record>

<!--&lt;!&ndash;    &lt;!&ndash;TODO[IMP]: res_user_view_search&ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;    <record id="res_user_inherit_view_search" model="ir.ui.view">&ndash;&gt;-->
<!--&lt;!&ndash;        <field name="name">res.users.inherit.search</field>&ndash;&gt;-->
<!--&lt;!&ndash;        <field name="model">res.users</field>&ndash;&gt;-->
<!--&lt;!&ndash;       <field name="priority" eval="10"/>&ndash;&gt;-->
<!--&lt;!&ndash;        <field name="inherit_id" ref="base.view_users_search"/>&ndash;&gt;-->
<!--&lt;!&ndash;        <field name="arch" type="xml">&ndash;&gt;-->
<!--&lt;!&ndash;            <xpath expr="//field[@name='name']" position="after">&ndash;&gt;-->
<!--&lt;!&ndash;                <group>&ndash;&gt;-->
<!--&lt;!&ndash;                     <field name="level"/>&ndash;&gt;-->
<!--&lt;!&ndash;                </group>&ndash;&gt;-->
<!--&lt;!&ndash;            </xpath>&ndash;&gt;-->
<!--&lt;!&ndash;        </field>&ndash;&gt;-->
<!--&lt;!&ndash;    </record>&ndash;&gt;-->

</odoo>
