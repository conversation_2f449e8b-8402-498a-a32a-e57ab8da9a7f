<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: res_user_view_form-->
    <record id="res_user_inherit_view_form" model="ir.ui.view">
        <field name="name">res.users.inherit.form</field>
        <field name="model">res.users</field>
         <field name="priority" eval="10"/>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="Access Management">
                    <group>
                     <field name="level"/>
                </group>
                </page>
            </xpath>
        </field>
    </record>

<!--    &lt;!&ndash;TODO[IMP]: res_user_view_list&ndash;&gt;-->
<!--    <record id="res_user_inherit_view_list" model="ir.ui.view">-->
<!--        <field name="name">res.users.inherit.list</field>-->
<!--        <field name="model">res.users</field>-->
<!--       <field name="priority" eval="10"/>-->
<!--        <field name="inherit_id" ref="base.view_users_tree"/>-->
<!--        <field name="arch" type="xml">-->
<!--            <xpath expr="//field[@name='name']" position="after">-->
<!--                <group>-->
<!--                     <field name="level"/>-->
<!--                </group>-->
<!--            </xpath>-->
<!--        </field>-->
<!--    </record>-->

<!--    &lt;!&ndash;TODO[IMP]: res_user_view_search&ndash;&gt;-->
<!--    <record id="res_user_inherit_view_search" model="ir.ui.view">-->
<!--        <field name="name">res.users.inherit.search</field>-->
<!--        <field name="model">res.users</field>-->
<!--       <field name="priority" eval="10"/>-->
<!--        <field name="inherit_id" ref="base.view_users_search"/>-->
<!--        <field name="arch" type="xml">-->
<!--            <xpath expr="//field[@name='name']" position="after">-->
<!--                <group>-->
<!--                     <field name="level"/>-->
<!--                </group>-->
<!--            </xpath>-->
<!--        </field>-->
<!--    </record>-->

</odoo>
