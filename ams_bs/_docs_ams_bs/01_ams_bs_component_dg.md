# AMS BioStar Component Diagram

## Introduction

This document provides an overview of the component diagram for the BioStar integration module within the Access Management System.

- **ams_base**:
  This module serves as a shared foundation among all custom modules. All modules are required to depend on `ams_base`. The core model within this module is `ams_base.abstract_model`, which serves as the base model for all other models across the modules.

- **ams** (Core Access Management System):
  This is the core module for the Access Management System. It builds upon the base module and provides the central functionality that other specialized modules depend on.

- **ams_bs** (BioStar Integration):
  Within the `ams_bs` module, the focus lies on integration with the BioStar 2 access control system. It encapsulates the business logic required for user management, device management, access control, and event logging through the BioStar 2 API.

## Component Diagram

This diagram represents modules and dependencies:

```mermaid
graph BT;
    ams_base[ams_base <br>Base Module]
    ams[ams Core <br> Access Management System]
    ams_bs[ams_bs <br> BioStar Integration]
    hr[Employees Management]
  
    ams_base --> hr
    ams --> ams_base
    ams_bs --> ams
```

## Module Structure

The `ams_bs` module is organized into several key components:

```mermaid
graph TB;
    subgraph ams_bs[ams_bs Module]
        api[API Integration Layer]
        models[Data Models]
        views[User Interface]
        security[Security & Access Control]
    end
    
    subgraph api[API Integration Layer]
        base_client[Base API Client]
        ac_client[Access Control API]
        user_client[User Management API]
        device_client[Device Management API]
        dto[Data Transfer Objects]
    end
    
    subgraph models[Data Models]
        user_models[User Management]
        device_models[Device Management]
        ac_models[Access Control]
        event_models[Event Logging]
    end
    
    api --> models
    models --> views
    security --> models
```

## Integration Architecture

The BioStar integration connects the Odoo-based AMS system with the BioStar 2 access control system:

```mermaid
graph LR;
    subgraph AMS[AMS System]
        ams_bs[ams_bs Module]
    end
    
    subgraph BioStar[BioStar 2 System]
        bs_api[BioStar API]
        bs_db[BioStar Database]
        bs_devices[Physical Devices]
    end
    
    ams_bs <-->|API Calls| bs_api
    bs_api <--> bs_db
    bs_api <--> bs_devices
```

## Key Components

### API Integration Layer

The API integration layer handles all communication with the BioStar 2 system through its REST API. It includes:

- Base API client for authentication and common functionality
- Specialized clients for access control, user management, and device operations
- Data transfer objects (DTOs) for structured data exchange

### Data Models

The data models represent the core business objects in the system:

- User management models (users, user groups)
- Device management models (devices, device groups)
- Access control models (access groups, access levels, doors, schedules)
- Event logging models

### User Interface

The user interface provides views and menus for managing:

- Users and credentials
- Devices and device groups
- Access control configuration
- Event logs and monitoring

### Security & Access Control

The security layer manages:

- Role-based access control within the module
- API authentication and secure communication
- Data validation and integrity
