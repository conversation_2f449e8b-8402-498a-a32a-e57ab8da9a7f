# AMS BioStar Integration Overview

## Introduction

The AMS BioStar Integration module (`ams_bs`) provides a comprehensive integration between the Access Management System (AMS) and Suprema's BioStar 2 physical access control system. This integration enables centralized management of users, access permissions, devices, and events across both systems, creating a unified platform for physical access control.

## System Purpose

The primary purpose of the AMS BioStar Integration is to:

1. Provide seamless management of physical access control through the Odoo interface
2. Synchronize user data between AMS and BioStar 2
3. Configure and monitor access control devices
4. Define and enforce access policies
5. Track and analyze access events
6. Support biometric authentication methods

## Key Features

### User Management

```mermaid
graph TD;
    A[AMS User Creation] --> B[User Data Synchronization]
    B --> C[BioStar User Creation]
    C --> D[Credential Assignment]
    D --> E[Access Group Assignment]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
```

- Create and manage users in BioStar 2 system
- Synchronize user data between AMS and BioStar 2
- Assign credentials (cards, fingerprints, face recognition)
- Group users for easier management
- Assign access permissions based on user groups

### Access Control Management

```mermaid
graph TD;
    A[Define Schedules] --> B[Configure Doors]
    B --> C[Create Access Levels]
    C --> D[Define Access Groups]
    D --> E[Assign to Users/Groups]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
```

- Define time-based access schedules
- Configure physical doors and their properties
- Create access levels combining doors and schedules
- Define access groups that determine who can access what
- Assign access groups to users or user groups

### Device Management

```mermaid
graph TD;
    A[Device Registration] --> B[Device Configuration]
    B --> C[Device Grouping]
    C --> D[Device Monitoring]
    D --> E[Device Commands]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
```

- Register and configure BioStar 2 devices
- Group devices for easier management
- Monitor device status and health
- Execute device commands (open door, lock door)
- Support various device types (fingerprint readers, card readers)

### Event Logging and Monitoring

```mermaid
graph TD;
    A[Event Generation] --> B[Event Capture]
    B --> C[Event Storage]
    C --> D[Event Analysis]
    D --> E[Event Reporting]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
```

- Capture and log all access events
- Store events for historical analysis
- Filter and search events
- Generate reports on access patterns
- Monitor events in real-time

## Technical Architecture

### API Integration

The module communicates with BioStar 2 through its REST API:

```mermaid
sequenceDiagram
    participant AMS as AMS System
    participant Client as API Client
    participant BioStar as BioStar 2 API
    
    AMS->>Client: Request Operation
    Client->>BioStar: API Call with Authentication
    BioStar->>Client: Response (JSON)
    Client->>AMS: Processed Result
```

The API integration is structured into specialized clients:
- Base API client for authentication and common functionality
- Access Control API client for managing access policies
- User API client for user management
- Device API client for device operations

### Data Synchronization

```mermaid
graph LR;
    subgraph AMS[AMS System]
        ams_models[AMS Models]
    end
    
    subgraph Integration[Integration Layer]
        sync[Synchronization Logic]
    end
    
    subgraph BioStar[BioStar 2]
        bs_data[BioStar Data]
    end
    
    ams_models <-->|Create/Update| sync
    sync <-->|API Calls| bs_data
```

Data synchronization ensures that changes in AMS are reflected in BioStar 2 and vice versa, maintaining consistency between the systems.

## User Workflows

### User Enrollment

1. Create user in AMS
2. Assign user to appropriate groups
3. Enroll biometric credentials (fingerprint, face)
4. Assign access card if needed
5. Assign access groups
6. Synchronize with BioStar 2

### Access Management

1. Define schedules (when access is allowed)
2. Configure doors and their properties
3. Create access levels combining doors and schedules
4. Define access groups
5. Assign access groups to users or user groups

### Event Monitoring

1. Access events are generated at devices
2. Events are captured by BioStar 2
3. Events are synchronized to AMS
4. Events can be filtered, searched, and analyzed
5. Reports can be generated based on event data

## Integration Benefits

1. **Centralized Management**: Manage physical access control from within Odoo
2. **Unified User Management**: Single source of truth for user data
3. **Streamlined Operations**: Simplified workflows for access management
4. **Enhanced Security**: Comprehensive event logging and monitoring
5. **Scalability**: Support for multiple devices and locations
6. **Flexibility**: Customizable access policies based on business needs

## Conclusion

The AMS BioStar Integration module provides a robust solution for physical access control management within the Odoo environment. By leveraging the capabilities of BioStar 2 and providing a seamless integration, it enables organizations to efficiently manage access control while maintaining high security standards.
