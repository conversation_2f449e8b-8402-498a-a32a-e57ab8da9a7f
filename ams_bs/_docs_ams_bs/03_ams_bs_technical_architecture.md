# AMS BioStar Technical Architecture

## System Architecture

The AMS BioStar integration module is designed to provide a seamless connection between the Odoo-based Access Management System (AMS) and Suprema's BioStar 2 physical access control system. This document outlines the technical architecture of this integration.

### High-Level Architecture

```mermaid
graph TD
    subgraph Odoo Environment
        ams_base[ams_base Module]
        ams[ams Core Module]
        ams_bs[ams_bs BioStar Module]
        
        ams_base --> ams
        ams --> ams_bs
    end
    
    subgraph Integration Layer
        api_clients[API Clients]
        dto[Data Transfer Objects]
        sync_logic[Synchronization Logic]
        
        ams_bs --> api_clients
        ams_bs --> dto
        ams_bs --> sync_logic
    end
    
    subgraph BioStar Environment
        bs_api[BioStar 2 API]
        bs_server[BioStar 2 Server]
        bs_devices[BioStar Devices]
        
        bs_api --> bs_server
        bs_server --> bs_devices
    end
    
    api_clients <-->|REST API| bs_api
```

## Module Structure

The `ams_bs` module is organized into several key components:

### API Integration Layer

```mermaid
classDiagram
    class BaseBiostarApiClient {
        +base_url: String
        +session: Session
        +login()
        +logout()
        +get_default_headers()
        +get_json_response()
    }
    
    class BiostarACApiClient {
        +get_ac_doors()
        +get_ac_door_groups()
        +get_ac_access_groups()
        +get_ac_access_levels()
        +get_ac_schedules()
        +create_access_group()
        +delete_access_group()
    }
    
    class BiostarUserApiClient {
        +get_users()
        +get_user_groups()
        +create_user()
        +update_user()
        +delete_user()
        +get_user_details()
        +scan_fingerprint()
        +scan_face()
        +scan_card()
    }
    
    class BiostarDeviceApiClient {
        +get_devices()
        +get_device_groups()
        +get_events()
        +control_device()
        +get_device_status()
    }
    
    BaseBiostarApiClient <|-- BiostarACApiClient
    BaseBiostarApiClient <|-- BiostarUserApiClient
    BaseBiostarApiClient <|-- BiostarDeviceApiClient
```

The API clients handle all communication with the BioStar 2 system, providing methods for:
- Authentication and session management
- Access control operations
- User management
- Device operations
- Event retrieval

### Data Models

```mermaid
classDiagram
    class BaseAbstractModel {
        +name: Char
        +active: Boolean
        +description: Text
    }
    
    class AMSUser {
        +card_number: Char
        +device_group_id: Many2one
        +user_group_id: Many2one
        +ac_group_ids: Many2many
        +device_id: Many2one
        +card_ids: One2many
    }
    
    class Device {
        +device_group_id: Many2one
    }
    
    class AccessGroup {
        +api_type: Selection
        +ac_levels_ids: Many2many
        +device_ids: Many2many
        +group_users_ids: One2many
        +user_groups_ids: One2many
    }
    
    class AccessLevel {
        +door_ids: Many2many
        +schedule_id: Many2one
    }
    
    class Door {
        +door_group_id: Many2one
        +schedule_ids: Many2many
    }
    
    class Schedule {
        +daily_schedules: One2many
    }
    
    class EventLog {
        +event_type: Selection
        +event_time: Datetime
        +device_id: Many2one
        +user_id: Many2one
    }
    
    BaseAbstractModel <|-- AMSUser
    BaseAbstractModel <|-- Device
    BaseAbstractModel <|-- AccessGroup
    BaseAbstractModel <|-- AccessLevel
    BaseAbstractModel <|-- Door
    BaseAbstractModel <|-- Schedule
    BaseAbstractModel <|-- EventLog
```

The data models represent the core business objects in the system, providing a structured way to store and manage:
- User information and credentials
- Device configuration
- Access control policies
- Event logs

### Data Transfer Objects (DTOs)

```mermaid
classDiagram
    class BaseResponse {
        +status: Object
        +data: Object
        +is_success()
        +get_error_message()
    }
    
    class AccessGroupsResponse {
        +access_groups: List
        +parse_response()
    }
    
    class UsersResponse {
        +users: List
        +parse_response()
    }
    
    class DevicesResponse {
        +devices: List
        +parse_response()
    }
    
    class EventsResponse {
        +events: List
        +parse_response()
    }
    
    BaseResponse <|-- AccessGroupsResponse
    BaseResponse <|-- UsersResponse
    BaseResponse <|-- DevicesResponse
    BaseResponse <|-- EventsResponse
```

The DTOs handle the structured exchange of data between AMS and BioStar 2, providing:
- Response parsing and validation
- Error handling
- Data transformation

## Authentication Flow

```mermaid
sequenceDiagram
    participant AMS as AMS System
    participant Client as API Client
    participant BioStar as BioStar 2 API
    
    AMS->>Client: Request Authentication
    Client->>BioStar: Login Request (credentials)
    BioStar->>Client: Session ID
    Client->>Client: Store Session ID
    Client->>AMS: Authentication Result
    
    Note over Client,BioStar: For subsequent requests
    
    AMS->>Client: API Request
    Client->>BioStar: Request with Session ID
    BioStar->>Client: Response
    Client->>AMS: Processed Result
    
    Note over Client,BioStar: Session expiration
    
    AMS->>Client: API Request
    Client->>BioStar: Request with Session ID
    BioStar->>Client: Session Expired Error
    Client->>BioStar: Login Request (credentials)
    BioStar->>Client: New Session ID
    Client->>Client: Store New Session ID
    Client->>BioStar: Retry Request
    BioStar->>Client: Response
    Client->>AMS: Processed Result
```

## Data Synchronization

```mermaid
sequenceDiagram
    participant Odoo as Odoo UI
    participant Model as AMS Model
    participant Sync as Sync Logic
    participant API as API Client
    participant BioStar as BioStar 2
    
    Odoo->>Model: Create/Update Record
    Model->>Model: Save to Database
    Model->>Sync: Trigger Synchronization
    Sync->>API: Prepare API Request
    API->>BioStar: Send API Request
    BioStar->>API: Response
    API->>Sync: Process Response
    Sync->>Model: Update Sync Status
    Model->>Odoo: Return Result
```

## Error Handling

```mermaid
graph TD
    A[API Request] --> B{Response Status}
    B -->|Success| C[Process Data]
    B -->|Error| D[Error Handling]
    D --> E{Error Type}
    E -->|Authentication| F[Re-authenticate]
    E -->|Validation| G[Log Validation Error]
    E -->|Server| H[Log Server Error]
    E -->|Network| I[Retry Request]
    F --> J[Retry Original Request]
    G --> K[Return Error to User]
    H --> K
    I --> J
    J --> B
    C --> L[Return Result]
```

## Security Considerations

1. **API Authentication**: Secure storage of BioStar 2 credentials
2. **Data Encryption**: Encryption of sensitive data in transit and at rest
3. **Access Control**: Role-based access control for AMS users
4. **Audit Logging**: Comprehensive logging of all operations
5. **Input Validation**: Validation of all input data before API calls

## Performance Optimization

1. **Connection Pooling**: Reuse of HTTP connections
2. **Caching**: Caching of frequently accessed data
3. **Batch Processing**: Batch processing of operations where possible
4. **Asynchronous Operations**: Use of asynchronous operations for non-critical tasks
5. **Pagination**: Pagination of large data sets

## Deployment Architecture

```mermaid
graph TD
    subgraph Production Environment
        subgraph VM1[Odoo Server]
            Odoo[Odoo Application]
            ams_bs[ams_bs Module]
        end
        
        subgraph VM2[BioStar Server]
            BS[BioStar 2 Application]
            BS_DB[BioStar Database]
        end
        
        subgraph Physical Locations
            Devices[Access Control Devices]
        end
    end
    
    ams_bs <-->|API| BS
    BS <--> BS_DB
    BS <-->|Device Communication| Devices
```

## Conclusion

The technical architecture of the AMS BioStar integration provides a robust foundation for physical access control management within the Odoo environment. By leveraging a well-structured API integration layer, comprehensive data models, and efficient synchronization mechanisms, it enables seamless communication between AMS and BioStar 2 while maintaining high security and performance standards.
