# AMS BioStar API Integration

## Introduction

This document provides detailed information about the API integration between the AMS system and BioStar 2. The integration is built on BioStar 2's REST API, which provides comprehensive endpoints for managing users, devices, access control, and events.

## API Architecture

The API integration is structured into several specialized clients that handle different aspects of the BioStar 2 system:

```mermaid
graph TD
    subgraph API Clients
        Base[Base API Client]
        AC[Access Control API Client]
        User[User API Client]
        Device[Device API Client]
    end

    subgraph DTOs
        BaseDTO[Base Response]
        ACDTO[Access Control DTOs]
        UserDTO[User DTOs]
        DeviceDTO[Device DTOs]
        EventDTO[Event DTOs]
    end

    Base --> AC
    Base --> User
    Base --> Device

    AC --> ACDTO
    User --> UserDTO
    Device --> DeviceDTO
    Device --> EventDTO
```

## Base API Client

The Base API Client (`BaseBiostarApiClient`) provides core functionality for all API interactions:

```python
class BaseBiostarAPIClient(models.AbstractModel):
    _name = 'ams_base.biostar_api_client'
    _inherit = 'ams_base.biostar_api_client'
    _description = 'Biostar API Client'

    @property
    def base_url(self):
        # Get the BioStar 2 base URL from company settings or system parameters
        return self.env.company.bs_base_url or self.env['ir.config_parameter'].sudo().get_param("ams_bs.base_url")

    @property
    def api_user(self):
        # Get the API username from company settings or system parameters
        return self.env.company.api_username or self.env['ir.config_parameter'].sudo().get_param("ams_bs.api_user")

    @property
    def api_password(self):
        # Get the API password from company settings or system parameters
        return self.env.company.api_password or self.env['ir.config_parameter'].sudo().get_param("ams_bs.api_password")

    @property
    def token(self):
        # Get or refresh the session token
        # Handles automatic re-authentication if token is expired
        return self.env.company.api_token

    def get_default_headers(self, **kwargs):
        # Return headers with session ID for API calls
        headers = {
            "Content-Type": "application/json",
            "bs-session-id": f"{self.token}"
        }
        return headers

    def login(self, base_url, username, password):
        # Authenticate with BioStar 2 and get session ID
        # Stores the session ID in company settings
        pass
```

Key features:

- Authentication management
- Session handling
- HTTP request/response processing
- Error handling
- Response validation

## Access Control API Client

The Access Control API Client (`BiostarACApiClient`) handles all access control related operations:

```python
class BiostarACAPIClient(models.AbstractModel):
    _name = 'ams_bs.biostar_ac_api_client'
    _inherit = 'ams_base.biostar_api_client'
    _description = 'Biostar AC API Client'

    def get_ac_doors(self, base_url, limit=0, order_by="id:true"):
        # Get all doors from BioStar 2
        # Returns DoorsResponse object
        url = f"{base_url}/api/doors?limit={limit}&order_by={order_by}"
        # ...

    def get_ac_door(self, base_url, door_id):
        # Get specific door details
        # Returns DoorResponse object
        url = f"{base_url}/api/doors/{door_id}"
        # ...

    def get_ac_schedules(self, base_url, limit=0, offset=0):
        # Get all schedules
        # Returns SchedulesResponse object
        url = f"{base_url}/api/schedules?limit={limit}&offset={offset}"
        # ...

    def get_ac_access_groups(self, base_url, limit=0, offset=0):
        # Get all access groups
        # Returns AccessGroupsResponse object
        url = f"{base_url}/api/access_groups?limit={limit}&offset={offset}"
        # ...

    def get_ac_access_group(self, base_url, access_group_id):
        # Get specific access group details
        # Returns AccessGroupResponse object
        url = f"{base_url}/api/access_groups/{access_group_id}"
        # ...
```

Key endpoints:

- `/api/access_groups` - Manage access groups
- `/api/access_levels` - Manage access levels
- `/api/doors` - Manage doors
- `/api/door_groups` - Manage door groups
- `/api/schedules` - Manage schedules
- `/api/access_groups/{id}` - Get specific access group details
- `/api/access_groups/{id}/users` - Get users assigned to an access group
- `/api/access_levels/{id}` - Get specific access level details

## User API Client

The User API Client (`BiostarUserApiClient`) handles all user management operations:

```python
class BiostarUserAPIClient(models.AbstractModel):
    _name = 'ams_bs.biostar_user_api_client'
    _inherit = 'ams_base.biostar_api_client'
    _description = 'Biostar User API Client'

    def get_users(self, base_url, group_id=None, limit=0, offset=0, order_by="user_id:false", last_modified=0):
        # Get users from BioStar 2
        # Returns UsersResponse object
        url = f"{base_url}/api/users?group_id={group_id}&limit={limit}&offset={offset}&order_by={order_by}&last_modified={last_modified}"
        # ...

    def get_user(self, base_url, user_id):
        # Get specific user details
        # Returns UserResponse object
        url = f"{base_url}/api/users/{user_id}"
        # ...

    def get_user_groups(self, base_url, limit=0, offset=0):
        # Get all user groups
        # Returns UserGroupsResponse object
        url = f"{base_url}/api/user_groups?limit={limit}&offset={offset}"
        # ...

    def get_user_group(self, base_url, group_id):
        # Get specific user group details
        # Returns UserGroupResponse object
        url = f"{base_url}/api/user_groups/{group_id}"
        # ...

    def scan_fingerprint(self, base_url, device_id, quality=80, timeout=10):
        # Scan fingerprint using a device
        # Returns ScanFingerprintResponse object
        url = f"{base_url}/api/devices/{device_id}/scan_fingerprint?quality={quality}&timeout={timeout}"
        # ...

    def scan_face(self, base_url, device_id, pose_sensitivity=4):
        # Scan face using a device
        # Returns ScanFaceResponse object
        url = f"{base_url}/api/devices/{device_id}/credentials/face?pose_sensitivity={pose_sensitivity}"
        # ...
```

Key endpoints:

- `/api/users` - Manage users (with query parameters: group_id, limit, offset, order_by, last_modified)
- `/api/users/{id}` - Get specific user details
- `/api/user_groups` - Manage user groups
- `/api/user_groups/{id}` - Get specific user group details
- `/api/users/{user_id}/cards` - Manage user cards
- `/api/users/{user_id}/fingerprints` - Manage user fingerprints
- `/api/users/{user_id}/faces` - Manage user face data
- `/api/login` - Authenticate with BioStar 2 API

## Device API Client

The Device API Client (`BiostarDeviceApiClient`) handles all device operations and event retrieval:

```python
class BiostarDeviceAPIClient(models.AbstractModel):
    _name = 'ams_bs.biostar_device_api_client'
    _inherit = 'ams_base.biostar_api_client'
    _description = 'Biostar Device API Client'

    def get_devices(self, base_url, limit=0, offset=0):
        # Get all devices from BioStar 2
        # Returns DevicesResponse object
        url = f"{base_url}/api/devices?limit={limit}&offset={offset}"
        # ...

    def get_device(self, base_url, device_id):
        # Get specific device details
        # Returns DeviceResponse object
        url = f"{base_url}/api/devices/{device_id}"
        # ...

    def get_device_groups(self, base_url):
        # Get all device groups
        # Returns DeviceGroupsResponse object
        url = f"{base_url}/api/v2/device_groups/search"
        # ...

    def get_events(self, base_url, params):
        # Get events with search parameters
        # Returns EventsResponse object
        url = f"{base_url}/api/events/search"
        # ...

    def control_device(self, base_url, device_id, command):
        # Send control command to device
        # Returns ControlDeviceResponse object
        url = f"{base_url}/api/devices/{device_id}/control"
        # ...
```

Key endpoints:

- `/api/devices` - Get all devices
- `/api/devices/{id}` - Get specific device details
- `/api/v2/device_groups/search` - Search device groups
- `/api/device_groups` - Manage device groups
- `/api/events/search` - Search events with filters
- `/api/devices/{device_id}/status` - Get device status
- `/api/devices/{device_id}/control` - Control device operations
- `/api/devices/{device_id}/credentials/face` - Scan face using device (with pose_sensitivity parameter)

## Data Transfer Objects (DTOs)

The DTOs provide structured data exchange between AMS and BioStar 2:

```mermaid
classDiagram
    class Response {
        +code: int
        +link: string
        +message: string
        +from_dict()
        +to_dict()
    }

    class BaseResponse {
        +response: Response
    }

    class AccessGroupsResponse {
        +access_group_collection: AccessGroupCollection
        +response: Response
        +from_dict()
        +to_dict()
    }

    class AccessLevelsResponse {
        +access_level_collection: AccessLevelCollection
        +response: Response
        +from_dict()
        +to_dict()
    }

    class DoorsResponse {
        +door_collection: DoorCollection
        +response: Response
        +from_dict()
        +to_dict()
    }

    class UsersResponse {
        +user_collection: UserCollection
        +response: Response
        +from_dict()
        +to_dict()
    }

    class DevicesResponse {
        +device_collection: DeviceCollection
        +response: Response
        +from_dict()
        +to_dict()
    }

    class EventsResponse {
        +event_collection: EventCollection
        +response: Response
        +from_dict()
        +to_dict()
    }

    BaseResponse <|-- AccessGroupsResponse
    BaseResponse <|-- AccessLevelsResponse
    BaseResponse <|-- DoorsResponse
    BaseResponse <|-- UsersResponse
    BaseResponse <|-- DevicesResponse
    BaseResponse <|-- EventsResponse
```

Each DTO:

- Provides static `from_dict()` methods to convert JSON data to typed objects
- Includes `to_dict()` methods to convert objects back to JSON-serializable dictionaries
- Implements proper type validation and conversion
- Structures the response data in a consistent format
- Separates the response status from the actual data

## Authentication Flow

```mermaid
graph TD
    A[Start] --> B[Get Credentials]
    B --> C[Send Login Request]
    C --> D{Login Successful?}
    D -->|Yes| E[Store Session ID]
    D -->|No| F[Handle Error]
    E --> G[Use Session ID for API Calls]
    G --> H{Session Valid?}
    H -->|Yes| I[Process API Response]
    H -->|No| J[Re-authenticate]
    J --> C
    F --> K[Return Error]
    I --> L[Return Result]
```

Authentication is handled through:

1. Initial login with credentials
2. Session ID storage
3. Session ID inclusion in all subsequent requests
4. Automatic re-authentication on session expiration

## Error Handling

The API integration includes comprehensive error handling:

```mermaid
graph TD
    A[API Request] --> B{Response Status}
    B -->|200 OK| C[Process Success]
    B -->|400 Bad Request| D[Handle Validation Error]
    B -->|401 Unauthorized| E[Handle Authentication Error]
    B -->|403 Forbidden| F[Handle Permission Error]
    B -->|404 Not Found| G[Handle Not Found Error]
    B -->|500 Server Error| H[Handle Server Error]
    B -->|Network Error| I[Handle Network Error]

    E --> J[Re-authenticate]
    J --> A

    C --> K[Return Success Result]
    D --> L[Return Error Result]
    F --> L
    G --> L
    H --> L
    I --> M{Retry Count < Max?}
    M -->|Yes| N[Increment Retry Count]
    N --> A
    M -->|No| L
```

Error handling includes:

- HTTP status code interpretation
- Error message extraction
- Automatic retry for transient errors
- Automatic re-authentication for session expiration
- Detailed error logging

## Rate Limiting and Performance

To ensure optimal performance and avoid overwhelming the BioStar 2 API:

1. **Connection Pooling**: Reuse of HTTP connections
2. **Request Throttling**: Limiting the rate of API requests
3. **Batch Processing**: Grouping operations where possible
4. **Pagination**: Handling large data sets in manageable chunks
5. **Caching**: Caching frequently accessed data

## Security Considerations

The API integration implements several security measures:

1. **Secure Credential Storage**: Credentials are stored securely in the database
2. **HTTPS Communication**: All API communication uses HTTPS
3. **Session Management**: Proper handling of session IDs
4. **Input Validation**: Validation of all input data before API calls
5. **Output Sanitization**: Sanitization of all data received from the API

## API Versioning

The integration is designed to handle BioStar 2 API versioning:

1. **Version Detection**: Automatic detection of API version
2. **Compatibility Layer**: Handling of differences between API versions
3. **Graceful Degradation**: Fallback to basic functionality when advanced features are not available

## Conclusion

The AMS BioStar API integration provides a robust and secure connection between the AMS system and BioStar 2. By leveraging a well-structured architecture with specialized API clients, comprehensive DTOs, and effective error handling, it enables seamless communication while maintaining high security and performance standards.
