# AMS BioStar User Guide

## Introduction

This user guide provides instructions for using the AMS BioStar integration module. The module enables management of physical access control through BioStar 2 directly from the Odoo interface.

## Getting Started

### System Requirements

- Odoo 18 with AMS modules installed
- BioStar 2 server (version 2.8.0 or higher)
- Network connectivity between Odoo and BioStar 2 server
- BioStar 2 API credentials with administrative privileges

### Initial Configuration

1. Navigate to **AMS → Configuration → Settings**
2. In the **BioStar Integration** section, enter:
   - BioStar 2 Server URL
   - API Username
   - API Password
3. Click **Test Connection** to verify connectivity
4. Click **Save** to store the configuration

## User Management

### Creating Users

```mermaid
graph TD;
    A[Navigate to AMS → Users] --> B[Click Create]
    B --> C[Enter User Details]
    C --> D[Assign User Group]
    D --> E[Click Save]
    E --> F[User Created in AMS]
    F --> G[User Synchronized to BioStar]
```

1. Navigate to **AMS → Users**
2. Click **Create** to create a new user
3. Enter the required information:
   - Name
   - Employee (optional)
   - User Group
   - Device Group
4. Click **Save** to create the user
5. The user will be automatically synchronized to BioStar 2

### Enrolling Credentials

#### Card Enrollment

1. Navigate to **AMS → Users**
2. Select the user to enroll
3. Click the **Cards** tab
4. Click **Add a line** to add a new card
5. Click **Scan Card** to scan a card using a connected reader
6. Alternatively, enter the card number manually
7. Click **Save** to enroll the card

#### Fingerprint Enrollment

1. Navigate to **AMS → Users**
2. Select the user to enroll
3. Click the **Fingerprints** tab
4. Select a device for enrollment
5. Click **Scan Fingerprint**
6. Follow the on-screen instructions to scan the fingerprint
7. Repeat for additional fingers if needed
8. Click **Save** to enroll the fingerprints

#### Face Enrollment

1. Navigate to **AMS → Users**
2. Select the user to enroll
3. Click the **Face** tab
4. Select a device for enrollment
5. Click **Scan Face**
6. Follow the on-screen instructions to capture the face
7. Click **Save** to enroll the face

### Managing User Groups

1. Navigate to **AMS → User Groups**
2. Click **Create** to create a new user group
3. Enter the required information:
   - Name
   - Description
4. Click **Save** to create the user group
5. The user group will be automatically synchronized to BioStar 2

## Access Control Management

### Creating Schedules

```mermaid
graph TD;
    A[Navigate to AMS → Access Control → Schedules] --> B[Click Create]
    B --> C[Enter Schedule Details]
    C --> D[Define Daily Schedules]
    D --> E[Click Save]
    E --> F[Schedule Created in AMS]
    F --> G[Schedule Synchronized to BioStar]
```

1. Navigate to **AMS → Access Control → Schedules**
2. Click **Create** to create a new schedule
3. Enter the required information:
   - Name
   - Description
4. In the **Daily Schedules** tab, add time periods for each day
5. Click **Save** to create the schedule
6. The schedule will be automatically synchronized to BioStar 2

### Configuring Doors

1. Navigate to **AMS → Access Control → Doors**
2. Click **Create** to create a new door
3. Enter the required information:
   - Name
   - Description
   - Door Group (optional)
4. Click **Save** to create the door
5. The door will be automatically synchronized to BioStar 2

### Creating Access Levels

1. Navigate to **AMS → Access Control → Access Levels**
2. Click **Create** to create a new access level
3. Enter the required information:
   - Name
   - Description
   - Doors (select one or more doors)
   - Schedule (select a schedule)
4. Click **Save** to create the access level
5. The access level will be automatically synchronized to BioStar 2

### Defining Access Groups

```mermaid
graph TD;
    A[Navigate to AMS → Access Control → Access Groups] --> B[Click Create]
    B --> C[Enter Access Group Details]
    C --> D[Assign Access Levels]
    D --> E[Click Save]
    E --> F[Access Group Created in AMS]
    F --> G[Access Group Synchronized to BioStar]
```

1. Navigate to **AMS → Access Control → Access Groups**
2. Click **Create** to create a new access group
3. Enter the required information:
   - Name
   - Description
   - Access Levels (select one or more access levels)
4. Click **Save** to create the access group
5. The access group will be automatically synchronized to BioStar 2

### Assigning Access Groups

#### To Users

1. Navigate to **AMS → Users**
2. Select the user to assign access
3. Click the **Access Groups** tab
4. Add the desired access groups
5. Click **Save** to update the user
6. The access assignment will be automatically synchronized to BioStar 2

#### To User Groups

1. Navigate to **AMS → Access Control → Access Groups**
2. Select the access group to assign
3. Click the **User Groups** tab
4. Add the desired user groups
5. Click **Save** to update the access group
6. The access assignment will be automatically synchronized to BioStar 2

## Device Management

### Managing Devices

```mermaid
graph TD;
    A[Navigate to AMS → Devices] --> B[Click Create]
    B --> C[Enter Device Details]
    C --> D[Assign Device Group]
    D --> E[Click Save]
    E --> F[Device Created in AMS]
    F --> G[Device Linked to BioStar]
```

1. Navigate to **AMS → Devices**
2. Click **Create** to create a new device
3. Enter the required information:
   - Name
   - Description
   - Device ID (must match BioStar 2 device ID)
   - Device Group (optional)
4. Click **Save** to create the device
5. The device will be linked to the corresponding device in BioStar 2

### Device Operations

1. Navigate to **AMS → Devices**
2. Select the device to operate
3. Click the appropriate action button:
   - **Unlock Door**: Temporarily unlock the door
   - **Lock Door**: Lock the door
   - **Release Alarm**: Release any active alarms
   - **Restart Device**: Restart the device
4. Confirm the action when prompted
5. The command will be sent to the device through BioStar 2

### Managing Device Groups

1. Navigate to **AMS → Device Groups**
2. Click **Create** to create a new device group
3. Enter the required information:
   - Name
   - Description
4. Click **Save** to create the device group
5. The device group will be automatically synchronized to BioStar 2

## Event Management

### Viewing Events

```mermaid
graph TD;
    A[Navigate to AMS → Events] --> B[View Event List]
    B --> C[Apply Filters]
    C --> D[View Event Details]
    D --> E[Export Events if needed]
```

1. Navigate to **AMS → Events**
2. View the list of events
3. Use the filter options to narrow down the events:
   - Date range
   - Event type
   - Device
   - User
4. Click on an event to view its details
5. Use the **Export** button to export events to CSV or Excel

### Event Monitoring

1. Navigate to **AMS → Dashboard**
2. View the real-time event monitor
3. Events will appear as they occur
4. Use the filter options to focus on specific events
5. Click on an event to view its details

## Troubleshooting

### Connection Issues

If you experience connection issues with BioStar 2:

1. Verify the BioStar 2 server is running
2. Check the server URL in the configuration
3. Verify the API credentials
4. Ensure network connectivity between Odoo and BioStar 2
5. Check the BioStar 2 API service status
6. Review the Odoo logs for detailed error messages

### Synchronization Issues

If you experience synchronization issues:

1. Navigate to **AMS → Configuration → Settings**
2. Click **Test Connection** to verify connectivity
3. Click **Synchronize All** to force a full synchronization
4. Check the synchronization logs for errors
5. Verify the data in both AMS and BioStar 2
6. Contact support if issues persist

### Device Issues

If you experience issues with devices:

1. Verify the device is online in BioStar 2
2. Check the device ID matches between AMS and BioStar 2
3. Restart the device if necessary
4. Check the device logs in BioStar 2
5. Verify network connectivity to the device
6. Contact support if issues persist

## Best Practices

1. **Regular Synchronization**: Perform regular synchronization to ensure data consistency
2. **User Management**: Create and manage users in AMS rather than directly in BioStar 2
3. **Access Control**: Define a clear access control strategy before implementation
4. **Testing**: Test access control changes in a controlled environment before deployment
5. **Backup**: Regularly backup both AMS and BioStar 2 databases
6. **Monitoring**: Regularly monitor events for unusual activity
7. **Updates**: Keep both AMS and BioStar 2 updated to the latest versions

## Conclusion

The AMS BioStar integration module provides a powerful solution for managing physical access control through the Odoo interface. By following this user guide, you can effectively manage users, access control, devices, and events in a unified system.
