# 📄 **List of Endpoints to Implement (AMS-BS Integration)**

This document tracks the remaining API endpoints that need to be implemented in our AMS module, mapped against the corresponding BS documentation.

---

## 📦 **Device Endpoints** ✅✅ 

**Endpoints to implement in our module:** 
- `sync_device` ✅
- `push_device` (Create or Update Device) ✅
- `delete_device` (Single or Multiple) ✅
- `delete_user_from_device`

**Endpoints in BS Documentation:**
- View Device Information *(same as Get Devices but requires ID as parameter)*
- Add Device ✅
- Update Device ✅
- Remove Device(s)✅
- Delete User from a Device

---

## 📦 **Device Group Endpoints** ✅ ✅

**Endpoints to implement in our module:**
- `sync_device_group`  
  > ⚠️ **Note:** There is no BS endpoint that returns a **single device group**, so syncing is currently **not possible**.
- `push_device_group` (Create Device Group) ✅
- `delete_device_group` ✅

**Endpoints in BS Documentation:**
- Update Device Group (not work and I see biostar UI it not support update) 
- Create New Device Group
- Delete Device Group

---

## 📦 **User Group Endpoints**

**Endpoints to implement in our module:**
- `sync_user_group` ✅

**Endpoints in BS Documentation:**
- List User Group by Id

---

## 📦 **Schedule Endpoints**

**Endpoints to implement in our module:**
- `sync_schedule` ✅
- `push_schedule` (Create/Update Schedule)
- `delete_schedule`

**⚠️ Note:**  
- Ask BS team for clarification on the difference between:
  - Create Schedule (**Daily**)
  - Create Schedule (**Weekly**)

**Endpoints in BS Documentation:**
- View a Schedule Detail ✅
- Create Schedule (Daily/Weekly)
- Update a Schedule
- Delete Schedules

---

## 📦 **Door Endpoints** ✅ ✅ (finished with test)

**⚠️ Note:**  
- if you put 2 doors in the same relay, you will get 500 internal server errors not defend error

**Endpoints to implement in our module:**
- `sync_door` ✅
- `push_door` (Create/Update Door) ✅
- `delete_door` (Single or Multiple) ✅

**Endpoints in BS Documentation:**
- View a Door Details
- Create a Door
- Update a Door
- Delete Door(s)

---

## 📦 **Door Group Endpoints**

**Endpoints to implement in our module:**
- `push_door_group` (Create/Update Door Group) ✅
- `delete_door_group` ✅

**⚠️ Note:**  
- No endpoint exists in BS to return **single door group**, so syncing `door_group` is **not possible**.

**Endpoints in BS Documentation:**
- Create Door Group
- Update Door Group
- Delete Door Group

---

## 📦 **Access Group Endpoints**

**Endpoints to implement in our module:**
- _(Syncing single access group is **not possible** — no such endpoint exists)_

---

## 📦 **Access Level Endpoints**

**Endpoints to implement in our module:**
- `push_access_level` (Create/Update Access Level)
- `delete_access_level`

**⚠️ Note:**  
- No endpoint exists in BS to return **single access level**, so syncing is **not possible**.

**Endpoints in BS Documentation:**
- Create Access Level
- Update Access Level
- Delete Access Level

---


