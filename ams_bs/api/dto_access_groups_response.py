
from .utils import *
from .dto_response import Response , BaseResponse
from typing import Any, List, Optional, TypeVar, Type, cast, Callable

class AccessLevel:
    id: int
    name: str
    description: str

    def __init__(self, id: int, name: str, description: str) -> None:
        self.id = id
        self.name = name
        self.description = description

    @staticmethod
    def from_dict(obj: Any) -> 'AccessLevel':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        description = from_str(obj.get("description"))
        return AccessLevel(id, name, description)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["description"] = from_str(self.description)
        return result

class ParentID:
    id: int
    name: str

    def __init__(self, id: int, name: str) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'ParentID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        return ParentID(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        return result

class UserGroup:
    id: int
    name: str
    description: str
    parent_id: ParentID
    depth: int
    inherited: bool
    face_count: int
    user_count: int

    def __init__(self, id: int, name: str, description: str, parent_id: ParentID, depth: int, inherited: bool, face_count: int, user_count: int) -> None:
        self.id = id
        self.name = name
        self.description = description
        self.parent_id = parent_id
        self.depth = depth
        self.inherited = inherited
        self.face_count = face_count
        self.user_count = user_count

    @staticmethod
    def from_dict(obj: Any) -> 'UserGroup':
        if not obj:
            return None

        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        description = from_str(obj.get("description"))
        parent_id = ParentID.from_dict(obj.get("parent_id"))
        depth = to_int(from_str(obj.get("depth")))
        inherited = from_stringified_bool(from_str(obj.get("inherited")))
        face_count = to_int(from_str(obj.get("face_count")))
        user_count = to_int(from_str(obj.get("user_count")))
        return UserGroup(id, name, description, parent_id, depth, inherited, face_count, user_count)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["description"] = from_str(self.description)
        result["parent_id"] = to_class(ParentID, self.parent_id)
        result["depth"] = from_str(str(self.depth))
        result["inherited"] = from_str(str(self.inherited).lower())
        result["face_count"] = from_str(str(self.face_count))
        result["user_count"] = from_str(str(self.user_count))
        return result





# class UserGroupUserGroup:
#     id: int
#     name: str
#
#     def __init__(self, id: int, name: str) -> None:
#         self.id = id
#         self.name = name
#
#     @staticmethod
#     def from_dict(obj: Any) -> 'UserGroupUserGroup':
#         if not obj:
#             return None
#         id = to_int(from_str(obj.get("id")))
#         name = from_str(obj.get("name"))
#         return UserGroupUserGroup(id, name)
#
#     def to_dict(self) -> dict:
#         result: dict = {}
#         result["id"] = from_str(str(self.id))
#         result["name"] = from_str(self.name)
#         return result
#
#
#
# class RowUserGroup:
#     id: int
#     name: str
#     description: str
#     depth: int
#     inherited: bool
#     face_count: int
#     user_count: int
#     user_groups: List[UserGroupUserGroup]
#
#     def __init__(self, id: int, name: str, description: str, depth: int, inherited: bool, face_count: int, user_count: int, user_groups: List[UserGroupUserGroup]) -> None:
#         self.id = id
#         self.name = name
#         self.description = description
#         self.depth = depth
#         self.inherited = inherited
#         self.face_count = face_count
#         self.user_count = user_count
#         self.user_groups = user_groups
#
#     @staticmethod
#     def from_dict(obj: Any) -> 'RowUserGroup':
#         if not obj:
#             return None
#         id = to_int(from_str(obj.get("id")))
#         name = from_str(obj.get("name"))
#         description = from_str(obj.get("description"))
#         depth = to_int(from_str(obj.get("depth")))
#         inherited = from_stringified_bool(from_str(obj.get("inherited")))
#         face_count = to_int(from_str(obj.get("face_count")))
#         user_count = to_int(from_str(obj.get("user_count")))
#         user_groups = from_list(UserGroupUserGroup.from_dict, obj.get("user_groups"))
#         return RowUserGroup(id, name, description, depth, inherited, face_count, user_count, user_groups)
#
#     def to_dict(self) -> dict:
#         result: dict = {}
#         result["id"] = from_str(str(self.id))
#         result["name"] = from_str(self.name)
#         result["description"] = from_str(self.description)
#         result["depth"] = from_str(str(self.depth))
#         result["inherited"] = from_str(str(self.inherited).lower())
#         result["face_count"] = from_str(str(self.face_count))
#         result["user_count"] = from_str(str(self.user_count))
#         result["user_groups"] = from_list(lambda x: to_class(UserGroupUserGroup, x), self.user_groups)
#         return result


class User:
    name: str
    user_id: int

    def __init__(self, name: str, user_id: int) -> None:
        self.name = name
        self.user_id = user_id

    @staticmethod
    def from_dict(obj: Any) -> 'User':
        if not obj:
            return None
        name = from_str(obj.get("name"))
        user_id = to_int(from_str(obj.get("user_id")))
        return User(name, user_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["name"] = from_str(self.name)
        result["user_id"] = from_str(str(self.user_id))
        return result


class Row:
    id: int
    name: str
    description: str
    users: Optional[List[User]]
    user_count: int
    user_groups: Optional[List[UserGroup]]
    access_levels: Optional[List[AccessLevel]]

    def __init__(self, id: int, name: str, description: str, users: Optional[List[User]], user_count: int, user_groups: Optional[List[UserGroup]], access_levels:Optional[List[AccessLevel]]) -> None:
        self.id = id
        self.name = name
        self.description = description
        self.users = users
        self.user_count = user_count
        self.user_groups = user_groups
        self.access_levels = access_levels

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        description = from_str(obj.get("description"))
        users = from_union([lambda x: from_list(User.from_dict, x), from_none], obj.get("users"))
        user_count = to_int(from_str(obj.get("user_count")))
        user_groups = from_union([lambda x: from_list(UserGroup.from_dict, x), from_none], obj.get("user_groups")) if obj.get("user_groups") is not None else []
        access_levels = from_list(AccessLevel.from_dict, obj.get("access_levels")) if obj.get("access_levels") is not None else []
        return Row(id, name, description, users, user_count, user_groups, access_levels)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["description"] = from_str(self.description)
        if self.users is not None:
            result["users"] = from_union([lambda x: from_list(lambda x: to_class(User, x), x), from_none], self.users)
        result["user_count"] = from_str(str(self.user_count))
        if self.user_groups is not None:
            result["user_groups"] = from_union([lambda x: from_list(lambda x: to_class(UserGroup, x), x), from_none],
                                               self.user_groups)
        result["access_levels"] = from_list(lambda x: to_class(AccessLevel, x), self.access_levels)
        return result


class AccessGroupCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'AccessGroupCollection':
        if not obj:
            return None
        total = to_int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return AccessGroupCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result




class AccessGroupsResponse(BaseResponse):
    access_group_collection: AccessGroupCollection
    response: Response

    def __init__(self, access_group_collection: AccessGroupCollection, response: Response) -> None:
        self.access_group_collection = access_group_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'AccessGroupsResponse':
        if not obj:
            return None
        access_group_collection = AccessGroupCollection.from_dict(obj.get("AccessGroupCollection"))
        response = Response.from_dict(obj.get("Response"))
        return AccessGroupsResponse(access_group_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["AccessGroupCollection"] = to_class(AccessGroupCollection, self.access_group_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def access_groups_response_from_dict(s: Any) -> AccessGroupsResponse:
    return AccessGroupsResponse.from_dict(s)


def access_groups_response_to_dict(x: AccessGroupsResponse) -> Any:
    return to_class(AccessGroupsResponse, x)



class AccessGroupResponse(BaseResponse):
    access_group: Row
    response: Response

    def __init__(self, access_group: Row, response: Response) -> None:
        self.access_group = access_group
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'AccessGroupResponse':
        if not obj:
            return None
        access_group = Row.from_dict(obj.get("AccessGroup"))
        response = Response.from_dict(obj.get("Response"))
        return AccessGroupResponse(access_group, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["AccessGroup"] = to_class(Row, self.access_group)
        result["Response"] = to_class(Response, self.response)
        return result


def access_group_response_from_dict(s: Any) -> AccessGroupResponse:
    return AccessGroupResponse.from_dict(s)


def access_group_response_to_dict(x: AccessGroupResponse) -> Any:
    return to_class(AccessGroupResponse, x)