from .utils import *
from .dto_response import Response , BaseResponse
from typing import Any

class Row:
    def __init__(
        self,
        id: int,
        name: str,
        description: str,
        max_connection: int,
        support_master: bool,
        support_slave: bool,
        input_port_num: int,
        output_port_num: int,
        relay_num: int,
        support_tamper: bool,
        tna_key_num: int,
        tna_extra_key_num: int,
        rs485_channel_num: int,
        wiegand_channel_num: int,
        mask: int,
        supervised_input_port_num: int,
        keypad: bool,
        fingerprint: bool,
        card: bool,
        face: bool,
        volume: bool,
        wifi: bool,
        support_lfd: bool,
        support_image_log: bool,
        support_dns_version: Optional[str] = None
    ):
        self.id = id
        self.name = name
        self.description = description
        self.max_connection = max_connection
        self.support_master = support_master
        self.support_slave = support_slave
        self.input_port_num = input_port_num
        self.output_port_num = output_port_num
        self.relay_num = relay_num
        self.support_tamper = support_tamper
        self.tna_key_num = tna_key_num
        self.tna_extra_key_num = tna_extra_key_num
        self.rs485_channel_num = rs485_channel_num
        self.wiegand_channel_num = wiegand_channel_num
        self.mask = mask
        self.supervised_input_port_num = supervised_input_port_num
        self.keypad = keypad
        self.fingerprint = fingerprint
        self.card = card
        self.face = face
        self.volume = volume
        self.wifi = wifi
        self.support_lfd = support_lfd
        self.support_image_log = support_image_log
        self.support_dns_version = support_dns_version


    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        assert isinstance(obj, dict)
        id = int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        description = from_str(obj.get("description"))
        max_connection = int(from_str(obj.get("max_connection")))
        support_master = from_stringified_bool(from_str(obj.get("support_master")))
        support_slave = from_stringified_bool(from_str(obj.get("support_slave")))
        input_port_num = int(from_str(obj.get("input_port_num")))
        output_port_num = int(from_str(obj.get("output_port_num")))
        relay_num = int(from_str(obj.get("relay_num")))
        support_tamper = from_stringified_bool(from_str(obj.get("support_tamper")))
        tna_key_num = int(from_str(obj.get("tna_key_num")))
        tna_extra_key_num = int(from_str(obj.get("tna_extra_key_num")))
        rs485_channel_num = int(from_str(obj.get("rs485_channel_num")))
        wiegand_channel_num = int(from_str(obj.get("wiegand_channel_num")))
        mask = int(from_str(obj.get("mask")))
        supervised_input_port_num = int(from_str(obj.get("supervised_input_port_num")))
        keypad = from_stringified_bool(from_str(obj.get("keypad")))
        fingerprint = from_stringified_bool(from_str(obj.get("fingerprint")))
        card = from_stringified_bool(from_str(obj.get("card")))
        face = from_stringified_bool(from_str(obj.get("face")))
        volume = from_stringified_bool(from_str(obj.get("volume")))
        wifi = from_stringified_bool(from_str(obj.get("wifi")))
        support_lfd = from_stringified_bool(from_str(obj.get("support_lfd")))
        support_image_log = from_stringified_bool(from_str(obj.get("support_image_log")))
        support_dns_version = from_union([from_str, from_none], obj.get("support_dns_version"))
        return Row(id, name, description, max_connection, support_master, support_slave, input_port_num, output_port_num, relay_num, support_tamper, tna_key_num, tna_extra_key_num, rs485_channel_num, wiegand_channel_num, mask, supervised_input_port_num, keypad, fingerprint, card, face, volume, wifi, support_lfd, support_image_log, support_dns_version)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["description"] = from_str(self.description)
        result["max_connection"] = from_str(str(self.max_connection))
        result["support_master"] = from_str(str(self.support_master).lower())
        result["support_slave"] = from_str(str(self.support_slave).lower())
        result["input_port_num"] = from_str(str(self.input_port_num))
        result["output_port_num"] = from_str(str(self.output_port_num))
        result["relay_num"] = from_str(str(self.relay_num))
        result["support_tamper"] = from_str(str(self.support_tamper).lower())
        result["tna_key_num"] = from_str(str(self.tna_key_num))
        result["tna_extra_key_num"] = from_str(str(self.tna_extra_key_num))
        result["rs485_channel_num"] = from_str(str(self.rs485_channel_num))
        result["wiegand_channel_num"] = from_str(str(self.wiegand_channel_num))
        result["mask"] = from_str(str(self.mask))
        result["supervised_input_port_num"] = from_str(str(self.supervised_input_port_num))
        result["keypad"] = from_str(str(self.keypad).lower())
        result["fingerprint"] = from_str(str(self.fingerprint).lower())
        result["card"] = from_str(str(self.card).lower())
        result["face"] = from_str(str(self.face).lower())
        result["volume"] = from_str(str(self.volume).lower())
        result["wifi"] = from_str(str(self.wifi).lower())
        result["support_lfd"] = from_str(str(self.support_lfd).lower())
        result["support_image_log"] = from_str(str(self.support_image_log).lower())
        if self.support_dns_version is not None:
            result["support_dns_version"] = from_union([from_str, from_none], self.support_dns_version)
        return result


class DeviceTypeCollection:
    def __init__(self, total: int, rows: List[Row]):
        self.total = total
        self.rows = rows


    @staticmethod
    def from_dict(obj: Any) -> 'DeviceTypeCollection':
        assert isinstance(obj, dict)
        total = int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return DeviceTypeCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


class Response:
    def __init__(self, code: int, link: str, message: str):
        self.code = code
        self.link = link
        self.message = message

    @staticmethod
    def from_dict(obj: Any) -> 'Response':
        assert isinstance(obj, dict)
        code = int(from_str(obj.get("code")))
        link = from_str(obj.get("link"))
        message = from_str(obj.get("message"))
        return Response(code, link, message)

    def to_dict(self) -> dict:
        result: dict = {}
        result["code"] = from_str(str(self.code))
        result["link"] = from_str(self.link)
        result["message"] = from_str(self.message)
        return result


class DeviceTypesResponse(BaseResponse):
    def __init__(self, device_type_collection: DeviceTypeCollection, response: Response):
        self.device_type_collection = device_type_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceTypesResponse':
        assert isinstance(obj, dict)
        device_type_collection = DeviceTypeCollection.from_dict(obj.get("DeviceTypeCollection"))
        response = Response.from_dict(obj.get("Response"))
        return DeviceTypesResponse(device_type_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["DeviceTypeCollection"] = to_class(DeviceTypeCollection, self.device_type_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def device_types_dto_from_dict(s: Any) -> DeviceTypesResponse:
    return DeviceTypesResponse.from_dict(s)


def device_types_dto_to_dict(x: DeviceTypesResponse) -> Any:
    return to_class(DeviceTypesResponse, x)
