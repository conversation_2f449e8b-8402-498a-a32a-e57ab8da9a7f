from .utils import *
from .dto_response import Response , BaseResponse
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast

class ScheduleID:
    id: int
    name: str
    description: str
    use_daily_iteration: bool
    start_date: datetime
    days_of_iteration: int

    def __init__(self, id: int, name: str, description: str, use_daily_iteration: bool, start_date: datetime, days_of_iteration: int) -> None:
        self.id = id
        self.name = name
        self.description = description
        self.use_daily_iteration = use_daily_iteration
        self.start_date = start_date
        self.days_of_iteration = days_of_iteration

    @staticmethod
    def from_dict(obj: Any) -> 'ScheduleID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        description = from_str(obj.get("description"))
        use_daily_iteration = from_stringified_bool(from_str(obj.get("use_daily_iteration")))
        start_date = from_datetime(obj.get("start_date"))
        days_of_iteration = to_int(from_str(obj.get("days_of_iteration")))
        return ScheduleID(id, name, description, use_daily_iteration, start_date, days_of_iteration)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["description"] = from_str(self.description)
        result["use_daily_iteration"] = from_str(str(self.use_daily_iteration).lower())
        result["start_date"] = self.start_date.isoformat()
        result["days_of_iteration"] = from_str(str(self.days_of_iteration))
        return result


class OperationMode:
    device_id: int
    mode: int
    schedule_id: ScheduleID

    def __init__(self, device_id: int, mode: int, schedule_id: ScheduleID) -> None:
        self.device_id = device_id
        self.mode = mode
        self.schedule_id = schedule_id

    @staticmethod
    def from_dict(obj: Any) -> 'OperationMode':
        if not obj:
            return None
        device_id = to_int(from_str(obj.get("device_id")))
        mode = to_int(from_str(obj.get("mode")))
        schedule_id = ScheduleID.from_dict(obj.get("schedule_id"))
        return OperationMode(device_id, mode, schedule_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["device_id"] = from_str(str(self.device_id))
        result["mode"] = from_str(str(self.mode))
        result["schedule_id"] = to_class(ScheduleID, self.schedule_id)
        return result


class Authentication:
    enable_private_auth: bool
    enable_server_matching: bool
    matching_timeout: int
    enable_full_access: bool
    enable_face_group_matching: bool
    auth_timeout: int
    operation_modes: List[OperationMode]
    enable_global_apb: bool
    global_apb_fail_action: int
    face_detection_level: int

    def __init__(self, enable_private_auth: bool, enable_server_matching: bool, matching_timeout: int, enable_full_access: bool, enable_face_group_matching: bool, auth_timeout: int, operation_modes: List[OperationMode], enable_global_apb: bool, global_apb_fail_action: int, face_detection_level: int) -> None:
        self.enable_private_auth = enable_private_auth
        self.enable_server_matching = enable_server_matching
        self.matching_timeout = matching_timeout
        self.enable_full_access = enable_full_access
        self.enable_face_group_matching = enable_face_group_matching
        self.auth_timeout = auth_timeout
        self.operation_modes = operation_modes
        self.enable_global_apb = enable_global_apb
        self.global_apb_fail_action = global_apb_fail_action
        self.face_detection_level = face_detection_level

    @staticmethod
    def from_dict(obj: Any) -> 'Authentication':
        if not obj:
            return None
        enable_private_auth = from_stringified_bool(from_str(obj.get("enable_private_auth")))
        enable_server_matching = from_stringified_bool(from_str(obj.get("enable_server_matching")))
        matching_timeout = to_int(from_str(obj.get("matching_timeout")))
        enable_full_access = from_stringified_bool(from_str(obj.get("enable_full_access")))
        enable_face_group_matching = from_stringified_bool(from_str(obj.get("enable_face_group_matching")))
        auth_timeout = to_int(from_str(obj.get("auth_timeout")))
        operation_modes = from_list(OperationMode.from_dict, obj.get("operation_modes"))
        enable_global_apb = from_stringified_bool(from_str(obj.get("enable_global_apb")))
        global_apb_fail_action = to_int(from_str(obj.get("global_apb_fail_action")))
        face_detection_level = to_int(from_str(obj.get("face_detection_level")))
        return Authentication(enable_private_auth, enable_server_matching, matching_timeout, enable_full_access, enable_face_group_matching, auth_timeout, operation_modes, enable_global_apb, global_apb_fail_action, face_detection_level)

    def to_dict(self) -> dict:
        result: dict = {}
        result["enable_private_auth"] = from_str(str(self.enable_private_auth).lower())
        result["enable_server_matching"] = from_str(str(self.enable_server_matching).lower())
        result["matching_timeout"] = from_str(str(self.matching_timeout))
        result["enable_full_access"] = from_str(str(self.enable_full_access).lower())
        result["enable_face_group_matching"] = from_str(str(self.enable_face_group_matching).lower())
        result["auth_timeout"] = from_str(str(self.auth_timeout))
        result["operation_modes"] = from_list(lambda x: to_class(OperationMode, x), self.operation_modes)
        result["enable_global_apb"] = from_str(str(self.enable_global_apb).lower())
        result["global_apb_fail_action"] = from_str(str(self.global_apb_fail_action))
        result["face_detection_level"] = from_str(str(self.face_detection_level))
        return result


class Barcode:
    use_barcode: bool
    scan_timeout: int
    use_visual_barcode: bool
    camera_timeout: int
    motion_sensor: int

    def __init__(self, use_barcode: bool, scan_timeout: int, use_visual_barcode: bool, camera_timeout: int, motion_sensor: int) -> None:
        self.use_barcode = use_barcode
        self.scan_timeout = scan_timeout
        self.use_visual_barcode = use_visual_barcode
        self.camera_timeout = camera_timeout
        self.motion_sensor = motion_sensor

    @staticmethod
    def from_dict(obj: Any) -> 'Barcode':
        if not obj:
            return None
        use_barcode = from_stringified_bool(from_str(obj.get("use_barcode")))
        scan_timeout = to_int(from_str(obj.get("scan_timeout")))
        use_visual_barcode = from_stringified_bool(from_str(obj.get("use_visual_barcode")))
        camera_timeout = to_int(from_str(obj.get("camera_timeout")))
        motion_sensor = to_int(from_str(obj.get("motion_sensor")))
        return Barcode(use_barcode, scan_timeout, use_visual_barcode, camera_timeout, motion_sensor)

    def to_dict(self) -> dict:
        result: dict = {}
        result["use_barcode"] = from_str(str(self.use_barcode).lower())
        result["scan_timeout"] = from_str(str(self.scan_timeout))
        result["use_visual_barcode"] = from_str(str(self.use_visual_barcode).lower())
        result["camera_timeout"] = from_str(str(self.camera_timeout))
        result["motion_sensor"] = from_str(str(self.motion_sensor))
        return result


class Card:
    byte_order: int
    smart_card_byte_order: int
    custom_smart_card_byte_order: int
    use_wiegand_format: bool
    use_csn: bool
    use_em: bool
    use_mifare_felica: bool
    use_wiegand: bool
    use_iclass: bool
    use_hi_dprox: bool
    use_smart: bool
    use_classic_plus: bool
    use_desfire_ev1: bool
    use_sr_se: bool
    use_seos: bool
    use_mobile: bool
    use_nfc: bool
    use_ble: bool
    use_custom_smart: bool
    use_custom_classic_plus: bool
    use_custom_desfire_ev1: bool
    use_tom: bool
    use_tom_ble: bool
    use_tom_nfc: bool
    tom_byte_order: int
    use_custom_smart_felica: bool

    def __init__(self, byte_order: int, smart_card_byte_order: int, custom_smart_card_byte_order: int, use_wiegand_format: bool, use_csn: bool, use_em: bool, use_mifare_felica: bool, use_wiegand: bool, use_iclass: bool, use_hi_dprox: bool, use_smart: bool, use_classic_plus: bool, use_desfire_ev1: bool, use_sr_se: bool, use_seos: bool, use_mobile: bool, use_nfc: bool, use_ble: bool, use_custom_smart: bool, use_custom_classic_plus: bool, use_custom_desfire_ev1: bool, use_tom: bool, use_tom_ble: bool, use_tom_nfc: bool, tom_byte_order: int, use_custom_smart_felica: bool) -> None:
        self.byte_order = byte_order
        self.smart_card_byte_order = smart_card_byte_order
        self.custom_smart_card_byte_order = custom_smart_card_byte_order
        self.use_wiegand_format = use_wiegand_format
        self.use_csn = use_csn
        self.use_em = use_em
        self.use_mifare_felica = use_mifare_felica
        self.use_wiegand = use_wiegand
        self.use_iclass = use_iclass
        self.use_hi_dprox = use_hi_dprox
        self.use_smart = use_smart
        self.use_classic_plus = use_classic_plus
        self.use_desfire_ev1 = use_desfire_ev1
        self.use_sr_se = use_sr_se
        self.use_seos = use_seos
        self.use_mobile = use_mobile
        self.use_nfc = use_nfc
        self.use_ble = use_ble
        self.use_custom_smart = use_custom_smart
        self.use_custom_classic_plus = use_custom_classic_plus
        self.use_custom_desfire_ev1 = use_custom_desfire_ev1
        self.use_tom = use_tom
        self.use_tom_ble = use_tom_ble
        self.use_tom_nfc = use_tom_nfc
        self.tom_byte_order = tom_byte_order
        self.use_custom_smart_felica = use_custom_smart_felica

    @staticmethod
    def from_dict(obj: Any) -> 'Card':
        if not obj:
            return None
        byte_order = to_int(from_str(obj.get("byte_order")))
        smart_card_byte_order = to_int(from_str(obj.get("smart_card_byte_order")))
        custom_smart_card_byte_order = to_int(from_str(obj.get("custom_smart_card_byte_order")))
        use_wiegand_format = from_stringified_bool(from_str(obj.get("use_wiegand_format")))
        use_csn = from_stringified_bool(from_str(obj.get("use_csn")))
        use_em = from_stringified_bool(from_str(obj.get("use_em")))
        use_mifare_felica = from_stringified_bool(from_str(obj.get("use_mifare_felica")))
        use_wiegand = from_stringified_bool(from_str(obj.get("use_wiegand")))
        use_iclass = from_stringified_bool(from_str(obj.get("use_iclass")))
        use_hi_dprox = from_stringified_bool(from_str(obj.get("use_HIDprox")))
        use_smart = from_stringified_bool(from_str(obj.get("use_smart")))
        use_classic_plus = from_stringified_bool(from_str(obj.get("use_classic_plus")))
        use_desfire_ev1 = from_stringified_bool(from_str(obj.get("use_desfire_ev1")))
        use_sr_se = from_stringified_bool(from_str(obj.get("use_SR_SE")))
        use_seos = from_stringified_bool(from_str(obj.get("use_SEOS")))
        use_mobile = from_stringified_bool(from_str(obj.get("use_mobile")))
        use_nfc = from_stringified_bool(from_str(obj.get("use_NFC")))
        use_ble = from_stringified_bool(from_str(obj.get("use_BLE")))
        use_custom_smart = from_stringified_bool(from_str(obj.get("use_custom_smart")))
        use_custom_classic_plus = from_stringified_bool(from_str(obj.get("use_custom_classic_plus")))
        use_custom_desfire_ev1 = from_stringified_bool(from_str(obj.get("use_custom_desfire_ev1")))
        use_tom = from_stringified_bool(from_str(obj.get("use_tom")))
        use_tom_ble = from_stringified_bool(from_str(obj.get("use_tom_ble")))
        use_tom_nfc = from_stringified_bool(from_str(obj.get("use_tom_nfc")))
        tom_byte_order = to_int(from_str(obj.get("tom_byte_order")))
        use_custom_smart_felica = from_stringified_bool(from_str(obj.get("use_custom_smart_felica")))
        return Card(byte_order, smart_card_byte_order, custom_smart_card_byte_order, use_wiegand_format, use_csn, use_em, use_mifare_felica, use_wiegand, use_iclass, use_hi_dprox, use_smart, use_classic_plus, use_desfire_ev1, use_sr_se, use_seos, use_mobile, use_nfc, use_ble, use_custom_smart, use_custom_classic_plus, use_custom_desfire_ev1, use_tom, use_tom_ble, use_tom_nfc, tom_byte_order, use_custom_smart_felica)

    def to_dict(self) -> dict:
        result: dict = {}
        result["byte_order"] = from_str(str(self.byte_order))
        result["smart_card_byte_order"] = from_str(str(self.smart_card_byte_order))
        result["custom_smart_card_byte_order"] = from_str(str(self.custom_smart_card_byte_order))
        result["use_wiegand_format"] = from_str(str(self.use_wiegand_format).lower())
        result["use_csn"] = from_str(str(self.use_csn).lower())
        result["use_em"] = from_str(str(self.use_em).lower())
        result["use_mifare_felica"] = from_str(str(self.use_mifare_felica).lower())
        result["use_wiegand"] = from_str(str(self.use_wiegand).lower())
        result["use_iclass"] = from_str(str(self.use_iclass).lower())
        result["use_HIDprox"] = from_str(str(self.use_hi_dprox).lower())
        result["use_smart"] = from_str(str(self.use_smart).lower())
        result["use_classic_plus"] = from_str(str(self.use_classic_plus).lower())
        result["use_desfire_ev1"] = from_str(str(self.use_desfire_ev1).lower())
        result["use_SR_SE"] = from_str(str(self.use_sr_se).lower())
        result["use_SEOS"] = from_str(str(self.use_seos).lower())
        result["use_mobile"] = from_str(str(self.use_mobile).lower())
        result["use_NFC"] = from_str(str(self.use_nfc).lower())
        result["use_BLE"] = from_str(str(self.use_ble).lower())
        result["use_custom_smart"] = from_str(str(self.use_custom_smart).lower())
        result["use_custom_classic_plus"] = from_str(str(self.use_custom_classic_plus).lower())
        result["use_custom_desfire_ev1"] = from_str(str(self.use_custom_desfire_ev1).lower())
        result["use_tom"] = from_str(str(self.use_tom).lower())
        result["use_tom_ble"] = from_str(str(self.use_tom_ble).lower())
        result["use_tom_nfc"] = from_str(str(self.use_tom_nfc).lower())
        result["tom_byte_order"] = from_str(str(self.tom_byte_order))
        result["use_custom_smart_felica"] = from_str(str(self.use_custom_smart_felica).lower())
        return result


class DeviceID:
    id: int
    name: str

    def __init__(self, id: int, name: str) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        return DeviceID(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        return result


class Display:
    language: int
    background: int
    background_theme: int
    menu_timeout: int
    message_timeout: int
    backlight_timeout: int
    display_datetime: bool
    use_screen_saver: bool
    time_format: int
    volume: int
    use_voice: bool
    device_private_message: bool
    server_private_message: bool
    date_type: int
    display_userid_option: int
    display_username_option: int

    def __init__(self, language: int, background: int, background_theme: int, menu_timeout: int, message_timeout: int, backlight_timeout: int, display_datetime: bool, use_screen_saver: bool, time_format: int, volume: int, use_voice: bool, device_private_message: bool, server_private_message: bool, date_type: int, display_userid_option: int, display_username_option: int) -> None:
        self.language = language
        self.background = background
        self.background_theme = background_theme
        self.menu_timeout = menu_timeout
        self.message_timeout = message_timeout
        self.backlight_timeout = backlight_timeout
        self.display_datetime = display_datetime
        self.use_screen_saver = use_screen_saver
        self.time_format = time_format
        self.volume = volume
        self.use_voice = use_voice
        self.device_private_message = device_private_message
        self.server_private_message = server_private_message
        self.date_type = date_type
        self.display_userid_option = display_userid_option
        self.display_username_option = display_username_option

    @staticmethod
    def from_dict(obj: Any) -> 'Display':
        if not obj:
            return None
        language = to_int(from_str(obj.get("language")))
        background = to_int(from_str(obj.get("background")))
        background_theme = to_int(from_str(obj.get("background_theme")))
        menu_timeout = to_int(from_str(obj.get("menu_timeout")))
        message_timeout = to_int(from_str(obj.get("message_timeout")))
        backlight_timeout = to_int(from_str(obj.get("backlight_timeout")))
        display_datetime = from_stringified_bool(from_str(obj.get("display_datetime")))
        use_screen_saver = from_stringified_bool(from_str(obj.get("use_screen_saver")))
        time_format = to_int(from_str(obj.get("time_format")))
        volume = to_int(from_str(obj.get("volume")))
        use_voice = from_stringified_bool(from_str(obj.get("use_voice")))
        device_private_message = from_stringified_bool(from_str(obj.get("device_private_message")))
        server_private_message = from_stringified_bool(from_str(obj.get("server_private_message")))
        date_type = to_int(from_str(obj.get("date_type")))
        display_userid_option = to_int(from_str(obj.get("display_userid_option")))
        display_username_option = to_int(from_str(obj.get("display_username_option")))
        return Display(language, background, background_theme, menu_timeout, message_timeout, backlight_timeout, display_datetime, use_screen_saver, time_format, volume, use_voice, device_private_message, server_private_message, date_type, display_userid_option, display_username_option)

    def to_dict(self) -> dict:
        result: dict = {}
        result["language"] = from_str(str(self.language))
        result["background"] = from_str(str(self.background))
        result["background_theme"] = from_str(str(self.background_theme))
        result["menu_timeout"] = from_str(str(self.menu_timeout))
        result["message_timeout"] = from_str(str(self.message_timeout))
        result["backlight_timeout"] = from_str(str(self.backlight_timeout))
        result["display_datetime"] = from_str(str(self.display_datetime).lower())
        result["use_screen_saver"] = from_str(str(self.use_screen_saver).lower())
        result["time_format"] = from_str(str(self.time_format))
        result["volume"] = from_str(str(self.volume))
        result["use_voice"] = from_str(str(self.use_voice).lower())
        result["device_private_message"] = from_str(str(self.device_private_message).lower())
        result["server_private_message"] = from_str(str(self.server_private_message).lower())
        result["date_type"] = from_str(str(self.date_type))
        result["display_userid_option"] = from_str(str(self.display_userid_option))
        result["display_username_option"] = from_str(str(self.display_username_option))
        return result


class Face:
    operation_mode: int

    def __init__(self, operation_mode: int) -> None:
        self.operation_mode = operation_mode

    @staticmethod
    def from_dict(obj: Any) -> 'Face':
        if not obj:
            return None
        operation_mode = to_int(from_str(obj.get("operation_mode")))
        return Face(operation_mode)

    def to_dict(self) -> dict:
        result: dict = {}
        result["operation_mode"] = from_str(str(self.operation_mode))
        return result


class Fingerprint:
    security_level: int
    fast_mode: int
    sensitivity: int
    show_image: bool
    scan_timeout: int
    detect_afterimage: bool
    template_format: int
    sensor_mode: int
    advanced_enrollment: bool
    duplicate_check: bool
    lfd_level: int

    def __init__(self, security_level: int, fast_mode: int, sensitivity: int, show_image: bool, scan_timeout: int, detect_afterimage: bool, template_format: int, sensor_mode: int, advanced_enrollment: bool, duplicate_check: bool, lfd_level: int) -> None:
        self.security_level = security_level
        self.fast_mode = fast_mode
        self.sensitivity = sensitivity
        self.show_image = show_image
        self.scan_timeout = scan_timeout
        self.detect_afterimage = detect_afterimage
        self.template_format = template_format
        self.sensor_mode = sensor_mode
        self.advanced_enrollment = advanced_enrollment
        self.duplicate_check = duplicate_check
        self.lfd_level = lfd_level

    @staticmethod
    def from_dict(obj: Any) -> 'Fingerprint':
        if not obj:
            return None
        security_level = to_int(from_str(obj.get("security_level")))
        fast_mode = to_int(from_str(obj.get("fast_mode")))
        sensitivity = to_int(from_str(obj.get("sensitivity")))
        show_image = from_stringified_bool(from_str(obj.get("show_image")))
        scan_timeout = to_int(from_str(obj.get("scan_timeout")))
        detect_afterimage = from_stringified_bool(from_str(obj.get("detect_afterimage")))
        template_format = to_int(from_str(obj.get("template_format")))
        sensor_mode = to_int(from_str(obj.get("sensor_mode")))
        advanced_enrollment = from_stringified_bool(from_str(obj.get("advanced_enrollment")))
        duplicate_check = from_stringified_bool(from_str(obj.get("duplicate_check")))
        lfd_level = to_int(from_str(obj.get("lfd_level")))
        return Fingerprint(security_level, fast_mode, sensitivity, show_image, scan_timeout, detect_afterimage, template_format, sensor_mode, advanced_enrollment, duplicate_check, lfd_level)

    def to_dict(self) -> dict:
        result: dict = {}
        result["security_level"] = from_str(str(self.security_level))
        result["fast_mode"] = from_str(str(self.fast_mode))
        result["sensitivity"] = from_str(str(self.sensitivity))
        result["show_image"] = from_str(str(self.show_image).lower())
        result["scan_timeout"] = from_str(str(self.scan_timeout))
        result["detect_afterimage"] = from_str(str(self.detect_afterimage).lower())
        result["template_format"] = from_str(str(self.template_format))
        result["sensor_mode"] = from_str(str(self.sensor_mode))
        result["advanced_enrollment"] = from_str(str(self.advanced_enrollment).lower())
        result["duplicate_check"] = from_str(str(self.duplicate_check).lower())
        result["lfd_level"] = from_str(str(self.lfd_level))
        return result


class InputConfig:
    input_port_num: int

    def __init__(self, input_port_num: int) -> None:
        self.input_port_num = input_port_num

    @staticmethod
    def from_dict(obj: Any) -> 'InputConfig':
        if not obj:
            return None
        input_port_num = to_int(from_str(obj.get("input_port_num",'0')))
        return InputConfig(input_port_num)

    def to_dict(self) -> dict:
        result: dict = {}
        result["input_port_num"] = from_str(str(self.input_port_num))
        return result


class Input:
    input_port_num: int
    input_config: InputConfig

    def __init__(self, input_port_num: int, input_config: InputConfig) -> None:
        self.input_port_num = input_port_num
        self.input_config = input_config

    @staticmethod
    def from_dict(obj: Any) -> 'Input':
        if not obj:
            return None
        input_port_num = to_int(from_str(obj.get("input_port_num")))
        input_config = InputConfig.from_dict(obj.get("input_config",{}))
        # suprevised_input
        return Input(input_port_num, input_config)

    def to_dict(self) -> dict:
        result: dict = {}
        result["input_port_num"] = from_str(str(self.input_port_num))
        result["input_config"] = to_class(InputConfig, self.input_config)
        return result


class LAN:
    enable_dhcp: bool
    ip: str
    gateway: str
    subnet_mask: str
    device_port: int
    server_port: int
    connection_mode: int
    mtu_size: int
    baseband: int

    def __init__(self, enable_dhcp: bool, ip: str, gateway: str, subnet_mask: str, device_port: int, server_port: int, connection_mode: int, mtu_size: int, baseband: int) -> None:
        self.enable_dhcp = enable_dhcp
        self.ip = ip
        self.gateway = gateway
        self.subnet_mask = subnet_mask
        self.device_port = device_port
        self.server_port = server_port
        self.connection_mode = connection_mode
        self.mtu_size = mtu_size
        self.baseband = baseband

    @staticmethod
    def from_dict(obj: Any) -> 'LAN':
        if not obj:
            return None
        enable_dhcp = from_stringified_bool(from_str(obj.get("enable_dhcp")))
        ip = from_str(obj.get("ip"))
        gateway = from_str(obj.get("gateway"))
        subnet_mask = from_str(obj.get("subnet_mask"))
        device_port = to_int(from_str(obj.get("device_port")))
        server_port = to_int(from_str(obj.get("server_port")))
        connection_mode = to_int(from_str(obj.get("connection_mode")))
        mtu_size = to_int(from_str(obj.get("mtu_size")))
        baseband = to_int(from_str(obj.get("baseband")))
        return LAN(enable_dhcp, ip, gateway, subnet_mask, device_port, server_port, connection_mode, mtu_size, baseband)

    def to_dict(self) -> dict:
        result: dict = {}
        result["enable_dhcp"] = from_str(str(self.enable_dhcp).lower())
        result["ip"] = from_str(self.ip)
        result["gateway"] = from_str(self.gateway)
        result["subnet_mask"] = from_str(self.subnet_mask)
        result["device_port"] = from_str(str(self.device_port))
        result["server_port"] = from_str(str(self.server_port))
        result["connection_mode"] = from_str(str(self.connection_mode))
        result["mtu_size"] = from_str(str(self.mtu_size))
        result["baseband"] = from_str(str(self.baseband))
        return result


class Channel:
    index: int
    mode: int
    baudrate: int

    def __init__(self, index: int, mode: int, baudrate: int) -> None:
        self.index = index
        self.mode = mode
        self.baudrate = baudrate

    @staticmethod
    def from_dict(obj: Any) -> 'Channel':
        if not obj:
            return None
        index = to_int(from_str(obj.get("index")))
        mode = to_int(from_str(obj.get("mode")))
        baudrate = to_int(from_str(obj.get("baudrate")))
        return Channel(index, mode, baudrate)

    def to_dict(self) -> dict:
        result: dict = {}
        result["index"] = from_str(str(self.index))
        result["mode"] = from_str(str(self.mode))
        result["baudrate"] = from_str(str(self.baudrate))
        return result


class IntelligentInfo:
    support_i_config: bool
    use_fail_code: bool
    fail_code: str
    card_format: int
    osdp_id: int

    def __init__(self, support_i_config: bool, use_fail_code: bool, fail_code: str, card_format: int, osdp_id: int) -> None:
        self.support_i_config = support_i_config
        self.use_fail_code = use_fail_code
        self.fail_code = fail_code
        self.card_format = card_format
        self.osdp_id = osdp_id

    @staticmethod
    def from_dict(obj: Any) -> 'IntelligentInfo':
        if not obj:
            return None
        support_i_config = from_stringified_bool(from_str(obj.get("supportIConfig")))
        use_fail_code = from_stringified_bool(from_str(obj.get("useFailCode")))
        fail_code = from_str(obj.get("failCode"))
        card_format = to_int(from_str(obj.get("cardFormat")))
        osdp_id = to_int(from_str(obj.get("osdpID")))
        return IntelligentInfo(support_i_config, use_fail_code, fail_code, card_format, osdp_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["supportIConfig"] = from_str(str(self.support_i_config).lower())
        result["useFailCode"] = from_str(str(self.use_fail_code).lower())
        result["failCode"] = from_str(self.fail_code)
        result["cardFormat"] = from_str(str(self.card_format))
        result["osdpID"] = from_str(str(self.osdp_id))
        return result


class Rs485:
    mode: int
    intelligent_info: IntelligentInfo
    channels: List[Channel]

    def __init__(self, mode: int, intelligent_info: IntelligentInfo, channels: List[Channel]) -> None:
        self.mode = mode
        self.intelligent_info = intelligent_info
        self.channels = channels

    @staticmethod
    def from_dict(obj: Any) -> 'Rs485':
        if not obj:
            return None
        mode = to_int(from_str(obj.get("mode")))
        intelligent_info = IntelligentInfo.from_dict(obj.get("intelligentInfo"))
        channels = from_list(Channel.from_dict, obj.get("channels"))
        return Rs485(mode, intelligent_info, channels)

    def to_dict(self) -> dict:
        result: dict = {}
        result["mode"] = from_str(str(self.mode))
        result["intelligentInfo"] = to_class(IntelligentInfo, self.intelligent_info)
        result["channels"] = from_list(lambda x: to_class(Channel, x), self.channels)
        return result


class RTSP:
    enabled: bool

    def __init__(self, enabled: bool) -> None:
        self.enabled = enabled

    @staticmethod
    def from_dict(obj: Any) -> 'RTSP':
        if not obj:
            return None
        enabled = from_stringified_bool(from_str(obj.get("enabled")))
        return RTSP(enabled)

    def to_dict(self) -> dict:
        result: dict = {}
        result["enabled"] = from_str(str(self.enabled).lower())
        return result


class System:
    timezone: int
    sync_time: bool
    interphone_type: int
    enable_clear_on_tamper: bool
    keypad_backlight_off: int
    use_alphanumeric: bool
    camera_frequency: int
    use_card_operation: bool

    def __init__(self, timezone: int, sync_time: bool, interphone_type: int, enable_clear_on_tamper: bool, keypad_backlight_off: int, use_alphanumeric: bool, camera_frequency: int, use_card_operation: bool) -> None:
        self.timezone = timezone
        self.sync_time = sync_time
        self.interphone_type = interphone_type
        self.enable_clear_on_tamper = enable_clear_on_tamper
        self.keypad_backlight_off = keypad_backlight_off
        self.use_alphanumeric = use_alphanumeric
        self.camera_frequency = camera_frequency
        self.use_card_operation = use_card_operation

    @staticmethod
    def from_dict(obj: Any) -> 'System':
        if not obj:
            return None
        timezone = to_int(from_str(obj.get("timezone")))
        sync_time = from_stringified_bool(from_str(obj.get("sync_time")))
        interphone_type = to_int(from_str(obj.get("interphone_type")))
        enable_clear_on_tamper = from_stringified_bool(from_str(obj.get("enable_clear_on_tamper")))
        keypad_backlight_off = to_int(from_str(obj.get("keypad_backlight_off")))
        use_alphanumeric = from_stringified_bool(from_str(obj.get("use_alphanumeric")))
        camera_frequency = to_int(from_str(obj.get("camera_frequency")))
        use_card_operation = from_stringified_bool(from_str(obj.get("use_card_operation")))
        return System(timezone, sync_time, interphone_type, enable_clear_on_tamper, keypad_backlight_off, use_alphanumeric, camera_frequency, use_card_operation)

    def to_dict(self) -> dict:
        result: dict = {}
        result["timezone"] = from_str(str(self.timezone))
        result["sync_time"] = from_str(str(self.sync_time).lower())
        result["interphone_type"] = from_str(str(self.interphone_type))
        result["enable_clear_on_tamper"] = from_str(str(self.enable_clear_on_tamper).lower())
        result["keypad_backlight_off"] = from_str(str(self.keypad_backlight_off))
        result["use_alphanumeric"] = from_str(str(self.use_alphanumeric).lower())
        result["camera_frequency"] = from_str(str(self.camera_frequency))
        result["use_card_operation"] = from_str(str(self.use_card_operation).lower())
        return result


class TnaKey:
    enabled: bool
    label: str
    icon: int

    def __init__(self, enabled: bool, label: str, icon: int) -> None:
        self.enabled = enabled
        self.label = label
        self.icon = icon

    @staticmethod
    def from_dict(obj: Any) -> 'TnaKey':
        if not obj:
            return None
        enabled = from_stringified_bool(from_str(obj.get("enabled")))
        label = from_str(obj.get("label"))
        icon = to_int(from_str(obj.get("icon")))
        return TnaKey(enabled, label, icon)

    def to_dict(self) -> dict:
        result: dict = {}
        result["enabled"] = from_str(str(self.enabled).lower())
        result["label"] = from_str(self.label)
        result["icon"] = from_str(str(self.icon))
        return result


class Tna:
    mode: int
    required: bool
    fixed_code: int
    tna_keys: List[TnaKey]

    def __init__(self, mode: int, required: bool, fixed_code: int, tna_keys: List[TnaKey]) -> None:
        self.mode = mode
        self.required = required
        self.fixed_code = fixed_code
        self.tna_keys = tna_keys

    @staticmethod
    def from_dict(obj: Any) -> 'Tna':
        if not obj:
            return None
        mode = to_int(from_str(obj.get("mode")))
        required = from_stringified_bool(from_str(obj.get("required")))
        fixed_code = to_int(from_str(obj.get("fixed_code")))
        tna_keys = from_list(TnaKey.from_dict, obj.get("tna_keys"))
        return Tna(mode, required, fixed_code, tna_keys)

    def to_dict(self) -> dict:
        result: dict = {}
        result["mode"] = from_str(str(self.mode))
        result["required"] = from_str(str(self.required).lower())
        result["fixed_code"] = from_str(str(self.fixed_code))
        result["tna_keys"] = from_list(lambda x: to_class(TnaKey, x), self.tna_keys)
        return result


class USB:
    enable_usb: bool
    enable_usb_memory: bool

    def __init__(self, enable_usb: bool, enable_usb_memory: bool) -> None:
        self.enable_usb = enable_usb
        self.enable_usb_memory = enable_usb_memory

    @staticmethod
    def from_dict(obj: Any) -> 'USB':
        if not obj:
            return None
        enable_usb = from_stringified_bool(from_str(obj.get("enable_usb")))
        enable_usb_memory = from_stringified_bool(from_str(obj.get("enable_usb_memory")))
        return USB(enable_usb, enable_usb_memory)

    def to_dict(self) -> dict:
        result: dict = {}
        result["enable_usb"] = from_str(str(self.enable_usb).lower())
        result["enable_usb_memory"] = from_str(str(self.enable_usb_memory).lower())
        return result


class Version:
    product_name: str
    hardware: str
    firmware: str
    kernel: str
    firmware_rev: str
    kernel_rev: str

    def __init__(self, product_name: str, hardware: str, firmware: str, kernel: str, firmware_rev: str, kernel_rev: str) -> None:
        self.product_name = product_name
        self.hardware = hardware
        self.firmware = firmware
        self.kernel = kernel
        self.firmware_rev = firmware_rev
        self.kernel_rev = kernel_rev

    @staticmethod
    def from_dict(obj: Any) -> 'Version':
        if not obj:
            return None
        product_name = from_str(obj.get("product_name"))
        hardware = from_str(obj.get("hardware"))
        firmware = from_str(obj.get("firmware"))
        kernel = from_str(obj.get("kernel"))
        firmware_rev = from_str(obj.get("firmware_rev"))
        kernel_rev = from_str(obj.get("kernel_rev"))
        return Version(product_name, hardware, firmware, kernel, firmware_rev, kernel_rev)

    def to_dict(self) -> dict:
        result: dict = {}
        result["product_name"] = from_str(self.product_name)
        result["hardware"] = from_str(self.hardware)
        result["firmware"] = from_str(self.firmware)
        result["kernel"] = from_str(self.kernel)
        result["firmware_rev"] = from_str(self.firmware_rev)
        result["kernel_rev"] = from_str(self.kernel_rev)
        return result


class Outbound:
    use_prox_ser: bool
    prox_ip_addrs: str
    prox_ser_port: str

    def __init__(self, use_prox_ser: bool, prox_ip_addrs: str, prox_ser_port: str) -> None:
        self.use_prox_ser = use_prox_ser
        self.prox_ip_addrs = prox_ip_addrs
        self.prox_ser_port = prox_ser_port

    @staticmethod
    def from_dict(obj: Any) -> 'Outbound':
        if not obj:
            return None
        use_prox_ser = from_stringified_bool(from_str(obj.get("use_prox_ser")))
        prox_ip_addrs = from_str(obj.get("prox_ip_addrs"))
        prox_ser_port = from_str(obj.get("prox_ser_port"))
        return Outbound(use_prox_ser, prox_ip_addrs, prox_ser_port)

    def to_dict(self) -> dict:
        result: dict = {}
        result["use_prox_ser"] = from_str(str(self.use_prox_ser).lower())
        result["prox_ip_addrs"] = from_str(self.prox_ip_addrs)
        result["prox_ser_port"] = from_str(self.prox_ser_port)
        return result


class Server:
    autid: str
    regdur: int

    def __init__(self, autid: str, regdur: int) -> None:
        self.autid = autid
        self.regdur = regdur

    @staticmethod
    def from_dict(obj: Any) -> 'Server':
        if not obj:
            return None
        autid = from_str(obj.get("autid"))
        regdur = to_int(from_str(obj.get("regdur")))
        return Server(autid, regdur)

    def to_dict(self) -> dict:
        result: dict = {}
        result["autid"] = from_str(self.autid)
        result["regdur"] = from_str(str(self.regdur))
        return result


class Voip:
    server: Server
    outbound: Outbound
    use_extension_num: bool
    speaker_volume: int
    mic_volume: int
    resolution: int
    server_transport: int

    def __init__(self, server: Server, outbound: Outbound, use_extension_num: bool, speaker_volume: int, mic_volume: int, resolution: int, server_transport: int) -> None:
        self.server = server
        self.outbound = outbound
        self.use_extension_num = use_extension_num
        self.speaker_volume = speaker_volume
        self.mic_volume = mic_volume
        self.resolution = resolution
        self.server_transport = server_transport

    @staticmethod
    def from_dict(obj: Any) -> 'Voip':
        if not obj:
            return None
        server = Server.from_dict(obj.get("server"))
        outbound = Outbound.from_dict(obj.get("outbound"))
        use_extension_num = from_stringified_bool(from_str(obj.get("use_extension_num")))
        speaker_volume = to_int(from_str(obj.get("speakerVolume")))
        mic_volume = to_int(from_str(obj.get("micVolume")))
        resolution = to_int(from_str(obj.get("resolution")))
        server_transport = to_int(from_str(obj.get("serverTransport")))
        return Voip(server, outbound, use_extension_num, speaker_volume, mic_volume, resolution, server_transport)

    def to_dict(self) -> dict:
        result: dict = {}
        result["server"] = to_class(Server, self.server)
        result["outbound"] = to_class(Outbound, self.outbound)
        result["use_extension_num"] = from_str(str(self.use_extension_num).lower())
        result["speakerVolume"] = from_str(str(self.speaker_volume))
        result["micVolume"] = from_str(str(self.mic_volume))
        result["resolution"] = from_str(str(self.resolution))
        result["serverTransport"] = from_str(str(self.server_transport))
        return result


class ID:
    id: int

    def __init__(self, id: int) -> None:
        self.id = id

    @staticmethod
    def from_dict(obj: Any) -> 'ID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        return ID(id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        return result


class Wiegand:
    wiegand_in_out: int
    out_pulse_width: int
    out_pulse_interval: int
    enable_bypass_mode: bool
    format_id: ID
    enable_fail_code: bool
    fail_code: int
    wiegand_output_info: int
    wiegand_csn_id: ID

    def __init__(self, wiegand_in_out: int, out_pulse_width: int, out_pulse_interval: int, enable_bypass_mode: bool, format_id: ID, enable_fail_code: bool, fail_code: int, wiegand_output_info: int, wiegand_csn_id: ID) -> None:
        self.wiegand_in_out = wiegand_in_out
        self.out_pulse_width = out_pulse_width
        self.out_pulse_interval = out_pulse_interval
        self.enable_bypass_mode = enable_bypass_mode
        self.format_id = format_id
        self.enable_fail_code = enable_fail_code
        self.fail_code = fail_code
        self.wiegand_output_info = wiegand_output_info
        self.wiegand_csn_id = wiegand_csn_id

    @staticmethod
    def from_dict(obj: Any) -> 'Wiegand':
        if not obj:
            return None
        wiegand_in_out = to_int(from_str(obj.get("wiegand_in_out")))
        out_pulse_width = to_int(from_str(obj.get("out_pulse_width")))
        out_pulse_interval = to_int(from_str(obj.get("out_pulse_interval")))
        enable_bypass_mode = from_stringified_bool(from_str(obj.get("enable_bypass_mode")))
        format_id = ID.from_dict(obj.get("format_id"))
        enable_fail_code = from_stringified_bool(from_str(obj.get("enable_fail_code")))
        fail_code = to_int(from_str(obj.get("fail_code")))
        wiegand_output_info = to_int(from_str(obj.get("wiegand_output_info")))
        wiegand_csn_id = ID.from_dict(obj.get("wiegand_csn_id"))
        return Wiegand(wiegand_in_out, out_pulse_width, out_pulse_interval, enable_bypass_mode, format_id, enable_fail_code, fail_code, wiegand_output_info, wiegand_csn_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["wiegand_in_out"] = from_str(str(self.wiegand_in_out))
        result["out_pulse_width"] = from_str(str(self.out_pulse_width))
        result["out_pulse_interval"] = from_str(str(self.out_pulse_interval))
        result["enable_bypass_mode"] = from_str(str(self.enable_bypass_mode).lower())
        result["format_id"] = to_class(ID, self.format_id)
        result["enable_fail_code"] = from_str(str(self.enable_fail_code).lower())
        result["fail_code"] = from_str(str(self.fail_code))
        result["wiegand_output_info"] = from_str(str(self.wiegand_output_info))
        result["wiegand_csn_id"] = to_class(ID, self.wiegand_csn_id)
        return result


class WLAN:
    enabled: bool
    operation_mode: int
    auth_type: int
    encryption_type: int
    essid: str
    auth_key: int

    def __init__(self, enabled: bool, operation_mode: int, auth_type: int, encryption_type: int, essid: str, auth_key: int) -> None:
        self.enabled = enabled
        self.operation_mode = operation_mode
        self.auth_type = auth_type
        self.encryption_type = encryption_type
        self.essid = essid
        self.auth_key = auth_key

    @staticmethod
    def from_dict(obj: Any) -> 'WLAN':
        if not obj:
            return None
        enabled = from_stringified_bool(from_str(obj.get("enabled")))
        operation_mode = to_int(from_str(obj.get("operation_mode")))
        auth_type = to_int(from_str(obj.get("auth_type")))
        encryption_type = to_int(from_str(obj.get("encryption_type")))
        essid = from_str(obj.get("essid"))
        auth_key = to_int(from_str(obj.get("auth_key")))
        return WLAN(enabled, operation_mode, auth_type, encryption_type, essid, auth_key)

    def to_dict(self) -> dict:
        result: dict = {}
        result["enabled"] = from_str(str(self.enabled).lower())
        result["operation_mode"] = from_str(str(self.operation_mode))
        result["auth_type"] = from_str(str(self.auth_type))
        result["encryption_type"] = from_str(str(self.encryption_type))
        result["essid"] = from_str(self.essid)
        result["auth_key"] = from_str(str(self.auth_key))
        return result


class Row:
    id: int
    name: str
    device_type_id: DeviceID
    status: int
    rs485: Rs485
    device_group_id: DeviceID
    version: Version
    lan: LAN
    usb: USB
    authentication: Authentication
    fingerprint: Fingerprint
    card: Card
    display: Display
    system: System
    dst1: int
    dst2: int
    wiegand: Wiegand
    wlan: WLAN
    tna: Tna
    input: Input
    voip: Voip
    face: Face
    barcode: Barcode
    pktversion: int
    support_occupancy: bool
    rtsp: RTSP
    print_status: int
    need_to_firmware_update: bool

    def __init__(self, id: int, name: str, device_type_id: DeviceID, status: int, rs485: Rs485, device_group_id: DeviceID, version: Version, lan: LAN, usb: USB, authentication: Authentication, fingerprint: Fingerprint, card: Card, display: Display, system: System, dst1: int, dst2: int, wiegand: Wiegand, wlan: WLAN, tna: Tna, input: Input, voip: Voip, face: Face, barcode: Barcode, pktversion: int, support_occupancy: bool, rtsp: RTSP, print_status: int, need_to_firmware_update: bool) -> None:
        self.id = id
        self.name = name
        self.device_type_id = device_type_id
        self.status = status
        self.rs485 = rs485
        self.device_group_id = device_group_id
        self.version = version
        self.lan = lan
        self.usb = usb
        self.authentication = authentication
        self.fingerprint = fingerprint
        self.card = card
        self.display = display
        self.system = system
        self.dst1 = dst1
        self.dst2 = dst2
        self.wiegand = wiegand
        self.wlan = wlan
        self.tna = tna
        self.input = input
        self.voip = voip
        self.face = face
        self.barcode = barcode
        self.pktversion = pktversion
        self.support_occupancy = support_occupancy
        self.rtsp = rtsp
        self.print_status = print_status
        self.need_to_firmware_update = need_to_firmware_update

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id"))) or 0
        name = from_str(obj.get("name")) or ""
        device_type_id = DeviceID.from_dict(obj.get("device_type_id"))
        status = to_int(from_str(obj.get("status"))) or 0
        rs485 = Rs485.from_dict(obj.get("rs485"))
        device_group_id = DeviceID.from_dict(obj.get("device_group_id"))
        version = Version.from_dict(obj.get("version"))
        lan = LAN.from_dict(obj.get("lan"))
        usb = USB.from_dict(obj.get("usb"))
        authentication = Authentication.from_dict(obj.get("authentication"))
        fingerprint = Fingerprint.from_dict(obj.get("fingerprint"))
        card = Card.from_dict(obj.get("card"))
        display = Display.from_dict(obj.get("display"))
        system = System.from_dict(obj.get("system"))
        dst1 = to_int(from_str(obj.get("dst1"))) or 0
        dst2 = to_int(from_str(obj.get("dst2"))) or 0
        wiegand = Wiegand.from_dict(obj.get("wiegand"))
        wlan = WLAN.from_dict(obj.get("wlan"))
        tna = Tna.from_dict(obj.get("tna"))
        input = Input.from_dict(obj.get("input"))
        voip = Voip.from_dict(obj.get("voip"))
        face = Face.from_dict(obj.get("face"))
        barcode = Barcode.from_dict(obj.get("barcode"))
        pktversion = to_int(from_str(obj.get("pktversion"))) or 0
        support_occupancy = from_stringified_bool(from_str(obj.get("support_occupancy")))
        rtsp = RTSP.from_dict(obj.get("rtsp"))
        print_status = to_int(from_str(obj.get("print_status"))) or 0
        need_to_firmware_update = from_stringified_bool(from_str(obj.get("needToFirmwareUpdate")))
        return Row(id, name, device_type_id, status, rs485, device_group_id, version, lan, usb, authentication, fingerprint, card, display, system, dst1, dst2, wiegand, wlan, tna, input, voip, face, barcode, pktversion, support_occupancy, rtsp, print_status, need_to_firmware_update)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["device_type_id"] = to_class(DeviceID, self.device_type_id)
        result["status"] = from_str(str(self.status))
        result["rs485"] = to_class(Rs485, self.rs485)
        result["device_group_id"] = to_class(DeviceID, self.device_group_id)
        result["version"] = to_class(Version, self.version)
        result["lan"] = to_class(LAN, self.lan)
        result["usb"] = to_class(USB, self.usb)
        result["authentication"] = to_class(Authentication, self.authentication)
        result["fingerprint"] = to_class(Fingerprint, self.fingerprint)
        result["card"] = to_class(Card, self.card)
        result["display"] = to_class(Display, self.display)
        result["system"] = to_class(System, self.system)
        result["dst1"] = from_str(str(self.dst1))
        result["dst2"] = from_str(str(self.dst2))
        result["wiegand"] = to_class(Wiegand, self.wiegand)
        result["wlan"] = to_class(WLAN, self.wlan)
        result["tna"] = to_class(Tna, self.tna)
        result["input"] = to_class(Input, self.input)
        result["voip"] = to_class(Voip, self.voip)
        result["face"] = to_class(Face, self.face)
        result["barcode"] = to_class(Barcode, self.barcode)
        result["pktversion"] = from_str(str(self.pktversion))
        result["support_occupancy"] = from_str(str(self.support_occupancy).lower())
        result["rtsp"] = to_class(RTSP, self.rtsp)
        result["print_status"] = from_str(str(self.print_status))
        result["needToFirmwareUpdate"] = from_str(str(self.need_to_firmware_update).lower())
        return result


class DeviceCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceCollection':
        if not obj:
            return None
        total = to_int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows", []) or [])
        return DeviceCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


class DevicesResponse(BaseResponse):
    device_collection: DeviceCollection
    response: Response

    def __init__(self, device_collection: DeviceCollection, response: Response) -> None:
        self.device_collection = device_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'DevicesResponse':
        if not obj:
            return None
        device_collection = DeviceCollection.from_dict(obj.get("DeviceCollection"))
        response = Response.from_dict(obj.get("Response"))
        return DevicesResponse(device_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["DeviceCollection"] = to_class(DeviceCollection, self.device_collection)
        result["Response"] = to_class(Response, self.response)
        return result




class DeviceResponse(BaseResponse):
    device: Row
    response: Response

    def __init__(self, device: Row, response: Response) -> None:
        self.device = device
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceResponse':
        if not obj:
            return None
        device = Row.from_dict(obj.get("Device"))
        response = Response.from_dict(obj.get("Response"))
        return DeviceResponse(device,  response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["Device"] = to_class(Row, self.device)
        result["Response"] = to_class(Response, self.response)
        return result




def devices_response_from_dict(s: Any) -> DevicesResponse:
    return DevicesResponse.from_dict(s)


def devices_response_to_dict(x: DevicesResponse) -> Any:
    return to_class(DevicesResponse, x)

def device_response_from_dict(s: Any) -> DeviceResponse:
    return DeviceResponse.from_dict(s)


def device_response_to_dict(x: DeviceResponse) -> Any:
    return to_class(DeviceResponse, x)

