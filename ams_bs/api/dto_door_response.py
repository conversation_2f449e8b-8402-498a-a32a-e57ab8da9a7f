from .utils import *
from .dto_response import Response, BaseResponse
from typing import Any


class DeviceResponseRow(BaseResponse):
    id: int
    code: int

    def __init__(self, id: int, code: int) -> None:
        self.id = id
        self.code = code

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceResponseRow':
        assert isinstance(obj, dict)
        id = int(from_str(obj.get("id")))
        code = int(from_str(obj.get("code")))
        return DeviceResponseRow(id, code)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["code"] = from_str(str(self.code))
        return result


class DeviceResponse(BaseResponse):
    rows: List[DeviceResponseRow]
    result: bool

    def __init__(self, rows: List[DeviceResponseRow], result: bool) -> None:
        self.rows = rows
        self.result = result

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceResponse':
        assert isinstance(obj, dict)
        rows = from_list(DeviceResponseRow.from_dict, obj.get("rows"))
        result = from_stringified_bool(from_str(obj.get("result")))
        return DeviceResponse(rows, result)

    def to_dict(self) -> dict:
        result: dict = {}
        result["rows"] = from_list(lambda x: to_class(DeviceResponseRow, x), self.rows)
        result["result"] = from_str(str(self.result).lower())
        return result


class DoorCollectionRow:
    id: int
    name: str

    def __init__(self, id: int, name: str) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'DoorCollectionRow':
        assert isinstance(obj, dict)
        id = int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        return DoorCollectionRow(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        return result


class DoorCollection:
    total: int
    rows: List[DoorCollectionRow]

    def __init__(self, total: int, rows: List[DoorCollectionRow]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'DoorCollection':
        assert isinstance(obj, dict)
        total = int(from_str(obj.get("total")))
        rows = from_list(DoorCollectionRow.from_dict, obj.get("rows"))
        return DoorCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(DoorCollectionRow, x), self.rows)
        return result


class DoorCreationResponse(BaseResponse):
    door_collection: DoorCollection
    device_response: Optional[DeviceResponse]
    response: Response

    def __init__(self, door_collection: DoorCollection, device_response: Optional[DeviceResponse], response: Response) -> None:
        self.door_collection = door_collection
        self.device_response = device_response
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'DoorCreationResponse':
        assert isinstance(obj, dict)
        door_collection = DoorCollection.from_dict(obj.get("DoorCollection"))
        # Handle optional device_response
        device_response_data = obj.get("DeviceResponse")
        device_response = DeviceResponse.from_dict(device_response_data) if device_response_data else None

        response = Response.from_dict(obj.get("Response"))
        return DoorCreationResponse(door_collection, device_response, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["DoorCollection"] = to_class(DoorCollection, self.door_collection)

        # Only include DeviceResponse if not None
        if self.device_response is not None:
            result["DeviceResponse"] = to_class(DeviceResponse, self.device_response)

        result["Response"] = to_class(Response, self.response)
        return result


def door_creation_response_from_dict(s: Any) -> DoorCreationResponse:
    return DoorCreationResponse.from_dict(s)


def door_creation_response_to_dict(x: DoorCreationResponse) -> Any:
    return to_class(DoorCreationResponse, x)
