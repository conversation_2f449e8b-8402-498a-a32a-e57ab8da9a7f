from .utils import *
from .dto_response import Response, BaseResponse
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast


def from_datetime(x: Any) -> datetime:
    return dateutil.parser.parse(x)


def from_list(f: Callable[[Any], T], x: Any) -> List[T]:
    assert isinstance(x, list)
    return [f(y) for y in x]



class CardType:
    id: int
    name: str
    type: int

    def __init__(self, id: int, name: str, type: int) -> None:
        self.id = id
        self.name = name
        self.type = type

    @staticmethod
    def from_dict(obj: Any) -> 'CardType':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        type = to_int(from_str(obj.get("type")))
        return CardType(id, name, type)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["type"] = from_str(str(self.type))
        return result


class WiegandFormatID:
    id: int

    def __init__(self, id: int) -> None:
        self.id = id

    @staticmethod
    def from_dict(obj: Any) -> 'WiegandFormatID':
        if not obj:
            return None

        id = to_int(from_str(obj.get("id")))
        return WiegandFormatID(id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        return result


class Card:
    id: int
    card_id: str
    display_card_id: str
    status: int
    is_blocked: bool
    is_assigned: bool
    card_type: CardType
    mobile_card: bool
    issue_count: int
    card_slot: int
    card_mask: int
    wiegand_format_id: WiegandFormatID

    def __init__(self, id: int, card_id: str, display_card_id: str, status: int, is_blocked: bool, is_assigned: bool, card_type: CardType, mobile_card: bool, issue_count: int, card_slot: int, card_mask: int, wiegand_format_id: WiegandFormatID) -> None:
        self.id = id
        self.card_id = card_id
        self.display_card_id = display_card_id
        self.status = status
        self.is_blocked = is_blocked
        self.is_assigned = is_assigned
        self.card_type = card_type
        self.mobile_card = mobile_card
        self.issue_count = issue_count
        self.card_slot = card_slot
        self.card_mask = card_mask
        self.wiegand_format_id = wiegand_format_id

    @staticmethod
    def from_dict(obj: Any) -> 'Card':
        if not obj:
            return None

        id = to_int(from_str(obj.get("id")))
        card_id = from_str(obj.get("card_id"))
        display_card_id = from_str(obj.get("display_card_id"))
        status = to_int(from_str(obj.get("status")))
        is_blocked = from_stringified_bool(from_str(obj.get("is_blocked")))
        is_assigned = from_stringified_bool(from_str(obj.get("is_assigned")))
        card_type = CardType.from_dict(obj.get("card_type"))
        mobile_card = from_stringified_bool(from_str(obj.get("mobile_card")))
        issue_count = to_int(from_str(obj.get("issue_count")))
        card_slot = to_int(from_str(obj.get("card_slot")))
        card_mask = to_int(from_str(obj.get("card_mask")))
        wiegand_format_id = WiegandFormatID.from_dict(obj.get("wiegand_format_id"))
        return Card(id, card_id, display_card_id, status, is_blocked, is_assigned, card_type, mobile_card, issue_count, card_slot, card_mask, wiegand_format_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["card_id"] = from_str(self.card_id)
        result["display_card_id"] = from_str(self.display_card_id)
        result["status"] = from_str(str(self.status))
        result["is_blocked"] = from_str(str(self.is_blocked).lower())
        result["is_assigned"] = from_str(str(self.is_assigned).lower())
        result["card_type"] = to_class(CardType, self.card_type)
        result["mobile_card"] = from_str(str(self.mobile_card).lower())
        result["issue_count"] = from_str(str(self.issue_count))
        result["card_slot"] = from_str(str(self.card_slot))
        result["card_mask"] = from_str(str(self.card_mask))
        result["wiegand_format_id"] = to_class(WiegandFormatID, self.wiegand_format_id)
        return result


class UserGroupID:
    id: int
    name: str

    def __init__(self, id: int, name: str) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'UserGroupID':
        if not obj:
            return None

        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        return UserGroupID(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        return result


class User:
    user_id: int
    name: str
    gender: int
    email: str
    photo_exists: bool
    pin_exists: bool
    password_exists: bool
    updated_count: int
    last_modified: int
    idx_last_modified: int
    start_datetime: datetime
    expiry_datetime: datetime
    security_level: int
    display_duration: int
    display_count: int
    inherited: bool
    user_group_id: UserGroupID
    disabled: bool
    expired: bool
    idx_user_id: int
    idx_user_id_num: int
    idx_name: int
    idx_phone: int
    idx_email: int
    fingerprint_login: bool
    fingerprint_template_count: int
    face_count: int
    visual_face_count: int
    cards: List[Card]
    card_count: int
    access_groups: Optional[List[UserGroupID]]

    def __init__(self, user_id: int, name: str, gender: int, email: str, photo_exists: bool, pin_exists: bool, password_exists: bool, updated_count: int, last_modified: int, idx_last_modified: int, start_datetime: datetime, expiry_datetime: datetime, security_level: int, display_duration: int, display_count: int, inherited: bool, user_group_id: UserGroupID, disabled: bool, expired: bool, idx_user_id: int, idx_user_id_num: int, idx_name: int, idx_phone: int, idx_email: int, fingerprint_login: bool, fingerprint_template_count: int, face_count: int, visual_face_count: int, cards: List[Card], card_count: int ,  access_groups: Optional[List[UserGroupID]]) -> None:
        self.user_id = user_id
        self.name = name
        self.gender = gender
        self.email = email
        self.photo_exists = photo_exists
        self.pin_exists = pin_exists
        self.password_exists = password_exists
        self.updated_count = updated_count
        self.last_modified = last_modified
        self.idx_last_modified = idx_last_modified
        self.start_datetime = start_datetime
        self.expiry_datetime = expiry_datetime
        self.security_level = security_level
        self.display_duration = display_duration
        self.display_count = display_count
        self.inherited = inherited
        self.user_group_id = user_group_id
        self.disabled = disabled
        self.expired = expired
        self.idx_user_id = idx_user_id
        self.idx_user_id_num = idx_user_id_num
        self.idx_name = idx_name
        self.idx_phone = idx_phone
        self.idx_email = idx_email
        self.fingerprint_login = fingerprint_login
        self.fingerprint_template_count = fingerprint_template_count
        self.face_count = face_count
        self.visual_face_count = visual_face_count
        self.cards = cards
        self.card_count = card_count
        self.access_groups = access_groups

    @staticmethod
    def from_dict(obj: Any) -> 'User':
        if not obj:
            return None

        user_id = to_int(from_str(obj.get("user_id")))
        name = from_str(obj.get("name"))
        gender = to_int(from_str(obj.get("gender")))
        email = from_str(obj.get("email"))
        photo_exists = from_stringified_bool(from_str(obj.get("photo_exists")))
        pin_exists = from_stringified_bool(from_str(obj.get("pin_exists")))
        password_exists = from_stringified_bool(from_str(obj.get("password_exists")))
        updated_count = to_int(from_str(obj.get("updated_count")))
        last_modified = to_int(from_str(obj.get("last_modified")))
        idx_last_modified = to_int(from_str(obj.get("idx_last_modified")))
        start_datetime = from_datetime(obj.get("start_datetime"))
        expiry_datetime = from_datetime(obj.get("expiry_datetime"))
        security_level = to_int(from_str(obj.get("security_level")))
        display_duration = to_int(from_str(obj.get("display_duration")))
        display_count = to_int(from_str(obj.get("display_count")))
        inherited = from_stringified_bool(from_str(obj.get("inherited")))
        user_group_id = UserGroupID.from_dict(obj.get("user_group_id"))
        disabled = from_stringified_bool(from_str(obj.get("disabled")))
        expired = from_stringified_bool(from_str(obj.get("expired")))
        idx_user_id = to_int(from_str(obj.get("idx_user_id")))
        idx_user_id_num = to_int(from_str(obj.get("idx_user_id_num")))
        idx_name = to_int(from_str(obj.get("idx_name")))
        idx_phone = to_int(from_str(obj.get("idx_phone")))
        idx_email = to_int(from_str(obj.get("idx_email")))
        fingerprint_login = from_stringified_bool(from_str(obj.get("fingerprint_login")))
        fingerprint_template_count = to_int(from_str(obj.get("fingerprint_template_count")))
        face_count = to_int(from_str(obj.get("face_count")))
        visual_face_count = to_int(from_str(obj.get("visual_face_count")))
        cards = from_list(Card.from_dict, obj.get("cards")) if obj.get("cards") is not None else []
        card_count = to_int(from_str(obj.get("card_count"))) if obj.get("card_count") is not None else 0
        access_groups = from_list(UserGroupID.from_dict, obj.get("access_groups")) if obj.get("access_groups") is not None else []

        return User(user_id, name, gender, email, photo_exists, pin_exists, password_exists, updated_count, last_modified, idx_last_modified, start_datetime, expiry_datetime, security_level, display_duration, display_count, inherited, user_group_id, disabled, expired, idx_user_id, idx_user_id_num, idx_name, idx_phone, idx_email, fingerprint_login, fingerprint_template_count, face_count, visual_face_count, cards, card_count , access_groups)

    def to_dict(self) -> dict:
        result: dict = {}
        result["user_id"] = from_str(str(self.user_id))
        result["name"] = from_str(self.name)
        result["gender"] = from_str(str(self.gender))
        result["email"] = from_str(self.email)
        result["photo_exists"] = from_str(str(self.photo_exists).lower())
        result["pin_exists"] = from_str(str(self.pin_exists).lower())
        result["password_exists"] = from_str(str(self.password_exists).lower())
        result["updated_count"] = from_str(str(self.updated_count))
        result["last_modified"] = from_str(str(self.last_modified))
        result["idx_last_modified"] = from_str(str(self.idx_last_modified))
        result["start_datetime"] = self.start_datetime.isoformat()
        result["expiry_datetime"] = self.expiry_datetime.isoformat()
        result["security_level"] = from_str(str(self.security_level))
        result["display_duration"] = from_str(str(self.display_duration))
        result["display_count"] = from_str(str(self.display_count))
        result["inherited"] = from_str(str(self.inherited).lower())
        result["user_group_id"] = to_class(UserGroupID, self.user_group_id)
        result["disabled"] = from_str(str(self.disabled).lower())
        result["expired"] = from_str(str(self.expired).lower())
        result["idx_user_id"] = from_str(str(self.idx_user_id))
        result["idx_user_id_num"] = from_str(str(self.idx_user_id_num))
        result["idx_name"] = from_str(str(self.idx_name))
        result["idx_phone"] = from_str(str(self.idx_phone))
        result["idx_email"] = from_str(str(self.idx_email))
        result["fingerprint_login"] = from_str(str(self.fingerprint_login).lower())
        result["fingerprint_template_count"] = from_str(str(self.fingerprint_template_count))
        result["face_count"] = from_str(str(self.face_count))
        result["visual_face_count"] = from_str(str(self.visual_face_count))
        result["cards"] = from_list(lambda x: to_class(Card, x), self.cards)
        result["card_count"] = from_str(str(self.card_count))
        result["access_groups"] = from_list(lambda x: to_class(UserGroupID, x), self.access_groups)
        return result


class UserDetailsResponse(BaseResponse):
    user: User
    response: Response

    def __init__(self, user: User, response: Response) -> None:
        self.user = user
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'UserDetailsResponse':
        if not obj:
            return None

        user = User.from_dict(obj.get("User"))
        response = Response.from_dict(obj.get("Response"))
        return UserDetailsResponse(user, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["User"] = to_class(User, self.user)
        result["Response"] = to_class(Response, self.response)
        return result


def user_details_response_from_dict(s: Any) -> UserDetailsResponse:
    return UserDetailsResponse.from_dict(s)


def user_details_response_to_dict(x: UserDetailsResponse) -> Any:
    return to_class(UserDetailsResponse, x)
