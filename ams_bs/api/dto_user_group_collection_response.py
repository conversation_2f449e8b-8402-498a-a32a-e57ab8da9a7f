from .utils import *
from .dto_response import Response, BaseResponse
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast, Optional, List


class ParentID:
    id: int

    def __init__(self, id: int) -> None:
        self.id = id

    @staticmethod
    def from_dict(obj: Any) -> 'ParentID':
        assert isinstance(obj, dict)
        id = from_int(obj.get("id"))
        return ParentID(id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_int(self.id)
        return result


class Row:
    id: int
    name: str
    depth: int
    user_count: int
    parent_id: ParentID
    description: Optional[str]

    def __init__(self, id: int, name: str, depth: int, user_count: int, parent_id: ParentID, description: Optional[str] = None) -> None:
        self.id = id
        self.name = name
        self.depth = depth
        self.user_count = user_count
        self.parent_id = parent_id
        self.description = description

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        assert isinstance(obj, dict)
        id = from_int(obj.get("id"))
        name = from_str(obj.get("name"))
        depth = from_int(obj.get("depth"))
        user_count = from_int(obj.get("user_count"))
        parent_id = ParentID.from_dict(obj.get("parent_id"))
        description = from_str(obj.get("description")) if obj.get("description") is not None else None
        return Row(id, name, depth, user_count, parent_id, description)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_int(self.id)
        result["name"] = from_str(self.name)
        result["depth"] = from_int(self.depth)
        result["user_count"] = from_int(self.user_count)
        result["parent_id"] = to_class(ParentID, self.parent_id)
        if self.description is not None:
            result["description"] = from_str(self.description)
        return result


class UserGroupCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'UserGroupCollection':
        assert isinstance(obj, dict)
        total = from_int(obj.get("total"))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return UserGroupCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_int(self.total)
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


class UserGroupCollectionResponse(BaseResponse):
    user_group_collection: UserGroupCollection
    response: Response

    def __init__(self, user_group_collection: UserGroupCollection, response: Response) -> None:
        self.user_group_collection = user_group_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'UserGroupCollectionResponse':
        assert isinstance(obj, dict)
        user_group_collection = UserGroupCollection.from_dict(obj.get("UserGroupCollection"))
        response = Response.from_dict(obj.get("Response"))
        return UserGroupCollectionResponse(user_group_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["UserGroupCollection"] = to_class(UserGroupCollection, self.user_group_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def user_group_collection_response_from_dict(s: Any) -> UserGroupCollectionResponse:
    return UserGroupCollectionResponse.from_dict(s)


def user_group_collection_response_to_dict(x: UserGroupCollectionResponse) -> Any:
    return to_class(UserGroupCollectionResponse, x)