from .utils import *
from .dto_response import Response , BaseResponse
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast,Optional


class Name(Enum):
    ALL_USERS = "All Users"
    MAIN_AC_GROUP = "Main AC Group"


class UserGroupID:
    id: int
    name: Name

    def __init__(self, id: int, name: Name) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'UserGroupID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = obj.get("name")
        return UserGroupID(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = to_enum(Name, self.name)
        return result


class Filter:
    user_group: List[int]
    device_group: List[int]
    door_group: List[int]
    elevator_group: List[int]
    zone_type: List[int]
    access_group: List[int]
    graphic_map_group: List[int]

    def __init__(self, user_group: List[int], device_group: List[int], door_group: List[int], elevator_group: List[int], zone_type: List[int], access_group: List[int], graphic_map_group: List[int]) -> None:
        self.user_group = user_group
        self.device_group = device_group
        self.door_group = door_group
        self.elevator_group = elevator_group
        self.zone_type = zone_type
        self.access_group = access_group
        self.graphic_map_group = graphic_map_group

    @staticmethod
    def from_dict(obj: Any) -> 'Filter':
        if not obj:
            return None
        user_group = from_list(lambda x: to_int(from_str(x)), obj.get("UserGroup"))
        device_group = from_list(lambda x: to_int(from_str(x)), obj.get("DeviceGroup"))
        door_group = from_list(lambda x: to_int(from_str(x)), obj.get("DoorGroup"))
        elevator_group = from_list(lambda x: to_int(from_str(x)), obj.get("ElevatorGroup"))
        zone_type = from_list(lambda x: to_int(from_str(x)), obj.get("ZoneType"))
        access_group = from_list(lambda x: to_int(from_str(x)), obj.get("AccessGroup"))
        graphic_map_group = from_list(lambda x: to_int(from_str(x)), obj.get("GraphicMapGroup"))
        return Filter(user_group, device_group, door_group, elevator_group, zone_type, access_group, graphic_map_group)

    def to_dict(self) -> dict:
        result: dict = {}
        result["UserGroup"] = from_list(lambda x: from_str((lambda x: str(x))(x)), self.user_group)
        result["DeviceGroup"] = from_list(lambda x: from_str((lambda x: str(x))(x)), self.device_group)
        result["DoorGroup"] = from_list(lambda x: from_str((lambda x: str(x))(x)), self.door_group)
        result["ElevatorGroup"] = from_list(lambda x: from_str((lambda x: str(x))(x)), self.elevator_group)
        result["ZoneType"] = from_list(lambda x: from_str((lambda x: str(x))(x)), self.zone_type)
        result["AccessGroup"] = from_list(lambda x: from_str((lambda x: str(x))(x)), self.access_group)
        result["GraphicMapGroup"] = from_list(lambda x: from_str((lambda x: str(x))(x)), self.graphic_map_group)
        return result


class Module:
    read: bool
    write: bool

    def __init__(self, read: bool, write: bool) -> None:
        self.read = read
        self.write = write

    @staticmethod
    def from_dict(obj: Any) -> 'Module':
        if not obj:
            return None
        read = from_stringified_bool(from_str(obj.get("read")))
        write = from_stringified_bool(from_str(obj.get("write")))
        return Module(read, write)

    def to_dict(self) -> dict:
        result: dict = {}
        result["read"] = from_str(str(self.read).lower())
        result["write"] = from_str(str(self.write).lower())
        return result


class Permission:
    id: int
    name: str
    description: str
    filter: Filter
    module: Dict[str, Module]

    def __init__(self, id: int, name: str, description: str, filter: Filter, module: Dict[str, Module]) -> None:
        self.id = id
        self.name = name
        self.description = description
        self.filter = filter
        self.module = module

    @staticmethod
    def from_dict(obj: Any) -> 'Permission':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        description = from_str(obj.get("description"))
        filter = Filter.from_dict(obj.get("filter"))
        module = from_dict(Module.from_dict, obj.get("module"))
        return Permission(id, name, description, filter, module)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["description"] = from_str(self.description)
        result["filter"] = to_class(Filter, self.filter)
        result["module"] = from_dict(lambda x: to_class(Module, x), self.module)
        return result


class PrivateOperationMode:
    operation_method: int
    index: int
    operation_mode: int
    exclude: bool

    def __init__(self, operation_method: int, index: int, operation_mode: int, exclude: bool) -> None:
        self.operation_method = operation_method
        self.index = index
        self.operation_mode = operation_mode
        self.exclude = exclude

    @staticmethod
    def from_dict(obj: Any) -> 'PrivateOperationMode':
        if not obj:
            return None
        operation_method = to_int(from_str(obj.get("operation_method")))
        index = to_int(from_str(obj.get("index")))
        operation_mode = to_int(from_str(obj.get("operation_mode")))
        exclude = from_stringified_bool(from_str(obj.get("exclude")))
        return PrivateOperationMode(operation_method, index, operation_mode, exclude)

    def to_dict(self) -> dict:
        result: dict = {}
        result["operation_method"] = from_str(str(self.operation_method))
        result["index"] = from_str(str(self.index))
        result["operation_mode"] = from_str(str(self.operation_mode))
        result["exclude"] = from_str(str(self.exclude).lower())
        return result


@dataclass
class FingerprintTemplate:
    finger_index: int
    finger_mask: bool
    template0: str
    template1: str
    template_image0: str
    template_image1: str

    @staticmethod
    def from_dict(obj: Any) -> 'FingerprintTemplate':
        assert isinstance(obj, dict)
        finger_index = int(from_str(obj.get("finger_index")))
        finger_mask = from_stringified_bool(from_str(obj.get("finger_mask")))
        template0 = from_str(obj.get("template0"))
        template1 = from_str(obj.get("template1"))
        template_image0 = from_str(obj.get("template_image0"))
        template_image1 = from_str(obj.get("template_image1"))
        return FingerprintTemplate(finger_index, finger_mask, template0, template1, template_image0, template_image1)

    def to_dict(self) -> dict:
        result: dict = {}
        result["finger_index"] = from_str(str(self.finger_index))
        result["finger_mask"] = from_str(str(self.finger_mask).lower())
        result["template0"] = from_str(self.template0)
        result["template1"] = from_str(self.template1)
        result["template_image0"] = from_str(self.template_image0)
        result["template_image1"] = from_str(self.template_image1)
        return result

class Row:
    user_id: int
    name: str
    gender: int
    birthday: Optional[datetime]
    photo_exists: bool
    pin_exists: bool
    login_id: Optional[str]
    password_exists: bool
    updated_count: int
    last_modified: int
    idx_last_modified: int
    start_datetime: datetime
    expiry_datetime: datetime
    security_level: int
    display_duration: int
    display_count: int
    permission: Optional[Permission]
    inherited: bool
    user_group_id: UserGroupID
    disabled: bool
    expired: bool
    idx_user_id: int
    idx_user_id_num: int
    idx_name: int
    idx_phone: int
    idx_email: int
    fingerprint_template_count: int
    face_count: int
    visual_face_count: int
    card_count: int
    fingerprint_templates: Optional[List[FingerprintTemplate]]
    access_groups_in_user_group: List[UserGroupID]
    private_operation_modes: Optional[List[PrivateOperationMode]]
    access_groups: Optional[List[UserGroupID]]
    email: Optional[str]

    def __init__(self, user_id: int, name: str, gender: int, birthday: Optional[datetime], photo_exists: bool, pin_exists: bool, login_id: Optional[str], password_exists: bool, updated_count: int, last_modified: int, idx_last_modified: int, start_datetime: datetime, expiry_datetime: datetime, security_level: int, display_duration: int, display_count: int, permission: Optional[Permission], inherited: bool, user_group_id: UserGroupID, disabled: bool, expired: bool, idx_user_id: int, idx_user_id_num: int, idx_name: int, idx_phone: int, idx_email: int, fingerprint_template_count: int, face_count: int, visual_face_count: int, card_count: int, fingerprint_templates: Optional[List[FingerprintTemplate]],access_groups_in_user_group: List[UserGroupID], private_operation_modes: Optional[List[PrivateOperationMode]], access_groups: Optional[List[UserGroupID] ], email: Optional[str]) -> None:
        self.user_id = user_id
        self.name = name
        self.gender = gender
        self.birthday = birthday
        self.photo_exists = photo_exists
        self.pin_exists = pin_exists
        self.login_id = login_id
        self.password_exists = password_exists
        self.updated_count = updated_count
        self.last_modified = last_modified
        self.idx_last_modified = idx_last_modified
        self.start_datetime = start_datetime
        self.expiry_datetime = expiry_datetime
        self.security_level = security_level
        self.display_duration = display_duration
        self.display_count = display_count
        self.permission = permission
        self.inherited = inherited
        self.user_group_id = user_group_id
        self.disabled = disabled
        self.expired = expired
        self.idx_user_id = idx_user_id
        self.idx_user_id_num = idx_user_id_num
        self.idx_name = idx_name
        self.idx_phone = idx_phone
        self.idx_email = idx_email
        self.fingerprint_template_count = fingerprint_template_count
        self.face_count = face_count
        self.visual_face_count = visual_face_count
        self.card_count = card_count
        self.fingerprint_templates = fingerprint_templates
        self.access_groups_in_user_group = access_groups_in_user_group
        self.private_operation_modes = private_operation_modes
        self.access_groups = access_groups
        self.email = email

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        user_id = to_int(from_str(obj.get("user_id")))
        name = from_str(obj.get("name"))
        gender = to_int(from_str(obj.get("gender")))
        birthday = from_union([from_datetime, from_none], obj.get("birthday"))
        photo_exists = from_stringified_bool(from_str(obj.get("photo_exists")))
        pin_exists = from_stringified_bool(from_str(obj.get("pin_exists")))
        login_id = from_union([from_str, from_none], obj.get("login_id"))
        password_exists = from_stringified_bool(from_str(obj.get("password_exists")))
        updated_count = to_int(from_str(obj.get("updated_count")))
        last_modified = to_int(from_str(obj.get("last_modified")))
        idx_last_modified = to_int(from_str(obj.get("idx_last_modified")))
        start_datetime = from_datetime(obj.get("start_datetime"))
        expiry_datetime = from_datetime(obj.get("expiry_datetime"))
        security_level = to_int(from_str(obj.get("security_level")))
        display_duration = to_int(from_str(obj.get("display_duration")))
        display_count = to_int(from_str(obj.get("display_count")))
        permission = from_union([Permission.from_dict, from_none], obj.get("permission")) if obj.get("permission") is not None else {}
        inherited = from_stringified_bool(from_str(obj.get("inherited")))
        user_group_id = UserGroupID.from_dict(obj.get("user_group_id"))
        disabled = from_stringified_bool(from_str(obj.get("disabled")))
        expired = from_stringified_bool(from_str(obj.get("expired")))
        idx_user_id = to_int(from_str(obj.get("idx_user_id")))
        idx_user_id_num = to_int(from_str(obj.get("idx_user_id_num")))
        idx_name = to_int(from_str(obj.get("idx_name")))
        idx_phone = to_int(from_str(obj.get("idx_phone")))
        idx_email = to_int(from_str(obj.get("idx_email")))
        fingerprint_template_count = to_int(from_str(obj.get("fingerprint_template_count")))
        face_count = to_int(from_str(obj.get("face_count")))
        visual_face_count = to_int(from_str(obj.get("visual_face_count")))
        card_count = to_int(from_str(obj.get("card_count")))
        fingerprint_templates = from_union([lambda x: from_list(FingerprintTemplate.from_dict, x), from_none], obj.get("fingerprint_templates")) if obj.get("fingerprint_templates") else []
        access_groups_in_user_group = from_list(UserGroupID.from_dict, obj.get("access_groups_in_user_group")) if obj.get("access_groups_in_user_group") else []
        private_operation_modes = from_union([lambda x: from_list(PrivateOperationMode.from_dict, x), from_none], obj.get("private_operation_modes"))
        access_groups = from_union([lambda x: from_list(UserGroupID.from_dict, x), from_none], obj.get("access_groups")) if obj.get("access_groups") else []
        email = from_union([from_str, from_none], obj.get("email"))
        return Row(user_id, name, gender, birthday, photo_exists, pin_exists, login_id, password_exists, updated_count, last_modified, idx_last_modified, start_datetime, expiry_datetime, security_level, display_duration, display_count, permission, inherited, user_group_id, disabled, expired, idx_user_id, idx_user_id_num, idx_name, idx_phone, idx_email, fingerprint_template_count, face_count, visual_face_count, card_count,  fingerprint_templates, access_groups_in_user_group, private_operation_modes, access_groups , email)

    def to_dict(self) -> dict:
        result: dict = {}
        result["user_id"] = from_str(str(self.user_id))
        result["name"] = from_str(self.name)
        result["gender"] = from_str(str(self.gender))
        if self.birthday is not None:
            result["birthday"] = from_union([lambda x: x.isoformat(), from_none], self.birthday)
        result["photo_exists"] = from_str(str(self.photo_exists).lower())
        result["pin_exists"] = from_str(str(self.pin_exists).lower())
        if self.login_id is not None:
            result["login_id"] = from_union([from_str, from_none], self.login_id)
        result["password_exists"] = from_str(str(self.password_exists).lower())
        result["updated_count"] = from_str(str(self.updated_count))
        result["last_modified"] = from_str(str(self.last_modified))
        result["idx_last_modified"] = from_str(str(self.idx_last_modified))
        result["start_datetime"] = self.start_datetime.isoformat()
        result["expiry_datetime"] = self.expiry_datetime.isoformat()
        result["security_level"] = from_str(str(self.security_level))
        result["display_duration"] = from_str(str(self.display_duration))
        result["display_count"] = from_str(str(self.display_count))
        if self.permission is not None:
            result["permission"] = from_union([lambda x: to_class(Permission, x), from_none], self.permission)
        result["inherited"] = from_str(str(self.inherited).lower())
        result["user_group_id"] = to_class(UserGroupID, self.user_group_id)
        result["disabled"] = from_str(str(self.disabled).lower())
        result["expired"] = from_str(str(self.expired).lower())
        result["idx_user_id"] = from_str(str(self.idx_user_id))
        result["idx_user_id_num"] = from_str(str(self.idx_user_id_num))
        result["idx_name"] = from_str(str(self.idx_name))
        result["idx_phone"] = from_str(str(self.idx_phone))
        result["idx_email"] = from_str(str(self.idx_email))
        result["fingerprint_template_count"] = from_str(str(self.fingerprint_template_count))
        result["face_count"] = from_str(str(self.face_count))
        result["visual_face_count"] = from_str(str(self.visual_face_count))
        result["card_count"] = from_str(str(self.card_count))
        if self.fingerprint_templates is not None:
            result["fingerprint_templates"] = from_union([lambda x: from_list(lambda x: to_class(FingerprintTemplate, x), x), from_none], self.fingerprint_templates)
        result["access_groups_in_user_group"] = from_list(lambda x: to_class(UserGroupID, x), self.access_groups_in_user_group)
        if self.private_operation_modes is not None:
            result["private_operation_modes"] = from_union([lambda x: from_list(lambda x: to_class(PrivateOperationMode, x), x), from_none], self.private_operation_modes)
        if self.access_groups is not None:
            result["access_groups"] = from_union([lambda x: from_list(lambda x: to_class(UserGroupID, x), x), from_none], self.access_groups)
        if self.email is not None:
            result["email"] = from_union([from_str, from_none], self.email)
        return result


class UserCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'UserCollection':
        if not obj:
            return None
        total = to_int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return UserCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


class UsersResponse(BaseResponse):
    user_collection: UserCollection
    response: Response

    def __init__(self, user_collection: UserCollection, response: Response) -> None:
        self.user_collection = user_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'UsersResponse':
        if not obj:
            return None
        user_collection = UserCollection.from_dict(obj.get("UserCollection"))
        response = Response.from_dict(obj.get("Response"))
        return UsersResponse(user_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["UserCollection"] = to_class(UserCollection, self.user_collection)
        result["Response"] = to_class(Response, self.response)
        return result


class UserResponse(BaseResponse):
    user: Row
    response: Response

    def __init__(self, user: Row, response: Response) -> None:
        self.user = user
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'UserResponse':
        if not obj:
            return None
        user = Row.from_dict(obj.get("User"))
        response = Response.from_dict(obj.get("Response"))
        return UserResponse(user, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["User"] = to_class(Row, self.user)
        result["Response"] = to_class(Response, self.response)
        return result


def users_response_from_dict(s: Any) -> UsersResponse:
    return UsersResponse.from_dict(s)


def users_response_to_dict(x: UsersResponse) -> Any:
    return to_class(UsersResponse, x)



def user_response_from_dict(s: Any) -> UserResponse:
    return UserResponse.from_dict(s)


def user_response_to_dict(x: UserResponse) -> Any:
    return to_class(UserResponse, x)