{"3352": "REC_FINISHED_FAIL", "3354": "REC_FINISHED_FAIL_NO_RETRY", "3462": "SNP_SHOT_FINISHED_FAIL", "3464": "SNP_SHOT_FINISHED_FAIL_NO_RETRY", "4001": "MAXIMUM_INVALID_LOGIN_ATTEMPTS", "4091": "VISUAL_FACE_SYNC_ERROR", "4094": "ABNORMAL_DEVICE", "4095": "DEVICE_DISCONNECT", "4096": "VERIFY_SUCCESS", "4097": "VERIFY_SUCCESS_ID_PIN", "4098": "VERIFY_SUCCESS_ID_FINGERPRINT", "4099": "VERIFY_SUCCESS_ID_FINGERPRINT_PIN", "4100": "VERIFY_SUCCESS_ID_FACE", "4101": "VERIFY_SUCCESS_ID_FACE_PIN", "4102": "VERIFY_SUCCESS_CARD", "4103": "VERIFY_SUCCESS_CARD_PIN", "4104": "VERIFY_SUCCESS_CARD_FINGERPRINT", "4105": "VERIFY_SUCCESS_CARD_FINGERPRINT_PIN", "4106": "VERIFY_SUCCESS_CARD_FACE", "4107": "VERIFY_SUCCESS_CARD_FACE_PIN", "4112": "VERIFY_SUCCESS_CARD_FACE_FINGER", "4113": "VERIFY_SUCCESS_CARD_FINGER_FACE", "4114": "VERIFY_SUCCESS_ID_FACE_FINGER", "4115": "VERIFY_SUCCESS_ID_FINGER_FACE", "4118": "VERIFY_SUCCESS_MOBLIE_CARD", "4119": "VERIFY_SUCCESS_MOBILE_CARD_PIN", "4120": "VERIFY_SUCCESS_MOBILE_CARD_FINGER", "4121": "VERIFY_SUCCESS_MOBILE_CARD_FINGER_PIN", "4122": "VERIFY_SUCCESS_MOBILE_CARD_FACE", "4123": "VERIFY_SUCCESS_MOBiLE_CARD_FACE_PIN", "4128": "VERIFY_SUCCESS_MOBILE_CARD_FACE_FINGER", "4129": "VERIFY_SUCCESS_MOBILE_CARD_FINGER_FACE", "4133": "VERIFY_SUCCESS_QR", "4134": "VERIFY_SUCCESS_QR_PIN", "4135": "VERIFY_SUCCESS_QR_FINGERPRINT", "4136": "VERIFY_SUCCESS_QR_FINGERPRINT_PIN", "4137": "VERIFY_SUCCESS_QR_FACE", "4138": "VERIFY_SUCCESS_QR_FACE_PIN", "4139": "VERIFY_SUCCESS_QR_FACE_FINGERPRINT", "4140": "VERIFY_SUCCESS_QR_FINGERPRINT_FACE", "4352": "VERIFY_FAIL", "4353": "VERIFY_FAIL_ID", "4354": "VERIFY_FAIL_CARD", "4355": "VERIFY_FAIL_PIN", "4356": "VERIFY_FAIL_FINGERPRINT", "4357": "VERIFY_FAIL_FACE", "4360": "VERIFY_FAIL_MOBILE_CARD", "4361": "VERIFY_FAIL_INVALID_QR", "4362": "VERIFY_FAIL_NON_PRINTABLE_QR", "4363": "VERIFY_FAIL_TOO_LONG_QR", "4364": "VERIFY_FAIL_CREDENTIAL_QR", "4608": "VERIFY_DURESS", "4610": "VERIFY_DURESS_ID_FINGERPRINT", "4611": "VERIFY_DURESS_ID_FINGERPRINT_PIN", "4616": "VERIFY_DURESS_CARD_FINGERPRINT", "4617": "VERIFY_DURESS_CARD_FINGERPRINT_PIN", "4624": "VERIFY_DURESS_CARD_FACE_FINGER", "4625": "VERIFY_DURESS_CARD_FINGER_FACE", "4626": "VERIFY_DURESS_ID_FACE_FINGER", "4627": "VERIFY_DURESS_ID_FINGER_FACE", "4632": "VERIFY_DURESS_MOBILE_CARD_FINGER", "4633": "VERIFY_DURESS_MOBiLE_CARD_FINGER_PIN", "4640": "VERIFY_DURESS_MOBILE_CARD_FACE_FINGER", "4641": "VERIFY_DURESS_MOBILE_CARD_FINGER_FACE", "4647": "VERIFY_DURESS_QR_FINGERPRINT", "4648": "VERIFY_DURESS_QR_FINGERPRINT_PIN", "4651": "VERIFY_DURESS_QR_FACE_FINGERPRINT", "4652": "VERIFY_DURESS_QR_FINGERPRINT_FACE", "4864": "IDENTIFY_SUCCESS", "4865": "IDENTIFY_SUCCESS_FINGERPRINT", "4866": "IDENTIFY_SUCCESS_FINGERPRINT_PIN", "4867": "IDENTIFY_SUCCESS_FACE", "4868": "IDENTIFY_SUCCESS_FACE_PIN", "4869": "IDENTIFY_SUCCESS_FACE_FINGER", "4870": "IDENTIFY_SUCCESS_FACE_FINGER_PIN", "4871": "IDENTIFY_SUCCESS_FINGER_FACE", "4872": "IDENTIFY_SUCCESS_FINGER_FACE_PIN", "5120": "IDENTIFY_FAIL", "5123": "IDENTIFY_FAIL_PIN", "5124": "IDENTIFY_FAIL_FINGERPRINT", "5125": "IDENTIFY_FAIL_FACE", "5376": "IDENTIFY_DURESS", "5377": "IDENTIFY_DURESS_FINGERPRINT", "5378": "IDENTIFY_DURESS_FINGERPRINT_PIN", "5381": "IDENTIFY_DURESS_FACE_FINGER", "5382": "IDENTIFY_DURESS_FACE_FINGER_PIN", "5383": "IDENTIFY_DURESS_FINGER_FACE", "5384": "IDENTIFY_DURESS_FINGER_FACE_PIN", "5632": "DUAL_AUTH_SUCCESS", "5888": "DUAL_AUTH_FAIL", "5889": "DUAL_AUTH_FAIL_TIMEOUT", "5890": "DUAL_AUTH_FAIL_ACCESS_GROUP", "6144": "AUTH_FAILED", "6145": "AUTH_FAILED_INVALID_AUTH_MODE", "6146": "AUTH_FAILED_INVALID_CREDENTIAL", "6147": "AUTH_FAILED_TIMEOUT", "6148": "AUTH_FAILED_MATCHING_REFUSAL", "6400": "ACCESS_DENIED", "6401": "ACCESS_DENIED_ACCESS_GROUP", "6402": "ACCESS_DENIED_DISABLED", "6403": "ACCESS_DENIED_EXPIRED", "6404": "ACCESS_DENIED_ON_BLACKLIST", "6405": "ACCESS_DENIED_APB", "6406": "ACCESS_DENIED_TIMED_APB", "6407": "ACCESS_DENIED_FORCED_LOCK_SCHEDULE", "6408": "ACCESS_EXCUSED_SOFT_APB", "6409": "ACCESS_EXCUSED_SOFT_TIMED_APB", "6410": "ACCESS_DEVICE_FAILED_TO_FACE_DETECT", "6411": "ACCESS_DENIED_FAILED_TO_CAMERA_CAPTURE", "6412": "FAKE_FINGERPRINT_DETECTED", "6414": "ACCESS_DENIED_INTRUSION_ALARM", "6415": "ACCESS_DENIED_INTERLOCK_ALARM", "6418": "ACCESS_DENIED_ANTI_TAILGATING_DEVICE", "6419": "ACCESS_DENIED_HIGH_TEMPERATURE", "6420": "ACCESS_DENIED_NONE_TEMPERATURE", "6421": "ACCESS_DENIED_UNMASK_DETECT", "6422": "BS2_EVENT_ZONE_OCCP_ACCESS_DENIED_VIOLATION", "6656": "AUTH_FAILED_TO_BAD_FINGERPRINT_PLACEMENT", "6912": "BYPASS_SUCCESS_NO_VIOLATION", "6913": "BYPASS_SUCCESS_THERMAL_ONLY", "6914": "BYPASS_SUCCESS_MASK_ONLY", "6915": "BYPASS_SUCCESS_MASK_AND_THERMAL", "7168": "BYPASS_FAIL_HIGH_TEMPERATURE", "7169": "BYPASS_FAIL_NONE_TEMPERATURE", "7170": "BYPASS_FAIL_UNMASK_DETECT", "7424": "ABNORMAL_FEVER_DETECT_HIGH_TEMPERATURE", "7425": "ABNORMAL_FEVER_DETECT_NONE_TEMPERATURE", "7680": "UNMASKED_FACE_DETECT", "8192": "ENROLL_SUCCESS", "8448": "ENROLL_FAIL", "8449": "ENROLL_FAIL(VISUAL FACE)", "8450": "ENROLL_FAIL_MISMATCHED_FORMAT", "8451": "ENROLL_FAIL_FULL_CREDENTIAL", "8457": "ENROLL_FAIL_USER_DATA_ERROR", "8704": "UPDATE_SUCCESS", "8960": "UPDATE_FAIL", "8961": "UPDATE_FAIL(VISUAL FACE)", "8962": "UPDATE_FAIL_MISMATCHED_FORMAT", "8969": "UPDATE_FAIL_USER_DATA_ERROR", "9216": "DELETE_SUCCESS", "9472": "DELETE_FAIL", "9728": "DELETE_ALL_SUCCESS", "10240": "DUPLICATE_CREDENTIAL", "10242": "DUPLICATE_CARD", "10244": "DUPLICATE_FINGERPRINT", "10245": "DUPLICATE_FACE", "10496": "PARTIAL_UPDATE_SUCCESS", "10752": "PARTIAL_UPDATE_FAIL", "10753": "PARTIAL_UPDATE_FAIL_INVALID_FACE", "10754": "PARTIAL_UPDATE_FAIL_MISMATCHED_FORMAT", "10755": "PARTIAL_UPDATE_FAIL_FULL_CREDENTIAL", "10756": "PARTIAL_UPDATE_FAIL_INVALID_USER", "10761": "PARTIAL_UPDATE_FAIL_USER_DATA_ERROR", "12288": "SYSTEM_RESET", "12368": "BS2_EVENT_DEVICE_SYSTEM_ERROR_OPENGL ", "12544": "SYSTEM_STARTED", "12800": "TIME_SET", "12801": "TIME_SET_TIME_ZONE", "12802": "DST_APPLIED", "13056": "LINK_CONNECTED", "13312": "LINK_DISCONNECTED", "13568": "DHCP_SUCCESS", "13824": "ADMIN_MENU", "13825": "BS2_EVENT_DEVICE_ADMIN_LOGIN_FAIL", "14080": "UI_LOCKED", "14336": "UI_UNLOCKED", "14592": "COMM_LOCKED", "14848": "COMM_UNLOCKED", "15104": "TCP_CONNECTED", "15120": "RTSP_CONNECTED", "15360": "TCP_DISCONNECTED", "15376": "RTSP_DISCONNECTED", "15616": "RS485_CONNECTED", "15872": "RS485_DISCONNECTED", "16128": "INPUT_DETECTED", "16384": "TAMPER_ON", "16640": "TAMPER_OFF", "16896": "EVENT_LOG_CLEARED", "17152": "FIRMWARE_UPGRADED", "17408": "RESOURCE_UPGRADED", "17664": "CONFIG_RESET", "17665": "DATABASE_RESET", "17666": "FACTORY_RESET", "17920": "SUPERVISED_INPUT_SHORT", "18176": "SUPERVISED_INPUT_OPEN", "18432": "AC_FAIL", "18688": "AC_SUCCESS", "18944": "BS2_EVENT_EXIT_BUTTON", "18945": "BS2_EVENT_SIMULATED_EXIT_BUTTON", "19200": "BS2_EVENT_OPERATOR_OPEN", "19456": "DOOR_UNLOCKED_BY_VOIP", "19712": "BS2_EVENT_LICENSE_ENABLE_SUCESS", "19713": "BS2_EVENT_LICENSE_ENABLE_FAIL", "19714": "BS2_EVENT_LICENSE_DISABLE_SUCESS", "19715": "BS2_EVENT_LICENSE_DISABLE_FAIL", "19716": "BS2_EVENT_LICENSE_EXPIRED", "19969": "UZ_BATTERY_LV_NORMAL", "19970": "UZ_BATTERY_LV_LOW", "19971": "UZ_BATTERY_LV_LOWEST", "20480": "UNLOCKED", "20736": "LOCKED", "20992": "OPEN", "21248": "CLOSE", "21504": "FORCED_OPEN", "21760": "HELD_OPEN", "22016": "FORCED_OPEN_ALARM", "22272": "FORCED_OPEN_ALARM_CLEAR", "22528": "HELD_OPEN_ALARM", "22784": "HELD_OPEN_ALARM_CLEAR", "23040": "DOOR_APB_ALARM", "23296": "DOOR_APB_ALARM_CLEAR", "23553": "RELEASE_DOOR_BY_SCHEDULE", "23554": "RELEASE_DOOR_BY_EMERGENCY", "23556": "RELEASE_DOOR_BY_OPERATOR", "23809": "LOCK_DOOR_BY_SCHEDULE", "23810": "LOCK_DOOR_BY_EMERGENCY", "23812": "LOCK_DOOR_BY_OPERATOR", "24065": "UNLOCK_DOOR_BY_SCHEDULE", "24066": "UNLOCK_DOOR_BY_EMERGENCY", "24068": "UNLOCK_DOOR_BY_OPERATOR", "24576": "APB_VIOLATION", "24577": "APB_VIOLATION_HARD", "24578": "APB_VIOLATION_SOFT", "24832": "APB_ALARM", "25088": "APB_ALARM_CLEAR", "25344": "TIMED_APB_VIOLATION", "25345": "TIMED_APB_VIOLATION_HARD", "25346": "TIMED_APB_VIOLATION_SOFT", "25600": "TIMED_APB_ALARM", "25856": "TIMED_APB_ALARM_CLEAR", "26112": "FIRE_ALARM_INPUT", "26368": "FIRE_ALARM", "26624": "FIRE_ALARM_CLEAR", "26880": "FORCED_LOCK_VIOLATION", "27136": "FORCED_LOCK_START", "27392": "FORCED_LOCK_END", "27648": "FORCED_UNLOCK_START", "27904": "FORCED_UNLOCK_END", "28160": "FORCED_LOCK_ALARM", "28416": "FORCED_LOCK_ALARM_CLEAR", "28417": "BS2_EVENT_ZONE_OCCP_FULL_DETECTED", "28418": "BS2_EVENT_ZONE_OCCP_AVAILABILITY_RECOVERED", "28419": "BS2_EVENT_ZONE_OCCP_EXIT_OCCURRED_COUNT_ZERO", "28420": "BS2_EVENT_ZONE_OCCP_ALMOST_FULL_STEP1", "28421": "BS2_EVENT_ZONE_OCCP_ALMOST_FULL_STEP2", "28672": "ELEVATOR_ACTIVATED", "28928": "ELEVATOR_DEACTIVATED", "29185": "RELEASE_ELEVATOR_BY_SCHEDULE", "29186": "RELEASE_ELEVATOR_BY_EMERGENCY", "29188": "RELEASE_ELEVATOR_BY_OPERATOR", "29192": "RELEASE_ELEVATOR_ALARM", "29441": "ACTIVATE_ELEVATOR_BY_SCHEDULE", "29442": "ACTIVATE_ELEVATOR_BY_EMERGENCY", "29444": "ACTIVATE_ELEVATOR_BY_OPERATOR", "29448": "ACTIVATE_ELEVATOR_ALARM", "29697": "DEACTIVATE_ELEVATOR_BY_SCHEDULE", "29698": "DEACTIVATE_ELEVATOR_BY_EMERGENCY", "29700": "DEACTIVATE_ELEVATOR_BY_OPERATOR", "29952": "ELEVATOR_ALARM_INPUT", "30208": "ELEVATOR_ALARM", "30464": "ELEVATOR_ALARM_CLEAR", "30720": "ALL_FLOOR_ACTIVATED", "30976": "ALL_FLOOR_DEACTIVATED", "36864": "ZONE_INTRUSION_ALARM_VIOLATION", "37120": "ZONE_INTRUSION_ALARM_ARM_GRANTED", "37376": "ZONE_INTRUSION_ALARM_ARM_SUCCESS", "37632": "ZONE_INTRUSION_ALARM_ARM_FAIL", "37888": "ZONE_INTRUSION_ALARM_DISARM_ARM_GRANTED", "38144": "ZONE_INTRUSION_ALARM_DISARM_ARM_SUCCESS", "38912": "ZONE_INTRUSION_ALARM", "39168": "ZONE_INTRUSION_ALARM_CLEAR", "39424": "ZONE_INTRUSION_ALARM_ARM_DENIED", "39680": "ZONE_INTRUSION_ALARM_DISARM_DENIED", "40960": "ZONE_INTERLOCK_VIOLATION", "40961": "ZONE_INTERLOCK_VIOLATION_DOOR_OPEN", "40962": "ZONE_INTERLOCK_VIOLATION_INPUT_DETECT", "41216": "ZONE_INTERLOCK_ALARM", "41472": "ZONE_INTERLOCK_ALARM_DOOR_OPEN_DENIED", "41728": "ZONE_INTERLOCK_ALARM_INDOOR_DENIED", "41984": "ZONE_INTERLOCK_ALARM_CLEAR", "42753": "BS2_EVENT_ZONE_OCCP_ACCESS_DENIED", "42754": "BS2_EVENT_ZONE_OCCP_ACCESS_DENIED_NET_FAILURE", "45056": "ZONE_MUSTER_VIOLATION", "45312": "ZONE_MUSTER_ALARM", "45568": "ZONE_MUSTER_ALARM_CLEAR", "47104": "ZONE_LIFT_UNLOCK_START", "47360": "ZONE_LIFT_UNLOCK_END", "49152": "DEVICE_USER_SYNC_TO_SERVER_FAIL", "49920": "RELAY_ACTIVATED", "50176": "RELAY_DEACTIVATE", "50177": "BACKUP_STARTED", "50179": "BACKUP_COMPLETE", "50181": "BACKUP_FAILED", "53248": "TOM_FACE_ENROLL_SUCCESS", "53504": "TOM_FACE_ENROLL_FAIL", "53505": "TOM_FACE_ENROLL_FAIL_SCAN_CANCELED", "53506": "TOM_FACE_ENROLL_FAIL_SCAN_TIMEOUT", "53507": "TOM_FACE_ENROLL_FAIL_WARP_ERROR", "53508": "TOM_FACE_ENROLL_FAIL_WRITE_ERROR", "53513": "TOM_FACE_ENROLL_FAIL_INTERNAL_ERROR", "53760": "TOM_FINGER_ENROLL_SUCCESS", "54016": "TOM_FINGER_ENROLL_FAIL", "54017": "TOM_FINGER_ENROLL_FAIL_SCAN_CANCELED", "54018": "TOM_FINGER_ENROLL_FAIL_SCAN_TIMEOUT", "54019": "TOM_FINGER_ENROLL_FAIL_WARP_ERROR", "54020": "TOM_FINGER_ENROLL_FAIL_WRITE_ERROR", "54025": "TOM_FINGER_ENROLL_FAIL_INTERNAL_ERROR", "54500": "QUICK_ACTION_ACTIVATED"}