# -*- coding: utf-8 -*-
from odoo import api, fields, models
from odoo.addons.ams_bs.api.dto_users_response import *
from odoo.addons.ams_bs.api.dto_users_response import UserResponse

import logging

_logger = logging.getLogger("Biostar-APIClient")


class BaseBiostarAPIClient(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.biostar_api_client'
    _inherit = 'ams_base.biostar_api_client'
    _description = 'Biostar API Client'

    # endregion

    # region ---------------------- TODO[IMP]: Properties ----------------------------------
    @property
    def logger(self):
        return _logger

    @property
    def token_key(self):
        return 'ams_bs.session_id'

    @property
    def base_url(self):
        base_url = self.env.company.bs_base_url

        return base_url

    @property
    def api_user(self):
        user = self.env.company.api_username

        return user

    @property
    def api_password(self):
        password = self.env.company.api_password

        return password

    @property
    def token(self):
        saved_token = self.env.company.api_token  # or self.env['ir.config_parameter'].sudo().get_param(self.token_key)
        # get diff now and last time token was saved if > 1 hour call login again
        if self.env.company.api_token_date:
            date_diff = datetime.now() - self.env.company.api_token_date
            if date_diff.total_seconds() > 1:
                saved_token = None
        else:
            saved_token = None

        if not saved_token:
            self.login(self.base_url, self.api_user, self.api_password)
            saved_token = self.env.company.api_token or self.env['ir.config_parameter'].sudo().get_param(self.token_key)

        return saved_token

    def get_default_headers(self, **kwargs):
        """return default headers and combined kwargs"""
        headers = {
            "Content-Type": "application/json",
            "bs-session-id": f"{self.token}"
        }
        # loop kwargs to update headers dictionary
        for key, value in kwargs.items():
            headers[key] = value

        return headers

    # endregion
    # region ---------------------- TODO[IMP]: API Methods -------------------------------------

    def login(self, base_url: str, username: str, password: str) -> UserResponse | Response | Any:
        """ Login to the Biostar API and retrieve user details."""
        base_url = base_url or self.base_url
        url = f"{base_url}/api/login"
        payload = {
            "User": {
                "login_id": username,
                "password": password
            }
        }

        try:
            response = self.session.post(url, json=payload, headers={"Content-Type": "application/json"})
            response_data = response.json()
            bs_token = response.headers.get("bs-session-id")
            if self.env.company:
                self.env.company.api_token = bs_token
                self.env.company.api_token_date = datetime.now()

            self.env['ir.config_parameter'].sudo().set_param(self.token_key, bs_token)

            if response_data:
                return user_response_from_dict(response_data)
            else:
                return Response(code=500, link="", message="Response Empty")

        except Exception as e:
            self.logger.error(f"An error occurred during login: {e}")
            return Response(code=500, link="", message=str(e))

    # endregion

    # region ---------------------- TODO[IMP]: Helper Methods -------------------------------------
    def is_success_response(self, response_result):
        code = -1
        if isinstance(response_result, BaseResponse):
            code = response_result.response.code
            if code != 0:
                return False
        elif isinstance(response_result, Response):
            code = response_result.code
            if code != 0:
                return False
        if code == 0:
            return True

    def get_response(self, response_result):
        if isinstance(response_result, BaseResponse):
            return response_result.response
        elif isinstance(response_result, Response):
            return response_result

    def get_json_response(self, response,direct_response=False):
        """handle http response data and return Response Object in case found error"""
        try:
            if 500 <= response.status_code < 600:
                try:
                    # Check for specific device connection message
                    response_data = response.json()
                    msg = response_data.get("Response", {}).get("message", "")
                    if msg == "Device is not connected to biostar":
                        return Response(code=response.status_code, link="", message=msg)
                except Exception:
                    pass  # fall through to default error

                return Response(code=response.status_code, link="", message="Can't connect to access control server")

            response_data = response.json()

            if response.status_code != 200 or direct_response:
                response_result = response_data.get("Response", {})
                record_id = self.extract_main_object_id(response_data) if direct_response else 0
                return Response(code=int(response_result.get("code", response.status_code)), link="",
                                message=response_result.get("message", response.reason),id=record_id)

            if not response_data:
                return Response(code=5000, link="", message="Response Empty")

            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred in  get_json_response : {e}")
            return Response(code=5000, link="", message=str(e))

    def extract_main_object_id(self, response):
        """
        Extracts the 'id' of the main object from the API response.
        Gives priority to 'rows[0]' if available, then falls back to top-level dicts.
        """
        main_object_id = 0
        main_object_size = 0

        for key, value in response.items():
            if isinstance(value, dict):
                # Step 1: Check inside 'rows' first
                if "rows" in value and isinstance(value["rows"], list) and value["rows"]:
                    first_row = value["rows"][0]
                    if isinstance(first_row, dict) and "id" in first_row:
                        row_size = len(first_row)
                        if row_size > main_object_size:
                            main_object_id = first_row["id"]
                            main_object_size = row_size

                # Step 2: Check the outer dict (if it has id directly)
                elif "id" in value:
                    dict_size = len(value)
                    if dict_size > main_object_size:
                        main_object_id = value["id"]
                        main_object_size = dict_size

        return main_object_id

    # endregion
