# -*- coding: utf-8 -*-
from odoo.addons.ams_bs.api.dto_access_groups_response import *
from odoo.addons.ams_bs.api.dto_access_levels_response import *
from odoo.addons.ams_bs.api.dto_doors_response import *
from odoo.addons.ams_bs.api.dto_door_response import *
from odoo.addons.ams_bs.api.dto_schedules_response import *
from odoo.addons.ams_bs.api.dto_door_groups_response import *
from odoo import api, fields, models


def get_headers(session_id: str) -> dict:
    return {
        'bs-session-id': f"{session_id}",
        'Content-Type': 'application/json',
    }


class BiostarACAPIClient(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.biostar_ac_api_client'
    _inherit = 'ams_base.biostar_api_client'
    _description = 'Biostar AC API Client'

    # endregion

    # region ---------------------- TODO[IMP]: API Methods -------------------------------------

    # region ---------------------- TODO[IMP]:AC-Door Methods -------------------------------------
    def get_ac_doors(self, base_url: str, limit: int = 0, order_by: str = "id:true") -> DoorsResponse | Response | Any:
        """
        view Doors which is recorded on BioStar 2 database.

        Args:
            base_url: The base URL of the API.
            limit: Limit the number of records according to the value specified.
            order_by: Sort the records based on the id, toggle true/false to switch ascending/descending.
        Raises:
            ValueError: if the request fails.
            requests.HTTPError: For HTTP-related issues.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/doors?limit={limit}&order_by={order_by}"
        try:
            response = self.session.get(url, headers=self.get_default_headers())
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return doors_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_ac_doors: {e}")
            return Response(code=500, link="", message=str(e))

    def get_ac_door(self, base_url: str, door_id: str) -> DoorResponse | Response | Any:
        """
        view one Door details recorded on BioStar 2 Database.

        Args:
            base_url: The base URL of the API.
            door_id: The ID of the Door.g
        Raises:
            ValueError: if the request fails.
            requests.HTTPError: For HTTP-related issues.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/doors/{door_id}"

        try:
            response = self.session.get(url, headers=self.get_default_headers())
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return door_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_ac_door: {e}")
            return Response(code=500, link="", message=str(e))

    def create_ac_door(self, base_url: str, payload: dict) -> DoorCreationResponse | Response | Any:
        """
        Create a new door in BioStar 2.

        Args:
            base_url: The base URL of the API
            payload: Dictionary containing door creation data

        Returns:
            DoorCreationResponse if successful, Response if error
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/doors"
        headers = self.get_default_headers()

        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            return door_creation_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during create_ac_door: {e}")
            return Response(code=500, link="", message=str(e))

    def update_ac_door(self, base_url: str, door_id: str, payload: dict) -> DoorCreationResponse | Response | Any:
        """
        Update an existing door in BioStar 2.

        Args:
            base_url: The base URL of the API
            door_id: ID of the door to update
            payload: Dictionary containing door update data

        Returns:
            DoorCreationResponse if successful, Response if error
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/doors/{door_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.put(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            return Response.from_dict(response_data.get("Response", {}))
        except Exception as e:
            self.logger.error(f"An error occurred during update_ac_door: {e}")
            return Response(code=500, link="", message=str(e))

    def delete_ac_door(self, base_url: str, door_id: str, **kwargs) -> Response | Any:
        """
        Delete a door or multiple doors from BioStar 2 Database.

        Args:
            base_url: The base URL of the API.
            door_id: The ID of the door(s). Multiple IDs should be space-separated.

        Returns:
            Response object with deletion status.
        """
        base_url = base_url or self.base_url
        # url = f"{base_url}/api/doors/{door_id}"
        url = f"{base_url}/api/doors?id={door_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.delete(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return Response.from_dict(response_data.get("Response", {}))

        except Exception as e:
            self.logger.error(f"An error occurred during delete_ac_door request: {e}")
            return Response(code=500, link="", message=str(e))

    #endregion

    # region ---------------------- TODO[IMP]:Door Group Methods -------------------------------------
    def get_door_groups(self, base_url: str) -> DoorGroupsResponse | Response | Any:
        """
        view all Access Groups (AG) recorded on BioStar 2 Database.
        There's no parameters, so you can't change the result.
        By default, the result will be sorted ascending by id.

        Args:
            base_url: The base URL of the API.
        Raises:
            ValueError: if the request fails.
            requests.HTTPError: For HTTP-related issues.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/v2/door_groups/search"

        payload = {
            "limit": 0,
            "offset": 0
        }

        try:
            response = self.session.post(url, headers=self.get_default_headers(), json=payload)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return door_groups_response_from_dic(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_ac_groups: {e}")
            return Response(code=500, link="", message=str(e))

    def create_door_group(self, base_url: str, payload: dict) -> Response | Any:
        """
        Create a new door group in BioStar
        Args:
            base_url: The base URL of the API
            payload: Dictionary containing door group creation data
        Returns:
            DoorGroupCreationResponse if successful, Response if error
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/door_groups"
        headers = self.get_default_headers()

        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data
        except Exception as e:
            self.logger.error(f"An error occurred during create_door_group: {e}")
            return Response(code=500, link="", message=str(e))

    def update_door_group(self, base_url: str, door_group_id: str, payload: dict) -> Response | Any:
        """
        Update an existing door group in BioStar
        Args:
            base_url: The base URL of the API
            door_group_id: ID of the door group to update
            payload: Dictionary containing door group update data
        Returns:
            Response object
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/door_groups/{door_group_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.put(url, json=payload, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data
        except Exception as e:
            self.logger.error(f"An error occurred during update_door_group: {e}")
            return Response(code=500, link="", message=str(e))

    def delete_door_group(self, base_url: str, door_group_id: str, **kwargs) -> Response | Any:
        """
        Delete a door group or multiple door groups from BioStar 2 Database.

        Args:
            base_url: The base URL of the API.
            door_group_id: The ID of the door group(s) as a string. Multiple IDs should be space-separated.
                          Example: "4" for single door group or "4 3" for multiple door groups.

        Returns:
            Response object with deletion status.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/door_groups?id={door_group_id}"  # Using query parameter format
        headers = self.get_default_headers()

        try:
            response = self.session.delete(url, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data

        except Exception as e:
            self.logger.error( f"An error occurred during delete_door_group request: {e}")
            return Response(code=500, link="", message=str(e))


    #endregion
    # region ---------------------- TODO[IMP]:Scheduler Methods -------------------------------------
    def get_ac_schedules(self, base_url: str, limit: int = 0, offset: int = 0) -> SchedulesResponse | Response | Any:
        """
        view all schedules recorded on BioStar 2 Database.

        Args:
            base_url: The base URL of the API.
            limit: Limit the number of records according to the value specified.
            offset: Offset the response shown by n records.
        Raises:
            ValueError: if the request fails.
            requests.HTTPError: For HTTP-related issues.

        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/schedules?limit={limit}&offset={offset}"

        try:
            response = self.session.get(url, headers=self.get_default_headers())
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return schedules_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_ac_schedules: {e}")
            return Response(code=500, link="", message=str(e))

    def get_ac_schedule(self, base_url: str, schedule_id: str) -> ScheduleResponse | Response | Any:
        """
        view one Access Schedule (AS) recorded on BioStar 2 Database.

        Args:
            base_url: The base URL of the API.
            schedule_id: The ID of the AS.

        Raises:
            ValueError: if the request fails.
            requests.HTTPError: For HTTP-related issues.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/schedules/{schedule_id}"

        try:
            response = self.session.get(url, headers=self.get_default_headers())
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return schedule_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_ac_schedule: {e}")
            return Response(code=500, link="", message=str(e))

    def create_schedule(self, base_url: str, payload: dict) -> Response | Any:
        """
        Create a new schedule in BioStar system

        Args:
            base_url: The base URL of the API
            schedule_vals: Dictionary containing schedule values

        Returns:
            ScheduleResponse or Response object
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/schedules"
        headers = self.get_default_headers()

        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during create_schedule request: {e}")
            return Response(code=500, link="", message=str(e))

    def update_schedule(self, base_url: str, schedule_id: str,
                        schedule_vals: dict) -> Response | Any:
        """
        Update an existing schedule in BioStar system

        Args:
            base_url: The base URL of the API
            schedule_id: ID of the schedule to update
            schedule_vals: Dictionary containing schedule values

        Returns:
            ScheduleResponse or Response object
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/schedules/{schedule_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.put(url, json=schedule_vals, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during update_schedule request: {e}")
            return Response(code=500, link="", message=str(e))

    def delete_schedule(self, base_url: str, schedule_id: str, **kwargs) -> Response | Any:
        """
        Delete a schedule from BioStar system.

        Args:
            base_url: The base URL of the API.
            schedule_id: The ID of the schedule to delete.

        Returns:
            Response object with deletion status
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/schedules?id={schedule_id}"
        headers = self.get_default_headers()
        try:
            response = self.session.delete(url, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during delete_schedule: {e}")
            return Response(code=500, link="", message=str(e))

    #endregion

    # region ---------------------- TODO[IMP]:Ac-Group Methods -------------------------------------
    def get_ac_groups(self, base_url: str) -> AccessGroupsResponse | Response | Any:
        """
        view all Access Groups (AG) recorded on BioStar 2 Database.
        There's no parameters, so you can't change the result.
        By default, the result will be sorted ascending by id.

        Args:
            base_url: The base URL of the API.
        Raises:
            ValueError: if the request fails.
            requests.HTTPError: For HTTP-related issues.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/access_groups"

        try:
            response = self.session.get(url, headers=self.get_default_headers())
            response_data = self.get_json_response(response)  #
            if isinstance(response_data, Response):
                return response_data
            else:
                return access_groups_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_ac_groups: {e}")
            return Response(code=500, link="", message=str(e))

    def get_ac_group(self, base_url: str, group_id: str) -> AccessGroupResponse | Response | Any:
        """
        view one Access Group (AG) recorded on BioStar 2 Database.

        Args:
            base_url: The base URL of the API.
            group_id: The ID of the AG.

        Raises:
            ValueError: if the request fails.
            requests.HTTPError: For HTTP-related issues.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/access_groups/{group_id}"

        try:
            response = self.session.get(url, headers=self.get_default_headers())
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return access_group_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_ac_group: {e}")
            return Response(code=500, link="", message=str(e))

    def create_access_group(self, base_url: str, payload: dict) -> Response | Any:
        base_url = base_url or self.base_url
        url = f"{base_url}/api/access_groups"
        headers = self.get_default_headers()
        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during create_access_group request: {e}")
            return Response(code=500, link="", message=str(e))

    def update_access_group(self, base_url: str, access_group_id: int, payload: dict) -> Response | Any:
        base_url = base_url or self.base_url
        url = f"{base_url}/api/access_groups/{access_group_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.put(url, json=payload, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data
        except Exception as e:
            self.logger.error(f"An error occurred during update_access_group request: {e}")
            return Response(code=500, link="", message=str(e))

    def delete_access_group(self, base_url: str, group_id: int, **kwargs)-> Response | Any:

        base_url = base_url or self.base_url
        url = f"{base_url}/api/access_groups/{group_id}"
        headers = self.get_default_headers()
        try:
            response = self.session.delete(url, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data
        except Exception as e:
            self.logger.error(f"An error occurred during delete_access_group request: {e}")
            return Response(code=500, link="", message=str(e))

    #endregion
    # region ---------------------- TODO[IMP]:Ac-level Methods -------------------------------------

    def get_ac_levels(self, base_url: str,
                      filter_object: str = "",
                      limit: int = 0,
                      offset: int = 0,
                      select_all_yn: str = "true",
                      order_by: str = "id:true",
                      query: str = "",
                      total: str = "") -> AccessLevelsResponse | Response | Any:

        """
        view all Access Level (AL) recorded on BioStar 2 Database.

        Args:
            base_url: The base URL of the API.
            filter_object: Filter the records according to the value specified.
            limit: Limit the number of records according to the value specified.
            offset: Offset the response shown by n records.
            select_all_yn: Select all records.
            order_by: Sort the records based on the id, toggle true/false to switch asceding/descending.
            query: Query the records according to the value specified.
            total: Total the records according to the value specified.

        Raises:
            ValueError: if the request fails.
            requests.HTTPError: For HTTP-related issues.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/access_levels?limit={filter_object}&limit={limit}&offset={offset}&SelectAllYN={select_all_yn}&order_by={order_by}&query={query}&total={total}"

        try:
            response = self.session.get(url, headers=self.get_default_headers())
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return access_levels_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_ac_levels: {e}")
            return Response(code=500, link="", message=str(e))


    def create_access_level(self, base_url: str, payload: dict) -> Response| Any:
        """
        Create a new access level in BioStar 2.
        
        Args:
            base_url: The base URL of the API
            payload: Dictionary containing access level creation data with multiple door schedules
        
        Returns:
            AccessLevelCreationResponse if successful, Response if error
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/access_levels"
        headers = self.get_default_headers()
        
        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response,direct_response=True)

            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during create_access_level: {e}")
            return Response(code=500, link="", message=str(e))

    def update_access_level(self, base_url: str, access_level_id: str, payload: dict) -> Response | Any:
        """
        Update an existing access level in BioStar 2.
        
        Args:
            base_url: The base URL of the API
            access_level_id: ID of the access level to update
            payload: Dictionary containing access level update data with multiple door schedules
        
        Returns:
            Response object with update status
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/access_levels/{access_level_id}"
        headers = self.get_default_headers()
        
        try:
            response = self.session.put(url, json=payload, headers=headers)
            response_data = self.get_json_response(response,direct_response=True)

            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during update_access_level: {e}")
            return Response(code=500, link="", message=str(e))

    def delete_access_level(self, base_url: str, access_level_id: str) -> Response | Any:
        """
        Delete an access level from BioStar 2.
        
        Args:
            base_url: The base URL of the API
            access_level_id: ID of the access level to delete
        
        Returns:
            AccessControlDeletionResponse if successful, Response if error
        """
        # access_level_id = int(access_level_id)
        base_url = base_url or self.base_url
        url = f"{base_url}/api/access_levels/{access_level_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.delete(url, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during delete_access_level: {e}")
            return Response(code=500, link="", message=str(e))

   # endregion

# endregion
# region ---------------------- TODO[IMP]: Helper Methods -------------------------------------

# endregion
