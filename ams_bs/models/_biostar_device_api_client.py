# -*- coding: utf-8 -*-
from odoo import api, fields, models
from odoo.addons.ams_bs.api.dto_device_type_response import DeviceTypesResponse, device_types_dto_from_dict
from odoo.addons.ams_bs.api.dto_events_response import *
from odoo.addons.ams_bs.api.dto_devices_response import *
from odoo.addons.ams_bs.api.dto_devices_response import DevicesResponse
from odoo.addons.ams_bs.api.dto_device_group_response import *
from odoo.addons.ams_bs.api.dto_device_group_response import DeviceGroupsResponse
from odoo.addons.ams_bs.api.dto_scan_card_response import *
from odoo.addons.ams_bs.api.dto_scan_fingerprint_response import *
from odoo.addons.ams_bs.api.dto_scan_face_response import *


class BiostarDeviceAPIClient(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.biostar_device_api_client'
    _inherit = 'ams_base.biostar_api_client'
    _description = 'Biostar Device API Client'

    # endregion

    # region ---------------------- TODO[IMP]: API Methods -------------------------------------
    # region ---------------------- TODO[IMP]: Device Methods -------------------------------------

    def get_device_types(self , base_url: str, **kwargs) -> DeviceTypesResponse | Response | Any:
        base_url = base_url or self.base_url
        url = f"{base_url}/api/device_types"
        headers = self.get_default_headers()

        try:
            response = self.session.get(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data  # return error response
            else:
                return device_types_dto_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during view_device_types request: {e}")
            return Response(code=500, link="", message=str(e))

    def get_devices(self, base_url: str, **kwargs) -> DevicesResponse | Response | Any:
        """
        Fetch the list of devices using the stored authentication token.

        Args:
            base_url: The base URL of the API.
            **kwargs: Additional headers for the request.

        Returns:
            A dictionary containing the list of devices.

        Raises:
            requests.HTTPError: For HTTP-related issues.
            ValueError: If the response format is unexpected.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/devices"
        headers = self.get_default_headers()

        try:
            response = self.session.get(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data  # return error response
            else:
                return devices_response_from_dict(response_data)

        except Exception as e:
            self.logger.error(f"An error occurred during view_devices request: {e}")
            return Response(code=500, link="", message=str(e))

    def get_device(self, base_url: str, device_id: str, **kwargs) -> DeviceResponse | Response | Any:
        """
        Fetch the details of a specific device using the stored authentication token.

        Args:
            base_url: The base URL of the API.
            device_id: The ID of the device to fetch information for.
            **kwargs: Additional headers for the request.

        Returns:
            A dictionary containing the details of the device.

        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/devices/{device_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.get(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data  # return error response
            else:
                return device_response_from_dict(response_data)

        except Exception as e:
            self.logger.error(f"An error occurred during get_device_info request: {e}")
            return Response(code=5000, link="", message=str(e))

    def create_device(self, base_url: str, payload: dict) ->  Response | Any:
        """
        Create a new device in BioStar system

        Args:
            base_url: The base URL of the API
            payload: Dictionary containing device values

        Returns:
            DeviceResponse or Response object
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/devices"
        headers = self.get_default_headers()

        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during create_device request: {e}")
            return Response(code=500, link="", message=str(e))

    def update_device(self, base_url: str, device_id: str, payload: dict) -> Response | Any:
        """
        Update an existing device in BioStar system

        Args:
            base_url: The base URL of the API
            device_id: ID of the device to update
            payload: Dictionary containing device values

        Returns:
            DeviceResponse or Response object
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/devices/{device_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.put(url, json=payload, headers=headers)
            response_data = self.get_json_response(response, direct_response=True)
            return response_data
        except Exception as e:
            self.logger.error(f"An error occurred during update_device request: {e}")
            return Response(code=500, link="", message=str(e))

    def delete_device(self, base_url: str, device_id: str, **kwargs) -> Response | Any:
        """
        Delete a specific device using id.
        Args:
            base_url: The base URL of the API.
            device_id: The ID of the specific device or multiple devices separated by '+'.
            **kwargs: Additional headers or parameters for the request.

        Returns:
            return dto Response

        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/devices?id={device_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.delete(url, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during delete device: {e}")
            return Response(code=500, link="", message=str(e))

    #endregion
    # region ---------------------- TODO[IMP]: Device group Methods -------------------------------------
    def get_device_groups(self, base_url: str, **kwargs) -> DeviceGroupsResponse | Response | Any:
        """
        Fetch the list of device groups using the stored authentication token.

        Args:
            base_url: The base URL of the API.
            **kwargs: Additional headers for the request.

        Returns:
            A dictionary containing the list of device groups.

        Raises:
            requests.HTTPError: For HTTP-related issues.
            ValueError: If the response format is unexpected.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/v2/device_groups/search"
        headers = self.get_default_headers()
        payload = {
            "order_by": "depth:false"

        }

        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data  # return error response
            else:
                return device_groups_response_from_dict(response_data)


        except Exception as e:
            self.logger.error(f"An error occurred during view_device_groups request: {e}")
            return Response(code=500, link="", message=str(e))

    def create_device_group(self, base_url: str, payload: dict) ->  Response | Any:
        """
        Create a new device group in BioStar system

        Args:
            base_url: The base URL of the API
            payload: Dictionary containing device group values

        Returns:
            DeviceGroupCreationResponse or Response object
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/device_groups"
        headers = self.get_default_headers()

        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during create_device_group request: {e}")
            return Response(code=500, link="", message=str(e))

    def delete_device_group(self, base_url: str, group_id: str) -> Response | Any:
        """
        Delete a device group from BioStar system

        Args:
            base_url: The base URL of the API
            group_id: ID of the device group to delete

        Returns:
            Response object with success or error information
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/device_groups/{group_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.delete(url, headers=headers)
            response_data = self.get_json_response(response , direct_response=True)
            return response_data

        except Exception as e:
            self.logger.error(f"An error occurred during delete_device_group request: {e}")
            return Response(code=500, link="", message=str(e))

    #endregion
    # region ---------------------- TODO[IMP]:Device Auth modes and events Methods -------------------------------------

    def scan_card(self, base_url: str, device_id: str, **kwargs) -> ScanCardResponse | Response | Any:

        base_url = base_url or self.base_url
        url = f"{base_url}/api/devices/{device_id}/scan_card"
        headers = self.get_default_headers()

        try:
            response = self.session.post(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data  # return error response
            else:
                return scan_card_response_from_dict(response_data)

        except Exception as e:
            self.logger.error(f"An error occurred during scan card request: {e}")
            return Response(code=5000, link="", message=str(e))

    def scan_fingerprint(self, base_url: str, device_id: str,enroll_quality: str, **kwargs) -> ScanFingerprintResponse | Response | Any:

        base_url = base_url or self.base_url
        url = f"{base_url}/api/devices/{device_id}/scan_fingerprint"
        headers = self.get_default_headers()

        payload = {
            "ScanFingerprintOption": {
                "enroll_quality": enroll_quality,
                "raw_image": False
            }
        }

        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data  # return error response
            else:
                return scan_fingerprint_response_from_dict(response_data)

        except Exception as e:
            self.logger.error(f"An error occurred during scan fingerprint request: {e}")
            return Response(code=5000, link="", message=str(e))

    def scan_face(self, base_url: str, device_id: str, pose_sensitivity: int = 4,
                  **kwargs) -> ScanFaceResponse | Response | Any:
        base_url = base_url or self.base_url
        url = f"{base_url}/api/devices/{device_id}/credentials/face?pose_sensitivity={pose_sensitivity}"
        headers = self.get_default_headers()

        try:
            # Sending a GET request
            response = self.session.get(url, headers=headers)
            response_data = self.get_json_response(response)

            if isinstance(response_data, Response):
                return response_data  # Return error response
            else:
                return scan_face_response_from_dict(response_data)

        except Exception as e:
            self.logger.error(f"An error occurred during scan face request: {e}")
            return Response(code=5000, link="", message=str(e))
    def get_events(self, base_url: str, payload, **kwargs) -> EventsResponse | Response | Any:

        """
        Fetch the list of events using the stored authentication token.

        Args:
            base_url: The base URL of the API.
            **kwargs: Additional headers for the request.

        Returns:
            A dictionary containing the list of events.

        Raises:
            requests.HTTPError: For HTTP-related issues.
            ValueError: If the response format is unexpected.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/events/search"
        headers = self.get_default_headers()
        # payload = self.get_events_payload()

        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data  # return error response
            else:
                return events_response_from_dict(response_data)

        except Exception as e:
            self.logger.error(f"An error occurred during view_devices request: {e}")
            return Response(code=500, link="", message=str(e))

    #endregion



    # endregion
