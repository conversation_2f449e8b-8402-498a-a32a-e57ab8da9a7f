# -*- coding: utf-8 -*-
from odoo import api, fields, models
from odoo.addons.ams_bs.api.dto_user_group_response import *
from odoo.addons.ams_bs.api.dto_user_group_collection_response import *
from odoo.addons.ams_bs.api.dto_card_types_response import *
from odoo.addons.ams_bs.api.dto_cards_response import *
from odoo.addons.ams_bs.api.dto_card_response import *
from odoo.addons.ams_bs.api.dto_users_response import *
from odoo.addons.ams_bs.api.dto_user_creation_response import *
from odoo.addons.ams_bs.api.dto_user_group_creation_response import *
import json


class BiostarUserAPIClient(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.biostar_user_api_client'
    _inherit = 'ams_base.biostar_api_client'
    _description = 'Biostar User API Client'

    # endregion
    # region ---------------------- TODO[IMP]: API Methods -------------------------------------
    # region ---------------------- TODO[IMP]: User API Methods -------------------------------------

    def get_users(self, base_url: str, group_id: int = 1, limit: int = 0, offset: int = 0,
                  order_by: str = "user_id:false", last_modified: int = 0, **kwargs) -> UsersResponse | Response | Any:
        """
        Fetch the list of users using the stored authentication token.

        Args:
            base_url: The base URL of the API.
            group_id: ID of the user group to filter.
            limit: Limit the number of users returned (0 for no limit).
            offset: Offset for pagination.
            order_by: Ordering for the response (default is 'user_id:false').
            last_modified: Timestamp for fetching users modified after this value.
            **kwargs: Additional headers for the request.

        Returns:
            A dictionary containing the list of users.

        Raises:
            requests.HTTPError: For HTTP-related issues.
            ValueError: If the response format is unexpected.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/users?group_id={group_id}&limit={limit}&offset={offset}&order_by={order_by}&last_modified={last_modified}"
        headers = self.get_default_headers()

        try:
            response = self.session.get(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return users_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_users request: {e}")
            return Response(code=500, link="", message=str(e))

    def get_user(self, base_url: str, user_id: int, **kwargs) -> UserResponse | Response | Any:
        """
        Fetch the details of a specific user using the stored authentication token.

        Args:
            base_url: The base URL of the API.
           user_id: The ID of the device to fetch information for.
            **kwargs: Additional headers for the request.

        Returns:
            A dictionary containing the details of the device.

        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/users/{user_id}"
        headers = self.get_default_headers()
        try:
            response = self.session.get(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return user_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_user_details request: {e}")
            return Response(code=500, link="", message=str(e))

    def create_user(self, base_url: str, payload: dict) -> UserCreationResponse | Response | Any:
        """
        Create a new user in BioStar system

        Args:
            base_url: The base URL of the API
            user_vals: Dictionary containing user values

        Returns:
            UserResponse or Response object
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/users"
        headers = self.get_default_headers()

        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)

            self.logger.info(f"BioStar API response: {response_data}")
            if isinstance(response_data, Response):
                return response_data
            else:
                return user_creation_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during create_user request: {e}")
            msg = f"Access control server error:{str(e)}"
            return Response(code=500, link="", message=msg)

    def update_user(self, base_url: str, payload: dict, user_id: int) -> Response | Any:
        """
        update a new user in BioStar system

        Args:
            base_url: The base URL of the API
            user_vals: Dictionary containing user values

        Returns:
            UserResponse or Response object
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/users/{user_id}"
        headers = self.get_default_headers()

        try:
            response = self.session.put(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)

            # self.logger.info(f"BioStar API response: {response_data}")
            if isinstance(response_data, Response):
                return response_data
            else:
                return Response.from_dict(response_data.get("Response", {}))

        except Exception as e:
            self.logger.error(f"An error occurred during update_user request: {e}")
            msg = f"Access control server error:{str(e)}"
            return Response(code=500, link="", message=msg)

    def delete_user(self, base_url: str, user_id: str, **kwargs) -> Response | Any:
        """
        delete a specific user using id.
        Args:
            base_url: The base URL of the API.
            user_id: The ID of the specific user.
            **kwargs: Additional headers or parameters for the request.

        Returns:
            return dto Response

        """

        base_url = base_url or self.base_url

        url = f"{base_url}/api/users?id={user_id}"
        headers = self.get_default_headers()
        try:
            response = self.session.delete(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data

            else:
                return Response.from_dict(response_data.get("Response", {}))

        except Exception as e:
            self.logger.error(f"An error occurred during delete user : {e}")
            return Response(code=500, link="", message=str(e))

    #endregion
    # region ---------------------- TODO[IMP]: User Group API Methods -------------------------------------

    def get_user_groups(self, base_url: str, **kwargs) -> UserResponse | Response | Any:
        """
         Fetch the groups of a specific user
         Args:
            base_url: The base URL of the API.
            **kwargs: Additional headers for the request.
         Returns:
            A dictionary containing the groups of the user.
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/user_groups"
        headers = self.get_default_headers()
        try:
            response = self.session.get(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return user_groups_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_user_groups request: {e}")
            return Response(code=500, link="", message=str(e))

    def get_user_group(self, base_url: str, group_id: int, **kwargs) -> UserGroupCollectionResponse | Response | Any:
        """
        Fetch details of a specific user group from BioStar system

        Args:
            base_url: The base URL of the API
            group_id: ID of the user group to fetch
            **kwargs: Additional headers for the request

        Returns:
            UserGroupsResponse or Response object

        Raises:
            Exception: For connection errors or API errors
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/v2/user_groups/search"
        headers = self.get_default_headers()

        payload = {
            "user_group_id_list": [str(group_id)]
        }

        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)

            if isinstance(response_data, Response):
                return response_data
            else:
                return user_group_collection_response_from_dict(response_data)

        except Exception as e:
            self.logger.error(f"An error occurred during get_user_group request: {e}")
            return Response(code=500, link="", message=str(e))

    def create_user_group(self, base_url: str, payload: dict) -> UserGroupCreationResponse | Response | Any:
        """
        Create a new user group in BioStar system

        Args:
            base_url: The base URL of the API
            user_vals: Dictionary containing user values

        Returns:
            UserResponse or Response object
        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/user_groups"
        headers = self.get_default_headers()
        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return user_group_creation_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during create_user_group request: {e}")
            return Response(code=500, link="", message=str(e))

    def update_user_group(self, base_url: str, payload: dict, user_group_id: int) -> Response | Any:
        base_url = base_url or self.base_url
        url = f"{base_url}/api/user_groups/{user_group_id}"
        headers = self.get_default_headers()
        try:
            response = self.session.put(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return Response.from_dict(response_data.get("Response", {}))
        except Exception as e:
            self.logger.error(f"An error occurred during update_user_group request: {e}")
            return Response(code=500, link="", message=str(e))

    def delete_user_group(self, base_url: str, group_id: int, **kwargs):

        base_url = base_url or self.base_url
        url = f"{base_url}/api/user_groups/{group_id}"
        headers = self.get_default_headers()
        try:
            response = self.session.delete(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return Response.from_dict(response_data.get("Response", {}))

        except Exception as e:
            self.logger.error(f"An error occurred during delete_user_group: {e}")
            return Response(code=500, link="", message=str(e))

    #endregion

    # region ---------------------- TODO[IMP]: Card API Methods -------------------------------------

    def get_card_types(self, base_url: str, **kwargs) -> CardTypesResponse | Response | Any:
        base_url = base_url or self.base_url
        url = f"{base_url}/api/cards/types"
        headers = self.get_default_headers()
        try:
            response = self.session.get(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return card_types_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_card_types request: {e}")
            return Response(code=500, link="", message=str(e))

    def get_cards(self, base_url: str, limit: int = 50, offset: int = 0, **kwargs) -> CardsResponse | Response | Any:
        base_url = base_url or self.base_url
        url = f"{base_url}/api/cards?limit={limit}&offset={offset}"
        headers = self.get_default_headers()
        try:
            response = self.session.get(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return cards_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during get_cards request: {e}")
            return Response(code=500, link="", message=str(e))

    def create_card(self, base_url: str, payload: dict) -> CardResponse | Response | Any:
        base_url = base_url or self.base_url
        url = f"{base_url}/api/cards"
        headers = self.get_default_headers()
        try:
            response = self.session.post(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return card_response_from_dict(response_data)
        except Exception as e:
            self.logger.error(f"An error occurred during create_card request: {e}")
            return Response(code=500, link="", message=str(e))

    def assign_card_to_user(self, base_url: str, user_id: str, card_id: str) -> Response | Any:
        """
        Assign a card to a user.

        Args:
            base_url: The base URL of the API.
            user_id: The ID of the user to assign the card to.
            card_id: The ID of the card to assign to the user.
        Returns:
            A dictionary containing the response from the API.

        """
        base_url = base_url or self.base_url
        url = f"{base_url}/api/users/{user_id}"
        headers = self.get_default_headers()
        payload = {
            "User": {
                "cards": [
                    {
                        "id": card_id
                    }
                ]
            }
        }
        try:
            response = self.session.put(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return Response.from_dict(response_data.get("Response", {}))
        except Exception as e:
            self.logger.error(f"An error occurred during assign_card_to_user request: {e}")
            return Response(code=500, link="", message=str(e))

    def delete_card(self, base_url: str, card_id: str, **kwargs):
        base_url = base_url or self.base_url
        url = f"{base_url}/api/cards?id={card_id}"
        headers = self.get_default_headers()
        try:
            response = self.session.delete(url, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return Response.from_dict(response_data.get("Response", {}))
        except Exception as e:
            self.logger.error(f"An error occurred during delete_card: {e}")
            return Response(code=500, link="", message=str(e))

    #endregion

    # region ---------------------- TODO[IMP]: Fingerprint & Face API Methods -------------------------------------

    def set_fingerprint(self, base_url: str, user_id: int,  template0: str, template1: str) -> Response | Any:
        base_url = base_url or self.base_url
        url = f"{base_url}/api/users/{user_id}"
        headers = self.get_default_headers()
        payload = {
        "User": {
            "fingerprint_templates": [
                {
                    "finger_mask": "false",
                    "template0": template0,
                    "template1": template0
                },
                {
                    "finger_mask": "false",
                    "template0": template1,
                    "template1": template1
                }
            ]
        }
    }
        try:
            response = self.session.put(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return Response.from_dict(response_data.get("Response", {}))
        except Exception as e:
            self.logger.error(f"An error occurred during set_fingerprint: {e}")
            return Response(code=500, link="", message=str(e))

    def set_face(self, base_url: str, user_id: int, face_json_text: str) -> Response | Any:
        base_url = base_url or self.base_url
        url = f"{base_url}/api/users/{user_id}"
        headers = self.get_default_headers()
        
        # Parse the face_json_text to get the face data
        face_data = json.loads(face_json_text)
        face_info = face_data.get("credentials", {}).get("faces", [{}])[0]
        
        payload = {
            "User": {
                "credentials": {
                    "visualFaces": [
                        {
                            "template_ex_normalized_image": face_info.get("template_ex_normalized_image", ""),
                            "templates": face_info.get("templates", []),
                        }
                    ]
                }
            }
        }
        
        try:
            response = self.session.put(url, json=payload, headers=headers)
            response_data = self.get_json_response(response)
            if isinstance(response_data, Response):
                return response_data
            else:
                return Response.from_dict(response_data.get("Response", {}))
        except Exception as e:
            self.logger.error(f"An error occurred during set_face: {e}")
            return Response(code=500, link="", message=str(e))

    #endregion
    # endregion
