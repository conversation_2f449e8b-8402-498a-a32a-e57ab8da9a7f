# -*- coding: utf-8 -*-
import uuid

from odoo import api, fields, models, Command

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.tools.translate import _


class AccessLevel(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.access_level'
    _description = "Access Level"
    _inherit = 'ams_base.api_model_chatter'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    door_schedule_ids = fields.One2many('ams_bs.door_schedule', 'ac_level_id', string="Door Schedules")
    ac_group_ids = fields.Many2many('ams_bs.access_group', string="Access Group")

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_ac_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.access_level'].sudo()

    def _sync_access_levels(self, base_url):
        response = self.api_client.get_ac_levels(base_url)
        if self.api_client.is_success_response(response):
            for rec in response.access_level_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)

        return response

    def _prepare_record_vals(self, response_record, origin=False):
        self = self.origin_model if origin else self

        # Choose correct model names depending on origin
        door_model = 'ams.door' if origin else 'ams_bs.door'
        schedule_model = 'ams.schedule' if origin else 'ams_bs.schedule'
        door_schedule_model = 'ams.door_schedule' if origin else 'ams_bs.door_schedule'

        result = {
            'name': response_record.name,
            'description': response_record.description,
            'record_id': response_record.id,
        }

        items = getattr(response_record, 'access_level_items', []) or []

        if any(getattr(item, 'doors', []) and getattr(item, 'schedule_id', None) for item in items):
            door_schedule_vals = []

            for item in items:
                doors = getattr(item, 'doors', []) or []
                schedule = getattr(item, 'schedule_id', None)
                if not doors or not schedule:
                    continue

                for door in doors:
                    door_id = self.env[door_model].get_res_id(door.id)
                    schedule_id = self.env[schedule_model].get_res_id(schedule.id)
                    if not door_id or not schedule_id:
                        continue

                    # Check if a matching door schedule already exists for this access level
                    access_level = self if self.id else self.search([('record_id', '=', response_record.id)], limit=1)
                    existing = self.env[door_schedule_model].search([
                        ('door_id', '=', door_id),
                        ('schedule_id', '=', schedule_id),
                        ('ac_level_id', '=',access_level.id)
                    ], limit=1)

                    if existing:
                        door_schedule_vals.append(Command.link(existing.id))
                    else:
                        door_schedule_vals.append(Command.create({
                            'record_id': item.id,
                            'door_record_id': int(door.id),
                            'schedule_record_id': int(schedule.id),
                            'door_id': door_id,
                            'schedule_id': schedule_id,
                            'ac_level_id': self.id,
                            'name': f"{access_level.name} | {door.name} , {schedule.name} | {uuid.uuid4().hex[:6]}"
                        }))

            result['door_schedule_ids'] = door_schedule_vals

        return result

    def prepare_access_level_payload(self) -> dict:
        """
        Prepare the payload for creating/updating an access level with multiple door schedules.
        
        Returns:
            Dictionary containing the access level data with door schedules
        """
        # Basic access level information
        payload = {
            "AccessLevel": {
                "name": self.name,
                "description": self.description or "",
                "access_level_items": []
            }
        }

        # Group doors by schedule for the access_level_items
        schedule_doors = {}

        # Iterate through door_schedule_ids to group doors by schedule
        for door_schedule in self.door_schedule_ids:
            schedule_id = door_schedule.schedule_id.record_id
            door_id = door_schedule.door_id.record_id

            if not schedule_id or not door_id:
                continue

            if schedule_id not in schedule_doors:
                schedule_doors[schedule_id] = []

            schedule_doors[schedule_id].append({"id": int(door_id)})

        # Create access_level_items from the grouped doors
        for schedule_id, doors in schedule_doors.items():
            if doors:  # Only add if there are doors for this schedule
                access_level_item = {
                    "doors": doors,
                    "schedule_id": {"id": schedule_id}
                }
                payload["AccessLevel"]["access_level_items"].append(access_level_item)

        return payload

    def _create_access_level(self, base_url, payload):
        """
        Create an access level in BioStar system

        Args:
            base_url: The base URL of the API
            payload: The payload to send to the API

        Returns:
            The created access level record if successful
        """
        response = self.api_client.create_access_level(base_url, payload)

        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = True
            self.record_id = response.id
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False

            # Create access level in origin model
            ams_access_level = self.origin_model.create_or_update(self._prepare_ams_access_level_record_vals())
            return ams_access_level
        else:
            self.assign_error(response)
            return False

    def _update_access_level(self, base_url, payload):
        """
        Update an access level in BioStar system

        Args:
            base_url: The base URL of the API
            payload: The payload to send to the API

        Returns:
            The updated access level record if successful
        """
        response = self.api_client.update_access_level(base_url, str(self.record_id), payload)

        if self.api_client.is_success_response(response):
            self.reset_error()
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False

            # Update access level in origin model
            ac_level_vals= self._prepare_ams_access_level_record_vals()
            ams_access_level = self.origin_model.create_or_update(ac_level_vals)
            return ams_access_level
        else:
            self.assign_error(response)
            return False


    def _delete_access_level(self, base_url):
        """
        Delete an access level in BioStar system

        Args:
            base_url: The base URL of the API

        Returns:
            True if successful, False otherwise
        """
        response = self.api_client.delete_access_level(base_url, self.record_id)

        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = False
            self.need_to_sync = True
            return True
        else:
            self.assign_error(response)
            return False

    def _prepare_ams_access_level_record_vals(self):
        """
        Prepare values for creating/updating an access level in the origin model

        Returns:
            Dictionary of values for the origin model
        """
        return {
            'name': self.name,
            'description': self.description,
            'synced': self.synced,
            'last_sync_date': self.last_sync_date,
            'record_id': self.record_id,
        }

    def action_push_access_level(self):
        """
        Push access level to BioStar API (create or update)
        """
        self.ensure_one()
        payload = self.prepare_access_level_payload()
        base_url = self.env.company.bs_base_url

        if self.synced and self.record_id:
            result = self._update_access_level(base_url, payload)
            if result:
                return self._notify_status(message="Access level updated successfully in BioStar", type="success")
        else:
            result = self._create_access_level(base_url, payload)
            if result:
                return self._notify_status(message="Access level created successfully in BioStar", type="success")

        return self._notify_status(message="Failed to push access level to BioStar", type="danger")


    def action_delete_access_level(self):
        """
        Delete access level from BioStar system
        """
        self.ensure_one()

        if not self.record_id:
            return self._notify_status(message="Access level has no external ID to delete", type="warning")

        if not self.synced:
            return self._notify_status(message="Access level is not synced, so it cannot be deleted from BioStar", type="warning")

        # Check if access level is related to doors
        if self.door_schedule_ids:
            raise ValidationError(
                _(
                    "Cannot delete this access level because it is associated with door schedules. "
                    "Please remove all door schedules first."
                )
            )

        # Check if access level is used in access groups
        if self.ac_group_ids:
            group_names = ", ".join(self.ac_group_ids.mapped("name"))
            raise ValidationError(
                _(
                    "Cannot delete this access level because it is used in the following access groups: %s. "
                    "Please remove it from these groups first."
                )
                % group_names
            )


        base_url = self.env.company.bs_base_url
        response = self.api_client.delete_access_level(base_url, str(self.record_id))

        if self.api_client.is_success_response(response):
            record_id_before_delete = self.record_id

            # Update the record to reflect deletion
            self.synced = False
            self.need_to_sync = True
            self.record_id = False

            # Update origin model if it exists
            origin_access_level = self.origin_model.get_record(record_id_before_delete)
            if origin_access_level:
                origin_access_level.write({
                    'synced': False,
                    'need_to_sync': True,
                    'record_id': False
                })

            return self._notify_status(message="Access level deleted successfully from BioStar", type="success")
        else:
            self.assign_error(response)
            return self._notify_status(message="Failed to delete access level from BioStar", type="danger")
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
