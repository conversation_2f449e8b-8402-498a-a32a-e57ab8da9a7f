# -*- coding: utf-8 -*-


from odoo import api, fields, models, _
from odoo.exceptions import UserError


class Door(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.door'
    _description = "Door"
    _inherit = 'ams_base.api_model_chatter'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    unconditional_lock = fields.Boolean(string="Unconditional Lock")
    open_once = fields.Boolean(string="Open Once")
    open_duration = fields.Integer(string="Open Duration" , help="Show open duration in Seconds")
    open_time_out = fields.Integer(string="Open Time Out")

    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    door_group_id = fields.Many2one('ams_bs.door_group', string="Door Group")
    entry_device_id = fields.Many2one('ams_bs.device', string="Entry Device")
    exit_device_id = fields.Many2one('ams_bs.device', string="Exit Device" )
    device_relay_id = fields.Many2one('ams_bs.device_relay', string=" Relay" )
    device_sensor_id = fields.Many2one('ams_bs.device_sensor', string=" Sensor")
    device_exit_button_id = fields.Many2one('ams_bs.device_exit_button', string=" Exit Button")

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_push_ac_door(self):
        """Push door data to BioStar - creates if new, updates if existing."""
        payload = self.prepare_ac_door_payload()
        base_url = self.env.company.bs_base_url
        door_id = str(self.record_id)
        door = self.api_client.get_ac_door(base_url, door_id=door_id)

        if self.api_client.is_success_response(door):
            return self._update_ac_door(base_url, payload)

        else:
            return self._create_ac_door(base_url, payload)

    def action_sync_ac_door(self):
        base_url = self.env.company.bs_base_url
        door_id = str(self.record_id)
        response = self.api_client.get_ac_door(base_url, door_id=door_id)
        if self.api_client.is_success_response(response):
            self._sync_record(response_record=response.door)
        else:
            self.synced = False
            self.assign_error(response)

        return response

    def action_delete_ac_door(self):
        """
        Delete door(s) from BioStar system.
        Can be called on a single record or multiple records.
        """
        base_url = self.env.company.bs_base_url
        door_ids_to_delete = []

        for door in self:
            if door.record_id:
                # Ensure record_id is a string
                door_ids_to_delete.append(str(door.record_id))

        if not door_ids_to_delete:
            raise UserError(_("No doors with valid record IDs found to delete."))

        # Join door IDs with spaces as required by the API
        door_ids_str = " ".join(door_ids_to_delete)

        # Call API to delete doors
        response = self.api_client.delete_ac_door(base_url, door_ids_str)

        if self.api_client.is_success_response(response):
            # Update door records to reflect deletion
            deleted_doors = self.filtered(
                lambda rec: str(rec.record_id) in door_ids_to_delete
            )
            for door in deleted_doors:
                record_id_before_delete = door.record_id
                door.synced = False
                door.record_id = False
                door.need_to_sync = True

                # Update origin model if it exists
                origin_door = door.origin_model.get_record(record_id_before_delete)
                if origin_door:
                    origin_door.write({"synced": False , "need_to_sync": True , "record_id": False})

            return self._notify_status(
                message="Doors deleted successfully from BioStar", type="success"
            )
        else:
            self.assign_error(response)
            raise UserError(_(f"Failed to delete doors: {response.message}"))

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_ac_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.door'].sudo()

    def _sync_doors(self, base_url):
        # Logic to sync doors
        response = self.api_client.get_ac_doors(base_url)

        if self.api_client.is_success_response(response):
            for rec in response.door_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)

        return response

    def _prepare_record_vals(self, response_record, origin=False):
        """Convert Response Record (Row) to Odoo Values"""
        self = self.origin_model if origin else self  # in case search from origin model
        # If this is the original model , return an alternate dict
        if self == self.origin_model:
            return {
                'name': response_record.name,
                'record_id': response_record.id,
                'description': response_record.description or '',
                'door_group_id': self.door_group_id.get_res_id(response_record.door_group_id.id) if response_record.door_group_id and response_record.door_group_id.id else False,
            }
        return {
            'name': response_record.name,
            'description': response_record.description if response_record.description else '',
            'record_id': response_record.id,
            'door_group_id': self.door_group_id.get_res_id(response_record.door_group_id.id)  if response_record.door_group_id and response_record.door_group_id.id else False,
            'entry_device_id':self.entry_device_id.get_res_id(response_record.entry_device_id.id) if response_record.entry_device_id and response_record.entry_device_id.id else False,
            'open_duration': response_record.open_duration if response_record.open_duration else 0,
            'open_time_out': response_record.open_timeout if response_record.open_timeout else 0,
            'open_once': response_record.open_once if response_record.open_once else False,
            'unconditional_lock': response_record.unconditional_lock if response_record.unconditional_lock else False
        }

    def _prepare_ams_ac_door_record_vals(self):
        """Prepare values for the original AMS door record."""
        door_group_id = self.env['ams.door_group'].get_res_id(self.door_group_id.record_id)
        return {
            'name': self.name,
            'record_id': self.record_id,
            'description': self.description,
            'door_group_id': door_group_id if door_group_id else False,
            'synced': self.synced,
            'last_sync_date': self.last_sync_date,
        }

    def prepare_ac_door_payload(self) -> dict:
        """Prepare the payload for creating/updating a door."""
        return {
            "Door": {
                "name": self.name,
                "description": self.description if self.description else "",
                "door_group_id": {
                    "id": self.door_group_id.record_id if self.door_group_id and self.door_group_id.record_id else 0
                },
                "open_timeout": self.open_time_out if self.open_time_out else 0,
                "open_duration": str(self.open_duration),
                "open_once": "true" if self.open_once else "false",
                "unconditional_lock": "true" if self.unconditional_lock else "false",
                "entry_device_id": {
                    "id": str(
                        self.entry_device_id.record_id) if self.entry_device_id and self.entry_device_id.record_id else ""
                },
                "relay_output_id": {
                    "device_id": {
                        "id": str(
                            self.entry_device_id.record_id) if self.entry_device_id and self.entry_device_id.record_id else ""
                    },
                    "relay_index": self.device_relay_id.port if self.device_relay_id and self.device_relay_id.port else 0
                },
                "exit_button_input_id": {
                    "device_id": {
                        "id": str(
                            self.entry_device_id.record_id) if self.entry_device_id and self.entry_device_id.record_id else ""

                    },
                    "input_index": self.device_exit_button_id.port if self.device_exit_button_id and self.device_exit_button_id.port else 0,
                    "type": self.device_exit_button_id.type if self.device_exit_button_id and self.device_exit_button_id.type else ""
                },
                "sensor_input_id": {
                    "device_id": {
                        "id": str(
                            self.entry_device_id.record_id) if self.entry_device_id and self.entry_device_id.record_id else ""
                    },
                    "input_index": self.device_sensor_id.port if self.device_sensor_id and self.device_sensor_id.port else 0,
                    "type": self.device_sensor_id.type if self.device_sensor_id and self.device_sensor_id.type else "",
                    "apb_use_door_sensor": ""
                }
            }
        }

    def _create_ac_door(self, base_url, payload):
        """Create a new door in BioStar."""
        response = self.api_client.create_ac_door(base_url, payload)

        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = True
            self.record_id = response.door_collection.rows[0].id if response.door_collection.rows else False
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False

            # Create door in origin model
            ac_door = self.origin_model.create_or_update(self._prepare_ams_ac_door_record_vals())
            return ac_door
        else:
           return self.assign_error(response)

    def _update_ac_door(self, base_url, payload):
        """Update an existing door in BioStar."""
        response = self.api_client.update_ac_door(base_url, str(self.record_id), payload)

        if self.api_client.is_success_response(response):
            self.reset_error()
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False

            # Update door in origin model
            ac_door = self.origin_model.create_or_update(self._prepare_ams_ac_door_record_vals())
            return ac_door
        else:
            return  self.assign_error(response)

    # endregion


