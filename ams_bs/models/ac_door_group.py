# -*- coding: utf-8 -*-


from odoo import api, fields, models, _
from odoo.exceptions import UserError


class DoorGroup(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.door_group'
    _description = "Door Group"
    _inherit = 'ams_base.api_model_chatter'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    depth = fields.Integer(string="Hierarchy Level Depth", default=1, readonly=True)
    # endregion

    # region  Special
    # endregion

    # region  Relational
    door_ids = fields.One2many('ams_bs.door', 'door_group_id', string="Doors")
    parent_id = fields.Many2one('ams_bs.door_group', string="Parent Door Group",
                               domain="[('synced', '=', True)]")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_ac_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.door_group'].sudo()

    def _sync_door_groups(self, base_url):
        response = self.api_client.get_door_groups(base_url)
        # Check if the response is valid
        if self.api_client.is_success_response(response):
            for rec in response.door_group_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)

        return response

    def _prepare_record_vals(self, response_record, origin=False):
        self = self.origin_model if origin else self  # in case search from origin model
        return {
            'name': response_record.name,
            # 'description': response_record.description,
            'record_id': response_record.id,
        }

    def prepare_door_group_payload(self) -> dict:
        """Prepare the payload for creating/updating a door group"""
        payload = {
            "DoorGroup": {
                "name": self.name,
                "depth": 1  # Always set depth to 1 as a static value
            }
        }

        # Only include parent_id if it's set
        if self.parent_id and self.parent_id.record_id:
            payload["DoorGroup"]["parent_id"] = {"id": self.parent_id.record_id}

        return payload

    def _create_door_group(self, base_url, payload):
        response = self.api_client.create_door_group(base_url, payload)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = True
            self.record_id = response.id
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            # Create door group in origin model
            ams_door_group = self.origin_model.create_or_update(self._prepare_ams_door_group_record_vals())
            return ams_door_group
        else:
            self.assign_error(response)

    def _update_door_group(self, base_url, payload):
        response = self.api_client.update_door_group(base_url, str(self.record_id), payload)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            ams_door_group = self.origin_model.create_or_update(self._prepare_ams_door_group_record_vals())
            return ams_door_group
        else:
            self.assign_error(response)

    def _prepare_ams_door_group_record_vals(self):
        return {
            'name': self.name,
            'synced': self.synced,
            'last_sync_date': self.last_sync_date,
            'record_id': self.record_id,
        }

    def action_push_door_group(self):
        """Push door group to BioStar API (create or update)"""
        self.ensure_one()
        payload = self.prepare_door_group_payload()
        base_url = self.env.company.bs_base_url

        if self.synced:
            self._update_door_group(base_url, payload)
        else:
            self._create_door_group(base_url, payload)

    def action_delete_door_group(self):
        """
        Delete door group(s) from BioStar system.
        Can be called on a single record or multiple records.
        """
        base_url = self.env.company.bs_base_url
        door_group_ids_to_delete = []

        for door_group in self:
            if door_group.record_id:
                # Ensure record_id is a string
                door_group_ids_to_delete.append(str(door_group.record_id))

        if not door_group_ids_to_delete:
            raise UserError(_("No door groups with valid record IDs found to delete."))

        # Join door group IDs with spaces as required by the API
        door_group_ids_str = " ".join(door_group_ids_to_delete)

        # Call API to delete door groups
        response = self.api_client.delete_door_group(base_url, door_group_ids_str)

        if self.api_client.is_success_response(response):
            # Update door group records to reflect deletion
            deleted_door_groups = self.filtered(
                lambda rec: str(rec.record_id) in door_group_ids_to_delete
            )
            for door_group in deleted_door_groups:
                door_group.synced = False
                door_group.last_sync_date = fields.Datetime.now()

                # Update origin model if it exists
                origin_door_group = door_group.origin_model.get_record(door_group.record_id)
                if origin_door_group:
                    origin_door_group.write({"synced": False})

            return self._notify_status(
                message="Door groups deleted successfully from BioStar", type="success"
            )
        else:
            self.assign_error(response)
            raise UserError(_(f"Failed to delete door groups: {response.message}"))

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
