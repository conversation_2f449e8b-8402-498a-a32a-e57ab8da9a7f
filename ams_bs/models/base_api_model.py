# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class BaseAPIModel(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.api_model'
    _inherit = ['ams_base.api_model']
    _description = 'Biostar Base API Model'

    # endregion

    # region ---------------------- TODO[IMP]:Action Methods ------------------------------------
    @property
    def api_model(self):
        return self.env.context.get('model', '')

    @property
    def sync_method_name(self):
        return self.env.context.get('sync_method_name', '')

    def action_sync(self):
        response = self.call_model_method_safely(self.env[self.api_model], self.sync_method_name,
                                                 self.env.company.bs_base_url)
        if self.api_client.is_success_response(response):
            self.reset_error()
            return self._notify_success_action()
        else:
            self.assign_error(response)

    def _notify_success_action(self):
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': 'Sync Completed Successfully',
                'type': 'success',
            }
        }

    def _notify_status(self, message='', type='success'):
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': message,
                'type': type,
            }
        }

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    @property
    def api_client(self):
        return self.env['ams_base.biostar_api_client'].sudo()  # to be override in child model

    @property
    def logger(self):
        return self.api_client.logger

    def assign_error(self, response, raise_error=False):
        response_dto = self.api_client.get_response(response)
        super().assign_error(
            {'response_code': response_dto.code,
             'response_message': response_dto.message},
            raise_error)

    # endregion

    # region ---------------------- TODO[IMP]: Property Methods -------------------------------------

    # endregion
