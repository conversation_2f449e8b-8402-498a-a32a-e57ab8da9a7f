# -*- coding: utf-8 -*-


from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
import logging
from odoo.addons.ams_bs.api.dto_response import BaseResponse, Response
from datetime import datetime,timezone

_logger = logging.getLogger(__name__)


class Device(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.device'
    _description = "Device"
    _inherit = 'ams_base.device'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    device_group_id = fields.Many2one('ams_bs.device_group', string="Device Group")
    device_relay_ids = fields.One2many('ams_bs.device_relay', 'device_id', string="Relays")
    device_sensor_ids = fields.One2many('ams_bs.device_sensor', 'device_id', string="Sensors")
    device_exit_button_ids = fields.One2many('ams_bs.device_exit_button', 'device_id', string="Exit Buttons")
    device_type_id = fields.Many2one('ams_bs.device_type', string="Device Type")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def _notify_status(self, message='', type='success'):
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': message,
                'type': type,
            }
        }
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_push_device(self):
        """Push device data to BioStar system"""
        self.ensure_one()
        base_url = self.env.company.bs_base_url
        device_vals = self.prepare_device_payload()
        # Check if device exists
        device = self.api_client.get_device(base_url, device_id=self.device_serial)

        if device.message == "Device is not connected to biostar":
            raise UserError(" Device exists but not connected to biostar so you can not update it")
        elif self.api_client.is_success_response(device):
            return self._update_device(base_url=base_url, device_vals=device_vals)

        else:
            # Create a new device
            return self._create_device(base_url=base_url, device_vals=device_vals)

    def action_sync_device(self):
        base_url = self.env.company.bs_base_url
        device_id = self.device_serial
        response = self.api_client.get_device(base_url, device_id=device_id)
        if response.message == "Device is not connected to biostar":
            raise UserError(" Device exists but not connected to biostar so you can not Sync it")
        elif self.api_client.is_success_response(response):
            self._sync_record(response_record=response.device)
        else:
            self.synced = False
            self.assign_error(response)

        return response

    def action_sync_events(self):
        self.ensure_one()
        base_url = self.api_client.base_url
        events_log = []
        payload = self.get_events_payload()

        response = self.api_client.get_events(base_url, payload)
        if self.api_client.is_success_response(response):
            for rec in response.event_collection.rows:
                events_log.append(rec)
                # print(rec.row_datetime,'  ',rec.device_id.id)
            # print(len(events_log))
            self.last_log_id = events_log[0].id
            self.last_log_date = events_log[0].row_datetime
        else:
            self.assign_error(response)

        return response

    def action_delete_devices(self):
        """Delete devices in BioStar system."""
        synced_devices = self.filtered(lambda rec: rec.synced)
        if not synced_devices:
            raise UserError(_("Devices you selected are already deleted or not synced."))

        # Build device ID string (BioStar expects "+"-joined IDs)
        device_id = "+".join(device.device_serial for device in synced_devices)

        response = self.api_client.delete_device(self.env.company.bs_base_url, device_id)
        if self.api_client.is_success_response(response):
            for device in synced_devices:
                # Update this model
                device.synced = False
                device.activate = False
                device.need_to_sync = True
                record_id_before_delete = device.record_id
                device.record_id = False

                # Update origin model
                origin_device = device.origin_model.get_record(record_id_before_delete)
                if origin_device:
                    origin_device.write({
                        'synced': False,
                        'activate': False,
                        'need_to_sync': True,
                        'record_id': False
                    })

            return self._notify_status(message="Devices deleted successfully in BioStar", type="success")
        else:
            self.assign_error(response)
            return self._notify_status(message="Failed to delete devices in BioStar", type="danger")

    # endregion

    # region ---------------------- TODO[IMP]: API Business Methods -------------------------------------
    @property
    def sync_fields(self):
        """Override sync_fields property to define fields for device synchronization"""
        return [
            "name",
            "device_serial",
            "device_type",
            "ip",
            "port",
            "device_group_id",
            "location_id",
            "status",
        ]

    @property
    def api_client(self):
        return self.env['ams_bs.biostar_device_api_client'].sudo()

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.device'].sudo()

    def _sync_devices(self, base_url):
        response = self.api_client.get_devices(base_url)
        if self.api_client.is_success_response(response):
            for rec in response.device_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)

        return response

    def _prepare_record_vals(self,  response_record, origin=False):
        self = self.origin_model if origin else self  # in case search from origin model
        return {
            'name': response_record.name,
            'record_id': response_record.id,
            'device_group_id':self.device_group_id.get_res_id(response_record.device_group_id.id),
            'device_type_id':self.device_type_id.get_res_id(response_record.device_type_id.id),
            'port':response_record.lan.device_port,
            'ip': response_record.lan.ip,
            'device_serial' : response_record.id,
            'state': str(response_record.status),
        }

    def _prepare_ams_device_record_vals(self):
        """ Prepare ams device record vals """
        device_record_vals = {
            'name': self.name,
            'device_serial': self.device_serial,
            'record_id': self.device_serial,
            'ip': self.ip,
            'port': self.port,
            'state': self.state,
            'activate': self.activate,
            'synced': self.synced,
            'last_sync_date': self.last_sync_date
        }
        # Add device group if available
        if self.device_group_id:
            device_record_vals['device_group_id'] = self.env["ams.device_group"].get_res_id(
                self.device_group_id.record_id)
        # Add device type if available
        if self.device_type_id:
            device_record_vals['device_type_id'] = self.env["ams.device_type"].get_res_id(
                self.device_type_id.record_id)

        return device_record_vals

    def prepare_device_payload(self):
        return {
            "Device": {
                "id": self.device_serial if self.device_serial else "",
                "name": self.name if self.name else "",
                "device_type_id": {
                    "id": str(self.device_type_id.record_id) if self.device_type_id.record_id else ""
                },
                "connection": {
                    "status": "3"
                },
                "lan": {
                    "ip": self.ip if self.ip else "",
                    "connection_mode": "1"
                },
                "system": {},
                "capacity": {
                    "support_alphanumeric": "true"
                },
                "support_occupancy": "false",
                "pktversion": "3",
                "device_group_id": {
                    "id": self.device_group_id.record_id
                }
            }
        }

    def get_events_payload(self):
        """
        Prepare the payload for synchronizing logs of a given device.
        :param device: recordset, the device for which to prepare the payload.
        :return: dict, the prepared payload for the API call.
        """
        self.ensure_one()  # Ensure the operation is performed on a single record

        last_log_date = (self.last_log_date.replace(tzinfo=timezone.utc).isoformat()
                      if self.last_log_date else "1970-01-01T00:00:00.000Z")
        current_date = datetime.utcnow().replace(tzinfo=timezone.utc).isoformat()

        return {
                "Query": {
                    "limit": 50 , # Limit the number of logs to 50,

                    "conditions": [
                        {
                            "column": "datetime",
                            "operator": 3,  # Between operator
                            "values": [
                                last_log_date,
                                current_date
                            ]
                        },
                        {
                            "column": "device_id.id",
                            "operator": 0,  # Equal operator
                            "values": [
                                self.device_serial
                            ]
                        }
                    ],
                    "orders": [
                        {
                            "column": "datetime",
                            "descending": True
                        }
                    ]
                }
            }

    def _update_device(self, base_url, device_vals):
        """Update device in BioStar system"""
        response = self.api_client.update_device(base_url, self.device_serial, device_vals)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = True
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            # Update device in origin_model
            ams_device = self.origin_model.create_or_update(self._prepare_ams_device_record_vals())
            return ams_device
        else:
            self.assign_error(response)

    def _create_device(self, base_url, device_vals):
        """Create device in BioStar system"""
        response = self.api_client.create_device(base_url, device_vals)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = True
            self.record_id = response.id
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            # Create device in origin_model
            ams_device = self.origin_model.create_or_update(self._prepare_ams_device_record_vals())
            return ams_device
        else:
            self.assign_error(response)

    @api.model
    def _cron_check_device_status(self):
        """
        Cron job to check device status every 15 minutes.
        Uses the existing get_devices method to get device statuses from BioStar.
        Updates only the status field in Odoo without full synchronization.
        """
        _logger.info("Starting device status check cron job")
        try:
            # Get API client and base URL
            base_url = self.api_client.base_url

            # Get devices from BioStar API
            response = self.api_client.get_devices(base_url)
            if not self.api_client.is_success_response(response):
                _logger.error(f"Failed to get devices: {response.message}")
                return

            devices = response.device_collection.rows

            # Collect all device_serials from the response
            device_serials = [str(device.id) for device in devices]

            # Search for all device records in one batch
            device_records = self.search([("device_serial", "in", device_serials)])

            # Create a mapping from device_serial to record for quick lookup
            record_map = {record.device_serial: record for record in device_records}
            devices_updated = 0

            # Loop through devices to update their status
            for device in devices:
                device_serial = str(device.id)
                device_status = str(device.status)
                # Find corresponding record
                record = record_map.get(device_serial)
                if record:
                    record.write({"state": device_status})
                    devices_updated += 1

            _logger.info(
                f"Device status check completed: {devices_updated} devices updated"
            )
        except Exception as e:
            _logger.error(f"Error during device status check: {str(e)}")



    # endregion


