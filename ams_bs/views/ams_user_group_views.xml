<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: user_group_view_form-->
    <record id="user_group_view_form" model="ir.ui.view">
        <field name="name">ams_bs.user_group.form</field>
        <field name="model">ams_bs.user_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="action_delete_user_group" string="Delete User Group"   icon="fa-trash" type="object"  class="btn btn-outline-danger" title="Delete User Group From Api"  />
            </xpath>

            <field name="name" position="after">
                <field name="parent_id" domain="[('synced', '=', True)]"/>
            </field>
              <xpath expr="//header" position="inside">
                <button name="action_push_user_group"   string="Push User Group"    type="object"    class="oe_highlight"   data-hotkey="q" />
                <button name="action_sync_user_group"   string="Sync User Group"    type="object"    class="oe_highlight"/>
              </xpath>

            <xpath expr="//page[@name='basic_info']" position="replace">
                <page name="users" string="Users">
                    <field name="user_ids" domain="[('synced', '=', True)]">
                        <list>
                            <field name="name" />
                            <field name="device_group_id" optional="hide" />
                            <field name="user_group_id" optional="hide" />
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: user_group_view_list-->
    <record id="user_group_view_list" model="ir.ui.view">
        <field name="name">ams_bs.user_group.list</field>
        <field name="model">ams_bs.user_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="inside">
                <field name="parent_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: user_group_view_search-->
    <record id="user_group_view_search" model="ir.ui.view">
        <field name="name">ams_bs.user_group.search</field>
        <field name="model">ams_bs.user_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="parent_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: user_group_action-->
    <record id="user_group_action" model="ir.actions.act_window">
        <field name="name">User Groups</field>
        <field name="res_model">ams_bs.user_group</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an access group
            </p>
            <p>
                Create access group
            </p>
        </field>
    </record>
</odoo>
