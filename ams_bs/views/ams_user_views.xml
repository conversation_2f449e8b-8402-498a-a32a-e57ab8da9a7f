<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: user_view_form-->
    <record id="user_view_form" model="ir.ui.view">
        <field name="name">ams_bs.user.form</field>
        <field name="model">ams_bs.user</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">


            <field name="description" position="replace">
                <field name="user_group_id" domain="[('synced', '=', True)]"/>
                <field name="enroll_number"/>
                <field name="email"/>
                <field name="employee_id"/>
                <field name="device_group_id" invisible="1" domain="[('synced', '=', True)]"/>
            </field>
            <field name="synced" position="before">
                <field name="device_id" domain="[('synced', '=', True)]" />
                <field name="card_number" />
                <field name="activate"/>
            </field>

            <xpath expr="//group[@name='basic_info_col1']" position="inside">
                <field name="user_type" widget="selection"/>
                <field name="start_datetime"/>
                <field name="end_datetime"/>
            </xpath>

            <xpath expr="//group[@name='basic_info_full_width']" position="inside">
                <field name="ac_group_ids" widget="many2many_tags"  domain="[('synced', '=', True)]"/>

            </xpath>
             <xpath expr="//header" position="inside">
                <button name="action_push_user"   string="Push User"    type="object"    class="oe_highlight"   data-hotkey="q" />
                <button name="action_scan_card"   string="Scan Card"    type="object"    class="oe_highlight"   data-hotkey="q" />
                 <button name="action_delete_users"   string="Delete User"    type="object"    class="btn btn-outline-danger"/>
                 <button name="action_sync_user"   string="Sync User"    type="object"    class="oe_highlight"/>

            </xpath>

               <xpath expr="//page[@name='technical']" position="before">
                <page name="card_info" string="Cards">
                        <field name="card_ids" readonly="1">
                            <list editable="bottom">
                                <field name="card_number" optional="show"/>
                                 <field name="name" optional="hide"/>
                                <field name="card_type_id" optional="hide" domain="[('synced', '=', True)]"/>
                            </list>
                        </field>
                </page>
                <page name="biometric_info" string="Biometric Data">
                        <group>
                            <field name="finger_quality" widget="selection" class="oe_inline"/>

                            <div class="row mt16">
                                    <div class="d-flex justify-content-around">
                                        <div class="mr-4">
                                            <span>Finger 1</span>
                                            <div>
                                                <img src="/ams_bs/static/img/nofp.png" alt="Finger 1" invisible="bs_fp1_template0"/>
                                                <img src="/ams_bs/static/img/fp.png" alt="Finger 1" invisible="not bs_fp1_template0"/>
                                            </div>
                                            <div class="text-center mt-2">
                                                <button type="object" name="action_scan_fp1" class="btn btn-sm btn-primary me-2">
                                                    <i class="fa fa-plus" title="scan fingerprint 1" />
                                                </button>
                                                <button type="object" name="action_remove_fp1" class="btn btn-sm btn-danger ml-2">
                                                    <i class="fa fa-minus" title="remove fingerprint 1"/>
                                                </button>
                                            </div>

                                        </div>
                                        <div class="mr-4">
                                            <span>Finger 2</span>
                                            <div>
                                                <img src="/ams_bs/static/img/nofp.png" alt="Finger 2" invisible="bs_fp2_template0"/>
                                                <img src="/ams_bs/static/img/fp.png" alt="Finger 2" invisible="not bs_fp2_template0"/>
                                            </div>
                                            <div class="text-center mt-2">
                                                <button type="object" name="action_scan_fp2" class="btn btn-sm btn-primary me-2">
                                                    <i class="fa fa-plus" title="scan fingerprint 2"/>
                                                </button>
                                                <button type="object" name="action_remove_fp2" class="btn btn-sm btn-danger ml-2">
                                                    <i class="fa fa-minus" title="remove fingerprint 2"/>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mr-4">
                                            <span>Face</span>
                                            <div>
                                                <img src="/ams_bs/static/img/noface.png" alt="Face" invisible="bs_face_template"/>
                                                <img src="/ams_bs/static/img/face.png" alt="Face" invisible="not bs_face_template"/>
                                            </div>
                                            <div class="text-center mt-2">
                                                <button type="object" name="action_scan_face" class="btn btn-sm btn-primary me-2">
                                                    <i class="fa fa-plus" title="scan face" />
                                                </button>
                                                <button type="object" name="action_remove_face" class="btn btn-sm btn-danger ml-2">
                                                    <i class="fa fa-minus" title="remove face"/>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                            </div>

                             <field name="bs_fp1_template0" />
                        <field name="bs_fp1_template1" />
                        <field name="bs_fp2_template0" />
                        <field name="bs_fp2_template1" />
                        <field name="bs_face_json_text" widget="text" style="height: 100px; width: 100%; overflow-y: auto;"/>
                        </group>

                </page>
            </xpath>

        </field>
    </record>

    <!--TODO[IMP]: user_view_list-->
    <record id="user_view_list" model="ir.ui.view">
        <field name="name">ams_bs.user.list</field>
        <field name="model">ams_bs.user</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
              <xpath expr="//header" position="inside">
                 <button name="action_delete_users"   string="Delete Users"    type="object"    class="btn btn-outline-danger"/>
            </xpath>

           <field name="name" position="after">
                <field name="device_group_id"/>
               <field name="ac_group_ids" widget="many2many_tags" optional="hide" />
                <field name="enroll_number" optional="show"/>
                <field name="start_datetime" optional="show"/>
                <field name="end_datetime" optional="show"/>
                <field name="activate" optional="show"/>
            </field>
        </field>
    </record>

    <!--TODO[IMP]: user_view_search-->
    <record id="user_view_search" model="ir.ui.view">
        <field name="name">ams_bs.user.search</field>
        <field name="model">ams_bs.user</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
        <field name="name" position="after">
                <field name="device_group_id"/>
                <field name="enroll_number" optional="show"/>

            </field>
        </field>
    </record>

    <!--TODO[IMP]: user_action-->
    <record id="user_action" model="ir.actions.act_window">
        <field name="name">Users</field>
        <field name="res_model">ams_bs.user</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an access group
            </p>
            <p>
                Create access group
            </p>
        </field>
    </record>
</odoo>
