<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: card_type_view_form-->
    <record id="card_type_view_form" model="ir.ui.view">
        <field name="name">ams_bs.card_type.form</field>
        <field name="model">ams_bs.card_type</field>
         <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
           <xpath expr="//group[@name='basic_info_col1']" position="inside">
               <field name="mode"/>
               <field name="type"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: card_type_view_list-->
    <record id="card_type_view_list" model="ir.ui.view">
        <field name="name">ams_bs.card_type.tree</field>
        <field name="model">ams_bs.card_type</field>
         <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="mode"/>
                <field name="type"/>
            </field>
        </field>
    </record>

    <!--TODO[IMP]: card_type_view_search-->
    <record id="card_type_view_search" model="ir.ui.view">
        <field name="name">ams_bs.card_type.search</field>
        <field name="model">ams_bs.card_type</field>
      <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
             <field name="name" position="after">
                <field name="mode"/>
                <field name="type"/>
            </field>
        </field>
    </record>

    <!--TODO[IMP]:card_type_action-->
    <record id="card_type_action" model="ir.actions.act_window">
        <field name="name">Card Types</field>
        <field name="res_model">ams_bs.card_type</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create card_type
            </p>
            <p>
                Create card_type
            </p>
        </field>
    </record>


</odoo>
