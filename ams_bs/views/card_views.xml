<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]:  card_view_form-->
    <record id="card_view_form" model="ir.ui.view">
        <field name="name">Cards</field>
        <field name="model">ams_bs.card</field>
        <field name="inherit_id" ref="ams.ams_base_card_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="header" position="inside">

                <button name="action_push_card" type="object" string="Push Card"
                        icon="fa-cloud-upload"
                        class="btn-primary"
                />
                <button name="action_delete_card" type="object" string="Delete Card"
                        icon="fa-trash"
                        class="btn-danger"
                />
            </xpath>
            <field name="card_number" position="after">
                <field name="card_type_id" domain="[('synced', '=', True)]"/>
                <field name="user_id" domain="[('synced', '=', True)]"/>
            </field>
            <field name="is_assigned" position="after">
                <field name="synced"/>
                </field>
        </field>
    </record>


    <!--TODO[IMP]:  card_view_list-->
    <record id="card_view_list" model="ir.ui.view">
        <field name="name">Cards</field>
        <field name="model">ams_bs.card</field>
        <field name="inherit_id" ref="ams.ams_base_card_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="card_number" position="after">
                <field name="card_type_id" optional="show"/>
                <field name="user_id" optional="show"/>
            </field>
        </field>
    </record>


    <!--TODO[IMP]: card_view_search-->
    <record id="card_view_search" model="ir.ui.view">
        <field name="name">Cards</field>
        <field name="model">ams_bs.card</field>
        <field name="inherit_id" ref="ams.ams_base_card_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="attributes">
            </xpath>
        </field>
    </record>


    <!--TODO[IMP]: card_action-->
    <record id="card_action" model="ir.actions.act_window">
        <field name="name">card</field>
        <field name="res_model">ams_bs.card</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>
</odoo>
