<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: device_type_view_form-->
    <record id="device_type_view_form" model="ir.ui.view">
        <field name="name">ams_bs.device_type.form</field>
        <field name="model">ams_bs.device_type</field>
         <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
           <xpath expr="//group[@name='basic_info_col1']" position="inside">
               <field name="input_port_num"/>
               <field name="output_port_num"/>
               <field name="relay_num"/>
               <field name="enable_card"/>
               <field name="enable_face"/>
               <field name="enable_fingerprint"/>
               <field name="enable_wifi"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: device_type_view_list-->
    <record id="device_type_view_list" model="ir.ui.view">
        <field name="name">ams_bs.device_type.tree</field>
        <field name="model">ams_bs.device_type</field>
         <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="input_port_num" optional="hide"/>
               <field name="output_port_num" optional="hide"/>
                <field name="relay_num" optional="hide"/>
               <field name="enable_card" optional="hide"/>
               <field name="enable_face" optional="hide"/>
               <field name="enable_fingerprint" optional="hide"/>
               <field name="enable_wifi" optional="hide"/>

            </field>
        </field>
    </record>

    <!--TODO[IMP]: device_type_view_search-->
    <record id="device_type_view_search" model="ir.ui.view">
        <field name="name">ams_bs.device_type.search</field>
        <field name="model">ams_bs.device_type</field>
      <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
         <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="attributes">
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]:device_type_action-->
    <record id="device_type_action" model="ir.actions.act_window">
        <field name="name">device Types</field>
        <field name="res_model">ams_bs.device_type</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create device_type
            </p>
            <p>
                Create device_type
            </p>
        </field>
    </record>


</odoo>
