<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: device_view_form-->
    <record id="device_view_form" model="ir.ui.view">
        <field name="name">ams_bs.device.form</field>
        <field name="model">ams_bs.device</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">

            <field name="name" position="after">
                <field name="device_group_id" domain="[('synced', '=', True)]"/>
                <field name="device_type_id" domain="[('synced', '=', True)]"/>
            </field>
            <xpath expr="header" position="inside">
                <field name="state" widget="statusbar"/>
                    <button name="action_sync_events" type="object" string="Sync Events" class="btn-primary"/>
                    <button name="action_sync_device" type="object" string="Sync Device" class="btn-primary"/>

                <button name="action_sync_events" type="object" string="Sync Events" class="btn-primary" />
                <button name="action_push_device" type="object" string="Push Device" class="btn-primary" />
                 <button name="action_delete_devices" type="object" string="Delete Device" class="btn btn-outline-danger" icon="fa-trash" invisible="synced==False"
                         title="Delete device from BioStar only" confirm="Are you sure you want to delete this device from BioStar? This action cannot be undone."/>

                </xpath>

            <xpath expr="//group[@name='basic_info_col1']" position="inside">
                <field name="ip"/>
                <field name="port"/>
                <field name="device_serial"/>
                <field name="state"/>
            </xpath>
            <xpath expr="//group[@name='basic_info_col2']" position="inside">
                <field name="last_log_id"/>
                <field name="last_log_date"/>
                <field name="activate"/>
                <field name="log_active"/>
            </xpath>

            <xpath expr="//page[@name='technical']" position="after">
                <page name="exit_buttons" string="Exit Buttons">
                    <field name="device_exit_button_ids">
                        <list editable="bottom">
                            <field name="name" />
                            <field name="port" />
                            <field name="type" />
                        </list>
                    </field>
                </page>

                <page name="sensors" string="Sensors">
                    <field name="device_sensor_ids">
                        <list editable="bottom">
                            <field name="name" />
                            <field name="port" />
                            <field name="type" />
                        </list>
                    </field>
                </page>

                <page name="relays" string="Relays">
                    <field name="device_relay_ids">
                        <list editable="bottom">
                            <field name="name" />
                            <field name="port" />
                            <field name="type" />
                        </list>
                    </field>
                </page>

            </xpath>

        </field>
    </record>

    <!--TODO[IMP]: device_view_list-->
    <record id="device_view_list" model="ir.ui.view">
        <field name="name">ams_bs.device.list</field>
        <field name="model">ams_bs.device</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="device_group_id"/>
                <field name="state" decoration-warning="state == '2'" decoration-success="state == '1'" decoration-danger="state == '0'"/>
                <field name="device_serial" optional="hide"/>
                <field name="ip" optional="hide"/>
            </field>

            <xpath expr="//header" position="inside">
                <button name="action_delete_devices" type="object" string="Delete Devices" class="btn btn-outline-danger" icon="fa-trash"
                   title="Delete selected devices from BioStar only"     confirm="Are you sure you want to delete the selected devices from BioStar? This action cannot be undone."/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: device_view_search-->
    <record id="device_view_search" model="ir.ui.view">
        <field name="name">ams_bs.device.search</field>
        <field name="model">ams_bs.device</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="device_group_id"/>
                <field name="state"/>
                <field name="device_serial" />
                <field name="ip"/>

            </field>
        </field>
    </record>

    <!--TODO[IMP]: device_action-->
    <record id="device_action" model="ir.actions.act_window">
        <field name="name">Devices</field>
        <field name="res_model">ams_bs.device</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an access group
            </p>
            <p>
                Create access group
            </p>
        </field>
    </record>
</odoo>
