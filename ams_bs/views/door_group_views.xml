<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: door_group_view_form-->
    <record id="door_group_view_form" model="ir.ui.view">
        <field name="name">ams_bs.door_group.form</field>
        <field name="model">ams_bs.door_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="parent_id"
                       domain="[('synced', '=', True)]"/>
                <field name="depth"/>
                <field name="parent_id"/>
            </field>
            <xpath expr="//page[@name='basic_info']" position="replace">
                <page name="doors" string="Doors">
                    <field name="door_ids"
                           domain="[('synced', '=', True)]">
                        <list>
                            <field name="name"/>
                            <field name="description" optional="hide"/>
                            <field name="record_id" optional="hide"/>
                            <field name="synced" optional="hide"/>
                            <field name="last_sync_date" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
            <xpath expr="//header" position="inside">
                <button name="action_push_door_group" string="Push Door Group" icon="fa-cloud-upload" type="object" class="oe_highlight" />
                <button name="action_delete_door_group"
                        string="Delete Door Group"
                        type="object"
                        class="btn-danger"
                        confirm="Are you sure you want to delete this door group from BioStar?"
                       invisible="synced==False"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_group_view_list-->
    <record id="door_group_view_list" model="ir.ui.view">
        <field name="name">ams_bs.door_group.list</field>
        <field name="model">ams_bs.door_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <!-- Add header with delete button for multi-selection -->
            <xpath expr="//list" position="inside">
                <header>
                    <button name="action_delete_door_group"
                            string="Delete Selected Door Groups"
                            type="object"
                            class="btn btn-outline-danger"
                            confirm="Are you sure you want to delete the selected door groups from BioStar?"/>
                </header>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_group_view_search-->
    <record id="door_group_view_search" model="ir.ui.view">
        <field name="name">ams_bs.door_group.search</field>
        <field name="model">ams_bs.door_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="door_ids"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_group_action-->
    <record id="door_group_action" model="ir.actions.act_window">
        <field name="name">Door Groups</field>
        <field name="res_model">ams_bs.door_group</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a door group
            </p>
            <p>
                Create door group
            </p>
        </field>
    </record>
</odoo>
