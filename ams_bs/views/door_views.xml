<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: door_view_form-->
    <record id="door_view_form" model="ir.ui.view">
        <field name="name">ams_bs.door.form</field>
        <field name="model">ams_bs.door</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="action_sync_ac_door" string="Sync Door"  type="object"  class="oe_highlight"  />

                <button name="action_push_ac_door" string="Push Door"   icon="fa-cloud-upload" type="object"  class="oe_highlight"  />

                 <button name="action_delete_ac_door" string="Delete Door" title="Delete door from BioStar only"  type="object" class="btn btn-outline-danger" icon="fa-trash" confirm="Are you sure you want to delete this door from BioStar?"
                        invisible="synced==False"/>

            </xpath>


            <field name="name" position="after">
                <field name="door_group_id" domain="[('synced', '=', True)]"/>
            </field>
             <xpath expr="//page[@name='technical']" position="after">
                <page name="configuration" string="Configuration">
                       <group>
                           <group>
                               <field name="entry_device_id" options="{'no_create': True}" domain="[('synced', '=', True)]"/>
                               <field name="exit_device_id" options="{'no_create': True}" domain="[('synced', '=', True)]"/>
                               <field name="device_relay_id" domain="[('device_id', '=', entry_device_id)]"/>
                               <field name="device_sensor_id" domain="[('device_id', '=', entry_device_id)]"/>
                               <field name="device_exit_button_id" domain="[('device_id', '=', entry_device_id)]"/>

                           </group>
                           <group>
                               <field name="open_time_out"/>
                               <field name="open_duration"/>
                               <field name="open_once"/>
                               <field name="unconditional_lock"/>
                           </group>
                       </group>
                </page>
             </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_view_list-->
    <record id="door_view_list" model="ir.ui.view">
        <field name="name">ams_bs.door.list</field>
        <field name="model">ams_bs.door</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <!-- Add header with delete button for multi-selection -->
            <xpath expr="//list" position="inside">
                <header>
                    <button name="action_delete_ac_door" 
                            string="Delete Doors" 
                            type="object" 
                            class="btn btn-outline-danger"
                            confirm="Are you sure you want to delete the selected doors from BioStar?"/>
                </header>
            </xpath>
             <xpath expr="//field[@name='name']" position="after">
                <field name="door_group_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_view_search-->
    <record id="door_view_search" model="ir.ui.view">
        <field name="name">ams_bs.door.search</field>
        <field name="model">ams_bs.door</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="door_group_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: door_action-->
    <record id="door_action" model="ir.actions.act_window">
        <field name="name">Doors</field>
        <field name="res_model">ams_bs.door</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a door
            </p>
            <p>
                Create door
            </p>
        </field>
    </record>
</odoo>
