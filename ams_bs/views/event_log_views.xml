<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: event_log_view_form-->
    <record id="event_log_view_form" model="ir.ui.view">
        <field name="name">ams_bs.event_log.form</field>
        <field name="model">ams_bs.event_log</field>
<!--        <field name="inherit_id" ref="ams_base.api_model_view_form"/>-->
<!--        <field name="mode">primary</field>-->
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <header>
                        <button name="action_execute_punch_log"
                                string="Execute Punch Log"
                                type="object"
                                class="btn-primary"
                                invisible="not is_punch_log"
                        />
                    </header>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="device_serial"/>
                            <field name="event_id"/>
                            <field name="event_code"/>
                            <field name="code_name"/>
                            <field name="event_datetime"/>
                        </group>
                        <group>
                            <field name="enroll_number"/>
                            <field name="synced"/>
                            <field name="last_sync_date"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="basic_info" string="Basic Info">
                            <group>
                                <group>
                                    <field name="log_date"/>
                                    <field name="executed_datetime"/>
                                    <field name="has_image"/>
                                    <field name="enroll_name"/>
                                    <field name="sub_code"/>
                                </group>
                                <group>
                                    <field name="temperature"/>
                                    <field name="timestamp"/>
                                    <field name="description"/>
                                    <field name="state"/>
                                    <field name="is_punch_log"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: event_log_view_list-->
    <record id="event_log_view_list" model="ir.ui.view">
        <field name="name">ams_bs.event_log.list</field>
        <field name="model">ams_bs.event_log</field>
<!--        <field name="inherit_id" ref="ams_base.api_model_view_list"/>-->
        <field name="arch" type="xml">
            <list>
                <field name="name" optional="hide"/>
                <field name="device_serial"/>
                <field name="event_id"/>
                <field name="event_code"/>
                <field name="code_name"/>
                <field name="event_datetime"/>
                <field name="enroll_number"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: event_log_view_search-->
    <record id="event_log_view_search" model="ir.ui.view">
        <field name="name">ams_bs.event_log.search</field>
        <field name="model">ams_bs.event_log</field>
<!--        <field name="inherit_id" ref="ams_base.api_model_view_search"/>-->
        <field name="arch" type="xml">
            <search>
                 <field name="device_serial"/>
                <field name="event_id"/>
                 <field name="event_code"/>
                <field name="code_name"/>
                <field name="event_datetime"/>
                 <field name="enroll_number"/>

            </search>
        </field>
    </record>

    <!--TODO[IMP]: event_log_action-->
    <record id="event_log_action" model="ir.actions.act_window">
        <field name="name">Event Logs</field>
        <field name="res_model">ams_bs.event_log</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an access group
            </p>
            <p>
                Create access group
            </p>
        </field>
    </record>
</odoo>
