<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <menuitem id="top_menu" name="Biostar 2" sequence="19" web_icon="ams_bs,static/description/icon.png" groups="ams_base.ams_group_manager,base.group_system,ams_base.ams_group_user"/>

        <menuitem id="op_menu" name="Operations" sequence="40" parent="top_menu">
            <menuitem id="event_log_menu" name="Event Log" action="event_log_action" sequence="5025"/>
        </menuitem>
        <menuitem id="device_config_menu" name="Devices" sequence="45" parent="top_menu">
            <menuitem id="device_group_menu" name="Device Group" action="device_group_action" sequence="10"/>
            <menuitem id="device_menu" name="Device" action="device_action" sequence="20"/>
            <menuitem id="user_group_menu" name="User Group" action="user_group_action" sequence="30"/>
            <menuitem id="user_menu" name="User" action="user_action" sequence="40"/>

        </menuitem>
        <menuitem id="config_menu" name="Configurations" sequence="50" parent="top_menu">
            <menuitem id="schedule_menu" name="Schedules" action="schedule_action" sequence="10"/>
            <menuitem id="door_group_menu" name="Door Groups" action="door_group_action" sequence="20"/>
            <menuitem id="door_menu" name="Doors" action="door_action" sequence="30"/>
            <menuitem id="access_level_menu" name="Access Levels" action="access_level_action" sequence="40"/>
            <menuitem id="access_group_menu" name="Access Groups" action="access_group_action" sequence="50"/>

            <menuitem id="card_type_menu" name="Card Type" action="card_type_action" sequence="60"/>
            <menuitem id="card_menu" name="Cards" action="card_action" sequence="70"/>
            <menuitem id="device_type_menu" name="Device Types" action="device_type_action" sequence="80"/>

            <menuitem id="company_menu" name="Company" action="base.action_res_company_form" sequence="100"/>

        </menuitem>

        <menuitem id="menu_sync" name="Biostar Sync" sequence="60" parent="top_menu">
            <menuitem id="menu_sync_user_groups"
                      name="Sync User Groups"
                      action="action_sync_user_groups"/>

            <menuitem id="menu_sync_users"
                      name="Sync Users"
                      action="action_sync_users"/>

            <menuitem id="menu_sync_device_groups"
                      name="Sync Device Groups"
                      action="action_sync_device_groups"/>

            <menuitem id="menu_sync_devices"
                      name="Sync Devices"
                      action="action_sync_devices"/>

             <menuitem id="menu_sync_device_types"
                      name="Sync Device Types"
                      action="action_sync_device_types"/>

            <menuitem id="menu_sync_doors"
                      name="Sync Doors"
                      action="action_sync_doors"/>

            <menuitem id="menu_sync_schedules"
                      name="Sync Schedules"
                      action="action_sync_schedules"/>

            <menuitem id="menu_sync_access_levels"
                      name="Sync Access Levels"
                      action="action_sync_access_levels"/>

            <menuitem id="menu_sync_access_groups"
                      name="Sync Access Groups"
                      action="action_sync_access_groups"/>


        </menuitem>


    </data>
</odoo>