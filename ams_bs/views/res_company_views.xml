<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="ams_bs_company_view_form_inherit" model="ir.ui.view">
        <field name="name">ams_company.form.inherit</field>
        <field name="model">res.company</field>
        <field name="inherit_id" ref="base.view_company_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="before">
                <div class="alert alert-danger" role="alert" invisible="not error">
                    <strong>Error! </strong> <field name="error_msg" readonly="1"/>
                    <button name="action_reset_error" icon="fa-close" type="object" class="btn btn-link mx-5"
                            title="Close error"/>
                </div>

            </xpath>
            <notebook position="inside">
                <page string="BioStar 2 Settings">
                    <group>
                        <header>

                            <button name="action_sync" type="object" string="Sync User Groups" class="btn-primary"
                                    context="{'model':'ams_bs.user_group','sync_method_name':'_sync_user_groups'}"/>

                            <button name="action_sync" type="object" string="Sync Users" class="btn-primary"
                                    context="{'model':'ams_bs.user','sync_method_name':'_sync_users'}"/>

                            <button name="action_sync" type="object" string="Sync Device Groups" class="btn-primary"
                                    context="{'model':'ams_bs.device_group','sync_method_name':'_sync_device_groups'}"/>

                            <button name="action_sync" type="object" string="Sync Devices" class="btn-primary"
                                    context="{'model':'ams_bs.device','sync_method_name':'_sync_devices'}"/>

                             <button name="action_sync" type="object" string="Sync Device Types" class="btn-primary"
                                    context="{'model':'ams_bs.device_type','sync_method_name':'_sync_device_types'}"/>


                            <button name="action_sync" type="object" string="Sync Doors" class="btn-primary"
                                    context="{'model':'ams_bs.door','sync_method_name':'_sync_doors'}"/>

                            <button name="action_sync" type="object" string="Sync Schedules" class="btn-primary"
                                    context="{'model':'ams_bs.schedule','sync_method_name':'_sync_schedules'}"/>


                            <button name="action_sync" type="object" string="Sync Access Levels" class="btn-primary"
                                    context="{'model':'ams_bs.access_level','sync_method_name':'_sync_access_levels'}"/>

                            <button name="action_sync" type="object" string="Sync Access Groups" class="btn-primary"
                                    context="{'model':'ams_bs.access_group','sync_method_name':'_sync_access_groups'}"/>

                            <button name="action_sync" type="object" string="Sync Card Types" class="btn-primary"
                                    context="{'model':'ams_bs.card_type','sync_method_name':'_sync_card_types'}"/>

                        <button name="action_sync" type="object" string="Sync Door Groups" class="btn-primary"
                                context="{'model':'ams_bs.door_group','sync_method_name':'_sync_door_groups'}"/>

                            <button name="action_sync" type="object" string="Sync Cards" class="btn-primary"
                                    context="{'model':'ams_bs.card','sync_method_name':'_sync_cards'}"/>

                            <button name="action_sync" type="object" string="Sync Events" class="btn-primary"
                                    context="{'model':'ams_bs.event_log','sync_method_name':'_sync_events'}"/>

                        </header>

                    </group>
                    <group>
                        <group>
                            <field name="bs_base_url"/>
                            <field name="api_username"/>
                            <field name="api_password" password="True" />
                            <field name="bs_last_event_log_datetime"/>

                        </group>
                        <group>

                        </group>

                    </group>
                </page>
            </notebook>
        </field>
    </record>
</odoo>
