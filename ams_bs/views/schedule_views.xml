<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: schedule_view_form-->
    <record id="schedule_view_form" model="ir.ui.view">
        <field name="name">ams_bs.schedule.form</field>
        <field name="model">ams_bs.schedule</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="action_sync_schedule" type="object" string="Sync Schedule" 
                        class="oe_highlight"/>
                <button name="action_push_schedule" type="object" string="Push Schedule" 
                        class="oe_highlight"/>
                <button name="action_delete_schedule"
                        string="Delete Schedule"
                        type="object"
                        class="btn btn-outline-danger" icon="fa-trash"
                        confirm="Are you sure you want to delete this schedule from BioStar?"
                        invisible="synced==False"/>
            </xpath>
            <xpath expr="//field[@name='description']" position="after">
                <field name="schedule_type" />
            </xpath>
            <xpath expr="//page[@name='basic_info']" position="replace">

            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: schedule_view_list-->
    <record id="schedule_view_list" model="ir.ui.view">
        <field name="name">ams_bs.schedule.list</field>
        <field name="model">ams_bs.schedule</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="inside">

            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: schedule_view_search-->
    <record id="schedule_view_search" model="ir.ui.view">
        <field name="name">ams_bs.schedule.search</field>
        <field name="model">ams_bs.schedule</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">

            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: schedule_action-->
    <record id="schedule_action" model="ir.actions.act_window">
        <field name="name">Schedules</field>
        <field name="res_model">ams_bs.schedule</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a schedule
            </p>
            <p>
                Create schedule
            </p>
        </field>
    </record>
</odoo>
