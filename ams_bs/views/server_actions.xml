<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="action_sync_user_groups" model="ir.actions.server">
        <field name="name">Sync User Groups</field>
        <field name="model_id" ref="model_ams_base_api_model"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_base.api_model'].with_context(
                {'model': 'ams_bs.user_group', 'sync_method_name': '_sync_user_groups'}).action_sync()
        </field>
    </record>

    <record id="action_sync_users" model="ir.actions.server">
        <field name="name">Sync Users</field>
        <field name="model_id" ref="model_ams_base_api_model"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_base.api_model'].with_context(
                {'model': 'ams_bs.user', 'sync_method_name': '_sync_users'}).action_sync()
        </field>
    </record>

    <record id="action_sync_device_groups" model="ir.actions.server">
        <field name="name">Sync Device Groups</field>
        <field name="model_id" ref="model_ams_base_api_model"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_base.api_model'].with_context(
                {'model': 'ams_bs.device_group', 'sync_method_name': '_sync_device_groups'}).action_sync()
        </field>
    </record>

    <record id="action_sync_devices" model="ir.actions.server">
        <field name="name">Sync Devices</field>
        <field name="model_id" ref="model_ams_base_api_model"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_base.api_model'].with_context(
                {'model': 'ams_bs.device', 'sync_method_name': '_sync_devices'}).action_sync()
        </field>
    </record>

     <record id="action_sync_device_types" model="ir.actions.server">
        <field name="name">Sync Device Types</field>
        <field name="model_id" ref="model_ams_base_api_model"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_base.api_model'].with_context(
                {'model': 'ams_bs.device_type', 'sync_method_name': '_sync_device_types'}).action_sync()
        </field>
    </record>


    <record id="action_sync_doors" model="ir.actions.server">
        <field name="name">Sync Doors</field>
        <field name="model_id" ref="model_ams_base_api_model"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_base.api_model'].with_context(
                {'model': 'ams_bs.door', 'sync_method_name': '_sync_doors'}).action_sync()
        </field>
    </record>

    <record id="action_sync_schedules" model="ir.actions.server">
        <field name="name">Sync Schedules</field>
        <field name="model_id" ref="model_ams_base_api_model"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_base.api_model'].with_context(
                {'model': 'ams_bs.schedule', 'sync_method_name': '_sync_schedules'}).action_sync()
        </field>
    </record>

    <record id="action_sync_access_levels" model="ir.actions.server">
        <field name="name">Sync Access Levels</field>
        <field name="model_id" ref="model_ams_base_api_model"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_base.api_model'].with_context(
                {'model': 'ams_bs.access_level', 'sync_method_name': '_sync_access_levels'}).action_sync()
        </field>
    </record>

    <record id="action_sync_access_groups" model="ir.actions.server">
        <field name="name">Sync Access Groups</field>
        <field name="model_id" ref="model_ams_base_api_model"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_base.api_model'].with_context(
                {'model': 'ams_bs.access_group', 'sync_method_name': '_sync_access_groups'}).action_sync()
        </field>
    </record>
</odoo>
