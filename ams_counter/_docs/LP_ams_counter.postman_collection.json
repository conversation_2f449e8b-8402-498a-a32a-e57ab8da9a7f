{"info": {"_postman_id": "aa0fc4b2-c71e-491f-a425-111ab28d8da2", "name": "LP_ams_counter", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "30587614"}, "item": [{"name": "trigger_counter", "request": {"method": "GET", "header": [], "url": {"raw": "http://ahmed-hp-zbook-15-g6:8069/trigger_counter?category_name=enter", "protocol": "http", "host": ["ahmed-hp-zbook-15-g6"], "port": "8069", "path": ["trigger_counter"], "query": [{"key": "category_name", "value": "occupancy", "disabled": true}, {"key": "category_name", "value": "enter"}, {"key": "category_name", "value": "exit", "disabled": true}]}}, "response": []}]}