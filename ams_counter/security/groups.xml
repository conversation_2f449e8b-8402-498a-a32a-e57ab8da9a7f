<?xml version="1.0" encoding="utf-8"?>
<odoo>
     <record id="ams_counter_group_category" model="ir.module.category" >
        <field name="name">IoT Category</field>
        <field name="description">IoT Category</field>
        <field name="sequence">10</field>
    </record>

    <record id="ams_group_counter_manager" model="res.groups">
        <field name="name">Counter Manager</field>
        <field name="category_id" ref="ams_counter_group_category"/>
    </record>

<!--    <record id="ams_group_user" model="res.groups">-->
<!--        <field name="name">AMS User</field>-->
<!--        <field name="category_id" ref="ams_group_category"/>-->
<!--    </record>-->

<!--    <record id="ams_group_readonly" model="res.groups">-->
<!--        <field name="name">AMS ReadOnly</field>-->
<!--        <field name="category_id" ref="ams_group_category"/>-->
<!--    </record>-->

</odoo>
