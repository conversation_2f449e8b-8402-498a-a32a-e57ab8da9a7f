/* @odoo-module */
import {Component, onWillStart, onMounted, onWillUnmount, onWillDestroy, useState, useRef} from "@odoo/owl";

import {loadJS} from "@web/core/assets";
import {useService} from "@web/core/utils/hooks";

export class ChartComponent extends Component {
    static template = "ams_counter.ChartComponent";
    static props = {
        label: String,
        // data: Object,
        type: String,  // Accept chart type in props
        options: Object,  // Accept chart options in props
    };

    setup() {
        this.currentDate = new Date().toISOString().split('T')[0];
        this.action = useService("action");
        this.orm = useService("orm");
        this.canvasRef = useRef("canvas");
        this.chart = null;  // Store the chart instance
        this.busService = this.env.services.bus_service;
        this.state = useState(
            {data: {}},
        );
        onWillStart(() => loadJS("/web/static/lib/Chart/Chart.js"));
        onMounted(async () => {
            this.state.data = await this.fetchAndAggregateData()
            this.renderChart();
        });
        onWillUnmount(() => {
            // this.chart.destroy();
        });

        const refreshBusListener = async (payload) => {
            if (payload.model === "ams_counter.queue") {
                this.state.data = await this.fetchAndAggregateData()
                this.renderChart();
            }
        }
        this.busService.subscribe('auto_refresh', refreshBusListener);
        this.busService.addChannel('auto_refresh');
        this._refreshStopBus = () => {
            this.busService.unsubscribe('auto_refresh', refreshBusListener);
            this.busService.deleteChannel('auto_refresh');
        }

        onWillDestroy(() => {
            this._refreshStopBus();
        });

    }

    renderChart() {
        // console.log("AwesomeDashboard >> renderChart , data:", this.props.data);

        // Destroy the existing chart instance if it exists
        if (this.chart) {
            this.destroyChart();
        }

        // Prepare datasets dynamically
        const labels = Object.keys(this.state.data.aggregatedData);  // 00:00, 01:00, ...
        const datasets = this.state.data.categories.map(category => ({
            label: category,
            data: labels.map(hour => this.state.data.aggregatedData[hour][category]),
            backgroundColor: this.getRandomColor(),
        }));

        // Create a new Chart.js instance
        this.chart = new Chart(this.canvasRef.el, {
            type: this.props.type,
            data: {
                labels: labels,
                datasets: datasets,
            },
            options: this.props.options,
        });
    }

    getRandomColor() {
        // Generate darker RGB color by limiting the random value range
        const r = Math.floor(Math.random() * 150); // Red: 0-149
        const g = Math.floor(Math.random() * 150); // Green: 0-149
        const b = Math.floor(Math.random() * 150); // Blue: 0-149
        return `rgba(${r}, ${g}, ${b}, 0.6)`; // Include transparency for better visuals
    }

    destroyChart() {
        if (this.chart) {
            this.chart.destroy();  // Destroy the existing chart instance
            this.chart = null;  // Reset the chart instance
        }
    }

    async fetchAndAggregateData() {
        const domain = [
            ['date', '=', this.currentDate],
        ];
        // Step 1: Fetch records from `ams_counter.queue` model
        const records = await this.orm.searchRead("ams_counter.queue", domain, ["timestamp", "category_name"]);

        console.log("AwesomeDashboard >> fetchAndAggregateData , records:", records);


        // Step 2: Extract unique categories dynamically
        const categories = [...new Set(records.map(r => r.category_name).filter(Boolean))];

        console.log("AwesomeDashboard >> fetchAndAggregateData , categories:", categories);


        // Step 3: Initialize the hour-wise data structure dynamically
        const aggregatedData = {};
        for (let i = 0; i < 24; i++) {
            const hour = `${String(i).padStart(2, "0")}:00`;
            aggregatedData[hour] = {};
            for (const category of categories) {
                aggregatedData[hour][category] = 0;
            }
        }

        console.log("AwesomeDashboard >> fetchAndAggregateData , aggregatedData:", aggregatedData);

        // Step 4: Aggregate counts dynamically
        for (const record of records) {
            const timestamp = record.timestamp;
            const category = record.category_name;

            if (timestamp && category) {
                // Create a Date object from the UTC timestamp
                const dateUTC = new Date(timestamp);

                // Get the local time zone offset
                const localOffset = new Date().getTimezoneOffset();
                const localTime = new Date(dateUTC.getTime() - (localOffset * 60000));

                const hour = `${String(localTime.getHours()).padStart(2, "0")}:00`;

                // Increment count for the hour and category
                aggregatedData[hour][category] += 1;
            }
        }

        const data = {
            'aggregatedData': aggregatedData,
            'categories': categories
        }

        console.log("AwesomeDashboard >> fetchAndAggregateData , data:", data);

        return data

    }

}
