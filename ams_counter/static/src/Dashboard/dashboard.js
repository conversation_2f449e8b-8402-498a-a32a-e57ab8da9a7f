/* @odoo-module */
import {Component, onWillStart, onWill<PERSON>estroy} from "@odoo/owl";
import {registry} from "@web/core/registry";
import {Layout} from "@web/search/layout";
import {useService} from "@web/core/utils/hooks";
import {DashboardCountItem} from "./dashboard_count_item";
import {ChartComponent} from "./chart/chart";

class AwesomeDashboard extends Component {
    static template = "ams_counter.dashboard";
    static components = {Layout, DashboardCountItem, ChartComponent};

    setup() {
        this.action = useService("action");
        this.orm = useService("orm");

        this.display = {
            controlPanel: {},
        };
        onWillStart(async () => {
            this.statistics = {};
            // this.statistics.queueCounts = await this.fetchAndAggregateData();
        });

        // console.log("AwesomeDashboard >> setup , this.statistics:", this.statistics);
    }


}

registry.category("actions").add("ams_counter.dashboard", AwesomeDashboard);
