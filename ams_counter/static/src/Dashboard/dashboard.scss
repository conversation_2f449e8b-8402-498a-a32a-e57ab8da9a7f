.o_dashboard {
    background-color: white;
}

/* Card customization */
.card {
  border-radius: 15px 15px 0 0;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  top: 25px;
  //overflow: hidden;
}

/* Circular icon half outside the card */
.icon-circle {
  width: 80px;
  height: 80px;
  border: 3px solid #f8f9fa;
  border-radius: 50%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translate(-50%, -50%);
  z-index: 1;

}

/* Show More button animation */
.show-more-btn {
  border: 3px solid #fff0ff;
  //border-bottom: 1px solid transparent ;
  color: #fff;
  font-size: large;
  background-color: transparent;
  //border-radius: 25px;
  transition: transform 0.3s ease, background-color 0.3s ease;
  position: absolute;
  bottom: 1px;
  left: 0;
  width: 100%;

}

.show-more-btn:hover {
  transform: translateY(-5px);
  background-color: #007bff;
  color: white;
}

/* Adjust the shape icon style */
.shape-icon i {
  //color: rgba(255, 255, 255, 0.7);
}
