<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
    <t t-name="ams_counter.dashboard">
        <Layout display="display" className="'o_dashboard h-100 bg-light'">

            <div class="container-fluid  text-center mb-4 mt-4">
        <div class="d-flex flex-column align-items-center">
            <h3 class="text-primary mx-2"><PERSON></h3>
            <h3 class="text-primary mx-2" t-esc="new Date().toISOString().split('T')[0]"/>
        </div>
    </div>
            <div class="d-flex flex-grow-1 w-100 h-50">

                <ChartComponent
                        label="'Category Counting Report'"
                        type="'bar'"
                        options="{
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    x: { title: { display: true, text: 'Time (Hours)' } },
                                    y: { beginAtZero: true, title: { display: true, text: 'Count' } }
                                },
                                plugins: {
                                    legend: { position: 'top' },
                                    title: { display: true, text: 'Category Counting Report' }
                                }
                            }"
                />

            </div>

        </Layout>
    </t>
</templates>
