/* @odoo-module */
import {Component, useState, onWill<PERSON><PERSON>roy} from "@odoo/owl";
import {useService} from "@web/core/utils/hooks";

export class DashboardCountItem extends Component {
    static template = "sms_module.DashboardCountItem"
    static props = {
        size: {
            type: Number,
            optional: true,
        },
        color: {
            type: String,
            optional: true,
        },
        main_icon: {
            type: String,
            optional: true,
        },
        chart_icon: {
            type: String,
            optional: true,
        },
        label: {
            type: String,
            optional: true,
        },
        model: {
            type: String,
            optional: true,
        },
        domain: {
            type: Array,
            optional: true,
        },
    };
    static defaultProps = {
        size: 1,
        color: "bg-success",
        main_icon: "fa-user",
        chart_icon: "fa-bar-chart",
        label: "Students",
        model: "sms_module.student",
        domain: [],
    };

    setup() {
        console.log("DashboardCountItem >> setup , this.props:", this.props);

        this.action = useService("action");
        this.orm = useService("orm");


        this.state = useState({count: 0});

        this.fetchCount(); // Trigger fetching count on setup


        console.log("auto refresh >> DashboardCountItem >> setup");
        this.env.services.bus_service.addChannel('auto_refresh'); // New channel
        const listenerRef = this.env.services.bus_service.addEventListener('notification', ({detail: notifications}) => {
            console.log("auto refresh >> DashboardCountItem >> notifications:", notifications);

            for (const {payload, type} of notifications) {
                if (type === "auto_refresh" && payload.model === this.props.model) {
                    this.fetchCount();
                }
            }
        });
        this.env.services.bus_service.start();

        onWillDestroy(() => {
            console.log("auto refresh >> DashboardCountItem >> onWillDestroy");
            this.env.bus.removeEventListener("notification", listenerRef);
            this.env.services.bus_service.stop();
        });


    }

    async fetchCount() {
        // try {
        //     const count = await this.orm.searchCount(this.props.model, this.props.domain);
        //     console.log("DashboardCountItem >> Count fetched:", count);
        //     this.state.count = count; // Store the resolved count in the component state
        // } catch (error) {
        //     console.error("Error fetching count:", error);
        // }


        this.orm.searchCount(this.props.model, this.props.domain).then(result => {
            console.log(result);  // Logs: "Data fetched successfully!"
            this.state.count = result;
        })

    }

    get count() {
        return this.state.count; // Use the stored count from state
    }

    showMore() {
        this.action.doAction(`${this.props.model}_action`);
    }

}
