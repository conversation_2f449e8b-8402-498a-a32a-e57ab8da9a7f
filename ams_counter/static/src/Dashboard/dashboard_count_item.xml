<?xml version="1.0" encoding="UTF-8"?>

<templates xml:space="preserve">
    <t t-name="sms_module.DashboardCountItem">
        <div class="card  m-4 pt-5 text-center m-2 position-relative" t-attf-style="width: 18rem;" t-attf-class="{{props.color}}">
            <!-- Half-circle icon positioned at the top -->
            <div class="icon-circle border-light text-primary position-absolute top-0 start-50 translate-middle " t-attf-class="{{props.color}}">
                <i t-attf-class="fa {{props.main_icon}} fa-2x  text-light" ></i>
            </div>

            <div class="card-body mt-1 pt-1 text-white  "  t-attf-class="{{props.color}}">
                <!-- Title -->
                <h5 t-esc="props.label" class="mt-2 text-white fs-2 fw-bold"></h5>

                <!-- Value -->
                <h2 t-esc="count" class=" text-white fs-1 fw-bolder rounded-pill mb-2"></h2>

                <!-- Shape Icon -->
                <div class="shape-icon mb-3">
                     <i t-attf-class="fa {{props.chart_icon}} fa-2x  text-light" ></i>
                </div>

                <!-- Animated More Info Button -->
                <a class="btn btn-outline-light show-more-btn mb-0 pb-0" t-on-click="showMore" t-attf-class="{{props.color}}" >Show More</a>

            </div>
        </div>
    </t>
</templates>
