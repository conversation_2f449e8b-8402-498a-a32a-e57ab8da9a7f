/* @odoo-module */
import { registry } from "@web/core/registry";
import { memoize } from "@web/core/utils/functions";
import { useService } from "@web/core/utils/hooks";
import { rpc } from "@web/core/network/rpc";

const statisticsService = {
    async: ["loadStatistics"],
    start() {
        return {
            loadStatistics: memoize(() => rpc("/ams_counter/statistics")),
        };
    },
};

registry.category("services").add("ams_counter.statistics", statisticsService);