<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- List View -->
    <record id="queue_view_list" model="ir.ui.view">
        <field name="name">ams.counter.queue.list</field>
        <field name="model">ams_counter.queue</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="timestamp"/>
                <field name="category_name"/>
                <field name="date" optional="hide"/>
                <field name="year" optional="hide"/>
                <field name="month" optional="hide"/>
                <field name="day" optional="hide"/>
                <field name="hour" optional="hide"/>

            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="queue_view_form" model="ir.ui.view">
        <field name="name">ams.counter.queue.form</field>
        <field name="model">ams_counter.queue</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_generate_random_data" type="object" string="Generate Random Data"
                            class="oe_highlight" groups="base.group_no_one" />

                </header>
                <sheet>
                    <group>
                           <group>
                        <field name="name"/>
                        <field name="timestamp"/>
                        <field name="date" readonly="1"/>
                        <field name="category_name"/>
                    </group>
                    <group>
                        <field name="year" readonly="1"/>
                        <field name="month" readonly="1"/>
                        <field name="day" readonly="1"/>
                        <field name="hour" readonly="1"/>
                    </group>
                    </group>

                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="queue_view_search" model="ir.ui.view">
        <field name="name">ams.counter.queue.search</field>
        <field name="model">ams_counter.queue</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="timestamp"/>
                <field name="category_name"/>

                <!-- Group By Options -->
                <group expand="0" string="Group By">
                    <filter name="group_by_category" string="Category Name" context="{'group_by': 'category_name'}"/>
                    <filter name="group_by_date" string="Date" context="{'group_by': 'date'}"/>
<!--                    <filter name="group_by_year" string="Year" context="{'group_by': 'year'}"/>-->
<!--                    <filter name="group_by_month" string="Month" context="{'group_by': 'month'}"/>-->
<!--                    <filter name="group_by_day" string="Day" context="{'group_by': 'day'}"/>-->
<!--                    <filter name="group_by_hour" string="Hour" context="{'group_by': 'hour'}"/>-->
                </group>
            </search>
        </field>
    </record>

    <!-- Action Window -->
    <record id="queue_action" model="ir.actions.act_window">
        <field name="name">AMS Counter Queue</field>
        <field name="res_model">ams_counter.queue</field>
        <field name="view_mode">list,form</field>
    </record>

</odoo>