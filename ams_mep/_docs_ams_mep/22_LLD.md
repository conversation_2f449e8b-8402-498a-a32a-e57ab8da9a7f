# Low-Level Design (LLD) - Visitor Management System

## Ministry of Economy and Planning

- **Document Version:** 1.1
- **Date:** 2025-03-08
- **Author:** Software Architecture Team
- **Target Audience:** Stakeholders, Developers, System Integrators
  
---

## AMS MEP Module Architecture Analysis

### 1. Executive Summary

This document provides a comprehensive Low-Level Design analysis of the Visitor Management System implemented across the AMS (Access Management System) modules, with specific focus on the MEP (Ministry of Economy and Planning) customizations. The system is built on a modular architecture with clear separation of concerns across four main modules: `ams_base`, `ams`, `ams_vm`, and `ams_mep`.

### 2. System Architecture Overview

The system follows a layered architecture pattern with inheritance-based design:

```mermaid
graph TB
    subgraph "Foundation Layer"
        ams_base[ams_base<br/>Foundation & Abstract Models]
    end
  
    subgraph "Core Layer"
        ams[ams<br/>Core Access Management]
        ams_bs[ams_bs<br/>BioStar Integration]
    end
  
    subgraph "Application Layer"
        ams_vm[ams_vm<br/>Visitor Management]
    end
  
    subgraph "Customization Layer"
        ams_mep[ams_mep<br/>MEP Customizations]
    end
  
    ams_base --> ams
    ams_base --> ams_bs
    ams --> ams_vm
    ams_vm --> ams_mep
    ams_bs --> ams_vm
```

### 3. Core Class Hierarchy and Inheritance Structure

#### 3.1 Foundation Models (ams_base)

```mermaid
classDiagram
    class BaseAbstractModel {
        <<abstract>>
        +company_id: Many2one
        +_get_tz(): String
        +call_model_method_safely()
        +send_auto_refresh()
        +_validate_time_field()
        +_format_time()
    }
  
    class BaseRequest {
        <<abstract>>
        +name: Char
        +subject: Char
        +start_date: Datetime
        +duration: Float
        +state: Selection
        +status: Selection
        +schedule_type: Selection
        +request_uid: Many2one
        +response_uid: Many2one
        +action_approve()
 +action_confirm()
        +action_reject()
        +action_cancel()
    }
  
    class BaseAPIModel {
        <<abstract>>
        +synced: Boolean
        +last_sync_date: Datetime
        +action_sync()
        +action_unsync()
    }
  
    BaseAbstractModel <|-- BaseRequest
    BaseAbstractModel <|-- BaseAPIModel
```

#### 3.2 Access Control Models (ams/ams_bs)

```mermaid
classDiagram
    class BaseAPIModel

    class AccessGroup {
        +Boolean is_visitor_group
        +Selection api_type
        +Many2many ac_levels_ids
        +Many2many device_ids
        +One2many group_users_ids
        +One2many user_groups_ids
    }

    class AMSUser {
        +Char card_number
        +Integer enroll_number
        +Selection user_type
        +Many2one device_group_id
        +Many2one user_group_id
        +One2many card_ids
        +void action_push_user()
        +dict prepare_user_payload()
    }

    class Card {
        +Char card_number
        +Char display_card_id
        +Char status
        +Char state
        +Boolean is_blocked
        +Boolean is_assigned
        +Many2one user_id
        +Many2one card_type_id
    }

    BaseAPIModel <|-- AccessGroup
    BaseAPIModel <|-- AMSUser
    BaseAPIModel <|-- Card

    AMSUser --> Card : has
    AccessGroup --> AMSUser : contains

```

### 4. Visitor Management Core Models (ams_vm)

#### 4.1 Visitor Management Class Diagram

```mermaid
classDiagram
    class Visitor {
        +Many2one partner_id
        +Integer enroll_number
        +Char id_number
        +Char qr_code
        +Char email
        +Char first_name
        +Char last_name
        +Selection gender
        +Char organization
        +Many2one card_id
        +Boolean synced
        +Boolean activate
        +Char color
        +create()
        +_generate_unique_numeric_qr_code()
        +_compute_color()
        +_check_email_format()
    }

    class BaseVisitorRequest {
        <<abstract>>
        +Many2one visitor_id
        +Many2many access_groups_ids
        +One2many invitation_visitor_ids
        +action_approve()
        +action_cancel()
        +action_reject()
    }

    class Invitation {
        +Char name
        +Float duration
        +Char room_name
        +Char location
        +Char qr_code
        +Integer total_visits
        +Char host_work_mobile
        +Char host_work_phone
        +create()
        +action_send_invitation_email()
        +action_open_visit_view()
        +_compute_visits_count()
        +_compute_qr_codes()
    }

    class Visit {
        +Char name
        +Many2one invitation_id
        +Char invitation_qr_code
        +create()
    }

    class InvitationVisitor {
        +Char name
        +Selection state
        +Selection email_state
        +Char qr_code
        +Many2one visitor_id
        +Many2one invitation_id
        +action_send_invitation_email()
        +_generate_unique_numeric_qr_code()
    }

    class BaseAbstractModel
    class BaseRequest
    class AccessGroup

    BaseAbstractModel <|-- Visitor
    BaseRequest <|-- BaseVisitorRequest
    BaseVisitorRequest <|-- Invitation
    BaseVisitorRequest <|-- Visit
    BaseAbstractModel <|-- InvitationVisitor

    Visitor --> BaseVisitorRequest : main_visitor
    Invitation --> Visit : generates
    Invitation --> InvitationVisitor : includes
    InvitationVisitor --> Visitor : references
    AccessGroup --> BaseVisitorRequest : controls_access

```

### 5. MEP Customizations (ams_mep)

#### 5.1 MEP-Specific Extensions

```mermaid
classDiagram
    class MEPInvitation {
        +Char visitor_id_number
        +Char extra_visitor_id_number
        +Boolean information_confirmed
        +Boolean is_warning
        +Boolean check_if_visitor_exists()
        +Boolean check_if_extra_visitor_exists()
        +search_visitors()
        +add_visitor()
        +approve_visitor()
        +action_open_visitor_form()
        +action_open_extra_visitor_form()
        +_check_information_confirmed()
        +_compute_visitor_id()
    }

    class MEPVisitor {
        +_check_id_number_digits_and_length()
        +create()
    }

    class Invitation
    class Visitor

    Invitation <|-- MEPInvitation : extends
    Visitor <|-- MEPVisitor : extends



```

#### 5.2 Visitor Management Lifecycle Workflow

```mermaid
stateDiagram-v2
    [*] --> VisitorRegistration

    state VisitorRegistration {
        [*] --> CheckExisting
        CheckExisting --> CreateNew : Not Found
        CheckExisting --> UseExisting : Found
        CreateNew --> ValidateID
        ValidateID --> StoreVisitor : Valid
        ValidateID --> [*] : Invalid
        UseExisting --> StoreVisitor
        StoreVisitor --> [*]
    }

    VisitorRegistration --> InvitationCreation

    state InvitationCreation {
        [*] --> SetBasicInfo
        SetBasicInfo --> AddMainVisitor
        AddMainVisitor --> AddExtraVisitors
        AddExtraVisitors --> SelectAccessGroups
        SelectAccessGroups --> ConfirmInformation
        ConfirmInformation --> GenerateQR : Confirmed
        ConfirmInformation --> [*] : Not Confirmed
        GenerateQR --> [*]
    }

    InvitationCreation --> ApprovalWorkflow

    state ApprovalWorkflow {
        [*] --> Pending
        Pending --> NeedApproval : Submit
        Pending --> Approved : Auto-Approve
        NeedApproval --> Approved : Manager Approval
        NeedApproval --> Rejected : Manager Rejection
        Approved --> Confirmed : Email Sent
        Rejected --> [*]
        Confirmed --> [*]
    }

    ApprovalWorkflow --> VisitExecution

    state VisitExecution {
        [*] --> QRScan
        QRScan --> AccessValidation
        AccessValidation --> GrantAccess : Valid
        AccessValidation --> DenyAccess : Invalid
        GrantAccess --> CreateVisitRecord
        CreateVisitRecord --> LogEvent
        LogEvent --> [*]
        DenyAccess --> [*]
    }

```

### 6. API Design Architecture (ams_bs)

#### 6.1 BioStar API Integration Layer

```mermaid
classDiagram
    class BaseBiostarAPIClient {
        <<abstract>>
        +base_url: String
        +session: Session
        +token: String
        +login()
        +logout()
        +get_default_headers()
        +get_json_response()
    }

    class BiostarUserAPIClient {
        +get_users()
        +get_user()
        +create_user()
        +update_user()
        +delete_user()
        +scan_fingerprint()
        +scan_face()
        +scan_card()
    }

    class BiostarACAPIClient {
        +get_ac_doors()
        +get_ac_door_groups()
        +get_ac_access_groups()
        +get_ac_access_levels()
        +get_ac_schedules()
        +create_access_group()
        +delete_access_group()
        +create_access_level()
    }

    class BiostarDeviceAPIClient {
        +get_devices()
        +get_device_groups()
        +get_events()
        +control_device()
        +get_device_status()
        +search_events()
    }

    BaseBiostarAPIClient <|-- BiostarUserAPIClient
    BaseBiostarAPIClient <|-- BiostarACAPIClient
    BaseBiostarAPIClient <|-- BiostarDeviceAPIClient
```

#### 6.2 API Endpoints and Methods

**Authentication & Session Management:**

- `POST /api/login` - Authenticate with BioStar 2
- Session management via `bs-session-id` header
- Automatic token refresh mechanism

**User Management API:**

- `GET /api/users` - Retrieve users with filtering
- `GET /api/users/{id}` - Get specific user details
- `POST /api/users` - Create new user
- `PUT /api/users/{id}` - Update existing user
- `DELETE /api/users/{id}` - Remove user

**Access Control API:**

- `GET /api/access_groups` - Manage access groups
- `GET /api/access_levels` - Manage access levels
- `GET /api/doors` - Manage doors
- `GET /api/schedules` - Manage time schedules

**Device Management API:**

- `GET /api/devices` - Get all devices
- `GET /api/devices/{id}/status` - Device status
- `POST /api/devices/{id}/control` - Control device operations
- `GET /api/events/search` - Search access events

### 7. Data Synchronization Patterns

#### 7.1 Synchronization Architecture

```mermaid
sequenceDiagram
    participant AMS as AMS System
    participant API as API Client
    participant BS as BioStar 2
    participant Device as Access Device

    Note over AMS,BS: User Creation & Sync
    AMS->>API: Create/Update User
    API->>BS: POST /api/users
    BS->>API: User Created Response
    API->>AMS: Update Sync Status

    Note over AMS,BS: Access Control Setup
    AMS->>API: Assign Access Groups
    API->>BS: POST /api/access_groups
    BS->>Device: Push Access Credentials
    Device->>BS: Acknowledge

    Note over AMS,BS: Event Logging
    Device->>BS: Access Event
    BS->>API: Event Notification
    API->>AMS: Create Visit Record
```

#### 7.2 Data Flow Patterns

**Push Synchronization:**

- User data: AMS → BioStar 2
- Access permissions: AMS → BioStar 2
- Device commands: AMS → BioStar 2

**Pull Synchronization:**

- Access events: BioStar 2 → AMS
- Device status: BioStar 2 → AMS
- System health: BioStar 2 → AMS

### 8. Core Design Patterns Implementation

#### 8.1 Inheritance Pattern

- **Abstract Base Classes**: `BaseAbstractModel`, `BaseRequest`, `BaseAPIModel`
- **Template Method**: Common CRUD operations with customizable hooks
- **Strategy Pattern**: Different API clients for various operations

### 9. Security and Validation Patterns

#### 9.1 Data Validation

- **ID Number Validation**: MEP-specific constraints for national/resident IDs
- **Email Format Validation**: RFC-compliant email validation
- **Time Field Validation**: Centralized time format validation
- **Access Permission Validation**: Role-based access control

### 10. Conclusion

The Visitor Management System demonstrates a well-architected solution that leverages:

1. **Modular Design**: Clear separation of concerns across modules
2. **Inheritance Hierarchy**: Efficient code reuse through abstract base classes
3. **API Integration**: Robust integration with external access control systems
4. **Workflow Management**: Comprehensive approval and notification workflows
5. **Security Focus**: Multi-layered security and validation mechanisms
6. **Performance Optimization**: Strategic caching and database optimization
7. **Error Resilience**: Comprehensive error handling and recovery mechanisms

The MEP customizations build upon this solid foundation to provide government-specific requirements while maintaining system integrity and extensibility. The design supports future enhancements and can accommodate additional customizations as needed.
