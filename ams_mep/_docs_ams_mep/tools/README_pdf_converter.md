# Markdown to PDF Converter - Improvements

## Issues Fixed

### 1. Highlight Style Error
**Problem**: The original script used `--highlight-style=github` which is not recognized by pandoc, causing the error:
```
Unknown highlight-style github
```

**Solution**: Changed to `--highlight-style=tango` which is a valid pandoc highlight style.

### 2. Diagram Image Placement
**Problem**: Mermaid diagrams were not being properly included in the final PDF.

**Solutions Applied**:
- Added better error handling for mermaid-cli (mmdc) command
- Improved image path resolution with `./diagram_X.png` format
- Added `--backgroundColor white` to mermaid image generation
- Added debugging output to track image creation and HTML generation
- Improved working directory handling with `cwd=temp_dir`
- Added `--resource-path` parameter to pandoc fallback method

### 3. Enhanced Error Handling
**Improvements**:
- Better error messages for missing tools (mmdc, pandoc, wkhtmltopdf)
- Fallback method with improved parameters
- File existence verification
- Detailed logging of conversion process

## Files Created

1. **convert_md_to_pdf.py** (Updated)
   - Fixed original script with all improvements
   - Hardcoded input/output paths

2. **convert_md_to_pdf_flexible.py** (New)
   - Command-line argument support
   - Usage: `python3 convert_md_to_pdf_flexible.py <input.md> [output.pdf]`
   - Automatic output filename generation
   - Better error handling and user feedback

## Usage Examples

### Fixed Script (Hardcoded paths)
```bash
python3 convert_md_to_pdf.py
```

### Flexible Script
```bash
# Auto-generate output filename
python3 convert_md_to_pdf_flexible.py document.md

# Specify output filename
python3 convert_md_to_pdf_flexible.py document.md output.pdf

# Convert LLD document
python3 convert_md_to_pdf_flexible.py ams_mep/_docs_ams_mep/23_LLD_ams_mep_code_design.md
```

## Requirements

Ensure these tools are installed:
- `pandoc` - Document converter
- `wkhtmltopdf` - HTML to PDF converter
- `mmdc` (mermaid-cli) - Mermaid diagram renderer
  ```bash
  npm install -g @mermaid-js/mermaid-cli
  ```

## Key Improvements Summary

✅ **Fixed highlight style error** - Changed from 'github' to 'tango'
✅ **Improved diagram placement** - Better path handling and debugging
✅ **Enhanced error handling** - Clear error messages and fallback methods
✅ **Added flexibility** - Command-line argument support
✅ **Better debugging** - Detailed logging of conversion process
✅ **File verification** - Check file existence and sizes

The script now successfully converts Markdown files with Mermaid diagrams to PDF format with proper image placement and styling.