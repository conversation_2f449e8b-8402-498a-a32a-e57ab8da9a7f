#!/usr/bin/env python3
"""
Improved script to convert Markdown file with Mermaid diagrams to PDF
This version creates a better formatted PDF with properly embedded diagrams
"""

import os
import re
import subprocess
import tempfile
import shutil
from pathlib import Path

def extract_mermaid_diagrams(content):
    """
    Extract mermaid diagrams from markdown content and replace with image references
    """
    mermaid_pattern = r'```mermaid\n(.*?)\n```'
    diagrams = []
    
    def replace_mermaid(match):
        diagram_content = match.group(1)
        diagram_id = len(diagrams)
        diagrams.append(diagram_content)
        # Use absolute path for better compatibility
        return f'\n\n![Diagram {diagram_id + 1}](diagram_{diagram_id + 1}.png)\n\n'
    
    # Replace mermaid blocks with image references
    modified_content = re.sub(mermaid_pattern, replace_mermaid, content, flags=re.DOTALL)
    
    # Clean up problematic Unicode characters in code blocks
    unicode_replacements = {
        '├──': '|--',
        '└──': '`--',
        '│': '|',
        '├': '|',
        '└': '`'
    }
    
    for unicode_char, ascii_replacement in unicode_replacements.items():
        modified_content = modified_content.replace(unicode_char, ascii_replacement)
    
    return modified_content, diagrams

def create_mermaid_images(diagrams, output_dir):
    """
    Create PNG images from mermaid diagram content with better quality
    """
    for i, diagram in enumerate(diagrams):
        # Create temporary mermaid file
        mermaid_file = os.path.join(output_dir, f'temp_diagram_{i + 1}.mmd')
        with open(mermaid_file, 'w') as f:
            f.write(diagram)
        
        # Convert to PNG with high quality settings
        output_image = os.path.join(output_dir, f'diagram_{i + 1}.png')
        cmd = [
            'mmdc', 
            '-i', mermaid_file, 
            '-o', output_image, 
            '-w', '1400', 
            '-H', '1000',
            '-b', 'white',
            '-s', '2'
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"Created high-quality diagram_{i + 1}.png")
        except subprocess.CalledProcessError as e:
            print(f"Error creating diagram {i + 1}: {e}")
            print(f"Error output: {e.stderr}")
        
        # Clean up temporary mermaid file
        os.remove(mermaid_file)

def create_html_with_embedded_images(markdown_file, diagrams, temp_dir):
    """
    Create HTML file with properly embedded images
    """
    # First convert markdown to HTML
    html_file = os.path.join(temp_dir, 'document.html')
    
    cmd = [
        'pandoc',
        markdown_file,
        '-o', html_file,
        '--standalone',
        '--number-sections',
        '--from=markdown',
        '--to=html5'
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True, text=True, cwd=temp_dir)
        print(f"HTML created: {html_file}")
        
        # Read the HTML and embed images as base64
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Replace image references with embedded base64 images
        import base64
        for i in range(len(diagrams)):
            img_file = os.path.join(temp_dir, f'diagram_{i + 1}.png')
            if os.path.exists(img_file):
                with open(img_file, 'rb') as img:
                    img_data = base64.b64encode(img.read()).decode('utf-8')
                    img_tag = f'<img src="data:image/png;base64,{img_data}" alt="Diagram {i + 1}" style="max-width: 100%; height: auto; display: block; margin: 20px auto; border: 1px solid #ddd; border-radius: 5px; padding: 10px; background-color: white;" />'
                    html_content = html_content.replace(f'<img src="diagram_{i + 1}.png" alt="Diagram {i + 1}" />', img_tag)
        
        # Add custom CSS for better formatting with smaller fonts
        css_style = """
        <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.5;
            margin: 25px;
            color: #333;
            max-width: 1200px;
            font-size: 11px;
        }
        
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 12px;
            page-break-after: avoid;
            font-weight: 600;
        }
        
        h1 {
            padding-bottom: 10px;
            font-size: 20px;
            page-break-before: always;
            margin-bottom: 20px;
        }
        
        h1:first-of-type {
            page-break-before: auto;
        }
        
        h2 {
            padding-bottom: 6px;
            font-size: 16px;
            margin-top: 30px;
        }
        
        h3 {
            font-size: 14px;
            margin-top: 25px;
        }
        
        h4, h5, h6 {
            font-size: 12px;
            margin-top: 20px;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
            font-size: 10px;
            page-break-inside: avoid;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        code {
            background-color: #f8f9fa;
            padding: 1px 3px;
            border-radius: 2px;
            font-family: 'Courier New', monospace;
            font-size: 9px;
        }
        
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 9px;
            page-break-inside: avoid;
        }
        
        ul, ol {
            margin: 10px 0;
            padding-left: 25px;
            page-break-inside: avoid;
        }
        
        li {
            margin: 3px 0;
        }
        
        blockquote {
            border-left: 3px solid #3498db;
            margin: 15px 0;
            padding: 8px 15px;
            background-color: #f8f9fa;
            page-break-inside: avoid;
        }
        
        p {
            margin: 10px 0;
            orphans: 3;
            widows: 3;
            text-align: justify;
        }
        
        /* Hide table of contents */
        #TOC, .toc {
            display: none;
        }
        
        /* Keep all sections visible - no hiding of technical content */
        
        /* Section grouping */
        section {
            page-break-inside: avoid;
        }
        
        /* Keep content blocks together */
        .keep-together {
            page-break-inside: avoid;
        }
        
        /* Horizontal rules as page breaks */
        hr {
            page-break-before: always;
            border: none;
            height: 0;
            margin: 0;
            padding: 0;
            visibility: hidden;
        }
        
        /* First page specific styling */
        body > h1:first-child {
            margin-top: 0;
            padding-top: 0;
        }
        
        /* Document header styling */
        body > h1:first-child + h2 {
            margin-top: 10px;
        }
        
        @media print {
            @page {
                margin: 15mm 20mm 20mm 20mm;
                size: A4;
            }
            
            body { 
                margin: 0;
                padding: 0;
                font-size: 11px;
                line-height: 1.4;
            }
            
            h1 { 
                page-break-before: always; 
                font-size: 18px;
                page-break-after: avoid;
                margin-top: 0;
                padding-top: 15mm;
                border: none !important;
            }
            
            h1:first-of-type { 
                page-break-before: auto;
                padding-top: 0;
                margin-top: 0;
            }
            
            h2 { 
                font-size: 15px; 
                page-break-after: avoid;
                margin-top: 20px;
                border: none !important;
            }
            
            h3 { 
                font-size: 13px; 
                page-break-after: avoid;
                margin-top: 15px;
            }
            
            h4, h5, h6 {
                font-size: 11px;
                page-break-after: avoid;
                margin-top: 12px;
            }
            
            hr { 
                page-break-before: always; 
                border: none; 
                height: 0; 
                margin: 0; 
                visibility: hidden;
            }
            
            /* Hide technical content in print */
            .technical-content {
                display: none !important;
            }
            
            /* Better spacing for print */
            table {
                font-size: 9px;
                margin: 10px 0;
            }
            
            code, pre {
                font-size: 8px;
            }
            
            p {
                margin: 8px 0;
            }
            
            ul, ol {
                margin: 8px 0;
            }
        }
        </style>
        """
        
        # Insert CSS into HTML head
        html_content = html_content.replace('</head>', css_style + '</head>')
        
        # Keep all content - no filtering of technical sections
        import re
        
        # Write the modified HTML back
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return html_file
        
    except subprocess.CalledProcessError as e:
        print(f"Error creating HTML: {e}")
        print(f"Error output: {e.stderr}")
        return None

def convert_html_to_pdf(html_file, output_pdf):
    """
    Convert HTML to PDF using wkhtmltopdf
    """
    cmd = [
        'wkhtmltopdf',
        '--page-size', 'A4',
        '--margin-top', '20mm',
        '--margin-bottom', '20mm',
        '--margin-left', '20mm',
        '--margin-right', '20mm',
        '--enable-local-file-access',
        '--print-media-type',
        '--disable-smart-shrinking',
        '--zoom', '0.8',
        '--no-stop-slow-scripts',
        '--javascript-delay', '1000',
        '--load-error-handling', 'ignore',
        '--load-media-error-handling', 'ignore',
        html_file,
        output_pdf
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"PDF created successfully: {output_pdf}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error converting HTML to PDF: {e}")
        print(f"Error output: {e.stderr}")
        return False

def main():
    input_file = '/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_ams/ams_mep/_docs_ams_mep/21_HLD_ams_mep_system_architecture.md'
    output_file = '/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_ams/ams_mep_system_architecture_improved.pdf'
    
    # Read the input markdown file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Working in temporary directory: {temp_dir}")
        
        # Extract mermaid diagrams and replace with image references
        modified_content, diagrams = extract_mermaid_diagrams(content)
        
        # Create mermaid images
        if diagrams:
            print(f"Found {len(diagrams)} mermaid diagrams")
            create_mermaid_images(diagrams, temp_dir)
        
        # Write modified markdown to temporary file
        temp_md_file = os.path.join(temp_dir, 'document.md')
        with open(temp_md_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        # Create HTML with embedded images
        html_file = create_html_with_embedded_images(temp_md_file, diagrams, temp_dir)
        
        if html_file:
            # Copy HTML file to current directory for debugging
            debug_html = '/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_ams/debug_document.html'
            import shutil
            shutil.copy2(html_file, debug_html)
            print(f"Debug HTML saved to: {debug_html}")
            
            # Convert HTML to PDF
            success = convert_html_to_pdf(html_file, output_file)
            if success:
                print(f"\nConversion complete! Output: {output_file}")
                # Show file size
                file_size = os.path.getsize(output_file)
                print(f"File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
            else:
                print("PDF conversion failed")
        else:
            print("HTML creation failed")

if __name__ == '__main__':
    main()