# -*- coding: utf-8 -*-

import logging
import traceback
from pickle import FALSE

import odoo.http as http
from odoo.tools import exception_to_unicode

_logger = logging.getLogger(__name__)

# Store the original serialize_exception function
_original_serialize_exception = http.serialize_exception


def custom_serialize_exception(exception):
    """
    Custom serialize_exception function that restricts debug information
    based on user admin status and system parameter ams_mep.debug_admin_only.
    
    Returns debug information only if:
    1. Current user is admin
    2. System parameter ams_mep.debug_admin_only = True
    
    Otherwise returns empty debug information.
    """
    name = type(exception).__name__
    module = type(exception).__module__
    
    # Base response structure
    result = {
        'name': f'{module}.{name}' if module else name,
        'debug': '',  # Default to empty debug info
        'message': exception_to_unicode(exception),
        'arguments': exception.args,
        'context': getattr(exception, 'context', {}),
    }
    
    try:
        # Import here to avoid circular imports
        from odoo.http import request
        
        # Check if we have a valid request context
        if not hasattr(request, 'env') or not request.env:
            # No request context, return without debug info
            _logger.debug("No request context available for serialize_exception")
            return result
            
        # Get security configuration
        try:
            config = request.env['res.config.settings'].get_security_config()
            debug_admin_only = config.get('debug_admin_only', False)
        except Exception as e:
            # Fallback: if we can't get config, assume debug_admin_only is True
            _logger.warning("Could not get security config in serialize_exception: %s", str(e))
            debug_admin_only = True
        
        # If debug_admin_only is disabled, return full debug info
        if not debug_admin_only:
            result['debug'] = traceback.format_exc()
            return result
            
        # Check if user is authenticated and is admin
        if request.session.uid:
            try:
                user = request.env['res.users'].sudo().browse(request.session.uid)
                if user.exists() and user._is_admin():
                    # User is admin, return full debug info
                    result['debug'] = traceback.format_exc()
                    _logger.debug("Debug info provided to admin user: %s", user.login)
                else:
                    # User is not admin, return empty debug info
                    _logger.debug("Debug info restricted for non-admin user: %s", 
                                user.login if user.exists() else 'unknown')
            except Exception as e:
                # Error checking user status, return empty debug info for security
                _logger.warning("Error checking user admin status in serialize_exception: %s", str(e))
        else:
            # No authenticated user, return empty debug info
            _logger.debug("Debug info restricted for unauthenticated request")
            
    except Exception as e:
        # Any error in our custom logic should not break the original functionality
        # Log the error and return empty debug info for security
        _logger.error("Error in custom serialize_exception: %s", str(e))
        
    return result


def apply_http_override():
    """
    Apply the custom serialize_exception override to the odoo.http module.
    This function should be called when the module is loaded.
    """
    try:
        # Replace the serialize_exception function in the http module
        http.serialize_exception = custom_serialize_exception
        _logger.info("Successfully applied custom serialize_exception override")
    except Exception as e:
        _logger.error("Failed to apply serialize_exception override: %s", str(e))


def restore_original_serialize_exception():
    """
    Restore the original serialize_exception function.
    This can be used for testing or if the override needs to be disabled.
    """
    try:
        http.serialize_exception = _original_serialize_exception
        _logger.info("Restored original serialize_exception function")
    except Exception as e:
        _logger.error("Failed to restore original serialize_exception: %s", str(e))


# Apply the override when this module is imported
apply_http_override()
