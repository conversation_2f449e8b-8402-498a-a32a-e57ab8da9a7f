# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import base64


class Invitation(models.Model):
    # region ---------------------- Private Attributes --------------------------------
    _name = 'ams_vm.invitation_visitor'
    _inherit = ['ams_vm.invitation_visitor']
    # endregion

    # region ----------------------  Properties ------------------------------------
    # endregion
    # region ---------------------- Default Methods ------------------------------------
    # endregion

    # region ---------------------- Fields Declaration ---------------------------------
    # region  Basic
    visitor_id_number = fields.Char(string="Visitor ID")
    # endregion

    # region  Relational
    visitor_id = fields.Many2one('ams_vm.visitor', string='Visitor', compute='_compute_visitor_id', store=True, ondelete='restrict')
    visit_duration_display = fields.Char(string="Visit Duration Display", compute='_compute_visit_duration_display')

    # endregion

    # endregion

    # region ---------------------- Compute methods ------------------------------------
    @api.depends('visitor_id_number')
    def _compute_visitor_id(self):
        for record in self:
            if record.visitor_id_number:
                visitor = self.env['ams_vm.visitor'].search([('id_number', '=', record.visitor_id_number)], limit=1)
                if visitor:
                    record.visitor_id = visitor.id
                    record.visitor_id.message_subscribe(partner_ids=[self.env.user.partner_id.id])
                else:
                    record.visitor_id = False

    @api.depends("invitation_id.start_date", "invitation_id.end_date")
    def _compute_visit_duration_display(self):
        for record in self:
            invitation = record.invitation_id
            if not (invitation.start_date and invitation.end_date):
                record.visit_duration_display = ""
                continue

            # Get user's timezone
            user_tz = record.env.user.tz or "UTC"

            # Apply context_timestamp with correct tz context
            start_dt = fields.Datetime.context_timestamp(
                record.with_context(tz=user_tz), invitation.start_date
            )
            end_dt = fields.Datetime.context_timestamp(
                record.with_context(tz=user_tz), invitation.end_date
            )

            # Format times in 24-hour format
            start_time = start_dt.strftime("%H:%M")
            end_time = end_dt.strftime("%H:%M")
            
            days_count = (
                invitation.end_date.date() - invitation.start_date.date()
            ).days + 1

            record.visit_duration_display = (
                f"{days_count} أيام من {start_time} إلى {end_time}"
            )

    # endregion

    # region ---------------------- CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- Action Methods -------------------------------------

    def action_search_visitor(self):
        """Search for a visitor by ID number and set visitor_id"""
        for record in self:
            if record.visitor_id_number:
                visitor = self.env['ams_vm.visitor'].search(
                    [('id_number', '=', record.visitor_id_number)], limit=1
                )
                if visitor:
                    record.visitor_id = visitor.id
                    record.visitor_id.message_subscribe(partner_ids=[self.env.user.partner_id.id])
                else:
                    record.visitor_id = False
                    raise UserError(_(
                        "No visitor found with this ID number, click on add button to add new visitor with this ID number."))

    def action_open_visitor_form(self):
        """Open the visitor form view with the ID number pre-filled."""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Create Visitor'),
            'res_model': 'ams_vm.visitor',
            'view_mode': 'form',
            'view_id': self.env.ref('ams_vm.visitor_view_form').id,
            'target': 'new',
            'context': {
                'default_id_number': self.visitor_id_number,
                'default_invitation_visitor_id': self.id,
            },
        }

    @property
    def badge_report_action(self):
        report_action = self.env.company.badge_report_id or self.env.ref('ams_mep.invitation_visitor_report_action')
        return report_action

    @property
    def badge_report_template(self):
        template = self.env.company.email_template_id or self.env.ref('ams_mep.ams_mep_default_email_template')
        return template

    # def action_approve(self):
    #     pass
    #
    # def action_reject(self):
    #     pass
    # endregion

    # region ---------------------- Business Methods -------------------------------------
    # endregion
