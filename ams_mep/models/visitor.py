# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError, AccessError
from functools import wraps
import base64

class Visitor(models.Model):
    # region ---------------------- Private Attributes --------------------------------
    _name = 'ams_vm.visitor'
    _inherit = ['ams_vm.visitor']
    # endregion

    # region ----------------------  Properties ------------------------------------
    # endregion
    # region ---------------------- Default Methods ------------------------------------
    # endregion

    # region ---------------------- Fields Declaration ---------------------------------

    # endregion
    # region  Basic
    # endregion

    # region  Relational
    # endregion

    # region ---------------------- CRUD Methods -------------------------------------
    def create(self, vals):
        visitor = super(Visitor, self).create(vals)
        invitation_id = self.env.context.get('invitation_id')

        if not invitation_id:
            return visitor

        # Automatically assign visitor to invitation if it was opened from an invitation
        if self.env.context.get('is_main_visitor'):
                invitation = self.env['ams_vm.invitation'].sudo().browse(invitation_id)
                if invitation.exists():
                    invitation.sudo().write({
                        'visitor_id': visitor.id,
                        'visitor_id_number': visitor.id_number,
                    })
        else:
            # create invitation visitor
            self.env['ams_vm.invitation_visitor'].sudo().create({
                'visitor_id_number': visitor.id_number,
                'visitor_id': visitor.id,
                'invitation_id': invitation_id,
            })

        return visitor

    # endregion


# region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------

@api.constrains('id_number', 'id_type_id')
def _check_id_number_digits_and_length(self):
    for record in self:
        if record.id_type_id.name in ['National ID', 'Resident ID']:
            id_number = record.id_number or ''

            if not id_number.isdigit() or len(id_number) < 10:
                raise ValidationError(
                    _("The ID number must contain at least 10 digits and consist of numeric characters only (no letters or symbols)."))
# endregion