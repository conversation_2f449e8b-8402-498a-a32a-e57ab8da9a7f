/** @odoo-module **/

import { MessagingMenu } from "@mail/core/public_web/messaging_menu";
import { registry } from "@web/core/registry";
import { user } from "@web/core/user";

// Override the messaging menu systray registration to add group restrictions
class RestrictedMessagingMenu extends MessagingMenu {
    setup() {
        super.setup();
        // Check if user has system admin rights
        this.isSystemAdmin = user.isAdmin || user.groups?.includes('base.group_system');
    }

    get isVisible() {
        // Only show messaging menu to system administrators
        return this.isSystemAdmin;
    }
}

// Remove the original messaging menu registration
registry.category("systray").remove("mail.messaging_menu");

// Add the restricted messaging menu only for system admins
if (user.isAdmin || user.groups?.includes('base.group_system')) {
    registry
        .category("systray")
        .add("mail.messaging_menu", { Component: RestrictedMessagingMenu }, { sequence: 25 });
}