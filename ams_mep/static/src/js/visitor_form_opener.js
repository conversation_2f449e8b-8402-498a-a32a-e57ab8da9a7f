/** @odoo-module **/

import {FormController} from '@web/views/form/form_controller';
import {patch} from '@web/core/utils/patch';
import {onWillDestroy} from "@odoo/owl";
import {rpc} from "@web/core/network/rpc";
import {useService} from "@web/core/utils/hooks";


patch(FormController.prototype, {
    dependencies: ['bus_service', 'notification'],

    setup() {
        super.setup();  // Always call super.setup() first to ensure proper initialization
        this.orm = useService("orm");
        this.action = useService("action");
        this.busService = this.env.services.bus_service;

        // Listen for changes on the 'visitor_not_found' field
        const visitorNotFoundListener = (payload) => {
            console.log("patch FormController >> setup >> auto_create_visitor_form >> payload:", payload);

            // Check if 'visitor_not_found' is true
            if (payload.is_visitor_not_found === true) {
                console.log("patch FormController >> setup >> auto_create_visitor_form >> payload.is_visitor_not_found:", payload.is_visitor_not_found);
                this.openCreateVisitorForm(payload);
            }
        }

        this.busService.subscribe('auto_create_visitor_form', visitorNotFoundListener);
        this.busService.addChannel('auto_create_visitor_form');

        // Cleanup when component is destroyed
        this._cleanupBus = () => {
            this.busService.unsubscribe('auto_create_visitor_form', visitorNotFoundListener);
            this.busService.deleteChannel('auto_create_visitor_form');
        }

        onWillDestroy(() => {
            this._cleanupBus();
        });
    },

    openCreateVisitorForm(payload) {
        // Ensure the correct reference to the view ID
        const actionData = {
            type: 'ir.actions.act_window',
            name: 'Create Visitor',
            res_model: 'ams_vm.visitor',
            view_mode: 'form',
            views: [[false, 'form']], // Fallback to standard form view
            target: 'new',
            context: {
                'default_id_number': payload.visitor_id_number,
                'is_main_visitor': true,
                'invitation_id': payload.id,
            }
        };

        this.action.doAction(actionData);
    },


});
