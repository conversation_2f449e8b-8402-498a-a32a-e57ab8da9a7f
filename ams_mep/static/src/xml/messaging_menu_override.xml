<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <!-- Override the messaging menu template to add visibility restrictions -->
    <t t-name="mail.MessagingMenu" t-inherit="mail.MessagingMenu" t-inherit-mode="extension">
        <!-- Only show the messaging menu if user is system admin -->
        <xpath expr="//t[@t-if='env.inDiscussApp']" position="attributes">
            <attribute name="t-if">env.inDiscussApp and (user.isAdmin or user.hasGroup('base.group_system'))</attribute>
        </xpath>
    </t>
</templates>