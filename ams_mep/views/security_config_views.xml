<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Security Configuration Form View -->
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.ams_mep_security</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="90"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <app data-string="Security" string="Security" name="ams_mep_security" groups="base.group_system">
                    <block title="Debug &amp; Development Security" name="debug_security_container">
                        <setting help="Restrict debug mode access to admin users only" title="Debug Access Control">
                            <field name="debug_admin_only"/>
                        </setting>
<!--                        <setting help="Add additional security logging for database manager access" title="Database Manager Security">-->
<!--                            <field name="restrict_db_manager"/>-->
<!--                        </setting>-->
                    </block>

<!--                    <block title="Rate Limiting &amp; Protection" name="rate_limiting_container">-->
<!--                        <setting help="Enable rate limiting for failed debug access attempts" title="Rate Limiting">-->
<!--                            <field name="enable_rate_limiting"/>-->
<!--                            <div class="content-group mt16" invisible="not enable_rate_limiting">-->
<!--                                <div class="row">-->
<!--                                    <label for="max_failed_attempts" class="col-3 o_light_label" string="Max Failed Attempts"/>-->
<!--                                    <field name="max_failed_attempts" class="col-2"/>-->
<!--                                </div>-->
<!--                                <div class="row">-->
<!--                                    <label for="block_duration_minutes" class="col-3 o_light_label" string="Block Duration (Minutes)"/>-->
<!--                                    <field name="block_duration_minutes" class="col-2"/>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </setting>-->
<!--                    </block>-->

<!--                    <block title="Access Control" name="access_control_container">-->
<!--                        <setting help="Only allow administrators to create new users" title="User Creation Control">-->
<!--                            <field name="restrict_user_creation"/>-->
<!--                        </setting>-->
<!--                        <setting help="Automatic session timeout in hours" title="Session Timeout">-->
<!--                            <div class="d-flex">-->
<!--                                <span class="flex-shrink-0 me-2">Session timeout:</span>-->
<!--                                <field name="session_timeout_hours" class="oe_inline"/>-->
<!--                                <span class="ms-2">hours</span>-->
<!--                            </div>-->
<!--                        </setting>-->
<!--                    </block>-->

<!--                    <block title="Password Security" name="password_security_container">-->
<!--                        <setting help="Require strong passwords for all users" title="Strong Password Policy">-->
<!--                            <field name="enforce_strong_passwords"/>-->
<!--                            <div class="content-group mt16" invisible="not enforce_strong_passwords">-->
<!--                                <div class="d-flex">-->
<!--                                    <span class="flex-shrink-0 me-2">Minimum password length:</span>-->
<!--                                    <field name="min_password_length" class="oe_inline"/>-->
<!--                                    <span class="ms-2">characters</span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </setting>-->
<!--                    </block>-->

<!--                    <block title="Logging &amp; Monitoring" name="logging_container">-->
<!--                        <setting help="Log all debug access attempts for audit purposes" title="Debug Access Logging">-->
<!--                            <field name="log_debug_attempts"/>-->
<!--                        </setting>-->
<!--                        <setting help="Log suspicious request patterns for security monitoring" title="Suspicious Activity Logging">-->
<!--                            <field name="log_suspicious_activity"/>-->
<!--                        </setting>-->
<!--                        <setting help="Log all user actions for compliance and audit" title="Audit Logging">-->
<!--                            <field name="enable_audit_log"/>-->
<!--                            <div class="content-group mt16" invisible="not enable_audit_log">-->
<!--                                <div class="d-flex">-->
<!--                                    <span class="flex-shrink-0 me-2">Keep logs for:</span>-->
<!--                                    <field name="retention_days" class="oe_inline"/>-->
<!--                                    <span class="ms-2">days</span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </setting>-->
<!--                    </block>-->
                </app>
            </xpath>
        </field>
    </record>

    <!-- Security Events Tree View -->
<!--    <record id="security_event_view_tree" model="ir.ui.view">-->
<!--        <field name="name">ams_mep.security.event.tree</field>-->
<!--        <field name="model">ams_mep.security.event</field>-->
<!--        <field name="arch" type="xml">-->
<!--            <list string="Security Events" default_order="create_date desc">-->
<!--                <field name="create_date"/>-->
<!--                <field name="event_type"/>-->
<!--                <field name="severity" widget="badge" -->
<!--                       decoration-danger="severity == 'critical'"-->
<!--                       decoration-warning="severity == 'high'"-->
<!--                       decoration-info="severity == 'medium'"-->
<!--                       decoration-muted="severity == 'low'"/>-->
<!--                <field name="user_login"/>-->
<!--                <field name="client_ip"/>-->
<!--                <field name="request_url"/>-->
<!--                <field name="description"/>-->
<!--            </tree>-->
<!--        </field>-->
<!--    </record>-->

<!--    &lt;!&ndash; Security Events Form View &ndash;&gt;-->
<!--    <record id="security_event_view_form" model="ir.ui.view">-->
<!--        <field name="name">ams_mep.security.event.form</field>-->
<!--        <field name="model">ams_mep.security.event</field>-->
<!--        <field name="arch" type="xml">-->
<!--            <form string="Security Event" create="false" edit="false">-->
<!--                <sheet>-->
<!--                    <div class="oe_title">-->
<!--                        <h1>-->
<!--                            <field name="event_type"/>-->
<!--                        </h1>-->
<!--                    </div>-->
<!--                    -->
<!--                    <group>-->
<!--                        <group string="Event Details">-->
<!--                            <field name="create_date"/>-->
<!--                            <field name="severity"/>-->
<!--                            <field name="event_type"/>-->
<!--                        </group>-->
<!--                        -->
<!--                        <group string="User Information">-->
<!--                            <field name="user_id"/>-->
<!--                            <field name="user_login"/>-->
<!--                            <field name="session_id"/>-->
<!--                        </group>-->
<!--                    </group>-->
<!--                    -->
<!--                    <group>-->
<!--                        <group string="Request Information">-->
<!--                            <field name="client_ip"/>-->
<!--                            <field name="request_url"/>-->
<!--                            <field name="referer"/>-->
<!--                        </group>-->
<!--                        -->
<!--                        <group string="Technical Details">-->
<!--                            <field name="user_agent"/>-->
<!--                        </group>-->
<!--                    </group>-->
<!--                    -->
<!--                    <group string="Description">-->
<!--                        <field name="description" nolabel="1"/>-->
<!--                    </group>-->
<!--                </sheet>-->
<!--            </form>-->
<!--        </field>-->
<!--    </record>-->

<!--    &lt;!&ndash; Security Events Search View &ndash;&gt;-->
<!--    <record id="security_event_view_search" model="ir.ui.view">-->
<!--        <field name="name">ams_mep.security.event.search</field>-->
<!--        <field name="model">ams_mep.security.event</field>-->
<!--        <field name="arch" type="xml">-->
<!--            <search string="Security Events">-->
<!--                <field name="event_type"/>-->
<!--                <field name="user_login"/>-->
<!--                <field name="client_ip"/>-->
<!--                <field name="description"/>-->
<!--                -->
<!--                <filter name="today" string="Today" -->
<!--                        domain="[('create_date', '&gt;=', datetime.datetime.now().replace(hour=0, minute=0, second=0))]"/>-->
<!--                <filter name="this_week" string="This Week" -->
<!--                        domain="[('create_date', '&gt;=', (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>-->
<!--                <filter name="this_month" string="This Month" -->
<!--                        domain="[('create_date', '&gt;=', datetime.datetime.now().replace(day=1, hour=0, minute=0, second=0))]"/>-->
<!--                -->
<!--                <separator/>-->
<!--                <filter name="critical" string="Critical" domain="[('severity', '=', 'critical')]"/>-->
<!--                <filter name="high" string="High" domain="[('severity', '=', 'high')]"/>-->
<!--                <filter name="medium" string="Medium" domain="[('severity', '=', 'medium')]"/>-->
<!--                <filter name="low" string="Low" domain="[('severity', '=', 'low')]"/>-->
<!--                -->
<!--                <separator/>-->
<!--                <filter name="debug_denied" string="Debug Access Denied" -->
<!--                        domain="[('event_type', '=', 'debug_access_denied')]"/>-->
<!--                <filter name="suspicious" string="Suspicious Activity" -->
<!--                        domain="[('event_type', '=', 'suspicious_activity')]"/>-->
<!--                <filter name="rate_limit" string="Rate Limited" -->
<!--                        domain="[('event_type', '=', 'rate_limit_exceeded')]"/>-->
<!--                -->
<!--                <group expand="0" string="Group By">-->
<!--                    <filter name="group_event_type" string="Event Type" context="{'group_by': 'event_type'}"/>-->
<!--                    <filter name="group_severity" string="Severity" context="{'group_by': 'severity'}"/>-->
<!--                    <filter name="group_user" string="User" context="{'group_by': 'user_login'}"/>-->
<!--                    <filter name="group_ip" string="Client IP" context="{'group_by': 'client_ip'}"/>-->
<!--                    <filter name="group_date" string="Date" context="{'group_by': 'create_date:day'}"/>-->
<!--                </group>-->
<!--            </search>-->
<!--        </field>-->
<!--    </record>-->

<!--    &lt;!&ndash; Actions &ndash;&gt;-->
<!--    <record id="action_security_config" model="ir.actions.act_window">-->
<!--        <field name="name">Security Settings</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="res_model">res.config.settings</field>-->
<!--        <field name="view_mode">form</field>-->
<!--        <field name="target">inline</field>-->
<!--        <field name="context">{'module': 'ams_mep_security', 'bin_size': False}</field>-->
<!--    </record>-->

<!--    <record id="action_security_events" model="ir.actions.act_window">-->
<!--        <field name="name">Security Events</field>-->
<!--        <field name="res_model">ams_mep.security.event</field>-->
<!--        <field name="view_mode">tree,form</field>-->
<!--        <field name="context">{'search_default_today': 1}</field>-->
<!--    </record>-->

    <!-- Menu Items -->
<!--    <menuitem id="menu_security_root" -->
<!--              name="Security" -->
<!--              parent="ams_base.menu_ams_configuration"-->
<!--              sequence="100"/>-->
<!--              -->
<!--    <menuitem id="menu_security_config" -->
<!--              name="Security Configuration" -->
<!--              parent="menu_security_root"-->
<!--              action="action_security_config"-->
<!--              sequence="10"/>-->
<!--              -->
<!--    <menuitem id="menu_security_events" -->
<!--              name="Security Events" -->
<!--              parent="menu_security_root"-->
<!--              action="action_security_events"-->
<!--              sequence="20"/>-->

</odoo>
