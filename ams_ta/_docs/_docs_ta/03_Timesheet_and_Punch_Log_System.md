# Timesheet and Punch Log System

## Overview

The timesheet and punch log system is the core of the AMS Time Attendance module. It handles the recording of employee attendance events and the calculation of various time metrics.

## System Architecture Diagram

```mermaid
flowchart TD
    subgraph External Sources
        BioDevice[Biometric Device]
        MobileApp[Mobile App]
        ManualEntry[Manual Entry]
    end

    subgraph Punch Log Processing
        PunchLog[Punch Log Creation]
        Validation[Validation]
        TimeUnitDetermination[Time Unit Determination]
        CheckInOutHandling[Check-in/Check-out Handling]
    end

    subgraph Timesheet Processing
        TimeUnitCalc[Time Unit Calculations]
        TimesheetAggregation[Timesheet Aggregation]
        SpecialCaseHandling[Special Case Handling]
        CounterUpdates[Counter Updates]
    end

    subgraph Reporting
        DetailedReports[Detailed Reports]
        SummaryReports[Summary Reports]
    end

    BioDevice --> PunchLog
    MobileApp --> PunchLog
    ManualEntry --> PunchLog

    PunchLog --> Validation
    Validation --> TimeUnitDetermination
    TimeUnitDetermination --> CheckInOutHandling

    CheckInOutHandling --> TimeUnitCalc
    TimeUnitCalc --> TimesheetAggregation
    TimesheetAggregation --> SpecialCaseHandling
    SpecialCaseHandling --> CounterUpdates

    CounterUpdates --> DetailedReports
    CounterUpdates --> SummaryReports
```

## Punch Log System

### Purpose

The punch log system captures individual check-in and check-out events from various sources, such as biometric devices, mobile applications, or manual entries.

### Key Components

#### Punch Log Model (`ams_ta.punch_log`)

Records individual attendance events with the following key information:
- Employee identification
- Date and time of the event
- Device information
- Geolocation data (latitude, longitude)
- Processing state

### Punch Log Processing Flow

1. **Creation**:
   - Punch logs are created through API endpoints, device integrations, or manual entry
   - Each log contains a timestamp, employee ID, and optional location data

2. **Validation**:
   - Basic validation ensures required fields are present
   - Coordinate validation ensures latitude and longitude are within valid ranges

3. **Time Unit Determination**:
   - The system identifies the appropriate time unit for the punch log
   - For regular time units, the log must fall within the defined start and end times
   - For overnight time units, special handling accounts for logs that occur after midnight
   - If no exact match is found, the nearest time unit is used

4. **Check-in/Check-out Handling**:
   - If no previous check-in exists, the log is treated as a check-in
   - If a check-in exists but no check-out, the log is treated as a check-out
   - If both check-in and check-out exist, the system updates whichever is more appropriate:
     - Earlier logs update the check-in time
     - Later logs update the check-out time

5. **Timesheet Update**:
   - The associated timesheet and timesheet time unit records are updated
   - Time calculations are triggered to update working time, delay, shortage, etc.

### Key Methods

- `action_execute_punch_log()`: Main entry point for processing a punch log
- `_get_day_time_unit()`: Determines the appropriate time unit for the punch log
- `_handle_checkin_checkout()`: Updates timesheet time unit with check-in/out data
- `_get_time_unit_start_date()`: Handles date determination for overnight shifts

## Timesheet System

### Purpose

The timesheet system aggregates punch log data into daily records and calculates various time metrics for reporting and payroll purposes.

### Key Components

#### Timesheet Model (`ams_ta.timesheet`)

Daily attendance record for an employee with the following key information:
- Employee identification
- Date
- First check-in and last check-out times
- Required, working, and remaining time
- Delay, shortage, and overtime calculations
- Various status flags (is_dayoff, is_absent, etc.)

#### Timesheet Time Unit Model (`ams_ta.timesheet_time_unit`)

Represents a specific time segment within a timesheet, typically corresponding to a work period:
- Time unit configuration (start time, end time, grace periods)
- Check-in and check-out times
- Time calculations specific to the unit

### Timesheet Calculation Flow

1. **Time Unit Calculations**:
   - Each timesheet time unit calculates its own metrics:
     - Working time based on check-in and check-out times
     - Delay time if check-in is after start time plus grace period
     - Shortage time if check-out is before end time minus grace period
     - Overtime if working time exceeds required time
     - Absence determination based on configured criteria

2. **Timesheet Aggregation**:
   - The timesheet aggregates data from all its time units:
     - First check-in and last check-out times
     - Total working time, delay time, shortage time, etc.
     - Determines if the day is a working day, day off, etc.

3. **Special Case Handling**:
   - Different calculation logic for different day types:
     - Working days: Calculate delay, shortage, and deductions
     - Days off: All working time counts as overtime
     - Permission days: Apply time-off hours to reduce delay and shortage

4. **Counter Updates**:
   - Boolean flags are converted to integer counters for reporting
   - Minute versions of time fields are calculated for different display formats

### Key Methods

- `calculate_ts_times()`: Main calculation method for timesheet
- `_aggregate_common_fields()`: Aggregates data from time units
- `_process_dayoff()`, `_process_working_day()`: Different logic for day types
- `_set_computed_fields()`, `_update_counters()`: Update derived fields

## Time Calculation Logic

### Working Time Calculation

- Basic calculation: `working_time = last_checkout_time - first_checkin_time`
- For overnight shifts: Adjust times to account for day boundary
- Half-day rules: If configured, apply half-day working time under certain conditions

### Delay Calculation

- If check-in is after start time plus grace period: `delay_time = first_checkin_time - start_time`
- Special handling for overnight shifts

### Shortage Calculation

- If check-out is before end time minus grace period: `shortage_time = end_time - last_checkout_time`

### Overtime Calculation

- If working time exceeds required time: `overtime = working_time - required_time`
- For days off: All working time counts as overtime
- Factored overtime: `overtime_factored = overtime * overtime_factor`

### Absence Determination

- No check-in and no check-out: Absent
- Check-in but no check-out: Depends on configuration
- Working time less than absence threshold: Absent

### Deduction Calculation

- If absent: `total_deduction = required_time`
- If working hour deduction applies: `total_deduction = remaining_time`
- Otherwise: `total_deduction = total_delay_shortage`

## Integration Points

- **HR Module**: Employee data
- **API Endpoints**: External system integration
- **Reports**: Data for attendance reports
- **Shift System**: Schedule configuration
