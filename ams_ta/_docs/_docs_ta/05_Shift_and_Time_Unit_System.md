# Shift and Time Unit System

## Overview

The Shift and Time Unit system in the AMS Time Attendance module provides a flexible framework for defining employee work schedules. It supports various shift types, including regular shifts, overnight shifts, and flexible working hours.

## System Architecture Diagram

```mermaid
flowchart TD
    subgraph Shift Management
        ShiftAssignment[Shift Assignment]
        ShiftExecution[Shift Execution]
        ShiftRecalculation[Shift Recalculation]
    end

    subgraph Time Unit Configuration
        TimeUnitDefinition[Time Unit Definition]
        TimeUnitRules[Time Unit Rules]
        TimeUnitTypes[Time Unit Types]
    end

    subgraph Special Handling
        OvernightShifts[Overnight Shifts]
        HalfDayRules[Half-Day Rules]
        GracePeriods[Grace Periods]
    end

    subgraph Integration
        TimesheetSystem[Timesheet System]
        PunchLogProcessing[Punch Log Processing]
    end

    ShiftAssignment --> ShiftExecution
    ShiftExecution --> ShiftRecalculation

    TimeUnitDefinition --> TimeUnitRules
    TimeUnitRules --> TimeUnitTypes

    TimeUnitTypes --> OvernightShifts
    TimeUnitTypes --> HalfDayRules
    TimeUnitRules --> GracePeriods

    ShiftRecalculation --> PunchLogProcessing
    PunchLogProcessing --> TimesheetSystem
```

## Key Components

### Shift Model (`ams_ta.shift`)

The Shift model represents a work schedule assignment for an employee during a specific period.

**Key Fields:**
- `name`: Identifier for the shift
- `employee_id`: Related employee
- `schedule_id`: Related schedule template
- `start_date`, `end_date`: Validity period for the shift
- `state`: Status of the shift (pending, running, finished)
- `is_default`: Whether this is the default shift for the employee
- `is_exception`: Whether this is an exception to the regular schedule

**Key Methods:**
- `_compute_state()`: Determines the current state based on dates
- `action_execute()`: Processes the shift, recalculating associated punch logs
- `_recalculate_shift_punch_logs()`: Recalculates punch logs for the shift period

### Time Unit Model (`ams.time_unit`)

The Time Unit model defines a specific time period within a schedule, such as a morning shift or afternoon shift.

**Key Fields:**
- Inherited from base model
- `rule_id`: Related time unit rule

### Time Unit Rule Model (`ams_ta.time_unit_rule`)

The Time Unit Rule model configures rules for time units, such as grace periods and overtime factors.

**Key Fields:**
- `name`: Identifier for the rule
- `grace_in_time`: Allowed grace period for check-in
- `grace_out_time`: Allowed grace period for check-out
- `apply_half_work`: Whether to apply half-day rules
- `half_work_checkin_criteria`: Threshold for half-day calculation based on late check-in
- `half_work_checkout_criteria`: Threshold for half-day calculation based on early check-out
- `calc_half_no_checkout_time`: Whether to calculate half-day if no check-out
- `overtime_factor`: Multiplier for overtime hours
- `apply_working_hour_deduction`: Whether to use working hours for deduction calculation

## Shift Processing Flow

1. **Shift Assignment**:
   - A shift is assigned to an employee for a specific period
   - The shift references a schedule template that defines the working pattern

2. **Shift Execution**:
   - When a shift is executed, the system recalculates all punch logs within the shift period
   - This ensures that attendance records are processed according to the correct schedule

3. **Punch Log Processing**:
   - Punch logs are processed based on the active shift for the employee
   - The system determines the appropriate time unit for each punch log
   - Time calculations consider the rules defined for the time unit

## Time Unit Types

The system supports different types of time units:

### Normal Time Unit

- Fixed start and end times
- Check-ins after start time (plus grace period) result in delay
- Check-outs before end time (minus grace period) result in shortage

### Flexible Time Unit

- Has a start limit that affects delay calculation
- If check-in is after the start limit, working time may be reduced
- Allows for more flexible working arrangements

### Open Time Unit

- No fixed start or end times
- All working time is calculated based on actual check-in and check-out
- Useful for roles with flexible working hours

## Overnight Shift Handling

The system includes special handling for overnight shifts:

1. **Identification**:
   - A time unit is considered overnight if it spans midnight
   - This is determined by comparing start and end times

2. **Time Calculation**:
   - For overnight shifts, the system adjusts time calculations to account for the day boundary
   - Check-out times after midnight are treated as being on the next day
   - Working time calculation spans the day boundary

3. **Punch Log Processing**:
   - The system determines the correct date for the time unit based on the punch log time
   - For logs after midnight but before the shift end, the time unit start date is the previous day

## Half-Day Rules

The system supports half-day calculations based on configurable rules:

1. **Late Check-in**:
   - If check-in is after start time plus the half-work check-in criteria, working time may be reduced to half

2. **Early Check-out**:
   - If check-out is before end time minus the half-work check-out criteria, working time may be reduced to half

3. **No Check-out**:
   - If configured, missing check-out can result in half-day calculation

## Integration with Timesheet System

The shift and time unit system integrates closely with the timesheet system:

1. **Timesheet Creation**:
   - When a punch log is processed, the system creates or updates timesheet records based on the active shift

2. **Time Unit Application**:
   - The timesheet time unit inherits rules from the shift's time units
   - This ensures consistent calculation of working time, delay, shortage, etc.

3. **Schedule Changes**:
   - When a shift is executed, associated timesheet records are recalculated
   - This ensures that schedule changes are properly reflected in attendance data

## Business Rules and Constraints

1. **Date Validation**:
   - End date must be after start date
   - Validation ensures valid shift periods

2. **Rule Validation**:
   - Overtime factor must be at least 1.0
   - Half-work criteria must be greater than grace periods
   - These constraints ensure logical time calculations

3. **State Determination**:
   - Shift state (pending, running, finished) is determined based on current date
   - This allows for automatic tracking of active shifts
