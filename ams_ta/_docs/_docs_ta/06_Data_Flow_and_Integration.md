# Data Flow and Integration

## Overview

The AMS Time Attendance module integrates with various other modules and external systems to provide a comprehensive attendance management solution. This document outlines the key data flows and integration points within the system.

## Data Flow Diagram

```mermaid
flowchart TD
    subgraph External Sources
        BioDevice[Biometric Devices]
        MobileApp[Mobile Apps]
        ManualEntry[Manual Entry]
        HRSystem[HR System]
    end
    
    subgraph AMS Time Attendance
        PunchLog[Punch Log]
        Timesheet[Timesheet]
        TimeUnit[Timesheet Time Unit]
        Shift[Shift Management]
        Reports[Reporting]
    end
    
    subgraph Other Modules
        HR[HR Module]
        Payroll[Payroll]
        Leave[Leave Management]
    end
    
    BioDevice -->|Check-in/Check-out Events| PunchLog
    MobileApp -->|Mobile Attendance| PunchLog
    ManualEntry -->|Manual Records| PunchLog
    
    PunchLog -->|Updates| Timesheet
    Timesheet -->|Contains| TimeUnit
    Shift -->|Configures| TimeUnit
    Shift -->|Recalculates| PunchLog
    
    Timesheet -->|Provides Data| Reports
    
    HRSystem -->|Employee Data| HR
    HR -->|Employee Information| Timesheet
    HR -->|Department Structure| Reports
    
    Timesheet -->|Working Hours| Payroll
    Timesheet -->|Absence Data| Leave
```

## Key Integration Points

### 1. External Systems Integration

#### Biometric Devices

- **Data Flow**: Biometric devices send check-in/check-out events to the system
- **Integration Method**: API endpoints receive device data
- **Data Processing**: Events are converted to punch logs
- **Key Fields**: Employee ID, timestamp, device ID

#### Mobile Applications

- **Data Flow**: Mobile apps allow employees to record attendance remotely
- **Integration Method**: API endpoints receive mobile app data
- **Data Processing**: Mobile check-ins include location data (latitude/longitude)
- **Special Handling**: Mobile attendance may use server time instead of device time

### 2. Internal Module Integration

#### HR Module Integration

- **Data Flow**: Employee data flows from HR to Time Attendance
- **Key Relationships**:
  - Timesheet references `hr.employee`
  - Reports group by `hr.department`
- **Data Dependencies**: Employee status affects attendance processing

#### Payroll Integration

- **Data Flow**: Working hours, overtime, and deductions flow to payroll
- **Key Metrics**:
  - Regular working hours
  - Overtime hours (with factors)
  - Deductions for absences and delays
- **Calculation Basis**: Timesheet aggregations provide the source data

#### Leave Management Integration

- **Data Flow**: Absence data is shared with leave management
- **Key Interactions**:
  - Leave requests affect timesheet calculations
  - Public holidays are considered in attendance processing
  - Vacation days are marked in timesheets

## Data Transformation Processes

### 1. Punch Log to Timesheet Transformation

```mermaid
sequenceDiagram
    participant PL as Punch Log
    participant TU as Time Unit
    participant TS as Timesheet
    
    PL->>PL: action_execute_punch_log()
    PL->>TU: _get_day_time_unit()
    TU-->>PL: Appropriate time unit
    PL->>TU: _handle_checkin_checkout()
    TU->>TU: calculate_times()
    TU->>TS: Update aggregated fields
    TS->>TS: calculate_ts_times()
```

**Key Steps**:
1. Punch log is executed
2. System determines the appropriate time unit
3. Time unit is updated with check-in/check-out data
4. Time unit recalculates its metrics
5. Timesheet aggregates data from all time units
6. Timesheet applies business rules based on day type

### 2. Shift Changes Propagation

```mermaid
sequenceDiagram
    participant S as Shift
    participant PL as Punch Log
    participant TS as Timesheet
    
    S->>S: action_execute()
    S->>PL: _recalculate_shift_punch_logs()
    PL->>PL: action_execute_punch_log()
    PL->>TS: Update timesheet
```

**Key Steps**:
1. Shift is executed (created, modified, or activated)
2. System identifies affected punch logs
3. Punch logs are recalculated with new shift parameters
4. Timesheets are updated based on recalculated punch logs

## Data Synchronization

### Real-time vs. Batch Processing

- **Real-time Processing**:
  - Punch log execution happens immediately upon receipt
  - Timesheet calculations are performed in real-time
  - UI updates reflect the latest data

- **Batch Processing**:
  - Shift recalculation processes multiple punch logs in batch
  - Report generation aggregates data in batch
  - Some calculations may be deferred for performance

### Consistency Mechanisms

- **Transaction Management**:
  - Database transactions ensure data consistency
  - Related records are updated within the same transaction

- **State Management**:
  - Punch logs have states (pending, executed)
  - Shifts have states (pending, running, finished)
  - State transitions trigger appropriate calculations

## API Integration Points

### External System APIs

The module provides API endpoints for external systems:

- **Punch Log Creation API**:
  - Endpoint: `/api/punch_log`
  - Method: POST
  - Purpose: Record attendance events from external systems

- **Employee Attendance Query API**:
  - Endpoint: `/api/employee_attendance`
  - Method: GET
  - Purpose: Retrieve attendance data for external systems

### Internal Module APIs

The module exposes methods for other modules:

- **Timesheet Data Access**:
  - Methods to retrieve working hours and attendance status
  - Used by payroll and reporting modules

- **Attendance Status Check**:
  - Methods to check if an employee is present
  - Used by other operational modules

## Integration Challenges and Solutions

### Time Zone Handling

- **Challenge**: Devices and servers may be in different time zones
- **Solution**: All timestamps are stored in UTC and converted for display
- **Implementation**: Datetime fields use Odoo's built-in time zone conversion

### Data Consistency

- **Challenge**: Ensuring data integrity across integrated systems
- **Solution**: Transaction management and validation rules
- **Implementation**: Database constraints and business logic validation

### Performance Optimization

- **Challenge**: Processing large volumes of attendance data
- **Solution**: Optimized queries and selective recalculation
- **Implementation**: Targeted updates instead of full recalculation
