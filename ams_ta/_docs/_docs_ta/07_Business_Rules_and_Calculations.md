# Business Rules and Calculations

## Overview

The AMS Time Attendance module implements a comprehensive set of business rules and calculations to process attendance data accurately. This document outlines the key business rules, calculation methods, and their implementation in the system.

## Time Calculation Diagram

```mermaid
flowchart TD
    subgraph Inputs
        CheckIn[Check-in Time]
        CheckOut[Check-out Time]
        StartTime[Scheduled Start Time]
        EndTime[Scheduled End Time]
        GraceIn[Grace Period In]
        GraceOut[Grace Period Out]
    end
    
    subgraph Calculations
        WorkingTime[Working Time Calculation]
        DelayTime[Delay Time Calculation]
        ShortageTime[Shortage Time Calculation]
        OvertimeCalc[Overtime Calculation]
        AbsenceCheck[Absence Determination]
        DeductionCalc[Deduction Calculation]
    end
    
    subgraph Outputs
        WorkingHours[Working Hours]
        Delay[Delay]
        Shortage[Shortage]
        Overtime[Overtime]
        AbsenceStatus[Absence Status]
        Deduction[Deduction]
    end
    
    CheckIn --> WorkingTime
    CheckOut --> WorkingTime
    WorkingTime --> WorkingHours
    
    CheckIn --> DelayTime
    StartTime --> DelayTime
    GraceIn --> DelayTime
    DelayTime --> Delay
    
    CheckOut --> ShortageTime
    EndTime --> ShortageTime
    GraceOut --> ShortageTime
    ShortageTime --> Shortage
    
    WorkingTime --> OvertimeCalc
    StartTime --> OvertimeCalc
    EndTime --> OvertimeCalc
    OvertimeCalc --> Overtime
    
    CheckIn --> AbsenceCheck
    CheckOut --> AbsenceCheck
    WorkingTime --> AbsenceCheck
    AbsenceCheck --> AbsenceStatus
    
    Delay --> DeductionCalc
    Shortage --> DeductionCalc
    AbsenceStatus --> DeductionCalc
    DeductionCalc --> Deduction
```

## Core Business Rules

### Working Time Calculation

The working time is the duration between check-in and check-out times, subject to various rules:

**Basic Calculation**:
```python
working_time = last_checkout_time - first_checkin_time
```

**Special Cases**:

1. **Overnight Shifts**:
   - If end time < start time, the shift spans midnight
   - Adjust calculation to account for day boundary

2. **No Check-out**:
   - If no check-out is recorded, working time may be:
     - Zero (strict policy)
     - Half-day (if configured)
     - Until scheduled end time (if configured)

3. **Half-day Rules**:
   - If check-in is after start time + half-work check-in criteria:
     ```python
     if first_checkin_time > (start_time + half_work_checkin_criteria):
         working_time = required_time / 2
     ```
   - If check-out is before end time - half-work check-out criteria:
     ```python
     if last_checkout_time < (end_time - half_work_checkout_criteria):
         working_time = required_time / 2
     ```

### Delay Calculation

Delay time represents late arrival and is calculated as:

```python
if first_checkin_time > (start_time + grace_in_time):
    delay_time = first_checkin_time - start_time
else:
    delay_time = 0
```

**Implementation Details**:
- Grace period provides flexibility for minor lateness
- For overnight shifts, special handling ensures correct calculation
- Delay is only calculated on working days, not days off

### Shortage Calculation

Shortage time represents early departure and is calculated as:

```python
if last_checkout_time < (end_time - grace_out_time):
    shortage_time = end_time - last_checkout_time
else:
    shortage_time = 0
```

**Implementation Details**:
- Grace period allows for minor early departures
- For overnight shifts, special handling ensures correct calculation
- Shortage is only calculated on working days, not days off

### Overtime Calculation

Overtime represents hours worked beyond the required time:

**Basic Calculation**:
```python
if working_time > required_time:
    overtime = working_time - required_time
else:
    overtime = 0
```

**Special Cases**:

1. **Days Off**:
   - All working time on days off counts as overtime
   ```python
   if is_dayoff:
       overtime = working_time
   ```

2. **Factored Overtime**:
   - Overtime may be multiplied by a factor for payroll purposes
   ```python
   overtime_factored = overtime * overtime_factor
   ```

### Absence Determination

An employee is considered absent based on the following rules:

```python
is_absent = False

if is_working_day:
    if not first_checkin_time and not last_checkout_time:
        is_absent = True
    elif working_time < absence_threshold:
        is_absent = True
```

**Implementation Details**:
- Absence is only determined for working days
- Days off, weekends, and vacations are not considered absences
- Minimum working time threshold can be configured

### Deduction Calculation

Deductions for payroll purposes are calculated based on various factors:

```python
if is_absent:
    total_deduction = required_time
elif apply_working_hour_deduction:
    total_deduction = required_time - working_time
else:
    total_deduction = delay_time + shortage_time
```

**Implementation Details**:
- Different policies can be configured through time unit rules
- Deductions may be capped at the required time
- Special handling for permission days and partial absences

## Day Type Handling

The system applies different business rules based on the day type:

### Working Day

- Regular working day rules apply
- Delay, shortage, and overtime are calculated
- Absence is determined based on check-in/check-out

### Day Off

- No delay or shortage is calculated
- All working time counts as overtime
- No absence determination

### Weekend

- Treated similarly to days off
- May have different overtime factors

### Vacation/Leave

- No attendance expected
- Any recorded attendance may be treated as overtime
- No absence determination

### Public Holiday

- Treated similarly to days off
- May have special overtime factors

## Time Unit Types and Rules

Different time unit types have specific calculation rules:

### Normal Time Unit

- Fixed start and end times
- Standard delay and shortage calculations
- Working time based on check-in and check-out

### Flexible Time Unit

- More flexible start time
- Delay calculated based on start limit rather than start time
- Working time may be adjusted based on check-in time

### Open Time Unit

- No fixed start or end times
- No delay or shortage calculation
- Working time based solely on check-in and check-out duration

## Implementation in Code

The business rules are implemented primarily in the following methods:

### Timesheet Time Unit Calculations

```python
def calculate_times(self):
    # Basic time calculations
    if self.first_checkin_datetime and self.last_checkout_datetime:
        self.working_time = (self.last_checkout_datetime - self.first_checkin_datetime).total_seconds() / 3600
    
    # Delay calculation
    if self.first_checkin_datetime and self.start_time:
        checkin_time = self._convert_datetime_to_float(self.first_checkin_datetime)
        if checkin_time > (self.start_time + self.grace_in_time):
            self.delay_time = checkin_time - self.start_time
    
    # Shortage calculation
    if self.last_checkout_datetime and self.end_time:
        checkout_time = self._convert_datetime_to_float(self.last_checkout_datetime)
        if checkout_time < (self.end_time - self.grace_out_time):
            self.shortage_time = self.end_time - checkout_time
    
    # Overtime calculation
    if self.working_time > self.required_time:
        self.overtime = self.working_time - self.required_time
    
    # Apply half-day rules if configured
    if self.apply_half_work:
        self._apply_half_day_rules()
```

### Timesheet Aggregation and Processing

```python
def calculate_ts_times(self):
    # Aggregate data from time units
    self._aggregate_common_fields()
    
    # Apply different logic based on day type
    if self.is_dayoff or self.is_weekend or self.is_vacation:
        self._process_dayoff()
    else:
        self._process_working_day()
    
    # Update computed fields and counters
    self._set_computed_fields()
    self._update_counters()
```

### Day Type Processing

```python
def _process_working_day(self):
    # Regular working day calculations
    self.total_delay_shortage = self.delay_time + self.shortage_time
    
    # Absence determination
    if not self.first_checkin_datetime and not self.last_checkout_datetime:
        self.is_absent = True
    elif self.working_time < self.absence_threshold:
        self.is_absent = True
    
    # Deduction calculation
    if self.is_absent:
        self.total_deduction = self.required_time
    elif self.apply_working_hour_deduction:
        self.total_deduction = max(0, self.required_time - self.working_time)
    else:
        self.total_deduction = self.total_delay_shortage

def _process_dayoff(self):
    # Day off calculations
    self.delay_time = 0
    self.shortage_time = 0
    self.total_delay_shortage = 0
    
    # All working time counts as overtime
    self.overtime = self.working_time
    
    # No deductions on days off
    self.total_deduction = 0
```

## Configuration Options

The business rules can be configured through various parameters:

### Time Unit Rule Configuration

- `grace_in_time`: Grace period for check-in
- `grace_out_time`: Grace period for check-out
- `apply_half_work`: Whether to apply half-day rules
- `half_work_checkin_criteria`: Threshold for half-day based on late check-in
- `half_work_checkout_criteria`: Threshold for half-day based on early check-out
- `overtime_factor`: Multiplier for overtime hours
- `apply_working_hour_deduction`: Whether to use working hours for deduction

### System Parameters

- `absence_threshold`: Minimum working time to avoid absence determination
- `default_overtime_factor`: Default overtime multiplier
- `allow_no_checkout`: Whether to allow missing check-out
- `no_checkout_calculation`: How to handle missing check-out
