{"info": {"_postman_id": "c85ec292-4b24-484a-be23-fd21807b878f", "name": "Employee Attendance API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Employee Attendance by Employee Number", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "frontend_lang=en_US", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "http://localhost:8018/GetEmployeeAttendanceByEmpNo?empNo=123&dateFrom=01/01/2025&dateTo=01/01/2026", "protocol": "http", "host": ["localhost"], "port": "8018", "path": ["GetEmployeeAttendanceByEmpNo"], "query": [{"key": "empNo", "value": "123"}, {"key": "dateFrom", "value": "01/01/2025"}, {"key": "dateTo", "value": "01/01/2026"}]}}, "response": []}, {"name": "Get Employee Attendance by Username", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "frontend_lang=en_US", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "http://localhost:8018/GetEmployeeAttendanceByUserName?userName=mohsen&dateFrom=01/01/2025&dateTo=01/01/2026", "protocol": "http", "host": ["localhost"], "port": "8018", "path": ["GetEmployeeAttendanceByUserName"], "query": [{"key": "userName", "value": "mohsen"}, {"key": "dateFrom", "value": "01/01/2025"}, {"key": "dateTo", "value": "01/01/2026"}]}}, "response": []}, {"name": "Get All Employees Attendance", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "frontend_lang=en_US", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "http://localhost:8018/GetEmployeesAttendance?dateFrom=01/01/2025&dateTo=01/02/2026", "protocol": "http", "host": ["localhost"], "port": "8018", "path": ["GetEmployeesAttendance"], "query": [{"key": "dateFrom", "value": "01/01/2025"}, {"key": "dateTo", "value": "01/02/2026"}]}}, "response": []}, {"name": "Get Employees Attendance by Department", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "frontend_lang=en_US", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "http://localhost:8018/GetEmployeesAttendanceByDeptName?deptName=Administration&dateFrom=01/01/2025&dateTo=01/01/2026", "protocol": "http", "host": ["localhost"], "port": "8018", "path": ["GetEmployeesAttendanceByDeptName"], "query": [{"key": "deptName", "value": "Administration"}, {"key": "dateFrom", "value": "01/01/2025"}, {"key": "dateTo", "value": "01/01/2026"}]}}, "response": []}, {"name": "Get Employees Attendance Deduction", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "frontend_lang=en_US", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "http://localhost:8018/GetEmployeesAttendanceDeduction?dateFrom=01012025&dateTo=01022026", "protocol": "http", "host": ["localhost"], "port": "8018", "path": ["GetEmployeesAttendanceDeduction"], "query": [{"key": "dateFrom", "value": "01012025"}, {"key": "dateTo", "value": "01022026"}]}}, "response": []}], "auth": {"type": "apikey", "apikey": [{"key": "value", "value": "frontend_lang=en_US", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Add pre-request logic here if needed"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Add test scripts here if needed"], "type": "text/javascript"}}]}