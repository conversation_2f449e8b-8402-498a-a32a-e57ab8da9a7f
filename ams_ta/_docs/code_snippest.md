


# Detect overlap in Schedule Day

    `
        @api.constrains('shift_unit_ids')
        def _check_overlap(self):
            for record in self:
                # Collect all periods' start and end times
                periods = [(shift.start, shift.end) for shift in record.shift_unit_ids]
                periods.sort()  # Sort periods by start time
    
                # Check for overlap
                for i in range(len(periods) - 1):
                    current_end = periods[i][1]
                    next_start = periods[i + 1][0]
                    if current_end > next_start:
                        raise ValidationError(
                            "Overlap detected in periods for the schedule day: %s. "
                            "Please ensure no periods overlap." % record.name
                        )
    
    `