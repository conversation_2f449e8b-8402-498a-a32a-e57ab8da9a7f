
## Master Data:
    _template_model.py: Defines a template model.
    base_device_log.py: Manages device logging.
    department.py: Defines department-related functionalities.
    employee.py: Contains employee-related data and functionalities.
    user_group.py: Manages policy groups.(user_group)
    res_user.py: Manages user-related data.
    schedule.py: Defines scheduling functionalities.
    schedule_day.py: Manages daily schedules.
    shift.py: Handles shift-related functionalities.
    shift_rule.py: Defines rules for shifts.
    shift_unit.py: Manages units of shifts.

# Operations
    base_timesheet.py: Handles timesheet functionalities.
    base_transaction.py: Manages transactions.
    punch_log.py: Handles punch log entries.
    timesheet.py: Contains core timesheet functionalities.
    timesheet_setting.py: Manages settings related to timesheets.
    timesheet_shift_unit.py: Handles shift units in timesheets.

# 1. Class Diagram Master Data
```mermaid

classDiagram
    class BaseCodeModel {
		- _name = "ams_base.code_model"
		- _inherit = ["ams_base.activate_model","mail.thread", "mail.activity.mixin"]
		- sql_constraints = [('code_unique', 'unique (code)', 'Code already exists !'),
							 ('ar_name_unique', 'unique (ar_name)', 'Arabic Name already exists !'),
							 ('en_name_unique', 'unique (en_name)', 'English Name already exists !')]
		- name : Char
		- code : Char	
		- ar_name : Char
		- en_name : Char
		- activate : Boolean
		- active : Boolean
    }
    
    class UserGroup {
        - _name = "ams.user_group"
		- _inherit = "ams_base.code_model"
		- employee_ids : One2Many(hr.employee)
        - primary_shift_id : Many2One(ams_ta.shift)
        - secondary_shift_id : Many2One(ams_ta.shift)
    }

    class Schedule {
        - _name = "ams_ta.shift_schedule"
        - _inherit = "ams_base.code_model"
        - cycle_days : Integer
        - schedule_type : Selection
        - default_shift_unit_id : Many2One(ams_ta.shift_unit)
        - day_ids : One2Many(ams_ta.shift_schedule_day)
    }
     class BaseAbstractModel {
		- _name = "ams_base.abstract_model"
		-_abstract = True
		- company_id : Many2One(res.company)
    }
    class ScheduleDay {
        - _name = "ams_ta.shift_schedule_day"
		- _inherit = ["ams_base.abstract_model", "mail.thread", "mail.activity.mixin"]

		- name : Char
       	- week_day : Selection
        - index : Integer
        - is_day_off : Boolean
        - units_overlapped : Boolean
        - units_mixed_types : Boolean
        - schedule_id : Many2One(ams_ta.shift_schedule)
        - shift_units_ids : Many2Many(ams_ta.shift_unit)
    }
   
    
    class BaseFollowUpModel{
        _name = "ams_base.follow_up_model"
        
    }
    class Shift {
        - _name = "ams_ta.shift"
        - _inherit = ["ams_ta.base_transaction"]
        - name : Char
       	- start_date : Date
        - end_date : Date
        - state : Selection
        - state_fixed : Selection
        - is_default : Boolean
        - is_exception : Boolean
        - status : Selection
        - employee_ids : One2Many(hr.employee)
        - exception_employee_ids : Many2Many(hr.employee)
        - schedule_id : Many2One(ams_ta.shift_schedule)
    }
     class ShiftUnit {
        - _name = "ams_ta.shift_unit"
		- _inherit = "ams_base.code_model"
        - start_time : Float
        - end_time : Float
        - start_limit : Float
        - shift_type : Selection
        - duration : Float
        - is_overnight : Boolean
        - color : Char
        - apply_min_max : Boolean
        - min_checkin_time : Float
        - max_checkout_time : Float
        - absent_time_criteria : Float
        - rule_id : Many2One(ams_ta.shift_rule)
    }

    class ShiftRule {
        - _name = "ams_ta.shift_rule"
		- _inherit = "ams_base.code_model"
     	- grace_in_time : Float
        - grace_out_time : Float
        - half_work_checkin_criteria : Float
        - half_work_checkout_criteria : Float
        - calc_half_no_checkout_time : Boolean
        - overtime_factor : Float
    }
    class ActivateModel {
        - _name = "ams_base.activate_model"
        - _inherit = "ams_base.abstract_model"
    }

    BaseAbstractModel <|--  ActivateModel :_inherit
%%    BaseAbstractModel <|--  ScheduleDay :_inherit
	BaseFollowUpModel <|--  Shift :_inherit
	ActivateModel <|--  BaseCodeModel :_inherit
	
%%	BaseCodeModel <|--  UserGroup :_inherit
%%    BaseCodeModel <|--  Schedule :_inherit
%%    BaseCodeModel <|--  ShiftUnit :_inherit
%%	BaseCodeModel <|--  ShiftRule :_inherit
	
	UserGroup --o Shift : Primary
	UserGroup --o Shift : Secondary
	Shift --o Schedule
	Schedule --* ScheduleDay
	ScheduleDay *--* ShiftUnit
	ShiftUnit --o ShiftRule


```

# 2. Class Diagram Operations
``` mermaid
classDiagram
    class BaseTimesheet {
        - _name = "ams_ta.base_timesheet"
		- _inherit = ["ams_base.abstract_model", "mail.thread", "mail.activity.mixin"]
		- name : Char
		- date : Date
		- employee_id : Many2One(hr.employee)
		- first_checkin_time : Float
		- last_checkout_time : Float
		- first_checkin_time_char : Char (computed)
		- last_checkout_time_char : Char (computed)
		- first_checkin_datetime : Datetime
		- last_checkout_datetime : Datetime
		- checked_in : Boolean
		- checked_out : Boolean
		- required_time : Float
		- working_time : Float
		- diff_working_time : Float
		- delay_time : Float
		- shortage_time : Float
		- total_delay_shortage : Float
		- overtime : Float
		- overtime_factored : Float
		- required_time_min : Float (computed)
		- working_time_min : Float
		- diff_working_time_min : Float
		- delay_time_min : Float
		- shortage_time_min : Float
		- total_delay_shortage_min : Float
		- overtime_min : Float
		- overtime_factored_min : Float
		- notes : Char
		- is_delayed : Boolean
		- is_shortage : Boolean
		- is_overnight : Boolean
		- from_mobile : Boolean
		- in_longitude : Float
		- in_latitude : Float
		- out_longitude : Float
		- out_latitude : Float
    }
    class PunchLog {
        - _name = "ams_ta.punch_log"
		- _inherit = ["ams_base.abstract_model", "ams_base.follow_up_model", "mail.thread", "mail.activity.mixin"]
        - name : Char
        - log_time : Datetime
        - date : Date
        - execute_log_time : Datetime
        - employee_number : Char
        - enroll_number : Char
        - device_serial : Char
        - state : Selection
        - dept_path_code : Char
        - notes : Char
        - longitude : Float
        - latitude : Float
        - from_mobile : Boolean
    }
    class Timesheet {
        - _name = "ams_ta.timesheet"
		- _inherit = ["ams_ta.base_timesheet", "ams_base.follow_up_model"]
		- _order = 'date'
        - total_deduction : Float
        - is_dayoff : Boolean
        - is_weekend : Boolean
        - is_vacation : Boolean
        - is_public_vacation : Boolean
        - is_absent : Boolean
        - is_attend_day : Boolean
        - is_working_day : Boolean
        - manual_edit : Boolean
        - delay_alert_sent : Boolean
        - shortage_alert_sent : Boolean
        - absent_alert_sent : Boolean
        - cycle_days : Integer
        - schedule_type : Selection
        - day_index : Integer
        - time_off_hours : Float
        - delay_count : Integer
        - shortage_count : Integer
        - absent_count : Integer
        - dayoff_count : Integer
        - weekend_count : Integer
        - attend_day_count : Integer
        - working_day_count : Integer
        - overtime_day_count : Integer
        - vacation_count : Integer
        - permission_count : Integer
        - total_vacation_count : Integer
        - color : Char
        - shift_units_ids : One2Many(ams_ta.timesheet_shift_unit)
        - shift_id : Many2One(ams_ta.shift)
        - punch_logs : Char
    }

    class TimeSheetShiftUnit {
        - _name = "ams_ta.timesheet_shift_unit"
        - _inherit = ["ams_ta.base_timesheet", "ams_ta.base_transaction"]
        - apply_min_max : Boolean
        - min_checkin_time : Float
        - max_checkout_time : Float
        - grace_in_time : Float
        - grace_out_time : Float
        - absent_time_criteria : Float
        - half_work_checkin_criteria : Float
        - half_work_checkout_criteria : Float
        - calc_half_no_checkout_time : Boolean
        - start_time : Float
        - end_time : Float
        - start_limit : Float
        - shift_type : Selection
        - color : Char
        - overtime_factor : Float
        - timesheet_id : Many2one(ams_ta.timesheet)
 
    }

  
    BaseFollowUpModel <|--  PunchLog :_inherit
    BaseFollowUpModel  <|--  Timesheet :_inherit
	BaseTimesheet <|--  Timesheet :_inherit
	BaseTimesheet  <|--  TimeSheetShiftUnit :_inherit
	
	Timesheet --* TimeSheetShiftUnit
	


```

# Class Diagram Master Data Odoo Model
```mermaid
classDiagram
    class Department {
    }
   
    class Employee {
        - _name = "hr.employee"
		- _inherit = ["hr.employee", "ams_base.follow_up_model"]
        - user_group_id : Many2one(ams_ta.user_group)
        - shift_id : Many2one(ams_ta.shift)
    }
    
    class ResUser {
        - _name = "res.users"
		- _inherit = "res.users"
    }
    
    class ResConfigSettings {
		- _name = "res.config.settings"
		- last_timesheet_date : Date
		
    }

    class BaseDeviceLog {
		- _name = "ams_base.device_log"
		- _inherit = "ams_base.device_log"
		- name : Char
		- device_id : Char
		- log_time : Datetime
		- date : Date (computed)
		- log_date : Date
		- event_id : Char
		- event_code : Integer
		- code_name : Char
		- sub_code : Integer
		- executed_datetime : Datetime
		- has_image : Boolean
		- temperature : Float
		- timestamp : Char
		- description : Char
		- state : Selection('pending', 'executed')
		- enroll_number : Char
		- enroll_name : Char
		- is_ta : Boolean
		- changed_on_device : Boolean
		- entity_id : Integer
    }
    
%%         class BaseTransaction {
%%		- _name = "ams_ta.base_transaction"
%%		- employee_number : Char
%%		- enroll_number : Char
%%		- user_name : Char
%%		- emp_user_id : Many2One(res.users)
%%		- dept_path_code : Char
%%		- color : Char
%%		- employee_id : Many2One(hr.employee)
%%		- user_group_id : Many2One(ams_ta.user_group)
%%		- department_id : Many2One(hr.department)
%%		- company_id : Many2One(res.company)
%%		- parent_department_id : Many2One(hr.department)
%%		- current_department_id : Many2One(hr.department)
%%		
%%    }


    
```
