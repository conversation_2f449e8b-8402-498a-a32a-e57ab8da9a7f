using DevExpress.ExpressApp;
using DevExpress.ExpressApp.Xpo;
using FTA.Module.BusinessObjects.Basics;
using FTA.Module.BusinessObjects.Info;
using FTA.Module.BusinessObjects.Operation;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using DevExpress.Persistent.Base;
using DevExpress.Xpo;
using DevExpress.Xpo.Metadata;
using DevExpress.Xpo.DB;
using System.Runtime.Serialization;
using DevExpress.Data.Filtering;
using FTA.DM.DTO;
using System.Diagnostics;
using System.Globalization;
using FTA.Module.BusinessObjects.Security;
using ADSecurity;
using System.Reflection;
using System.IO;
using System.ServiceModel.Web;
using System.Web;
using LP.Logger;
using System.ServiceModel;
using System.ServiceModel.Channels;
using System.Collections;

namespace FTA.INTBLL
{

    public class FTAIntegration
    {
        public static readonly LP.Logger.LPLogger Log = new LP.Logger.LPLogger(MethodBase.GetCurrentMethod().DeclaringType);
        IObjectSpace _ObjectSpace;//Device log win service
        XPObjectSpaceProvider osProvider;
        public DateTime DateNow { get { return DateTime.UtcNow.AddHours(3); } } //

        // Define a property to hold the default AuditInfo
        private AuditInfo auditInfo;
        // Property to hold AuditInfo
        public AuditInfo AuditInfo { get; private set; }

        public FTAIntegration()
        {
            InitLog();
            RegisterModule();
        }
        public String InitLog()
        {
            try
            {
                if (!EventLog.SourceExists("FTAINT"))
                {
                    // Requires administrator privileges
                    EventLog.CreateEventSource("FTAINT", "Application");
                }
                EventLog.WriteEntry("FTAINT", "InitLog()", EventLogEntryType.Information);

                InitializeAuditInfo();

                string configFile = Path.Combine(System.Web.Hosting.HostingEnvironment.ApplicationPhysicalPath, "Log4Net.config");
                Log.Debug("AppPhy: " + configFile);

                EventLog.WriteEntry("FTAINT","Config File:"+ configFile, EventLogEntryType.Information);
                log4net.Config.XmlConfigurator.ConfigureAndWatch(new FileInfo(configFile));

                EventLog.WriteEntry("FTAINT", "log4net.Config Done" + configFile, EventLogEntryType.Information);

             
                return "Config File:" + configFile;
            }
            catch (Exception ex)
            {
                Log.Warn("Error In Logger Init...", ex);
                return ex.Message;
            }

        }
        public String RegisterModule()
        {
            //_ObjectSpace = objectSpace;
            //http://dennisgaravsky.blogspot.com/2016/07/how-to-reuse-xaf-views-and-other.html
            //https://documentation.devexpress.com/#eXpressAppFramework/CustomDocument113709
            string connString = "";
            try
            {
                string logPath = "\\Logs\\INTLOG_" + DateNow.ToString("yyyyMMdd");

                Trace.Listeners.Add(new TextWriterTraceListener(logPath + ".log"));
                Tracing.Initialize(String.Empty);
                Tracing.LogName = logPath;

                Tracing.Tracer.LogText("FTA.INTBLL.FTAIntegration.RegisterModule()");
                connString = ConfigurationManager.ConnectionStrings["ConnectionString"].ConnectionString;

                // EventLog.WriteEntry("FTA.WinServiceManager", "Attendance Service,conn:" + connString, EventLogEntryType.Information);
                XpoTypesInfoHelper.GetXpoTypeInfoSource();
                //XafTypesInfo.Instance.RegisterEntity(typeof(Device));
                //XafTypesInfo.Instance.RegisterEntity(typeof(DeviceAction));
                //XafTypesInfo.Instance.RegisterEntity(typeof(ServiceSetting));
                XafTypesInfo.Instance.RegisterEntity(typeof(Attendance));
                XafTypesInfo.Instance.RegisterEntity(typeof(AttendanceLog));
                XafTypesInfo.Instance.RegisterEntity(typeof(Employee));
                XafTypesInfo.Instance.RegisterEntity(typeof(FTAUser));
                XafTypesInfo.Instance.RegisterEntity(typeof(Device));
                XafTypesInfo.Instance.RegisterEntity(typeof(DeviceAction));
                XafTypesInfo.Instance.RegisterEntity(typeof(EmployeeEnrollment));
                XafTypesInfo.Instance.RegisterEntity(typeof(EmployeePermission));
                XafTypesInfo.Instance.RegisterEntity(typeof(EmployeeVacation));


                osProvider = new XPObjectSpaceProvider(connString, null, true);
                if (osProvider == null)
                    Tracing.Tracer.LogText(string.Format("FTA.INTBLL.FTAIntegration.RegisterModule(), osProvider can't init (XPObjectSpaceProvider)"));
                else
                {
                    _ObjectSpace = osProvider.CreateObjectSpace();//Create 
                }

                if (_ObjectSpace == null)
                    Tracing.Tracer.LogText(string.Format("FTA.INTBLL.FTAIntegration.RegisterModule(), osProvider can't create (ObjectSpace)"));
            }
            catch (Exception ex)
            {
                Tracing.Tracer.LogError(ex);
                return ex.Message;
            }

            return "";
        }

        private static IDataLayer GetDataLayer()
        {
            XpoDefault.Session = null;

            string conn = ConfigurationManager.ConnectionStrings["ConnectionString"].ConnectionString;
            conn = XpoDefault.GetConnectionPoolString(conn);
            XPDictionary dict = new ReflectionDictionary();
            IDataStore store = XpoDefault.GetConnectionProvider(conn, AutoCreateOption.SchemaAlreadyExists);
            dict.GetDataStoreSchema(System.Reflection.Assembly.GetExecutingAssembly());
            IDataLayer dl = new ThreadSafeDataLayer(dict, store);
            return dl;
        }

        public void LogRequest(object bodyObj = null)
        {
            using (log4net.ThreadContext.Stacks["NDC"].Push(GetType().FullName + "." +MethodBase.GetCurrentMethod().Name))
            {
                try
                {
                    // Initialize or update AuditInfo
                    InitializeAuditInfo();

                    // Log the Request URL using WebOperationContext
                    var requestContext = WebOperationContext.Current?.IncomingRequest;
                    if (requestContext != null)
                    {
                        var url = requestContext.UriTemplateMatch.RequestUri.ToString();
                        Log.Info($"Request URL: {url}", AuditInfo);
                    }
                    else
                    {
                        Log.Warn("WebOperationContext.Current is null. Unable to log the request URL.", AuditInfo);
                    }

                    // Log the Source IP address and User Identity using HttpContext
                    if (bodyObj != null)
                    {
                        //var jsonBody = SerializeToJson(bodyObj);
                        //Log.Debug($"Request Body: {jsonBody}", AuditInfo);
                    }
                    else
                    {
                        Log.Debug($"Request doesn't contain body", AuditInfo);
                    }
                  
                }
                catch (Exception ex)
                {
                    // Handle any exceptions that occur during logging
                    Log.Error($"Error logging request: {ex.Message}", ex, AuditInfo); // Log full exception with stack trace
                }
            }
        }
        public static string SerializeToJson(object obj)
        {
            if (obj == null)
                return "null";

            Type objType = obj.GetType();

            // Handle string
            if (obj is string)
                return $"\"{obj}\"";

            // Handle numeric and boolean types
            if (obj is int || obj is long || obj is float || obj is double || obj is bool)
                return obj.ToString().ToLower();

            // Handle collections
            if (obj is IEnumerable)
            {
                var elements = new List<string>();
                foreach (var item in (IEnumerable)obj)
                {
                    elements.Add(SerializeToJson(item));
                }
                return $"[{string.Join(",", elements)}]";
            }

            // Handle complex objects
            var jsonElements = new List<string>();
            foreach (var prop in objType.GetProperties(BindingFlags.Public | BindingFlags.Instance))
            {
                var propValue = prop.GetValue(obj);
                string jsonValue = SerializeToJson(propValue);
                jsonElements.Add($"\"{prop.Name}\":{jsonValue}");
            }

            return $"{{{string.Join(",", jsonElements)}}}";
        }

        // Method to initialize or update AuditInfo based on the current request context
        public void InitializeAuditInfo()
        {
            var username = "...";
            var ip = "...";
            var version = System.Configuration.ConfigurationManager.AppSettings["APIVersion"] ?? "2.0.0";
            if (OperationContext.Current != null)
            {
                string loggedin = OperationContext.Current.ServiceSecurityContext?.WindowsIdentity.Name.ToLower();
                username = loggedin?.Substring(loggedin.LastIndexOf("\\") + 1).ToLower() ?? "...";//cut name of domain

                var remoteEndpoint = OperationContext.Current?.IncomingMessageProperties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                ip = remoteEndpoint?.Address ?? "...";
            }
            this.AuditInfo = new AuditInfo
            {
                UserName = username,
                AppVersion = version,
                DeviceName = ip
            };

           
        }

        #region  Employees Methods
        internal EmployeeDTO MappEmployeeInfo(Employee emp)
        {
            var empObj = new EmployeeDTO()
            {
                EmpNo = emp.HRCode,
                UserName = emp.Username,
                ArabicName = emp.ArabicName,
                EnglishName = emp.EnglishName,
                Email = emp.Email,//TODO add phone no
                DeptArabicName = emp.Department?.ArabicName,
                DeptEnglishName = emp.Department?.EnglishName,

                BGArabicName = emp.BusinessGroup?.ArabicName,
                BGEnglishName = emp.BusinessGroup?.EnglishName,

                BranchArabicName = emp.Branch?.ArabicName,
                BranchEnglishName = emp.Branch?.EnglishName,

                AreaArabicName = emp.Area?.ArabicName,
                AreaEnglishName = emp.Area?.EnglishName,


            };

            return empObj;
        }

        public string AddEmployee(EmployeeDTO emp)
        {
            var TAG = "init";
            try
            {
                if (emp == null)
                    return "Error:No input found";

                using (var os = osProvider.CreateObjectSpace())
                {
                    string empCriteria = !String.IsNullOrEmpty(emp.UserName) ? "Username=?" : "HRCode=?";
                    string key = !String.IsNullOrEmpty(emp.UserName) ? emp.UserName : emp.EmpNo;
                    TAG = "Build_Criteria";
                    var employee = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                    TAG = "OS_EMP";
                    if (employee == null)
                    {
                        //Data in creation Only
                        employee = os.CreateObject<Employee>();//add new object
                        employee.Username = emp.UserName;
                        employee.HRCode = emp.EmpNo;
                        employee.EnrollID = Employee.GenerateEnrollID(((XPObjectSpace)os).Session);//Generate squenceID
                        TAG = "Create_NewEmp";
                    }
                    //Data will update
                    employee.ArabicName = emp.ArabicName;
                    employee.EnglishName = emp.EnglishName;
                    employee.Email = emp.Email;
                    TAG = "Update_Emp_Done";
                    //Generate default
                    employee.BusinessGroup = !string.IsNullOrEmpty(emp.BGEnglishName) ? CreateBG(os, emp.BGEnglishName, emp.BGArabicName) :
                        CreateBG(os, "Default Group");
                    TAG = "Update_EMP_BG_Done";
                    employee.Department = !string.IsNullOrEmpty(emp.DeptEnglishName) ? CreateDept(os, emp.DeptEnglishName, emp.DeptArabicName) :
                        CreateDept(os, "Default Dept");
                    TAG = "Update_EMP_Dept_Done";
                    employee.Branch = !string.IsNullOrEmpty(emp.BranchEnglishName) ? CreateBranch(os, emp.BranchEnglishName, emp.BGArabicName) :
                        CreateBranch(os, "Default Branch");
                    TAG = "Update_EMP_BR_Done";
                    employee.Area = !string.IsNullOrEmpty(emp.AreaEnglishName) ? CreateArea(os, emp.AreaEnglishName, emp.AreaArabicName) :
                        CreateArea(os, "Default Area");
                    TAG = "Update_EMP_ARea_Done";
                    employee.Company = os.FindObject<Company>(null);
                    employee.Level = Module.BusinessObjects.Enum.SecurityLevel.User;
                    TAG = "OS_TODO_Commit";
                    os.CommitChanges();
                    //update data

                }
            }
            catch (Exception ex)
            {
                return string.Format("Error:{0},{1},{2}", TAG, ex.Message, ex.StackTrace);
            }
            return "";//or errorMessage
        }

        public string AddEmployees(EmployeeListDTO empList)
        {
            try
            {
                foreach (var emp in empList.Employees)
                {
                    var response = AddEmployee(emp);
                    if (!string.IsNullOrEmpty(response)) {
                        return response;
                    }
                }
            }
            catch (Exception ex)
            {
                return "Error:" + ex.Message;
            }
            return "1";//or errorMessage
        }
        //start point for single employee
        public EmployeeDTO GetEmployeeInfo(string key, FilterKeyType filterType = FilterKeyType.HrCode)
        {
            if (osProvider == null)
                return new EmployeeDTO() { ErrorMessage = string.Format("OpjectSpace Provider is null") };

            using (var os = osProvider.CreateObjectSpace())
            {
                string empCriteria = filterType == FilterKeyType.Username ? "Username=?" : "HRCode=?";
                var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                if (emp != null)
                {
                    var empObj = MappEmployeeInfo(emp);
                    return empObj;
                }
                else
                {
                    return new EmployeeDTO() { ErrorMessage = string.Format("Error: Employee {0} doesn't exist!!", key) };
                }
            }
            //return null;
        }

        //start point for employees list
        public EmployeeListDTO GetEmployeesInfo(string key = null, FilterKeyType filterType = FilterKeyType.HrCode)
        {
            var empLst = new EmployeeListDTO();
            empLst.Employees = new List<EmployeeDTO>();

            using (var os = osProvider.CreateObjectSpace())
            {
                string empCriteria = "";
                if (!string.IsNullOrEmpty(key) && key != "0")
                {
                    switch (filterType)
                    {
                        case FilterKeyType.HrCode:
                            empCriteria = "HRCode=?";
                            break;
                        case FilterKeyType.Username:
                            empCriteria = "Username=?";
                            break;
                        case FilterKeyType.DeptName:
                            empCriteria = "Department.EnglishName=?";
                            break;
                        case FilterKeyType.DeptCode:
                            empCriteria = "Department.Code=?";
                            break;
                        case FilterKeyType.BusinessGroupName:
                            empCriteria = "BusinessGroup.EnglishName=?";
                            break;
                        case FilterKeyType.BusinessGroupCode:
                            empCriteria = "BusinessGroup.Code=?";
                            break;
                        default:
                            empCriteria = "";
                            break;
                    }
                }

                var employees = os.GetObjects<Employee>(CriteriaOperator.Parse(empCriteria, key));
                if (employees != null && employees.Count > 0)
                {
                    foreach (var emp in employees)
                    {
                        var empObj = MappEmployeeInfo(emp);
                        empLst.Employees.Add(empObj);
                    }
                }


            }
            return empLst;
        }

        BusinessGroup CreateBG(IObjectSpace os, string name, string nameAr = null)
        {
            BusinessGroup group = string.IsNullOrEmpty(name) ? null :
                os.FindObject<BusinessGroup>(CriteriaOperator.Parse("Upper([EnglishName])=?", name.ToUpper())); //new BinaryOperator("EnglishName", name)
            if (group == null)
            {
                group = os.CreateObject<BusinessGroup>();
                group.EnglishName = name;
                group.ArabicName = nameAr ?? name;
                group.Shift = os.FindObject<Shift>(new BinaryOperator("IsDefault", true));
            }
            return group;
        }
        Department CreateDept(IObjectSpace os, string name, string nameAr = null)
        {
            Department dept = string.IsNullOrEmpty(name) ? null :
                os.FindObject<Department>(CriteriaOperator.Parse("Upper([EnglishName])=?", name.ToUpper()));
            if (dept == null)
            {
                dept = os.CreateObject<Department>();
                dept.EnglishName = name;
                dept.ArabicName = nameAr ?? name;
            }
            return dept;
        }
        Branch CreateBranch(IObjectSpace os, string name, string nameAr = null)
        {
            Branch branch = string.IsNullOrEmpty(name) ? null :
                os.FindObject<Branch>(CriteriaOperator.Parse("Upper([EnglishName])=?", name.ToUpper()));
            if (branch == null)
            {
                branch = os.CreateObject<Branch>();
                branch.EnglishName = name;
                branch.ArabicName = nameAr ?? name;
            }
            return branch;
        }
        Area CreateArea(IObjectSpace os, string name, string nameAr = "")
        {
            Area area = string.IsNullOrEmpty(name) ? null :
                os.FindObject<Area>(CriteriaOperator.Parse("Upper([EnglishName])=?", name.ToUpper()));
            if (area == null)
            {
                area = os.CreateObject<Area>();
                area.EnglishName = name;
                area.ArabicName = nameAr ?? name;
            }
            return area;
        }

        #endregion

        #region Get Simple Attendance

        internal AttendanceSimpleDTO GetSimpleAttendanceByEmp(Employee emp, DateTime dateFrom, DateTime dateTo, IObjectSpace os)
        {
            var empLogs = new AttendanceSimpleDTO() { Emp = new EmployeeDTO() { EmpNo = emp.HRCode, ArabicName = emp.ArabicName, EnglishName = emp.EnglishName } };
            var logs = os.GetObjects<Attendance>(CriteriaOperator.Parse("Employee=? and Date >=? and Date <= ? ", emp.Oid, dateFrom, dateTo));
            if (logs != null && logs.Count > 0)
            {
                var logsLst = logs.Select(a =>
                  new AttendanceSimpleLogDTO()
                  {
                      Date = a.Date,
                      InTime = a.FirstCheckIn == null ? "--:--" : a.FirstCheckIn.Value.ToString(@"hh\:mm\:ss"),
                      OutTime = a.LastCheckOut == null ? "--:--" : a.LastCheckOut.Value.ToString(@"hh\:mm\:ss"),
                  }).ToList();

                empLogs.Logs = logsLst;
            }
            return empLogs;
        }

        //start point for employees list
        public AttendanceSimpleListDTO GetSimpleEmployeesAttendance(string key, DateTime dateFrom, DateTime dateTo, bool keyIsUsername = false)
        {
            var attLst = new AttendanceSimpleListDTO();
            attLst.AttList = new List<AttendanceSimpleDTO>();

            using (var os = osProvider.CreateObjectSpace())
            {
                if (string.IsNullOrEmpty(key) || key == "0")
                {
                    var employees = os.GetObjects<Employee>(CriteriaOperator.Parse("HasEnrollment='True'", key));
                    if (employees != null && employees.Count > 0)
                    {
                        foreach (var emp in employees)
                        {
                            var empLogs = GetSimpleAttendanceByEmp(emp, dateFrom, dateTo, os);
                            attLst.AttList.Add(empLogs);
                        }
                    }

                }
                else
                {
                    string empCriteria = keyIsUsername ? "Username=?" : "HRCode=?";
                    var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                    if (emp != null)
                    {
                        var empLogs = GetSimpleAttendanceByEmp(emp, dateFrom, dateTo, os);
                        attLst.AttList.Add(empLogs);

                    }

                }

            }
            return attLst;
        }

        //start point for single employees
        public AttendanceSimpleDTO GetSimpleEmployeeAttendance(string key, DateTime dateFrom, DateTime dateTo, bool keyIsUsername = false)
        {
            using (var os = osProvider.CreateObjectSpace())
            {
                string empCriteria = keyIsUsername ? "Username=?" : "HRCode=?";
                var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                if (emp != null)
                {
                    var empLogs = GetSimpleAttendanceByEmp(emp, dateFrom, dateTo, os);
                    return empLogs;
                }
            }
            return null;
        }
        #endregion

        #region Attendance Detail Methods
        //for mobile usage
        public string AddAttendanceLog(EmpAttendanceLogDTO log ,bool isMobileApp=true)
        {
            try
            {
                if (log.Time == default(DateTime))
                {
                    return "Error: DateTimeString has incorrect format.";
                }
                using (var os = osProvider.CreateObjectSpace())
                {
                    string empCriteria = !String.IsNullOrEmpty(log.Username) ? "Username=?" : "HRCode=?";
                    string key = !String.IsNullOrEmpty(log.Username) ? log.Username : log.EmpNo;

                    var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));

                    //Check time between Min Allowed time and max Allowd time
                    if (isMobileApp)
                    {
                        //ToDO:Authenticate from mobile app
                        //string authRes = Authenticate(new AuthDTO
                        //{
                        //    Username = log.UserName,
                        //    EmpNo = log.EmpNo,
                        //    IsMobileDevice = true,
                        //    AppId = log.AppId
                        //}, true);
                        //if (authRes != "1")
                        //    return authRes;//return fauiler message

                        var unitShift=  Attendance.GetMyCurrentShiftUnit(emp, log.Time);
                        if (unitShift == null)
                        {
                            return string.Format("Error:attendance at this time not allowed!!");
                        }
                        
                    }
                    if (emp != null)
                    {
                        var device = os.FindObject<Device>(CriteriaOperator.Parse("DeviceID=?", log.AppId));
                        var empLog = os.CreateObject<AttendanceLog>();
                        empLog.LogDateTime =isMobileApp?DateNow: log.Time; //take time from server in mobile app
                        empLog.Employee = emp;
                        empLog.Device = device;
                        empLog.Comment = "EXTERNAL_SERVICE";
                        empLog.Longitude = log.Longitude;
                        empLog.Latitude = log.Latitude;
                        os.CommitChanges();
                    }
                    else
                    {
                        return string.Format("Error: Employee {0} doesn't exist!!", key);
                    }
                }
                
            }
            catch (Exception ex)
            {
                return "Error:" + ex.Message;
            }
            return "1";//or errorMessage
        }

        public EmpAttendanceLogDTO MAddAttendance(EmpAttendanceLogDTO data, bool isMobileApp = true)
        {
            var tag = "01";
            var result = data;
            using (log4net.ThreadContext.Stacks["NDC"].Push(GetType().FullName + "." + MethodBase.GetCurrentMethod().Name))
            {
                try
                {
                    if (data != null && !string.IsNullOrEmpty(data.Username))
                    {
                        using (var os = osProvider.CreateObjectSpace())
                        {
                            tag = "02";
                            string empCriteria = !String.IsNullOrEmpty(data.Username) ? "Username=?" : "HRCode=?";
                            string key = !String.IsNullOrEmpty(data.Username) ? data.Username : data.EmpNo;

                            var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                            var appTime = DateNow;
                            tag = "03";
                            //var emp = new MobileEmployee(username: data.Username);
                            if (emp != null)
                            {
                                var authData = data as BaseObj;
                                tag = "04";
                                if (IsTokenValid(authData, emp))
                                {
                                    tag = "05";
                                    if (IsAppIdValid(authData, emp))
                                    {
                                        tag = "06";

                                        if (!emp.MobileAllowAttendance)
                                        {
                                            tag = "07";
                                            return new EmpAttendanceLogDTO { ResponseCode = "114", ResponseMessage = "Attendance permission is denied" };
                                        }
                                        if (isMobileApp)
                                        {
                                            tag = "08";
                                            var unitShift = Attendance.GetMyCurrentShiftUnit(emp, data.Time);
                                            if (unitShift == null)
                                            {
                                                tag = "09";
                                                return new EmpAttendanceLogDTO { ResponseCode = "115", ResponseMessage = "Attendance not allowed at this time" };
                                            }
                                        }
                                        tag = "10";
                                       // var device = os.FindObject<Device>(CriteriaOperator.Parse("DeviceID=?", data.AppId));
                                        var empLog = os.CreateObject<AttendanceLog>();
                                        empLog.LogDateTime = isMobileApp ? DateNow : data.Time; //take time from server in mobile app
                                        empLog.Employee = emp;
                                        //empLog.Device = device;
                                        empLog.Comment = "MOBILE_SERVICE";
                                        empLog.Longitude = data.Longitude;
                                        empLog.Latitude = data.Latitude;

                                        tag = "12";
                                        os.CommitChanges();

                                        tag = "14";
                                        result.ResponseCode = "1";
                                        result.ResponseMessage = "OK";
                                        return result;

                                    }
                                    else
                                    {
                                        Log.Error("AppId not valid ,code:113,AppID:" + data.AppId, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                        return new EmpAttendanceLogDTO { ResponseCode = "113", ResponseMessage = "AppId not valid" };
                                    }
                                }
                                else
                                {
                                    // error 
                                    Log.Error("Token not valid ,code:112,Token:" + data.Token, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                    return new EmpAttendanceLogDTO { ResponseCode = "112", ResponseMessage = "Token not valid" };
                                }

                            }
                            else
                            {
                                Log.Error("user not registered ,code:111", new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                return new EmpAttendanceLogDTO { ResponseCode = "111", ResponseMessage = "This user not registered in time attendance system" };
                            }
                        }

                       
                    }
                    else
                    {
                        Log.Error("Input data not found, code:100,EmpNo:" + data.EmpNo, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });

                        return new EmpAttendanceLogDTO { ResponseCode = "100", ResponseMessage = "Input data not found" };
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("101 unhandeled exception", ex, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                    return new EmpAttendanceLogDTO { ResponseCode = "101", ResponseMessage = "error ,tag:" + tag };

                }
                return result;
            }
        }

        public AttendanceDTO GetAttendanceByEmp(Employee emp, DateTime dateFrom, DateTime dateTo, IObjectSpace os)
        {
            var empLogs = new AttendanceDTO() { Emp = new EmployeeDTO() { EmpNo = emp.HRCode, ArabicName = emp.ArabicName, EnglishName = emp.EnglishName, UserName = emp.Username } };
            var logs = os.GetObjects<Attendance>(CriteriaOperator.Parse("Employee=? and Date >=? and Date <= ? ", emp.Oid, dateFrom, dateTo),
                new List<SortProperty>() { new SortProperty("Date", SortingDirection.Ascending) }, false);
            if (logs != null && logs.Count > 0)
            {
                var logsLst = logs.Select(a =>
                  new AttendanceLogDTO()
                  {
                      Date = a.Date,
                      InTime = a.FirstCheckIn == null ? "--:--:--" : a.FirstCheckIn.Value.ToString(@"hh\:mm\:ss"),
                      OutTime = a.LastCheckOut == null ? "--:--:--" : a.LastCheckOut.Value.ToString(@"hh\:mm\:ss"),
                      Status = a.DayOff ? 1 : a.IsAbsent ? 2 : a.IsVacation ? 3 : 0,
                      RequiredTime = a.RequiredTime == null ? "--:--:--" : a.RequiredTime.ToString(@"hh\:mm\:ss"),
                      WorkingTime = a.WorkingTime == null ? "--:--:--" : a.WorkingTime.ToString(@"hh\:mm\:ss"),
                      Delay = a.Delay == null ? "--:--:--" : a.Delay.ToString(@"hh\:mm\:ss"),
                      Shortage = a.Shortage == null ? "--:--:--" : a.Shortage.ToString(@"hh\:mm\:ss"),
                      Comment = a.Comment == null ? "" : a.Comment
                  }).ToList();

                empLogs.Logs = logsLst;
            }
            return empLogs;
        }

        //start point for employees list
        public AttendanceListDTO GetEmployeesAttendance(string key, DateTime dateFrom, DateTime dateTo, FilterKeyType filterType = FilterKeyType.HrCode)
        {
            var attLst = new AttendanceListDTO();
            attLst.AttList = new List<AttendanceDTO>();

            try
            {
                using (var os = osProvider.CreateObjectSpace())
                {
                    string empCriteria = "";// (string.IsNullOrEmpty(key) || key == "0") ? "" :
                    if (!string.IsNullOrEmpty(key) && key != "0")
                    {
                        switch (filterType)
                        {
                            case FilterKeyType.HrCode:
                                empCriteria = "HRCode=?";
                                break;
                            case FilterKeyType.Username:
                                empCriteria = "Username=?";
                                break;
                            case FilterKeyType.DeptName:
                                empCriteria = "Department.EnglishName=?";
                                break;
                            case FilterKeyType.DeptCode:
                                empCriteria = "Department.Code=?";
                                break;
                            case FilterKeyType.BusinessGroupName:
                                empCriteria = "BusinessGroup.EnglishName=?";
                                break;
                            case FilterKeyType.BusinessGroupCode:
                                empCriteria = "BusinessGroup.Code=?";
                                break;
                            default:
                                empCriteria = "";
                                break;
                        }
                    }
                    //filterType == FilterKeyType.DeptName ? "Department.EnglishName=?" : "";
                    var employees = os.GetObjects<Employee>(CriteriaOperator.Parse(empCriteria, key));
                    if (employees != null && employees.Count > 0)
                    {
                        foreach (var emp in employees)
                        {
                            var empLogs = GetAttendanceByEmp(emp, dateFrom, dateTo, os);
                            attLst.AttList.Add(empLogs);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                attLst.ErrorMessage = "Error:" + ex.Message;
                return attLst;
            }
            return attLst;
        }

        //start point for single employees
        public AttendanceDTO GetEmployeeAttendance(string key, DateTime dateFrom, DateTime dateTo, FilterKeyType filterType = FilterKeyType.HrCode)
        {
            if (osProvider == null)
                return new AttendanceDTO() { ErrorMessage = string.Format("OpjectSpace Provider is null") };

            using (var os = osProvider.CreateObjectSpace())
            {
                string empCriteria = filterType == FilterKeyType.Username ? "Username=?" : "HRCode=?";
                var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                if (emp != null)
                {
                    var empLogs = GetAttendanceByEmp(emp, dateFrom, dateTo, os);
                    return empLogs;
                }
                else
                {
                    return new AttendanceDTO() { ErrorMessage = string.Format("Error: Employee {0} doesn't exist!!", key) };
                }
            }
            //return null;
        }

        //start point for employees list
        public AttendanceDeductionListDTO GetEmployeesAttendanceDeduction(string key, DateTime dateFrom, DateTime dateTo, FilterKeyType filterType = FilterKeyType.HrCode)
        {
            var attLst = new AttendanceDeductionListDTO();
             attLst.DeductionList = new List<AttendanceDeductionDTO>();

            try
            {
                using (var os = osProvider.CreateObjectSpace())
                {
                    //var logs = os.GetObjects<Attendance>(CriteriaOperator.Parse("Date >=? and Date <= ? ", dateFrom, dateTo));
                    string empCriteria = "";// (string.IsNullOrEmpty(key) || key == "0") ? "" :
                    if (!string.IsNullOrEmpty(key) && key != "0")
                    {
                        switch (filterType)
                        {
                            case FilterKeyType.HrCode:
                                empCriteria = "HRCode=?";
                                break;
                            case FilterKeyType.Username:
                                empCriteria = "Username=?";
                                break;
                            case FilterKeyType.DeptName:
                                empCriteria = "Department.EnglishName=?";
                                break;
                            case FilterKeyType.DeptCode:
                                empCriteria = "Department.Code=?";
                                break;
                            case FilterKeyType.BusinessGroupName:
                                empCriteria = "BusinessGroup.EnglishName=?";
                                break;
                            case FilterKeyType.BusinessGroupCode:
                                empCriteria = "BusinessGroup.Code=?";
                                break;
                            default:
                                empCriteria = "";
                                break;
                        }
                    }
                    //filterType == FilterKeyType.DeptName ? "Department.EnglishName=?" : "";
                    var employees = os.GetObjects<Employee>(CriteriaOperator.Parse(empCriteria, key));
                    if (employees != null && employees.Count > 0)
                    {
                        foreach (var emp in employees)
                        {
                            var totalDeduction = emp.Attendances.Where(a=>a.Date>=dateFrom&&a.Date<=dateTo) //filter period
                                .Sum(d => d.TotalDeduction.Ticks);//Ticks

                            var totalRequiredTime = emp.Attendances.Where(a => a.Date >= dateFrom && a.Date <= dateTo) //filter period
                                .Sum(d => d.RequiredTime.Ticks);//Ticks

                            var totalDeductionMinutes = (int)TimeSpan.FromTicks(totalDeduction).TotalMinutes;
                            var totalRequiredMinutes = (int)TimeSpan.FromTicks(totalRequiredTime).TotalMinutes;
                            var totalDeductionHours = (int)TimeSpan.FromTicks(totalDeduction).TotalHours;

                            //
                            attLst.DeductionList.Add(new AttendanceDeductionDTO
                            {
                                EmpNo = emp.HRCode,
                                DeductionMinutes = totalDeductionMinutes,
                                DeductionHours = totalDeductionHours,
                                RequiredMinutes=totalRequiredMinutes
                            });
                            
                        }
                        
                    }
                }
            }
            catch (Exception ex)
            {
                attLst.ErrorMessage = "Error:" + ex.Message;
                return attLst;
            }
            return attLst;
        }

        #endregion

        #region Employee  Permissions / Vacations Methods
        RequestLogDTO ValidateRequestInput(RequestLogDTO request)
        {
            if (!string.IsNullOrEmpty(request.ErrorMessage))
            {
                return request;
            }
            //if (request.StartDate.Date <= DateNow.Date)
            //{
            //    request.ErrorMessage = "Error: Request start date must be greater than today";
            //    return request;
            //}
            if (request.EndDate.Date < request.StartDate.Date)
            {
                var errorMessage = "Error: End date must be greater than or equal start date";
                Log.Info(errorMessage, AuditInfo);
                request.ErrorMessage = errorMessage;
                return request;
            }
            return request;
        }
        public RequestLogDTO AddPermissionRequest(RequestLogDTO request)
        {
            using (log4net.ThreadContext.Stacks["NDC"].Push(GetType().FullName + "." + MethodBase.GetCurrentMethod().Name))
            {
                try
                {
                    #region Validate input from user
                    Log.Debug("TODO: ValidateRequestInput", AuditInfo);
                    request = ValidateRequestInput(request);
                    if (!string.IsNullOrEmpty(request.ErrorMessage))
                    {
                        Log.Warn("Validation Error: ValidateRequestInput" + request.ErrorMessage, AuditInfo);
                        return request;
                    }
                    #endregion

                    using (var os = osProvider.CreateObjectSpace())
                    {
                        string empCriteria = !String.IsNullOrEmpty(request.UserName) ? "Username=?" : "HRCode=?";
                        string key = !String.IsNullOrEmpty(request.UserName) ? request.UserName : request.EmpNo;

                        var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                        if (emp != null)
                        {
                            EmployeePermission perm = null;
                            perm = String.IsNullOrEmpty(request.RequestNo?.Trim()) ?
                                os.CreateObject<EmployeePermission>() ://create new request 
                                os.GetObjectByKey<EmployeePermission>(Guid.Parse(request.RequestNo));//To update existing one

                            #region Validate Request Before change data
                            if (perm == null)
                            {
                                request.ErrorMessage = string.Format("Error: Request No [{0}] Not found!!", request.RequestNo);
                                return request;
                            }
                            if (perm.Closed || perm.Status > 0)
                            {
                                request.ErrorMessage = string.Format("Error: Can't update request no [{0}] because it is closed or reply has done", request.RequestNo);
                                return request;
                            }

                            #endregion

                            perm.RequestBy = emp;
                            perm.RequestDate = DateNow;
                            perm.StartDate = request.StartDate;
                            perm.EndDate = request.EndDate;

                            perm.Type = (Module.BusinessObjects.Enum.Transaction)request.TransactionType;
                            perm.Duration = TimeSpan.FromMinutes(request.TotalMinutes);
                            perm.Permission = CreatePermission(os, request.RequestName);


                            perm.Status = (Module.BusinessObjects.Enum.RequestStatus)request.Status;//Module.BusinessObjects.Enum.RequestStatus.Accept;//accept direct from
                                                                                                    //Enum.Parse(typeof(Module.BusinessObjects.Enum.Transaction),request.TransactionType);
                            #region Validate Request Before Saving Data
                            if (perm.HasDateOverlap)
                            {
                                request.ErrorMessage = string.Format("Error: Can't save request because it has overlap in start date and end date with pervious requests,StartDate:{0},EndDate:{1}",
                                    perm.StartDate.ToLongDateString(), perm.EndDate.ToLongDateString());
                                return request;
                            }

                            #endregion
                            os.CommitChanges();

                            request.RequestNo = perm.Oid.ToString();//for future use
                            var infoMessage = string.Format("Add Employee {0} permission success., Ref:{1}", key, request.RequestNo);
                            Log.Info(infoMessage, AuditInfo);
                        }
                        else
                        {
                            var errorMsg = string.Format("Error: Employee {0} doesn't exist!!", key);
                            Log.Error(errorMsg, AuditInfo);
                            request.ErrorMessage = errorMsg;
                            return request;
                        }
                    }
                }
                catch (Exception ex)
                {
                    var errorMsg = "Error:" + ex.Message;
                    Log.Error(errorMsg, AuditInfo);
                    request.ErrorMessage = errorMsg;
                    return request;

                }
            }
            return request;//or errorMessage
        }
        internal RequestDTO GetPermissionByEmp(Employee emp, DateTime dateFrom, DateTime dateTo, IObjectSpace os)
        {
            var empLogs = new RequestDTO() { Emp = new EmployeeDTO() { EmpNo = emp.HRCode, ArabicName = emp.ArabicName, EnglishName = emp.EnglishName, UserName = emp.Username } };
            var logs = os.GetObjects<EmployeePermission>(
                CriteriaOperator.Parse("RequestBy=? and (StartDate >=? and StartDate <=?) and (EndDate >=? and EndDate <=?) ",
                emp.Oid, dateFrom, dateTo, dateFrom, dateTo),
                new List<SortProperty>() { new SortProperty("StartDate", SortingDirection.Ascending) }, false);
            if (logs != null && logs.Count > 0)
            {
                var logsLst = logs.Select(a =>
                  new RequestLogDTO()
                  {
                      RequestNo = a.Oid.ToString(),
                      RequestDate = DateNow,
                      StartDate = a.StartDate,
                      EndDate = a.EndDate,
                      RequestName = a.Permission?.EnglishName,
                      Reason = a.Reason,
                      ReplyComment = a.ReplyComment,
                      Status = (int)a.Status,
                      TotalMinutes = (int)a.TotalMinutes, //In/Out permission only
                      TransactionType = (int)a.Type //In/Out permission only
                  }).ToList();

                empLogs.Logs = logsLst;
            }
            return empLogs;
        }

        //start point for employees list
        public RequestListDTO GetEmployeesPermissions(string key, DateTime dateFrom, DateTime dateTo, FilterKeyType filterType = FilterKeyType.HrCode)
        {
            var requestLst = new RequestListDTO();
            requestLst.Requests = new List<RequestDTO>();

            using (var os = osProvider.CreateObjectSpace())
            {
                string empCriteria = "";// (string.IsNullOrEmpty(key) || key == "0") ? "" :
                if (!string.IsNullOrEmpty(key) && key != "0")
                {
                    switch (filterType)
                    {
                        case FilterKeyType.HrCode:
                            empCriteria = "HRCode=?";
                            break;
                        case FilterKeyType.Username:
                            empCriteria = "Username=?";
                            break;
                        case FilterKeyType.DeptName:
                            empCriteria = "Department.EnglishName=?";
                            break;
                        case FilterKeyType.DeptCode:
                            empCriteria = "Department.Code=?";
                            break;
                        case FilterKeyType.BusinessGroupName:
                            empCriteria = "BusinessGroup.EnglishName=?";
                            break;
                        case FilterKeyType.BusinessGroupCode:
                            empCriteria = "BusinessGroup.Code=?";
                            break;
                        default:
                            empCriteria = "";
                            break;
                    }
                }
                //filterType == FilterKeyType.DeptName ? "Department.EnglishName=?" : "";
                var employees = os.GetObjects<Employee>(CriteriaOperator.Parse(empCriteria, key));
                if (employees != null && employees.Count > 0)
                {
                    foreach (var emp in employees)
                    {
                        var request = GetPermissionByEmp(emp, dateFrom, dateTo, os);
                        requestLst.Requests.Add(request);
                    }
                }
            }
            return requestLst;
        }

        //start point for single employees
        public RequestDTO GetEmployeePermissions(string key, DateTime dateFrom, DateTime dateTo, FilterKeyType filterType = FilterKeyType.HrCode)
        {
            if (osProvider == null)
                return new RequestDTO() { ErrorMessage = string.Format("OpjectSpace Provider is null") };

            using (var os = osProvider.CreateObjectSpace())
            {
                string empCriteria = filterType == FilterKeyType.Username ? "Username=?" : "HRCode=?";
                var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                if (emp != null)
                {
                    var requests = GetPermissionByEmp(emp, dateFrom, dateTo, os);
                    return requests;
                }
                else
                {
                    return new RequestDTO() { ErrorMessage = string.Format("Error: Employee {0} doesn't exist!!", key) };
                }
            }
            //return null;
        }

        Permission CreatePermission(IObjectSpace os, string name, string nameAr = "")
        {
            Permission perm = string.IsNullOrEmpty(name) ? null :
                os.FindObject<Permission>(CriteriaOperator.Parse("Upper([EnglishName])=?", name.ToUpper()));
            if (perm == null)
            {
                perm = os.CreateObject<Permission>();
                perm.EnglishName = name;
                perm.ArabicName = nameAr ?? name;
            }
            return perm;
        }
       

       // Employee Vacations Methods--------------------------------------------------------------

  
        public RequestLogDTO AddVacationRequest(RequestLogDTO request)
        {
            using (log4net.ThreadContext.Stacks["NDC"].Push(GetType().FullName + "." + MethodBase.GetCurrentMethod().Name))
            {
                try
                {
                    #region Validate input from user
                    Log.Debug("TODO: ValidateRequestInput", AuditInfo);
                    request = ValidateRequestInput(request);
                    if (!string.IsNullOrEmpty(request.ErrorMessage))
                    {
                        Log.Warn("Validation Error: ValidateRequestInput" + request.ErrorMessage, AuditInfo);
                        return request;
                    }
                    #endregion
                    using (var os = osProvider.CreateObjectSpace())
                    {
                        Log.Debug("TODO: FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key))", AuditInfo);
                        string empCriteria = !String.IsNullOrEmpty(request.UserName) ? "Username=?" : "HRCode=?";
                        string key = !String.IsNullOrEmpty(request.UserName) ? request.UserName : request.EmpNo;

                        var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                        if (emp != null)
                        {
                            EmployeeVacation vac = null;
                            vac = String.IsNullOrEmpty(request.RequestNo?.Trim()) ?
                                  os.CreateObject<EmployeeVacation>() ://create new request 
                                  os.GetObjectByKey<EmployeeVacation>(Guid.Parse(request.RequestNo));//To update existing one

                            #region Validate Request
                            if (vac == null)
                            {
                                var errorMessage = string.Format("Error: Request No [{0}] Not found!!", request.RequestNo);
                                Log.Error(errorMessage, AuditInfo);
                                request.ErrorMessage = errorMessage;
                                return request;
                            }
                            if (vac.Closed || vac.Status > 0)
                            {
                                var errorMessage = string.Format("Error: Can't update request no [{0}] because it is closed or reply has done.", request.RequestNo);
                                Log.Error(errorMessage, AuditInfo);
                                request.ErrorMessage = errorMessage;
                                return request;
                            }
                            #endregion
                            vac.RequestBy = emp;
                            vac.RequestDate = DateNow;
                            vac.StartDate = request.StartDate;
                            vac.EndDate = request.EndDate;

                            vac.Vacation = CreateVacation(os, request.RequestName);
                            vac.Status = (Module.BusinessObjects.Enum.RequestStatus)request.Status;
                            //vac.Status = Module.BusinessObjects.Enum.RequestStatus.Accept;
                            //Enum.Parse(typeof(Module.BusinessObjects.Enum.Transaction),request.TransactionType);
                            #region Validate Request Before Saving Data
                            if (vac.HasDateOverlap)
                            {
                                var errorMessage = string.Format("Error: Can't save request because it has overlap in start date and end date with pervious requests",
                                    request.RequestNo);
                                Log.Error(errorMessage, AuditInfo);
                                request.ErrorMessage = errorMessage;
                                return request;
                            }
                            #endregion
                            os.CommitChanges();

                            request.RequestNo = vac.Oid.ToString();//for future use
                            var infoMessage = string.Format("Add Employee {0} vacation success, Ref:{1}", key, request.RequestNo);
                            Log.Info(infoMessage, AuditInfo);
                        }
                        else
                        {
                            var errorMsg = string.Format("Error: Employee {0} doesn't exist!!", key);
                            Log.Error(errorMsg, AuditInfo);
                            request.ErrorMessage = errorMsg;
                            return request;
                        }
                    }
                }
                catch (Exception ex)
                {
                    var errorMsg = "Error:" + ex.Message;
                    Log.Error(errorMsg,ex, AuditInfo);
                    request.ErrorMessage = errorMsg;
                    return request;

                }
            }
            return request;//or errorMessage
        }

        internal RequestDTO GetVacationByEmp(Employee emp, DateTime dateFrom, DateTime dateTo, IObjectSpace os)
        {
            var empLogs = new RequestDTO() { Emp = new EmployeeDTO() { EmpNo = emp.HRCode, ArabicName = emp.ArabicName, EnglishName = emp.EnglishName, UserName = emp.Username } };
            var logs = os.GetObjects<EmployeeVacation>(
                CriteriaOperator.Parse("RequestBy=? and (StartDate >=? and StartDate <=?) and (EndDate >=? and EndDate <=?) ",
                emp.Oid, dateFrom, dateTo, dateFrom, dateTo),
                new List<SortProperty>() { new SortProperty("StartDate", SortingDirection.Ascending) }, false);
            if (logs != null && logs.Count > 0)
            {
                var logsLst = logs.Select(a =>
                  new RequestLogDTO()
                  {
                      RequestNo = a.Oid.ToString(),
                      RequestDate = DateNow,
                      StartDate = a.StartDate,
                      EndDate = a.EndDate,
                      RequestName = a.Vacation?.EnglishName,
                      Reason = a.Reason,
                      ReplyComment = a.ReplyComment,
                      Status = (int)a.Status
                  }).ToList();

                empLogs.Logs = logsLst;
            }
            return empLogs;
        }

        //start point for employees list
        public RequestListDTO GetEmployeesVacations(string key, DateTime dateFrom, DateTime dateTo, FilterKeyType filterType = FilterKeyType.HrCode)
        {
            var requestLst = new RequestListDTO();
            requestLst.Requests = new List<RequestDTO>();

            using (var os = osProvider.CreateObjectSpace())
            {
                string empCriteria = "";// (string.IsNullOrEmpty(key) || key == "0") ? "" :
                if (!string.IsNullOrEmpty(key) && key != "0")
                {
                    switch (filterType)
                    {
                        case FilterKeyType.HrCode:
                            empCriteria = "HRCode=?";
                            break;
                        case FilterKeyType.Username:
                            empCriteria = "Username=?";
                            break;
                        case FilterKeyType.DeptName:
                            empCriteria = "Department.EnglishName=?";
                            break;
                        case FilterKeyType.DeptCode:
                            empCriteria = "Department.Code=?";
                            break;
                        case FilterKeyType.BusinessGroupName:
                            empCriteria = "BusinessGroup.EnglishName=?";
                            break;
                        case FilterKeyType.BusinessGroupCode:
                            empCriteria = "BusinessGroup.Code=?";
                            break;
                        default:
                            empCriteria = "";
                            break;
                    }
                }
                //filterType == FilterKeyType.DeptName ? "Department.EnglishName=?" : "";
                var employees = os.GetObjects<Employee>(CriteriaOperator.Parse(empCriteria, key));
                if (employees != null && employees.Count > 0)
                {
                    foreach (var emp in employees)
                    {
                        var request = GetVacationByEmp(emp, dateFrom, dateTo, os);
                        requestLst.Requests.Add(request);
                    }
                }
            }
            return requestLst;
        }

        //start point for single employees
        public RequestDTO GetEmployeeVacations(string key, DateTime dateFrom, DateTime dateTo, FilterKeyType filterType = FilterKeyType.HrCode)
        {
            if (osProvider == null)
                return new RequestDTO() { ErrorMessage = string.Format("OpjectSpace Provider is null") };

            using (var os = osProvider.CreateObjectSpace())
            {
                string empCriteria = filterType == FilterKeyType.Username ? "Username=?" : "HRCode=?";
                var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                if (emp != null)
                {
                    var requests = GetVacationByEmp(emp, dateFrom, dateTo, os);
                    return requests;
                }
                else
                {
                    return new RequestDTO() { ErrorMessage = string.Format("Error: Employee {0} doesn't exist!!", key) };
                }
            }
            //return null;
        }

        Vacation CreateVacation(IObjectSpace os, string name, string nameAr = "")
        {
            Vacation vac = string.IsNullOrEmpty(name) ? null :
                os.FindObject<Vacation>(CriteriaOperator.Parse("Upper([EnglishName])=?", name.ToUpper()));
            if (vac == null)
            {
                vac = os.CreateObject<Vacation>();
                vac.EnglishName = name;
                vac.ArabicName = nameAr ?? name;
            }
            return vac;
        }

        #endregion

        #region Mobile App Methods
        public string Authenticate(AuthDTO auth ,bool validateMobileAppId=false)
        {
            if (auth != null)
            {
                using (var os = osProvider.CreateObjectSpace())
                {
                    var emp=os.FindObject<Employee>(CriteriaOperator.Parse("Username=? or HRCode=?",auth.Username,auth.EmpNo));
                    if (emp != null)
                    {
                        //validate user permission when authenticate from mobile app
                        if (auth.IsMobileDevice &&! emp.UseMobileApp) 
                        {
                           return "Error:You don't have mobile app using permission";
                        }
                        if (validateMobileAppId && auth.IsMobileDevice &&
                            (string.IsNullOrEmpty(emp.MobileRegisterId) || emp.MobileRegisterId.Trim() == ""))
                        {
                            return "Error:You don't have registeration for mobile app";
                        }
                        if (validateMobileAppId && auth.IsMobileDevice &&
                           (emp.MobileRegisterId!= auth.AppId))
                        {
                            return "Error:Not allowed for muliple registeration";
                        }
                        //AD/Form Authentication 
                        if (emp.User != null)
                        {
                            //Active direcory Authentication
                            if (emp.User.AciveDirectoryIsDefault)
                            {
                                string domain = System.Configuration.ConfigurationManager.AppSettings["Domain"];//< add key = "Domain" value = "GVITT.COM" />
                                string ADServer = System.Configuration.ConfigurationManager.AppSettings["ADServer"];    //< add key = "ADServer" value = "*************" />
                                var valid = ADAuth.ValidateUser(emp.Username, auth.Password, domain, ADServer);
                                if (valid)
                                {
                                    return "1";
                                }
                                else
                                {
                                    return "AD Error: Invalid Authentication";
                                }
                            }
                            else
                            { //  Form Authentication
                                if (auth.ExternalAuth)
                                    return "1";

                                if (emp.User.ComparePassword(auth.Password))
                                {
                                    return "1";
                                }
                                else
                                {
                                   return "App Error: Invalid Authentication";
                                }
                                
                            }

                         
                        }
                        else
                        {
                            return "Error:Employee doesn't have username";
                        }
                    }
                    else
                    {
                        return "Error:Employee doesn't exist";
                    }
               
                }



                }
            return "0";
        }

        public string RegisterApp(AuthDTO auth)
        {
            var validAuth = Authenticate(auth);//validate login and allow mobile app permission
            if (validAuth == "1")
            {
                using (var os = osProvider.CreateObjectSpace())
                {
                    var emp = os.FindObject<Employee>(CriteriaOperator.Parse("Username=? or HRCode=?", auth.Username, auth.EmpNo));
                    if (emp != null)
                    {
                        if (string.IsNullOrEmpty(emp.MobileRegisterId) ||emp.MobileRegisterId.Trim()=="")
                        {
                            emp.MobileRegisterId = auth.AppId;
                            try
                            {
                                os.CommitChanges();
                                return "1";
                            }
                            catch (Exception ex)
                            {
                                return ex.Message;
                            }
                            
                        }
                        else
                        {
                           return "Error:this app already register!! please contact administrator";
                        }

                           
                    }
                }
            }
            else
                return validAuth;//return error

            return "0";
        }

        public EmployeeDTO MAuthenticate(AuthDTO data)
        {
            string action = "01";// login or register
            var result = new EmployeeDTO();
            var isAuthenticated = false;
            using (log4net.ThreadContext.Stacks["NDC"].Push(GetType().FullName + "." + MethodBase.GetCurrentMethod().Name))
            {
                try
                {

                    if (data != null && !string.IsNullOrEmpty(data.AppId))
                    {
                        using (var os = osProvider.CreateObjectSpace())
                        {
                             action = "02";
                            var emp = os.FindObject<Employee>(CriteriaOperator.Parse("Username=? or HRCode=?", data.Username, data.EmpNo));
                            if (emp != null)
                            {
                                action = "03";
                                //validate user permission when authenticate from mobile app
                                if (data.IsMobileDevice && !emp.UseMobileApp)
                                {
                                    action = "04";
                                    Log.Error("This user does not have permission to access mobile app, code:103,user:" + data.Username, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                    return new EmployeeDTO { ResponseCode = "103", ResponseMessage = "This user does not have permission to access mobile app!!" };

                                }
                                if (emp.User != null)
                                {
                                    action = "05";
                                    //Active direcory Authentication
                                    if (emp.User.AciveDirectoryIsDefault)
                                    {
                                        action = "06";
                                        string domain = System.Configuration.ConfigurationManager.AppSettings["Domain"];//< add key = "Domain" value = "GVITT.COM" />
                                        string ADServer = System.Configuration.ConfigurationManager.AppSettings["ADServer"];    //< add key = "ADServer" value = "*************" />
                                        isAuthenticated = ADAuth.ValidateUser(emp.Username, data.Password, domain, ADServer);
                                        Log.Info("Success AD Authentication ,user:" + emp.Username, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });

                                    }
                                    else
                                    { //  Form Authentication
                                       
                                        if (data.ExternalAuth)
                                        {
                                            action = "07";
                                            isAuthenticated =true;
                                            Log.Info("Refer authentication to external service,user:" + emp.Username, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                        }
                                        else if (emp.User.ComparePassword(data.Password))
                                        {
                                            action = "08";
                                            isAuthenticated = true;
                                            Log.Info("Success Form Authentication ,user:" + emp.Username, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                        }
                                        else
                                        {
                                            action = "09";
                                            Log.Warn("Invalid Authentication Type ,user:" + emp.Username, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                        }

                                    }
                                }

                            }

                            if (data.AuthType == 2) // username only // for test 
                            {
                                isAuthenticated = true; // delay to next step
                                Log.Debug("Bypass Authentication ,test user:" + emp.Username, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                            }

                            if (isAuthenticated)
                            {
                                if (emp != null)
                                {
                                    // success fta user -- refer to register
                                    if (string.IsNullOrEmpty(emp.MobileRegisterId))
                                    {
                                        //check if appid exist
                                        var userAppId = os.FindObject<Employee>(CriteriaOperator.Parse("MobileRegisterId=?", data.AppId));
                                        //var userAppId = new MobileEmployee(appId: data.AppId);
                                        if (userAppId != null)
                                        {
                                            Log.Error("This device already registered,appID:" + data.AppId, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });

                                            return new EmployeeDTO { ResponseCode = "109", ResponseMessage = "This device already registered for another user,please contact administrator!!" };
                                        }
                                        // todo: register & login
                                        action = "0"; //register
                                        emp.MobileRegisterDate = Constants.DateNow;
                                        emp.MobileLastLoginDate = Constants.DateNow;
                                        emp.MobileRegisterId = data.AppId;
                                        emp.Username = string.IsNullOrEmpty(emp.Username) ? data.Username : emp.Username;
                                        emp.MobileToken = TokenManager.GenerateToken(emp.Username);// Guid.NewGuid().ToString();

                                        // generate toke then login success
                                        //user.Update();
                                        os.CommitChanges();
                                        Log.Info("Success  Register ,Token:" + emp.MobileToken, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });

                                        return MapEmpToDto(emp);

                                    }
                                    else
                                    {
                                        // success fta user -- refer to login
                                        action = "1"; // login
                                                      // check MobileApp is Correct
                                        if (emp.MobileRegisterId == data.AppId)
                                        {
                                            // success login --> return token
                                            emp.MobileLastLoginDate = Constants.DateNow;
                                            emp.MobileToken = TokenManager.GenerateToken(emp.Username);//Guid.NewGuid().ToString();
                                            //user.Update();
                                            os.CommitChanges();
                                            Log.Info("Success  Login ,Token:" + emp.MobileToken, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });

                                            return MapEmpToDto(emp);
                                        }
                                        else
                                        {
                                            Log.Error("This user already registered, code:110,user:" + data.Username, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                            return new EmployeeDTO { ResponseCode = "110", ResponseMessage = "This user already registered,please contact administrator!!" };

                                        }
                                    }
                                }
                                else
                                {
                                    Log.Error("user not registered ,code:111", new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                    return new EmployeeDTO { ResponseCode = "111", ResponseMessage = "This user not registered in time attendance system" };
                                }
                            }
                            else
                            {
                                Log.Error("login failed ,code:102", new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                return new EmployeeDTO { ResponseCode = "102", ResponseMessage = "Login failed !!" };

                            }
                        }    
                    }
                    else
                    {
                        Log.Error("Input data not found ,code:100", new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                        return new EmployeeDTO { ResponseCode = "100", ResponseMessage = "Input data not found" };

                    }

                }
                catch (Exception ex)
                {
                    Log.Error("login falied,code:101", ex, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                    return new EmployeeDTO { ResponseCode = "101", ResponseMessage = "login failed ,tag:" + action };

                }

                return result;
            }
        }

        public EmployeeDTO MapEmpToDto(Employee emp)
        {
            return new EmployeeDTO()
            {
                AppId = emp.MobileRegisterId,
                Token = emp.MobileToken,
                Username = emp.Username,
                EmpNo = emp.HRCode,
                ArabicName = emp.ArabicName,
                EnglishName = emp.EnglishName,
                DeptArabicName = emp.Department?.ArabicName,
                DeptEnglishName = emp.Department?.EnglishName,
                BGArabicName = emp.BusinessGroup?.EnglishName,
                BGEnglishName = emp.BusinessGroup?.ArabicName,
                Email = string.IsNullOrEmpty(emp.Email)? emp.Username : emp.Email,
                AppVersion = System.Configuration.ConfigurationManager.AppSettings["AppVersion"] // todo add appversion
            };
        }

        public EmployeeDTO MGetEmployee(AuthDTO data)
        {
            var tag = "01";
            var result = new EmployeeDTO();
            using (log4net.ThreadContext.Stacks["NDC"].Push(GetType().FullName + "." + MethodBase.GetCurrentMethod().Name))
            {
                try
                {
                    if (data != null && !string.IsNullOrEmpty(data.Username))
                    {
                         tag = "02";
                        using (var os = osProvider.CreateObjectSpace())
                        {
                             tag = "03";
                            var user = os.FindObject<Employee>(CriteriaOperator.Parse("Username=? or HRCode=?", data.Username, data.EmpNo));
                            if (user != null)
                            {
                                tag = "04";
                                return MapEmpToDto(user);
                            }
                            else
                            {
                                tag = "05";
                                Log.Error("user not registered ,code:111", new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                                return new EmployeeDTO { ResponseCode = "111", ResponseMessage = "This user not registered in time attendance system" };
                            }
                        }

                    }
                    else
                    {
                        tag = "06";
                        Log.Error("Input data not found, code:100,EmpNo:" + data.EmpNo, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });

                        return new EmployeeDTO { ResponseCode = "100", ResponseMessage = "Input data not found" };
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("101 unhandeled exception", ex, new LP.Logger.AuditInfo() { UserName = data.Username, AppVersion = data.AppVersion, DeviceName = data.AppId });
                    return new EmployeeDTO { ResponseCode = "101", ResponseMessage = "error ,tag:" + tag };

                }
                return result;
            }
        }

        public MobileAttendanceDTO MGetAtt(string username, string from, string to,string returnData="-1")
        {
            var tag = "01";
            returnData = string.IsNullOrEmpty(returnData) ? "-1" : returnData;
            MobileAttendanceDTO result = new MobileAttendanceDTO();
            using (log4net.ThreadContext.Stacks["NDC"].Push(GetType().FullName + "." + MethodBase.GetCurrentMethod().Name))
            {
                try
                {
                    if (!string.IsNullOrEmpty(username))
                    {
                        using (var os = osProvider.CreateObjectSpace())
                        {
                            tag = "02";
                            var emp = os.FindObject<Employee>(CriteriaOperator.Parse("Username=?", username));
                            if (emp != null)
                            {
                                tag = "03";
                                var dtFrom = DateTime.ParseExact(from, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);
                                var dtTo = DateTime.ParseExact(to, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

                                tag = "04";


                                //returnData "-1" =all[att,permissions,vaction] ,"0" =attendance,"1" =permission,"2"=vacations
                                if (returnData == "-1" || returnData == "0")
                                {
                                    tag = "05";
                                    var attendace = os.GetObjects<Attendance>(CriteriaOperator.Parse("Employee=? and Date >=? and Date <=?", emp.Oid, dtFrom, dtTo));
                                    result.Attendance = MapAttendance(attendace);
                                    result.Summary = MapResultSummary(result);
                                    tag = "06";
                                }

                                if (returnData == "-1" || returnData == "1")
                                {
                                    tag = "07";
                                    var permisions = os.GetObjects<EmployeePermission>(CriteriaOperator.Parse("RequestBy=? and (StartDate >=? and StartDate <=?) and (EndDate >=? and EndDate <=?)", emp.Oid, dtFrom, dtTo, dtFrom, dtTo));
                                    result.Permissions = MapPermissions(permisions);
                                    tag = "08";
                                }

                                if (returnData == "-1" || returnData == "2")
                                {
                                    tag = "09";
                                    var vacations = os.GetObjects<EmployeeVacation>(CriteriaOperator.Parse("RequestBy=? and (StartDate >=? and StartDate <=?) and (EndDate >=? and EndDate <=?)", emp.Oid, dtFrom, dtTo, dtFrom, dtTo));
                                    result.Vacations = MapVacations(vacations);
                                    tag = "09";
                                }

                               
                                
                                //result = new MobileAttendance().GetAttendanceDetails(0, 0, 0, 0, 0, emp.ID, 0, dtFrom, dtTo, "-1");
                            }
                            else
                            {
                                Log.Error("user not registered ,code:111", new LP.Logger.AuditInfo() { UserName = username });
                                return new MobileAttendanceDTO { ResponseCode = "111", ResponseMessage = "This user not registered in time attendance system" };
                            }
                        }
                    }
                    else
                    {
                         Log.Error("Input data not found, code:100,username:" + username, new LP.Logger.AuditInfo() { UserName = username, AppVersion = username, DeviceName = username });

                        return new MobileAttendanceDTO { ResponseCode = "100", ResponseMessage = "Input data not found" };
                    }
                }
                catch (FormatException ex)
                {
                    Log.Error("150 date format exception", ex, new LP.Logger.AuditInfo() { UserName = username, AppVersion = username, DeviceName = username });
                    return new MobileAttendanceDTO { ResponseCode = "150", ResponseMessage = "error ,tag:" + tag };
                }
                catch (Exception ex)
                {
                     Log.Error("101 unhandeled exception", ex, new LP.Logger.AuditInfo() {UserName = username, AppVersion = username, DeviceName = username });
                    return new MobileAttendanceDTO { ResponseCode = "101", ResponseMessage = "error ,tag:" + ex.Message };
                }
                return result;
            }

        }

        public MobileAttendanceDTO MGetEmployeeAttendance(PrmAttendance data)
        {
            // "-1" =all[att,permissions,vaction] ,"0" =attendance,"1" =permission,"2"=vacations
            return this.MGetAtt(data.Username,data.DateFrom,data.DateTo,data.Action);
        }
        public List<MobileEmployeePermission> MapPermissions(IList< EmployeePermission> list)
        {
            var result = new List<MobileEmployeePermission>();

            if (list != null &&  list.Count > 0)
            {
                foreach (var item in list)
                {
                    MobileEmployeePermission obj = new MobileEmployeePermission()
                    {
                        ID=item.Oid.ToString(),
                        ResponseDate = item.RequestDate,
                        EmployeeID = item.RequestBy.Oid.ToString(),
                        StartDate = item.StartDate,
                        EndDate = item.EndDate,
                        TotalMinutes = (int)item.TotalMinutes,
                        Reason = item.Reason,
                        Status = (byte)item.Status,
                        Type = item.Type == Module.BusinessObjects.Enum.Transaction.In ?
                        PermissionType.DayBegin : PermissionType.DayEnd,
                        PermissionID=item.Permission.Oid.ToString(),
                        ResponseBy= item.RequestBy.Oid.ToString(),
                        PermissionNameAr =item.Permission?.ArabicName,
                        PermissionNameEn = item.Permission?.EnglishName
                    };
                }
            }

            return result;

        }
        public List<MobileEmployeeVacation> MapVacations(IList<EmployeeVacation> list)
        {
            var result = new List<MobileEmployeeVacation>();

            if (list != null && list.Count > 0)
            {
                foreach (var item in list)
                {
                    var obj = new MobileEmployeeVacation()
                    {
                        ID = item.Oid.ToString(),
                        ResponseDate = item.RequestDate,
                        EmployeeID = item.RequestBy.Oid.ToString(),
                        StartDate = item.StartDate,
                        EndDate = item.EndDate,
                       
                        Reason = item.Reason,
                        Status = (byte)item.Status,
                        
                        VacationID = item.Vacation.Oid.ToString(),
                        ResponseBy = item.RequestBy.Oid.ToString(),
                        VacationNameAr = item.Vacation?.ArabicName,
                        VacationNameEn = item.Vacation?.EnglishName
                    };
                    result.Add(obj);
                }
            }

            return result;

        }
        public List<MobileAttendanceDayDTO> MapAttendance(IList<Attendance> list)
        {
            var result = new List<MobileAttendanceDayDTO>();

            if (list != null && list.Count > 0)
            {
                foreach (var item in list)
                {
                    var obj = new MobileAttendanceDayDTO()
                    {
                        Date = item.Date,
                        CheckInTime = item.CheckInTime == null ? (DateTime?)null : item.Date.Add(item.CheckInTime.Value),
                        CheckOutTime = item.CheckOutTime == null ? (DateTime?)null : item.Date.Add(item.CheckOutTime.Value),
                        DelayMinutes = (int)item.Delay.TotalMinutes,
                        ShortageMinutes = (int)item.Shortage.TotalMinutes,
                        DayLength = (int)item.RequiredTime.TotalMinutes,
                        PermissionMinutes = (int)item.Permission.TotalMinutes,
                        WorkingMinutes = (int)item.WorkingTime.TotalMinutes,
                        OvertimeMinutes = (int)item.Overtime.TotalMinutes,
                        DifferenceMinutes = (int)item.DiffWorkingTime.TotalMinutes,

                        Status = item.DayOff ? 1 : item.IsAbsent ? 2 : item.IsVacation ? 3 : 0,// 0 = normal, 1 = dayOff ,2 =absent, 3=vacation=


                        DepartmentNameAr = item.DeptArabicName,
                        DepartmentNameEn = item.DeptEnglishName,

                        ShiftNameAr = item.ShiftHistory?.Shift?.ArabicName,
                        ShiftNameEn = item.ShiftHistory?.Shift?.EnglishName,
                        


                        NoteAr = item.Comment,
                        NoteEn = item.Comment,
                        EmployeeID = item.Employee.Oid.ToString()

                    };
                    result.Add(obj);
                }
            }

            return result;

        }

        public ATTSummary MapResultSummary(MobileAttendanceDTO result)
        {
            var totalDelay = result.Attendance.Sum(a => a.DelayMinutes);
            var totalShortage = result.Attendance.Sum(a => a.ShortageMinutes);
            var totalDelayShortage = totalDelay + totalShortage;
            var totalOvertime = result.Attendance.Sum(a => a.OvertimeMinutes);
            var totalWorking = result.Attendance.Sum(a => a.WorkingMinutes);
            var totalPerm = result.Attendance.Sum(a => a.WorkingMinutes);

            var totalDelayString = Math.Abs(totalDelay / 60).ToString().PadLeft(2, '0') + ':' + (totalDelay - (Math.Abs(totalDelay / 60) * 60)).ToString().PadLeft(2, '0');
            var totalShortageString = Math.Abs(totalShortage / 60).ToString().PadLeft(2, '0') + ':' + (totalShortage - (Math.Abs(totalShortage / 60) * 60)).ToString().PadLeft(2, '0');
            var totalDelayShortageString = Math.Abs(totalDelayShortage / 60).ToString().PadLeft(2, '0') + ':' + (totalDelayShortage - (Math.Abs(totalDelayShortage / 60) * 60)).ToString().PadLeft(2, '0');
            var totalOvertimeString = Math.Abs(totalOvertime / 60).ToString().PadLeft(2, '0') + ':' + (totalOvertime - (Math.Abs(totalOvertime / 60) * 60)).ToString().PadLeft(2, '0');
            var totalWorkingString = Math.Abs(totalWorking / 60).ToString().PadLeft(2, '0') + ':' + (totalWorking - (Math.Abs(totalWorking / 60) * 60)).ToString().PadLeft(2, '0');
            var totalPermString = Math.Abs(totalPerm / 60).ToString().PadLeft(2, '0') + ':' + (totalPerm - (Math.Abs(totalPerm / 60) * 60)).ToString().PadLeft(2, '0');

            // 0 = normal, 1 = dayOff ,2 =absent, 3=vacation=
            var summary= new ATTSummary()
            {
                TotalAbsent = result.Attendance.Count(a => a.Status == 2),
                TotalVacation = result.Attendance.Count(a => a.Status == 3),
                TotalDelayMinutes = totalDelay,
                TotalShortageMinutes = totalShortage,
                TotalDelayAndShortageMinutes = totalDelayShortage,
                TotalOvertimeMinutes = totalOvertime,
                TotalPermissionMinutes=totalPerm,
                TotalWorkingMinutes=totalWorking,

                TotalDelayMinutesString=totalDelayString,
                TotalShortageMinutesString = totalShortageString,
                TotalDelayAndShortageMinutesString = totalDelayShortageString,
                TotalOvertimeMinutesString = totalOvertimeString,
                TotalPermissionMinutesString = totalPermString,
                TotalWorkingMinutesString=totalWorkingString,
            };

            return summary;
        }
        //-- hepler
        public bool IsTokenValid(BaseObj auth, Employee emp)
        {
            var action = "";
            var result = false;
            using (log4net.ThreadContext.Stacks["NDC"].Push(GetType().FullName + "." + MethodBase.GetCurrentMethod().Name))
            {
                try
                {
                    if (emp != null)
                    {
                        if (string.IsNullOrEmpty(auth.Token))
                            return false;

                        // success fta user
                        return ValidateJWTToken(auth.Token, emp.Username);
                        //if (string.IsNullOrEmpty(auth.Token))
                        //    return false;
                        //else if(auth.Token == emp.MobileToken)
                        //{
                        //    return true;
                        //}
                    }
                    else
                    {
                        Log.Error("user not registered ,code:111", new LP.Logger.AuditInfo() { UserName = auth.Username, AppName = auth.AppVersion, DeviceName = auth.AppId });
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("unhandeled exception,code: 101", ex, new LP.Logger.AuditInfo() { UserName = auth.Username, AppName = auth.AppVersion, DeviceName = auth.AppId });
                    return false;

                }
                return result;
            }
        }
        public bool IsAppIdValid(BaseObj auth, Employee emp)
        {
            var action = "";
            var result = false;
            using (log4net.ThreadContext.Stacks["NDC"].Push(GetType().FullName + "." + MethodBase.GetCurrentMethod().Name))
            {
                try
                {
                    if (emp != null)
                    {
                        // success fta user
                        if (string.IsNullOrEmpty(auth.Token) || string.IsNullOrEmpty(auth.AppId) 
                            || string.IsNullOrEmpty(emp.MobileRegisterId))
                            return false;
                       
                        else if (auth.AppId == emp.MobileRegisterId)
                        {
                            return true;
                        }
                    }
                    else
                    {
                        Log.Error("user not registered ,code:111", new LP.Logger.AuditInfo() { UserName = auth.Username, AppName = auth.AppVersion, DeviceName = auth.AppId });
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("unhandeled exception,code: 101", ex, new LP.Logger.AuditInfo() { UserName = auth.Username, AppName = auth.AppVersion, DeviceName = auth.AppId });
                    return false;

                }
                return result;
            }
        }

        //for test token only
        public string ValidateToken(string token, string username)
        {
            string tokenUsername = TokenManager.ValidateToken(token);
            if (username.Equals(tokenUsername))
                return "OK";
            else
            {
                return "Token Not Valid";
            }

        }

        public bool ValidateJWTToken(string token, string username)
        {
            string tokenUsername = TokenManager.ValidateToken(token);
            if (username.Equals(tokenUsername))
                return true;
            else
            {
                return false;
            }

        }
        #endregion 

        //====TEST Area
        #region Test Device Management
        List<EnrollUserInfo> GenerateEnrollUsersInfo(IList<DeviceAction> actions)
        {
            List<EnrollUserInfo> userLst = new List<EnrollUserInfo>();
            foreach (var action in actions)
            {
                var enrollInfo = _ObjectSpace.FindObject<EmployeeEnrollment>(CriteriaOperator.Parse("Employee=?", action.Employee));
                if (enrollInfo != null)
                {
                    EnrollUserInfo user = new EnrollUserInfo()
                    {

                        DeviceID = action.DeviceID,
                        UserName = action.Employee.Username ?? string.Empty,
                        UserID = action.EmployeeCode,
                        Cards = new List<CardInfo>(),//assign later
                        Fingerprints = new List<FingerprintInfo>()//assign later

                    };
                    if (enrollInfo.Card != null && enrollInfo.Card.Length > 0)
                    {
                        user.Cards.Add(new CardInfo { Data = enrollInfo.Card, SDKVersion = enrollInfo.SDKVersion });
                    }
                    if (enrollInfo.Fingerprint1 != null && enrollInfo.Fingerprint1.Length > 0)
                    {
                        user.Fingerprints.Add(new FingerprintInfo { Data = enrollInfo.Fingerprint1, SDKVersion = enrollInfo.SDKVersion });
                    }
                    if (enrollInfo.Fingerprint2 != null && enrollInfo.Fingerprint2.Length > 0)
                    {
                        user.Fingerprints.Add(new FingerprintInfo { Data = enrollInfo.Fingerprint2, SDKVersion = enrollInfo.SDKVersion });
                    }

                    userLst.Add(user);
                }
                else
                {
                    Tracing.Tracer.LogText("Employee Enrollment not found OID:" + action.Employee);
                }

            }
            return userLst;
        }

        List<string> GenerateDeletedUserIDs(IList<DeviceAction> actions)
        {
            List<string> userLst = new List<string>();
            foreach (var action in actions)
            {
                userLst.Add(action.EmployeeCode);
            }
            return userLst;
        }

        void UpdateDeviceActionsStatus(IList<DeviceAction> actions)
        {
            foreach (var action in actions)
            {
                action.ActionStatus = DeviceActionStatus.Executed;
                action.ExecutedDate = DateNow;
            }
            _ObjectSpace.CommitChanges();//Save
        }

        void ExecuteEnrollmentUsers(Device enrollDevice, IList<DeviceAction> actions)
        {

            DeviceManagement.DeviceAPI_V2_5 api = new DeviceManagement.DeviceAPI_V2_5();
            api.RegisterSDK();
            {
                var users = GenerateEnrollUsersInfo(actions);
                DeviceInfo device = new DeviceInfo() { ID = enrollDevice.DeviceID, IP = enrollDevice.IPAddress, Port = enrollDevice.Port };
                try
                {
                    var resultInfo = api.EnrollAllUsers(device, users);
                    if (string.IsNullOrEmpty(resultInfo.ErrorMessage))
                    {
                        //Success Enroll
                        enrollDevice.LastAction = "Enroll All Users," + DateNow;
                        UpdateDeviceActionsStatus(actions);
                    }

                    else
                    {
                        enrollDevice.LastAction = resultInfo.ErrorMessage + "," + resultInfo.InfoMessage;
                        _ObjectSpace.CommitChanges();
                    }

                }
                catch (Exception ex)
                {
                    //Comment = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
                    // HasError = true;
                }

            }
        }

        void ExecuteDeleteUsers(Device enrollDevice, IList<DeviceAction> actions)
        {

            DeviceManagement.DeviceAPI_V2_5 api = new DeviceManagement.DeviceAPI_V2_5();
            api.RegisterSDK();
            {
                var users = GenerateDeletedUserIDs(actions);
                DeviceInfo device = new DeviceInfo() { ID = enrollDevice.DeviceID, IP = enrollDevice.IPAddress, Port = enrollDevice.Port };
                try
                {
                    var resultInfo = api.DeleteAllUsers(device, users);
                    if (string.IsNullOrEmpty(resultInfo.ErrorMessage))
                    {
                        //Success delete
                        enrollDevice.LastAction = "Delete All Users," + DateNow;
                        UpdateDeviceActionsStatus(actions);
                    }

                    else
                    {
                        enrollDevice.LastAction = resultInfo.ErrorMessage + "," + resultInfo.InfoMessage;
                        _ObjectSpace.CommitChanges();
                    }
                    //var enrollData = Session.FindObject<EmployeeEnrollment>(CriteriaOperator.Parse("Employee=?", Employee.Oid));
                    //if (enrollData != null)
                    //{
                    //    DeviceUserInfo resultInfo = new DeviceUserInfo();//

                    //    if (ActionType == DeviceActionType.Add)
                    //    {
                    //        List<FingerprintInfo> lst = new List<FingerprintInfo>();
                    //        if (enrollData.Fingerprint1 != null && enrollData.Fingerprint1.Length > 0)
                    //        {
                    //            lst.Add(new FingerprintInfo() { Data = enrollData.Fingerprint1 });

                    //        }
                    //        if (enrollData.Fingerprint2 != null && enrollData.Fingerprint2.Length > 0)
                    //        {
                    //            lst.Add(new FingerprintInfo() { Data = enrollData.Fingerprint2 });
                    //        }

                    //        List<CardInfo> cards = new List<CardInfo>();
                    //        if (enrollData.Card != null && enrollData.Card.Length > 0)
                    //        {
                    //            cards.Add(new CardInfo() { Data = enrollData.Card });
                    //        }
                    //        //Device.Lock = true;//
                    //        // Session.CommitTransaction();//
                    //        string userName = Employee.Username ?? "";
                    //        device.SDKVersion = enrollData.SDKVersion;//For backword compaitability
                    //        resultInfo = service.EnrollUser(device, EmployeeCode, userName, "", lst, cards);
                    //    }

                    //    else if (ActionType == DeviceActionType.Remove)
                    //    {
                    //        resultInfo = service.DeleteUser(device, EmployeeCode);
                    //    }

                    //    if (string.IsNullOrEmpty(resultInfo.ErrorMessage))
                    //    {
                    //        ActionStatus = DeviceActionStatus.Executed;
                    //        ExecutedDate = DateTime.Now;
                    //        HasError = false;
                    //        Comment = "";
                    //    }

                    //    else
                    //    {
                    //        Comment = resultInfo.ErrorMessage + "," + resultInfo.InfoMessage;
                    //        HasError = true;
                    //    }

                    //    //Session.CommitTransaction();
                    //}
                }
                catch (Exception ex)
                {
                    //Comment = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
                    // HasError = true;
                }

            }
        }

        public void TestEnrollAllUser()
        {
            _ObjectSpace.Refresh();
            var os = _ObjectSpace;
            var devices = os.GetObjects<Device>(CriteriaOperator.Parse("Status='Online'"));
            foreach (var device in devices)
            {
                var addActions = os.GetObjects<DeviceAction>(CriteriaOperator.Parse("ActionType='Add' and ActionStatus='Wating' and Device=?", device.Oid));
                if (addActions != null && addActions.Count > 0)
                {
                    Tracing.Tracer.LogText(string.Format("Execute Enrollment actions {0},{1}", addActions.Count(), DateNow));
                    ExecuteEnrollmentUsers(device, addActions);
                    Tracing.Tracer.LogText(string.Format("Finished Enrollment  {0}", DateNow));
                }
                var removeActions = os.GetObjects<DeviceAction>(CriteriaOperator.Parse("ActionType='Remove' and ActionStatus='Wating' and Device=?", device.Oid));
                if (removeActions != null && removeActions.Count > 0)
                {
                    Tracing.Tracer.LogText(string.Format("Execute delete actions {0},{1}", removeActions.Count(), DateNow));
                    ExecuteDeleteUsers(device, removeActions);
                    Tracing.Tracer.LogText(string.Format("Finished Delete all users  {0}", DateNow));
                }
            }

        }
    }
    #endregion

    public enum FilterKeyType
    {
        Non = -1,
        HrCode = 0,
        Username = 1,
        DeptName = 2,
        DeptCode = 3,//TODO:Implemented
        BusinessGroupName = 4,
        BusinessGroupCode = 5///TODO:Implemented
    }

}
//DTOs=============================================


[DataContract]
public class BaseResponse
{
    [DataMember]
    public String ResponseCode { get; set; } = "1";

    [DataMember]
    public String ResponseMessage { get; set; } = "OK";
}

[DataContract]
public class BaseObj : BaseResponse
{
    [DataMember]
    public String AppId { get; set; }

    [DataMember]
    public String Token { get; set; }

    [DataMember]
    public String AppVersion { get; set; }

    [DataMember]
    public String Username { get; set; }

    [DataMember]
    public String EmpNo { get; set; }

    [DataMember]
    public String HRCode { get; set; }

    [DataMember]
    public string DeviceInfo { get; set; }

}

public static class Constants
{
    public static string DATE_DISPLAY_FORMAT = "dd/MM/yyyy HH:mm:ss";
    public static string DATE_SORT_FORMAT = "yyyyMMddHHmmss";
    public static string DATE_FORMAT = "yyyyMMdd";
    public static string DATE_TIME_FORMAT = "yyyyMMddHHmmss";

    public static DateTime DateNow { get { return DateTime.UtcNow.AddHours(3); } }
}
//public static class Constants
//{
//    public static string DATE_FORMAT = "dd/MM/yyyy HH:mm:ss";

//    public static DateTime DateNow { get { return DateTime.UtcNow.AddHours(3); } }
//}
#region Employees
[DataContract]
public class EmployeeListDTO
{
    [DataMember]
    public List<EmployeeDTO> Employees { get; set; }
}
[DataContract]
public class EmployeeDTO:BaseObj
{

    [DataMember]
    public string UserName { get; set; }

    [DataMember]
    public string EnglishName { get; set; }
    [DataMember]
    public string ArabicName { get; set; }

    [DataMember]
    public string Email { get; set; }

    [DataMember]
    public string PhoneNo { get; set; }

    [DataMember]
    public string DeptArabicName { get; set; }
    [DataMember]
    public string DeptEnglishName { get; set; }

    [DataMember]
    public string BGArabicName { get; set; }//Business Group
    [DataMember]
    public string BGEnglishName { get; set; }//Business Group

    [DataMember]
    public string AreaArabicName { get; set; }
    [DataMember]
    public string AreaEnglishName { get; set; }

    [DataMember]
    public string BranchArabicName { get; set; }
    [DataMember]
    public string BranchEnglishName { get; set; }

    [DataMember]
    public string ErrorMessage { get; set; }

}
#endregion

#region Simple Attendance DTOs
[DataContract]
public class AttendanceSimpleLogDTO
{
    [DataMember]
    public DateTime Date { get; set; }

    [DataMember]
    public String InTime { get; set; } //HH:mm

    [DataMember]
    public String OutTime { get; set; } ////HH:mm
}

[DataContract]
public class AttendanceSimpleDTO
{
    [DataMember]
    public EmployeeDTO Emp { get; set; }

    [DataMember]
    public List<AttendanceSimpleLogDTO> Logs { get; set; }
}

[DataContract]
public class AttendanceSimpleListDTO
{
    [DataMember]
    public List<AttendanceSimpleDTO> AttList { get; set; }
}
#endregion

#region Mobile Attendance
[DataContract]
public class MobileAttendanceDTO
{
    [DataMember]
    public List<MobileAttendanceDayDTO> Attendance { get; set; }
    [DataMember]
    public List<MobileEmployeePermission> Permissions { get; set; }
    [DataMember]
    public List<MobileEmployeeVacation> Vacations { get; set; }
    [DataMember]
    public ATTSummary Summary { get; set; }

    [DataMember]
    public string ResponseCode { get; set; } = "1";

    [DataMember]
    public string ResponseMessage { get; set; } = "OK";
    [DataMember]
    public string DeviceInfo { get; set; }

}
[DataContract]
public class ATTSummary
{


    [DataMember]
    public int TotalDelayMinutes { get; set; }

    [DataMember]
    public string TotalDelayMinutesString { get; set; }

    [DataMember]
    public int TotalShortageMinutes { get; set; }
    [DataMember]
    public string TotalShortageMinutesString { get; set; }

    [DataMember]
    public int TotalDelayAndShortageMinutes { get; set; }
    [DataMember]
    public string TotalDelayAndShortageMinutesString { get; set; }

    [DataMember]
    public int TotalOvertimeMinutes { get; set; }

    [DataMember]
    public string TotalOvertimeMinutesString { get; set; }

    [DataMember]
    public int TotalWorkingMinutes { get; set; }

    [DataMember]
    public string TotalWorkingMinutesString { get; set; }

    [DataMember]
    public int TotalPermissionMinutes { get; set; }
    [DataMember]
    public string TotalPermissionMinutesString { get; set; }

    [DataMember]
    public int TotalVacation { get; set; }

    [DataMember]
    public int TotalAbsent { get; set; }

}

[DataContract]
public class MobileAttendanceDayDTO
{
    [DataMember]
    public string DepartmentNameEn { get; set; }

    [DataMember]
    public string DepartmentNameAr { get; set; }
    [DataMember]
    public string EmployeeID { get; set; }

 
    [DataMember]
    public string ShiftNameEn { get; set; }

    [DataMember]
    public string ShiftNameAr { get; set; }

 
    [DataMember]
    public string DateString { get; set; }
    public DateTime Date { get; set; }

    public DateTime? CheckInTime { get; set; }
    [DataMember]
    public string CheckInTimeString { get; set; }

    public DateTime? CheckOutTime { get; set; }

    [DataMember]
    public string CheckOutTimeString { get; set; }
    [DataMember]
    public int DayLength { get; set; }
    [DataMember]
    public int WorkingMinutes { get; set; }
    [DataMember]
    public int DifferenceMinutes { get; set; }
    [DataMember]
    public int DelayMinutes { get; set; }
    [DataMember]
    public int ShortageMinutes { get; set; }
    [DataMember]
    public int OvertimeMinutes { get; set; }
    [DataMember]
    public int PermissionMinutes { get; set; }
    [DataMember]
    public string NoteAr { get; set; }
    [DataMember]
    public string NoteEn { get; set; }
    [DataMember]
    public int Status { get; set; } // 0 = normal, 1 = dayOff ,2 =absent, 3=vacation


    [OnSerializing]
    void OnSerializing(StreamingContext context)
    {
        //this.RequestDateString = this.RequestDate.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

        //try
        // {
        this.CheckInTimeString = this.CheckInTime?.ToString(Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);
        this.CheckOutTimeString = this.CheckOutTime?.ToString(Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);
        this.DateString = this.Date.ToString(Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);
        //}
        //catch (Exception ex)
        //{
        //   // this.ResponseMessage = "Error:StartDateString or EndDateString has incorrect format. ";
        //}
    }

    [OnDeserialized]
    void OnDeserializing(StreamingContext context)
    {
        //try
        //{
        // this.RequestDate = string.IsNullOrEmpty(this.RequestDateString) ? default(DateTime) :
        //    DateTime.ParseExact(this.RequestDateString, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

        this.CheckInTime = string.IsNullOrEmpty(this.CheckInTimeString) ? default(DateTime) :
           DateTime.ParseExact(this.CheckInTimeString, Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);

        this.CheckOutTime = string.IsNullOrEmpty(this.CheckOutTimeString) ? default(DateTime) :
           DateTime.ParseExact(this.CheckOutTimeString, Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);

        this.Date = string.IsNullOrEmpty(this.DateString) ? default(DateTime) :
        DateTime.ParseExact(this.DateString, Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);
        // }
        //catch (Exception ex)
        //{

        //}

    }

}

[DataContract]
public partial class MobileEmployeePermission
{
    [DataMember]
    public string ID { get; set; }
    [DataMember]
    public string EmployeeID { get; set; }
    [DataMember]
    public string PermissionID { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    [DataMember]
    public string StartDateString { get; set; }
    [DataMember]
    public string EndDateString { get; set; }
    [DataMember]
    public PermissionType Type { get; set; }
    [DataMember]
    public int TotalMinutes { get; set; }
    [DataMember]
    public string Reason { get; set; }
    [DataMember]
    public string ResponseBy { get; set; }
    public DateTime ResponseDate { get; set; }
    [DataMember]
    public byte Status { get; set; }
    public int CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public int ModifiedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    [DataMember]
    public string PermissionNameEn { get; set; }
    [DataMember]
    public string PermissionNameAr { get; set; }
  
    public string UniversityID { get; set; }
    //[DataMember]
    //public string StatusNameEn
    //{
    //    get
    //    {
    //        switch (Status)
    //        {
    //            case 0: return "Active";
    //            case 1: return "Inactive";
    //            case 2: return "Deleted";
    //            case 3: return "Requested";
    //            case 4: return "Canceled";
    //            case 5: return "Approved";
    //            case 6: return "Rejected";
    //            default: return null;
    //        }
    //    }
    //}
    //[DataMember]
    //public string StatusNameAr
    //{
    //    get
    //    {
    //        switch (Status)
    //        {
    //            case 0: return "نشط";
    //            case 1: return "غير نشط";
    //            case 2: return "حذف";
    //            case 3: return "طلب";
    //            case 4: return "ملغي";
    //            case 5: return "مقبول";
    //            case 6: return "مرفوض";
    //            default: return null;
    //        }
    //    }
    //}
    //[DataMember]
    //public bool CanCancel
    //{
    //    get
    //    {
    //        switch (this.Status)
    //        {
    //            case 0: //(active)
    //            case 3: //(requested)
    //                    //case 5: //(approved)
    //                if (this.StartDate > DateTime.Today)
    //                    return true;
    //                else
    //                    return false;
    //            default:
    //                //(1 inactive)
    //                //(2 deleted)
    //                //(4 canceled)
    //                //(6 rejected)
    //                return false;
    //        }
    //    }
    //}
    //public bool CanChangeResponse
    //{
    //    get
    //    {
    //        switch (this.Status)
    //        {
    //            case 3://(requested)
    //                if (this.StartDate > DateTime.Today)
    //                    return true;
    //                else
    //                    return false;
    //            case 5://(approved)
    //            case 6://(reject)
    //                return false;
    //            default:
    //                return false;
    //        }
    //    }

    //}
    //[DataMember]
    //public string Duration
    //{
    //    get
    //    {
    //        return TimeSpan.FromMinutes(TotalMinutes).ToString(@"hh\:mm"); ;//ToString(@"dd\.hh\:mm\:ss"); 
    //        //return Math.Abs(TotalMinutes / 60).ToString().PadLeft(2, '0') + " : " + (TotalMinutes - (Math.Abs(TotalMinutes / 60) * 60)).ToString().PadLeft(2, '0');
    //    }
    //}


    public bool IsNew { get; set; }


    [OnSerializing]
    void OnSerializing(StreamingContext context)
    {
        //this.RequestDateString = this.RequestDate.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

        // try
        // {
        this.StartDateString = this.StartDate.ToString(Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);
        this.EndDateString = this.EndDate.ToString(Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);
        // }
        //catch (Exception ex)
        //{
        //    //this.ResponseMessage = "Error:StartDateString or EndDateString has incorrect format. ";
        //}
    }

    [OnDeserialized]
    void OnDeserializing(StreamingContext context)
    {
        //try
        //{
        // this.RequestDate = string.IsNullOrEmpty(this.RequestDateString) ? default(DateTime) :
        //    DateTime.ParseExact(this.RequestDateString, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

        this.StartDate = string.IsNullOrEmpty(this.StartDateString) ? default(DateTime) :
           DateTime.ParseExact(this.StartDateString, Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);

        this.EndDate = string.IsNullOrEmpty(this.EndDateString) ? default(DateTime) :
           DateTime.ParseExact(this.EndDateString, Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);
        //}
        //catch (Exception ex)
        //{
        //    //this.ResponseMessage = "Error:StartDateString or EndDateString has incorrect format. ";
        //}

    }
}

public enum PermissionType
{
    DayBegin = 0,
    DayEnd = 1
}

[DataContract]
public class PrmAttendance : BaseObj
{
    [DataMember]
    public String DateFrom { get; set; }

    [DataMember]
    public String DateTo { get; set; }

    [DataMember]
    public String Action { get; set; } // "-1" =all[att,permissions,vaction] ,"0" =attendance,"1" =permission,"2"=vacations

}

[DataContract]
public partial class MobileEmployeeVacation
{
    [DataMember]
    public string ID { get; set; }
    [DataMember]
    public string EmployeeID { get; set; }
    [DataMember]
    public string VacationID { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    [DataMember]
    public string StartDateString { get; set; }
    [DataMember]
    public string EndDateString { get; set; }
    [DataMember]
    public string Reason { get; set; }
    [DataMember]
    public string ResponseBy { get; set; }
    public DateTime ResponseDate { get; set; }
    [DataMember]
    public byte Status { get; set; }
    public int CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public int ModifiedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    [DataMember]
    public string VacationNameEn { get; set; }

    [DataMember]
    public string VacationNameAr { get; set; }
    public string EmployeeNameEn { get; set; }
    public string EmployeeNameAr { get; set; }
    public string UniversityID { get; set; }
   

    public bool IsNew { get; set; }

    [OnSerializing]
    void OnSerializing(StreamingContext context)
    {
        //this.RequestDateString = this.RequestDate.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

        // try
        // {
        this.StartDateString = this.StartDate.ToString(Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);
        this.EndDateString = this.EndDate.ToString(Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);
        //}
        //catch (Exception ex)
        //{
        //    //this.ResponseMessage = "Error:StartDateString or EndDateString has incorrect format. ";
        //}
    }

    [OnDeserialized]
    void OnDeserializing(StreamingContext context)
    {
        // try
        // {
        // this.RequestDate = string.IsNullOrEmpty(this.RequestDateString) ? default(DateTime) :
        //    DateTime.ParseExact(this.RequestDateString, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

        this.StartDate = string.IsNullOrEmpty(this.StartDateString) ? default(DateTime) :
           DateTime.ParseExact(this.StartDateString, Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);

        this.EndDate = string.IsNullOrEmpty(this.EndDateString) ? default(DateTime) :
           DateTime.ParseExact(this.EndDateString, Constants.DATE_TIME_FORMAT, CultureInfo.InvariantCulture);
        //}
        //catch (Exception ex)
        //{
        //    //this.ResponseMessage = "Error:StartDateString or EndDateString has incorrect format. ";
        //}

    }
}

#endregion

#region Attendance Detail summary
[DataContract]//used when post data from mobile
public class EmpAttendanceLogDTONative
{
    [DataMember]
    public String EmpNo { get; set; }

    [DataMember]
    public String UserName { get; set; }

    [DataMember]
    public String DeviceId { get; set; }// if mobile generate uique id for insallation
   
    //[DataMember]
    public DateTime Time { get; set; } //complete date and time formate yyyy/MM/dd HH:mm:ss

    [DataMember]
    public Double Longitude { get; set; } //sent from mobile
    [DataMember]
    public Double Latitude { get; set; } //sent from Mobile

    [DataMember]
    public String AppId { get; set; }//from mobile

    [DataMember]
    public String DateTimeString { get; set; } //complete date and time formate yyyy/MM/dd HH:mm:ss

    [OnSerializing]
    void OnSerializing(StreamingContext context)
    {
        //this.RequestDateString = this.RequestDate.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

        try
        {
            this.DateTimeString = this.Time.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);
        }
        catch (Exception ex)
        {
            
        }
    }

    [OnDeserialized]
    void OnDeserializing(StreamingContext context)
    {
        try
        {
            this.Time = string.IsNullOrEmpty(this.DateTimeString) ? Constants.DateNow :
               DateTime.ParseExact(this.DateTimeString, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);
        }
        catch (Exception ex)
        {
            this.Time = default(DateTime);
        }

    }

    //[DataMember]
    // public int Time { get; set; }//Total Minutes of inTime or outTime


}

public class EmpAttendanceLogDTO : BaseObj
{
    //[DataMember]
    public DateTime Time { get; set; } //

    [DataMember]
    public Double Longitude { get; set; } //sent from mobile
    [DataMember]
    public Double Latitude { get; set; } //sent from Mobile

    [DataMember]
    public String DateTimeString { get; set; } //complete date and time formate yyyy/MM/dd HH:mm:ss


    [DataMember]
    public int Type { get; set; } // -- 0 action taken from user, 1 = automated action

    [OnSerializing]
    void OnSerializing(StreamingContext context)
    {

        //this.RequestDateString = this.RequestDate.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

        try
        {
            this.DateTimeString = this.Time.ToString(Constants.DATE_SORT_FORMAT, CultureInfo.InvariantCulture);
        }
        catch (Exception ex)
        {

        }
    }

    [OnDeserialized]
    void OnDeserializing(StreamingContext context)
    {
        // try
        // {
        this.Time = string.IsNullOrEmpty(this.DateTimeString) ? Constants.DateNow :
               DateTime.ParseExact(this.DateTimeString, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);
        // }
        //catch (Exception ex)
        //{
        //    this.Time = default(DateTime);
        //}

    }

    //[DataMember]
    // public int Time { get; set; }//Total Minutes of inTime or outTime


}
[DataContract]
public class AttendanceLogDTO
{
    [DataMember]
    public DateTime Date { get; set; }

    /// <summary>
    /// 0=WorkingDay,1=DayOff,2=Absent,3=Vacation
    /// </summary>
    [DataMember]
    public int Status { get; set; } // 0=WorkingDay,1=DayOff,2=Absent,3=Vacation

    [DataMember]
    public String InTime { get; set; } //HH:mm

    [DataMember]
    public String OutTime { get; set; } //HH:mm

    [DataMember]
    public String RequiredTime { get; set; } //HH:mm

    [DataMember]
    public String WorkingTime { get; set; } //HH:mm

    [DataMember]
    public String Delay { get; set; } //HH:mm

    [DataMember]
    public String Shortage { get; set; } //HH:mm

    [DataMember]
    public string Comment { get; set; } //HH:mm

    [DataMember]
    public String DateString { get; set; }//dd/MM/yyyy //todo:add to document

    [OnSerializing]
    void OnSerializing(StreamingContext context)
    {
        //this.RequestDateString = this.RequestDate.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

        try
        {
            this.DateString = this.Date.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);
        }
        catch (Exception ex)
        {

        }
    }

    [OnDeserialized]
    void OnDeserializing(StreamingContext context)
    {
        try
        {
            //this.Date = string.IsNullOrEmpty(this.DateString) ? default(DateTime):
            //   DateTime.ParseExact(this.DateString, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);
        }
        catch (Exception ex)
        {
           // this.Date = default(DateTime);
        }

    }


}

[DataContract]
public class AttendanceDTO
{
    [DataMember]
    public EmployeeDTO Emp { get; set; }

    [DataMember]
    public List<AttendanceLogDTO> Logs { get; set; }

    [DataMember]
    public string ErrorMessage { get; set; } //HH:mm
}

[DataContract]
public class AttendanceListDTO
{
    [DataMember]
    public List<AttendanceDTO> AttList { get; set; }
    [DataMember]
    public String ErrorMessage { get; set; }
}

[DataContract]
public class AttendanceDeductionDTO
{
    [DataMember]
    public String EmpNo { get; set; }

    [DataMember]
    public int DeductionMinutes { get; set; } //Total Minutes

    [DataMember]
    public int RequiredMinutes { get; set; } //Total Minutes

    [DataMember]
    public int DeductionHours { get; set; } //Total Hours
}

[DataContract]
public class AttendanceDeductionListDTO
{
    [DataMember]
    public List<AttendanceDeductionDTO> DeductionList { get; set; }

   public string ErrorMessage { get; set; }
}
#endregion

#region Employee Request (Permission/Vacation)

[DataContract]
public class RequestLogDTO //add request next avalilable date
{
    [DataMember]
    public string RequestNo { get; set; } // for future query
   // [DataMember]
    public DateTime RequestDate { get; set; }
    //[DataMember]
    public DateTime StartDate { get; set; }
   // [DataMember]
    public DateTime EndDate { get; set; }

    //[DataMember]
    public String RequestDateString { get; set; } //dd/MM/yyyy HH:mm:ss
    [DataMember]
    public String StartDateString { get; set; } //dd/MM/yyyy HH:mm:ss
    [DataMember]
    public String EndDateString { get; set; } //dd/MM/yyyy HH:mm:ss
    [DataMember]
    public string RequestName { get; set; } // Vacation Name/Permission Name (English Name)
    [DataMember]
    public string Reason { get; set; } //for user
    [DataMember]
    public string ReplyComment { get; set; } // for managers
    [DataMember]
    public int Status { get; set; } // 0=wait ,1=accepted,2=rejected,3=Canceled //ignore when add rquest

    //required in request permissions only
    [DataMember]
    public int TransactionType { get; set; } //0=start of day ,1=end of day (to effect dealy/shortage)
    [DataMember]
    public int TotalMinutes { get; set; } // permission total minutes

    //need when add object
    [DataMember]
    public String EmpNo { get; set; }

    [DataMember]
    public String UserName { get; set; }

    [DataMember]
    public String ErrorMessage { get; set; }


    [OnSerializing]
    void OnSerializing(StreamingContext context)
    {
        //this.RequestDateString = this.RequestDate.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

        try
        {
            this.StartDateString = this.StartDate.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);
            this.EndDateString = this.EndDate.ToString(Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);
        }
        catch (Exception ex)
        {
            this.ErrorMessage = "Error:StartDateString or EndDateString has incorrect format. ";
        }
    }

    [OnDeserialized]
    void OnDeserializing(StreamingContext context)
    {
        try
        {
            // this.RequestDate = string.IsNullOrEmpty(this.RequestDateString) ? default(DateTime) :
            //    DateTime.ParseExact(this.RequestDateString, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

            this.StartDate = string.IsNullOrEmpty(this.StartDateString) ? default(DateTime) :
               DateTime.ParseExact(this.StartDateString, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);

            this.EndDate = string.IsNullOrEmpty(this.EndDateString) ? default(DateTime) :
               DateTime.ParseExact(this.EndDateString, Constants.DATE_DISPLAY_FORMAT, CultureInfo.InvariantCulture);
        }
        catch (Exception ex)
        {
            this.ErrorMessage = "Error:StartDateString or EndDateString has incorrect format. ";
        }

    }

}

[DataContract]
public class RequestDTO
{
    [DataMember]
    public EmployeeDTO Emp { get; set; }

    [DataMember]
    public List<RequestLogDTO> Logs { get; set; }

    [DataMember]
    public string ErrorMessage { get; set; } //HH:mm
}

[DataContract]
public class RequestListDTO
{
    [DataMember]
    public List<RequestDTO> Requests { get; set; }

    [DataMember]
    public string ErrorMessage { get; set; } //HH:mm
}
#endregion

[DataContract]
public class AuthDTO:BaseObj
{
    [DataMember]
    public String Password { get; set; }
    [DataMember]
    public String Domain { get; set; }
  
    [DataMember]
    public bool IsMobileDevice { get; set; }//authenticate 

    [DataMember]
    public bool ExternalAuth { get; set; }// example google auth

    [DataMember]
    public int AuthType { get; set; } // 0= Active  directory  , 1= user&pass , 2 =>user name only ,3 => external

}


