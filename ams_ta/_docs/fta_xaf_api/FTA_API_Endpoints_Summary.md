# FTA Attendance Service API Endpoints Summary

This document provides a comprehensive overview of the available endpoints in the FTA Attendance Service API.

## 1. System Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `IsAlive` | GET | Health check endpoint to verify if the service is running |
| `TestPost` | POST | Test endpoint for verifying authentication |

## 2. Employee Management Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `AddEmployee` | POST | Add a single employee to the system |
| `AddEmployees` | POST | Bulk add multiple employees to the system |
| `GetEmployeeByEmpNo` | GET | Retrieve employee details by employee number |
| `GetEmployeeByUserName` | GET | Retrieve employee details by username |
| `GetEmployees` | GET | Retrieve all employees in the system |
| `GetEmployeesByDeptName` | GET | Filter employees by department name |
| `GetEmployeesByDeptCode` | GET | Filter employees by department code |
| `GetEmployeeByBGName` | GET | Filter employees by business group name |
| `GetEmployeesByBGCode` | GET | Filter employees by business group code |

## 3. Attendance Management Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `AddAttendance` | POST | Record a new attendance log entry |
| `GetEmployeeAttendanceByEmpNo` | GET | Retrieve attendance records for an employee by employee number within a date range |
| `GetEmployeeAttendanceByUserName` | GET | Retrieve attendance records for an employee by username within a date range |
| `GetEmployeesAttendance` | GET | Retrieve attendance records for all employees within a date range |
| `GetEmployeesAttendanceByDeptName` | GET | Filter attendance records by department name within a date range |
| `GetEmployeesAttendanceByDeptCode` | GET | Filter attendance records by department code within a date range |
| `GetEmployeesAttendanceByBGName` | GET | Filter attendance records by business group name within a date range |
| `GetEmployeesAttendanceByBGCode` | GET | Filter attendance records by business group code within a date range |
| `GetEmployeesAttendanceDeduction` | GET | Retrieve attendance deduction information for an employee within a date range |

## 4. Permission Management Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `AddPermissionRequest` | POST | Submit a new permission request |
| `GetEmployeePermissionsByEmpNo` | GET | Retrieve permission records for an employee by employee number |
| `GetEmployeePermissionsByUserName` | GET | Retrieve permission records for an employee by username |
| `GetEmployeesPermissions` | GET | Retrieve permission records for all employees |
| `GetEmployeesPermissionsByDeptName` | GET | Filter permission records by department name |
| `GetEmployeesPermissionsByDeptCode` | GET | Filter permission records by department code |
| `GetEmployeesPermissionsByBGName` | GET | Filter permission records by business group name |
| `GetEmployeesPermissionsByBGCode` | GET | Filter permission records by business group code |

## 5. Vacation Management Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `AddVacationRequest` | POST | Submit a new vacation request |
| `GetEmployeeVacationsByEmpNo` | GET | Retrieve vacation records for an employee by employee number |
| `GetEmployeeVacationsByUserName` | GET | Retrieve vacation records for an employee by username |
| `GetEmployeesVacations` | GET | Retrieve vacation records for all employees |
| `GetEmployeesVacationsByDeptName` | GET | Filter vacation records by department name |
| `GetEmployeesVacationsByDeptCode` | GET | Filter vacation records by department code |
| `GetEmployeesVacationsByBGName` | GET | Filter vacation records by business group name |
| `GetEmployeesVacationsByBGCode` | GET | Filter vacation records by business group code |

## 6. SharePoint Integration Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `GetAttByUserName` | GET | Retrieve attendance data for SharePoint integration by username |
| `GetAttByEmpNo` | GET | Retrieve attendance data for SharePoint integration by employee number |

## 7. Mobile Service Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `MAuthenticate` | POST | Authenticate mobile user and return employee details |
| `MAuthenticateNative` | POST | Authenticate mobile user for native apps |
| `MRegisterApp` | POST | Register a mobile device/app |
| `MGetEmployee` | POST | Get employee details for mobile app |
| `MGetEmployeeByEmpNo` | GET | Get employee details by employee number for mobile app |
| `MGetEmployeeByUserName` | GET | Get employee details by username for mobile app |
| `MAddAttendanceNative` | POST | Record attendance from native mobile app |
| `MAddAttendance` | POST | Record attendance from mobile app |
| `MGetAtt` | GET | Get attendance data for mobile app |
| `MGetEmployeeAttendance` | POST | Get detailed attendance data for mobile app |
| `MGetEmployeeAttendanceByEmpNo` | GET | Get attendance by employee number for mobile app |
| `MGetEmployeeAttendanceByUserName` | GET | Get attendance by username for mobile app |

## Technical Details

### Authentication

- **API Key**: Most endpoints require authentication via API key or user credentials
- **Format**: Authentication data is passed in the request body for POST methods

### Request Format

- **Content Type**: `application/json` for request and response
- **Date Format**: Dates are expected in "ddMMyyyy" format for all endpoints
- **Example**: `dateFrom=01012023&dateTo=31012023` for January 2023

### Response Format

All responses follow a standard format:

```json
{
  "success": true,
  "data": [...],
  "errorMessage": null
}
```

Or in case of error:

```json
{
  "success": false,
  "data": null,
  "errorMessage": "Error description"
}
```

### Error Handling

- All endpoints include comprehensive error handling
- Errors are logged in the system for troubleshooting
- Error responses include descriptive messages to aid in debugging

### Data Models

The API uses the following primary data models:

- **EmployeeDTO**: Employee information
- **AttendanceDTO**: Attendance records
- **RequestDTO**: Permission and vacation requests
- **EmpAttendanceLogDTO**: Individual attendance log entries

## Implementation Notes

This API is implemented as a WCF service with RESTful endpoints. It provides JSON-formatted responses and supports both GET and POST HTTP methods. The service integrates with the FTA time and attendance system and provides specialized endpoints for mobile applications and SharePoint integration.
