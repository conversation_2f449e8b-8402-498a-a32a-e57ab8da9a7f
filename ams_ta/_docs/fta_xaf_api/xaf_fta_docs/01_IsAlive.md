# IsAlive Endpoint

## Endpoint Information
- **URL**: `/IsAlive`
- **Method**: GET
- **Description**: Health check endpoint to verify if the service is running

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebGet(BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
string IsAlive();
```

### Request
- No parameters required

### Response
- **Type**: `string`
- **Format**: Plain text
- **Success Response**: "OK" followed by configuration information
- **Error Response**: Error message

### Implementation Logic
The implementation performs the following operations:
1. Registers the module with `ftaInt.RegisterModule()`
2. Initializes logging with `ftaInt.InitLog()`
3. Logs the request with `ftaInt.LogRequest()`
4. Returns "OK" with configuration information if successful, or an error message if not

### Example Response
```
OK,Config File:/path/to/config/file
```

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- No authentication required for this endpoint
- Return a simple success message with configuration information

### Logic
- Implement basic health check functionality
- Verify database connection
- Log the request
- Return appropriate response

### Response Format
- Return a simple string response
- Include configuration information if available
- Return error message if any issues are encountered

### Notes
This endpoint is used for monitoring and health checks to verify that the service is operational. It should be lightweight and respond quickly.
