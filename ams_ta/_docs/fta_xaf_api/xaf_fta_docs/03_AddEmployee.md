# AddEmployee Endpoint

## Endpoint Information
- **URL**: `/AddEmployee`
- **Method**: POST
- **Description**: Add a single employee to the system

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebInvoke(Method = "POST", BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
String AddEmployee(EmployeeDTO emp);
```

### Request
- **Type**: `EmployeeDTO`
- **Content-Type**: application/json

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    
    // Department information
    public string DeptArabicName { get; set; }  // Department name in Arabic
    public string DeptEnglishName { get; set; } // Department name in English
    
    // Business Group information
    public string BGArabicName { get; set; }    // Business group name in Arabic
    public string BGEnglishName { get; set; }   // Business group name in English
    
    // Branch information
    public string BranchArabicName { get; set; } // Branch name in Arabic
    public string BranchEnglishName { get; set; } // Branch name in English
    
    // Area information
    public string AreaArabicName { get; set; }   // Area name in Arabic
    public string AreaEnglishName { get; set; }  // Area name in English
    
    public string ErrorMessage { get; set; }     // Error message if any
}
```

### Response
- **Type**: `string`
- **Format**: Plain text
- **Success Response**: Empty string ("") indicating success
- **Error Response**: String starting with "Error:" followed by error details

### Implementation Logic
The implementation performs the following operations:
1. Validates that the employee data is not null
2. Creates an object space for database operations
3. Searches for an existing employee using either Username or HRCode
4. If the employee doesn't exist, creates a new employee record
   - Generates a unique EnrollID for the employee
5. Updates employee information (names, email)
6. Creates or retrieves related entities (BusinessGroup, Department, Branch, Area)
   - If specific values are provided, uses those
   - If not provided, creates default values
7. Sets the employee's company and security level
8. Commits the changes to the database

### Example Request
```json
{
  "EmpNo": "EMP001",
  "UserName": "john.doe",
  "ArabicName": "جون دو",
  "EnglishName": "John Doe",
  "Email": "<EMAIL>",
  "DeptEnglishName": "IT Department",
  "DeptArabicName": "قسم تكنولوجيا المعلومات",
  "BGEnglishName": "Technology",
  "BGArabicName": "تكنولوجيا",
  "BranchEnglishName": "Main Branch",
  "BranchArabicName": "الفرع الرئيسي",
  "AreaEnglishName": "Headquarters",
  "AreaArabicName": "المقر الرئيسي"
}
```

### Example Response
- Success: `""`
- Error: `"Error:OS_EMP,Employee with this username already exists,Stack trace details..."`

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept POST requests with JSON body
- Parse the employee data from the request body

### Request DTO
- Create a model to represent the EmployeeDTO with all required fields
- Include validation for required fields (EmpNo or UserName must be provided)

### Logic
- Check if employee exists by username or employee number
- If not exists, create new employee record
- If exists, update the employee information
- Handle related entities (department, business group, branch, area)
  - Create them if they don't exist
  - Link them to the employee
- Set default values for missing fields
- Commit changes to the database

### Response Format
- Return empty string for success
- Return error message with details for failures

### Error Handling
- Validate input data
- Handle database errors
- Return descriptive error messages
- Log errors with stack traces for debugging

### Notes
- Either Username or EmpNo must be provided to identify the employee
- If both are provided, Username takes precedence
- The endpoint will create related entities (departments, business groups, etc.) if they don't exist
- Default values will be used for missing related entities
