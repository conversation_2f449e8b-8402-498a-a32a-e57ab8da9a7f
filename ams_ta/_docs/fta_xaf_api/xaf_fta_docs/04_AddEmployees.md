# AddEmployees Endpoint

## Endpoint Information
- **URL**: `/AddEmployees`
- **Method**: POST
- **Description**: Bulk add multiple employees to the system

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebInvoke(Method = "POST", BodyStyle = WebMessageBodyStyle.WrappedRequest, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
String AddEmployees(EmployeeListDTO empList);
```

### Request
- **Type**: `EmployeeListDTO`
- **Content-Type**: application/json

#### EmployeeListDTO Structure
```csharp
public class EmployeeListDTO
{
    public List<EmployeeDTO> Employees { get; set; }  // List of employees to add
    public string ErrorMessage { get; set; }          // Error message if any
}
```

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    
    // Department information
    public string DeptArabicName { get; set; }  // Department name in Arabic
    public string DeptEnglishName { get; set; } // Department name in English
    
    // Business Group information
    public string BGArabicName { get; set; }    // Business group name in Arabic
    public string BGEnglishName { get; set; }   // Business group name in English
    
    // Branch information
    public string BranchArabicName { get; set; } // Branch name in Arabic
    public string BranchEnglishName { get; set; } // Branch name in English
    
    // Area information
    public string AreaArabicName { get; set; }   // Area name in Arabic
    public string AreaEnglishName { get; set; }  // Area name in English
    
    public string ErrorMessage { get; set; }     // Error message if any
}
```

### Response
- **Type**: `string`
- **Format**: Plain text
- **Success Response**: "1" indicating success
- **Error Response**: String starting with "Error:" followed by error details

### Implementation Logic
The implementation performs the following operations:
1. Iterates through each employee in the list
2. Calls the AddEmployee method for each employee
3. If any employee addition fails, returns the error message immediately
4. If all employees are added successfully, returns "1"

### Example Request
```json
{
  "Employees": [
    {
      "EmpNo": "EMP001",
      "UserName": "john.doe",
      "ArabicName": "جون دو",
      "EnglishName": "John Doe",
      "Email": "<EMAIL>",
      "DeptEnglishName": "IT Department",
      "BGEnglishName": "Technology"
    },
    {
      "EmpNo": "EMP002",
      "UserName": "jane.smith",
      "ArabicName": "جين سميث",
      "EnglishName": "Jane Smith",
      "Email": "<EMAIL>",
      "DeptEnglishName": "HR Department",
      "BGEnglishName": "Administration"
    }
  ]
}
```

### Example Response
- Success: `"1"`
- Error: `"Error: Employee with this username already exists"`

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept POST requests with JSON body
- Parse the employee list data from the request body

### Request DTO
- Create models to represent the EmployeeListDTO and EmployeeDTO
- Include validation for required fields

### Logic
- Iterate through each employee in the list
- For each employee:
  - Check if employee exists by username or employee number
  - If not exists, create new employee record
  - If exists, update the employee information
  - Handle related entities (department, business group, branch, area)
- Stop processing and return error if any employee addition fails
- Return success if all employees are added successfully

### Response Format
- Return "1" for success
- Return error message with details for failures

### Error Handling
- Validate input data
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Notes
- This endpoint is a batch version of the AddEmployee endpoint
- It processes employees sequentially and stops on the first error
- The same validation and business rules apply as in the AddEmployee endpoint
- This endpoint is useful for bulk imports of employee data
