# GetEmployeeByEmpNo Endpoint

## Endpoint Information
- **URL**: `/GetEmployeeByEmpNo`
- **Method**: GET
- **Description**: Retrieve employee details by employee number

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebGet(BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
EmployeeDTO GetEmployeeByEmpNo(String empNo);
```

### Request
- **Type**: URL Parameter
- **Parameter**: `empNo` - The employee number (HRCode) to search for

### Response
- **Type**: `EmployeeDTO`
- **Content-Type**: application/json

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    
    // Department information
    public string DeptArabicName { get; set; }  // Department name in Arabic
    public string DeptEnglishName { get; set; } // Department name in English
    
    // Business Group information
    public string BGArabicName { get; set; }    // Business group name in Arabic
    public string BGEnglishName { get; set; }   // Business group name in English
    
    // Branch information
    public string BranchArabicName { get; set; } // Branch name in Arabic
    public string BranchEnglishName { get; set; } // Branch name in English
    
    // Area information
    public string AreaArabicName { get; set; }   // Area name in Arabic
    public string AreaEnglishName { get; set; }  // Area name in English
    
    public string ErrorMessage { get; set; }     // Error message if any
}
```

### Implementation Logic
The implementation performs the following operations:
1. Calls the `GetEmployeeInfo` method with the provided employee number
2. The `GetEmployeeInfo` method:
   - Creates an object space for database operations
   - Searches for an employee using the HRCode
   - If found, maps the employee data to an EmployeeDTO
   - If not found, returns an EmployeeDTO with an error message

### Example Request
```
GET /GetEmployeeByEmpNo?empNo=EMP001
```

### Example Response
```json
{
  "EmpNo": "EMP001",
  "UserName": "john.doe",
  "ArabicName": "جون دو",
  "EnglishName": "John Doe",
  "Email": "<EMAIL>",
  "DeptArabicName": "قسم تكنولوجيا المعلومات",
  "DeptEnglishName": "IT Department",
  "BGArabicName": "تكنولوجيا",
  "BGEnglishName": "Technology",
  "BranchArabicName": "الفرع الرئيسي",
  "BranchEnglishName": "Main Branch",
  "AreaArabicName": "المقر الرئيسي",
  "AreaEnglishName": "Headquarters",
  "ErrorMessage": null
}
```

### Error Response
```json
{
  "EmpNo": null,
  "UserName": null,
  "ArabicName": null,
  "EnglishName": null,
  "Email": null,
  "DeptArabicName": null,
  "DeptEnglishName": null,
  "BGArabicName": null,
  "BGEnglishName": null,
  "BranchArabicName": null,
  "BranchEnglishName": null,
  "AreaArabicName": null,
  "AreaEnglishName": null,
  "ErrorMessage": "Error: Employee EMP001 doesn't exist!!"
}
```

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept GET requests with URL parameters
- Parse the employee number from the request

### Response DTO
- Create a model to represent the EmployeeDTO with all fields
- Include error message field for reporting issues

### Logic
- Search for an employee by employee number (HRCode)
- If found, map the employee data to the response DTO
- If not found, return an error message
- Include related entity information (department, business group, branch, area)

### Response Format
- Return JSON representation of EmployeeDTO
- Include all fields, even if null
- Set ErrorMessage field if an error occurs

### Error Handling
- Handle case where employee is not found
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Notes
- This endpoint is used to retrieve detailed information about a single employee
- The employee is identified by their employee number (HRCode)
- All related entity information should be included in the response
- The endpoint should return quickly for good user experience
