# GetEmployees Endpoint

## Endpoint Information
- **URL**: `/GetEmployees`
- **Method**: GET
- **Description**: Retrieve all employees in the system

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebGet(BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
EmployeeListDTO GetEmployees();
```

### Request
- No parameters required

### Response
- **Type**: `EmployeeListDTO`
- **Content-Type**: application/json

#### EmployeeListDTO Structure
```csharp
public class EmployeeListDTO
{
    public List<EmployeeDTO> Employees { get; set; }  // List of employees
    public string ErrorMessage { get; set; }          // Error message if any
}
```

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    
    // Department information
    public string DeptArabicName { get; set; }  // Department name in Arabic
    public string DeptEnglishName { get; set; } // Department name in English
    
    // Business Group information
    public string BGArabicName { get; set; }    // Business group name in Arabic
    public string BGEnglishName { get; set; }   // Business group name in English
    
    // Branch information
    public string BranchArabicName { get; set; } // Branch name in Arabic
    public string BranchEnglishName { get; set; } // Branch name in English
    
    // Area information
    public string AreaArabicName { get; set; }   // Area name in Arabic
    public string AreaEnglishName { get; set; }  // Area name in English
    
    public string ErrorMessage { get; set; }     // Error message if any
}
```

### Implementation Logic
The implementation performs the following operations:
1. Calls the `GetEmployeesInfo` method without any filter parameters
2. The `GetEmployeesInfo` method:
   - Creates an object space for database operations
   - Retrieves all employees from the database
   - Maps each employee to an EmployeeDTO
   - Adds each EmployeeDTO to the Employees list in the EmployeeListDTO
   - Returns the EmployeeListDTO

### Example Request
```
GET /GetEmployees
```

### Example Response
```json
{
  "Employees": [
    {
      "EmpNo": "EMP001",
      "UserName": "john.doe",
      "ArabicName": "جون دو",
      "EnglishName": "John Doe",
      "Email": "<EMAIL>",
      "DeptArabicName": "قسم تكنولوجيا المعلومات",
      "DeptEnglishName": "IT Department",
      "BGArabicName": "تكنولوجيا",
      "BGEnglishName": "Technology",
      "BranchArabicName": "الفرع الرئيسي",
      "BranchEnglishName": "Main Branch",
      "AreaArabicName": "المقر الرئيسي",
      "AreaEnglishName": "Headquarters",
      "ErrorMessage": null
    },
    {
      "EmpNo": "EMP002",
      "UserName": "jane.smith",
      "ArabicName": "جين سميث",
      "EnglishName": "Jane Smith",
      "Email": "<EMAIL>",
      "DeptArabicName": "قسم الموارد البشرية",
      "DeptEnglishName": "HR Department",
      "BGArabicName": "إدارة",
      "BGEnglishName": "Administration",
      "BranchArabicName": "الفرع الرئيسي",
      "BranchEnglishName": "Main Branch",
      "AreaArabicName": "المقر الرئيسي",
      "AreaEnglishName": "Headquarters",
      "ErrorMessage": null
    }
  ],
  "ErrorMessage": null
}
```

### Error Response
```json
{
  "Employees": [],
  "ErrorMessage": "Error: Database connection failed"
}
```

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept GET requests without parameters

### Response DTO
- Create models to represent the EmployeeListDTO and EmployeeDTO
- Include error message fields for reporting issues

### Logic
- Retrieve all employees from the database
- Map each employee to an EmployeeDTO
- Include related entity information (department, business group, branch, area)
- Return a list of all employees

### Response Format
- Return JSON representation of EmployeeListDTO
- Include all fields for each employee, even if null
- Set ErrorMessage field if an error occurs

### Error Handling
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Performance Considerations
- This endpoint may return a large amount of data
- Consider implementing pagination if the number of employees is large
- Optimize database queries to improve performance
- Consider caching frequently accessed data

### Notes
- This endpoint is used to retrieve information about all employees in the system
- All related entity information should be included for each employee
- The response may be large if there are many employees in the system
- Consider implementing pagination or filtering options for better performance
