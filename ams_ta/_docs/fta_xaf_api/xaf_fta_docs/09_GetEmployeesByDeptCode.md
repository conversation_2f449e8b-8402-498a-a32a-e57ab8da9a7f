# GetEmployeesByDeptCode Endpoint

## Endpoint Information
- **URL**: `/GetEmployeesByDeptCode`
- **Method**: GET
- **Description**: Filter employees by department code

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebGet(BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
EmployeeListDTO GetEmployeesByDeptCode(String deptCode);
```

### Request
- **Type**: URL Parameter
- **Parameter**: `deptCode` - The department code to filter by

### Response
- **Type**: `EmployeeListDTO`
- **Content-Type**: application/json

#### EmployeeListDTO Structure
```csharp
public class EmployeeListDTO
{
    public List<EmployeeDTO> Employees { get; set; }  // List of employees
    public string ErrorMessage { get; set; }          // Error message if any
}
```

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    
    // Department information
    public string DeptArabicName { get; set; }  // Department name in Arabic
    public string DeptEnglishName { get; set; } // Department name in English
    
    // Business Group information
    public string BGArabicName { get; set; }    // Business group name in Arabic
    public string BGEnglishName { get; set; }   // Business group name in English
    
    // Branch information
    public string BranchArabicName { get; set; } // Branch name in Arabic
    public string BranchEnglishName { get; set; } // Branch name in English
    
    // Area information
    public string AreaArabicName { get; set; }   // Area name in Arabic
    public string AreaEnglishName { get; set; }  // Area name in English
    
    public string ErrorMessage { get; set; }     // Error message if any
}
```

### Implementation Logic
The implementation performs the following operations:
1. Calls the `GetEmployeesInfo` method with the department code and `FilterKeyType.DeptCode`
2. The `GetEmployeesInfo` method:
   - Creates an object space for database operations
   - Builds a criteria to filter employees by department code
   - Retrieves employees matching the criteria
   - Maps each employee to an EmployeeDTO
   - Adds each EmployeeDTO to the Employees list in the EmployeeListDTO
   - Returns the EmployeeListDTO

### Example Request
```
GET /GetEmployeesByDeptCode?deptCode=IT001
```

### Example Response
```json
{
  "Employees": [
    {
      "EmpNo": "EMP001",
      "UserName": "john.doe",
      "ArabicName": "جون دو",
      "EnglishName": "John Doe",
      "Email": "<EMAIL>",
      "DeptArabicName": "قسم تكنولوجيا المعلومات",
      "DeptEnglishName": "IT Department",
      "BGArabicName": "تكنولوجيا",
      "BGEnglishName": "Technology",
      "BranchArabicName": "الفرع الرئيسي",
      "BranchEnglishName": "Main Branch",
      "AreaArabicName": "المقر الرئيسي",
      "AreaEnglishName": "Headquarters",
      "ErrorMessage": null
    },
    {
      "EmpNo": "EMP003",
      "UserName": "bob.johnson",
      "ArabicName": "بوب جونسون",
      "EnglishName": "Bob Johnson",
      "Email": "<EMAIL>",
      "DeptArabicName": "قسم تكنولوجيا المعلومات",
      "DeptEnglishName": "IT Department",
      "BGArabicName": "تكنولوجيا",
      "BGEnglishName": "Technology",
      "BranchArabicName": "الفرع الرئيسي",
      "BranchEnglishName": "Main Branch",
      "AreaArabicName": "المقر الرئيسي",
      "AreaEnglishName": "Headquarters",
      "ErrorMessage": null
    }
  ],
  "ErrorMessage": null
}
```

### Error Response
```json
{
  "Employees": [],
  "ErrorMessage": "Error: Database connection failed"
}
```

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept GET requests with URL parameters
- Parse the department code from the request

### Response DTO
- Create models to represent the EmployeeListDTO and EmployeeDTO
- Include error message fields for reporting issues

### Logic
- Filter employees by department code
- Map each employee to an EmployeeDTO
- Include related entity information (department, business group, branch, area)
- Return a list of filtered employees

### Response Format
- Return JSON representation of EmployeeListDTO
- Include all fields for each employee, even if null
- Set ErrorMessage field if an error occurs

### Error Handling
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Notes
- This endpoint is used to retrieve information about employees in a specific department
- The department is identified by its code
- All related entity information should be included for each employee
- The comparison should be exact for department codes
- The endpoint should return an empty list if no employees are found in the specified department
- This endpoint is similar to GetEmployeesByDeptName but uses department code instead of name
