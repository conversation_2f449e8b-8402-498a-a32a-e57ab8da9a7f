# AddAttendance Endpoint

## Endpoint Information
- **URL**: `/AddAttendance`
- **Method**: POST
- **Description**: Record a new attendance log entry

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebInvoke(Method = "POST", BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
String AddAttendance(EmpAttendanceLogDTO log);
```

### Request
- **Type**: `EmpAttendanceLogDTO`
- **Content-Type**: application/json

#### EmpAttendanceLogDTO Structure
```csharp
public class EmpAttendanceLogDTO : BaseObj
{
    public DateTime Time { get; set; }          // Time of the attendance log
    public Double Longitude { get; set; }       // Longitude coordinate of the attendance location
    public Double Latitude { get; set; }        // Latitude coordinate of the attendance location
    public String DateTimeString { get; set; }  // Date and time string in format "yyyyMMddHHmmss"
    public int Type { get; set; }               // 0 = action taken from user, 1 = automated action

    // Inherited from BaseObj:
    public String AppId { get; set; }           // Device/App ID
    public String Token { get; set; }           // Authentication token
    public String AppVersion { get; set; }      // App version
    public String Username { get; set; }        // Username for authentication
    public String EmpNo { get; set; }           // Employee number (HRCode)
    public String HRCode { get; set; }          // Alternative employee number
    public string DeviceInfo { get; set; }      // Device information
    public String ResponseCode { get; set; }    // Response code (inherited from BaseResponse)
    public String ResponseMessage { get; set; } // Response message (inherited from BaseResponse)
}
```

### Response
- **Type**: `string`
- **Format**: Plain text
- **Success Response**: Empty string ("") or success message
- **Error Response**: String starting with "Error:" followed by error details

### Implementation Logic
The implementation performs the following operations:
1. Logs the request with `ftaInt.LogRequest(log)`
2. Calls the `AddAttendanceLog` method with the attendance log data
3. The `AddAttendanceLog` method:
   - Validates the attendance log data (checks if Time is default DateTime)
   - Creates an object space for database operations
   - For mobile app requests, validates against the employee's current shift unit
   - Finds the employee by username or employee number
   - Finds the device by AppId
   - Creates a new attendance log record
   - Sets the log time (uses server time if from mobile app, otherwise uses provided time)
   - Sets location data (latitude and longitude)
   - Commits the changes to the database
   - Returns "1" for success or error message

### Example Request
```json
{
  "EmpNo": "EMP001",
  "Username": "john.doe",
  "DateTimeString": "20230615083000",
  "Type": 0,
  "AppId": "DEV001",
  "AppVersion": "1.0",
  "Longitude": 46.6753,
  "Latitude": 24.7136,
  "DeviceInfo": "Main Entrance Terminal"
}
```

### Example Response
- Success: `"1"`
- Error: `"Error: Employee not found"` or `"Error: DateTimeString has incorrect format."`

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept POST requests with JSON body
- Parse the attendance log data from the request body

### Request DTO
- Create a model to represent the EmpAttendanceLogDTO with all required fields
- Include validation for required fields

### Logic
- Validate the attendance log data
- Find the employee by username or employee number
- Create a new attendance log record
- Set the log time, type, device information, and other details
- Commit the changes to the database
- Return success or error message

### Response Format
- Return "1" for success
- Return error message with details for failures, prefixed with "Error:"

### Error Handling
- Validate input data
- Handle case where employee is not found
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Notes
- This endpoint is used to record attendance events (check-in, check-out)
- Either Username or EmpNo must be provided to identify the employee
- The DateTimeString should be in format "yyyyMMddHHmmss" (e.g., "20230615083000" for June 15, 2023, 08:30:00)
- The Type field indicates whether the action was taken by user (0) or automated (1)
- For mobile app requests, the server time is used instead of the provided time
- For mobile app requests, the current shift unit is validated
- Location data (latitude and longitude) is stored with the attendance record
- The endpoint validates that the employee exists before creating the log
- The AppId field is used to find the device that recorded the attendance
