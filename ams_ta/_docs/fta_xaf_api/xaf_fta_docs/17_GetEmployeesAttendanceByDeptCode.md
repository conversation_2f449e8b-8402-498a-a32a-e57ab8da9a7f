# GetEmployeesAttendanceByDeptCode Endpoint

## Endpoint Information
- **URL**: `/GetEmployeesAttendanceByDeptCode`
- **Method**: GET
- **Description**: Filter attendance records by department code within a date range

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebGet(BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
AttendanceListDTO GetEmployeesAttendanceByDeptCode(String deptCode, String dateFrom, String dateTo);
```

### Request
- **Type**: URL Parameters
- **Parameters**:
  - `deptCode` - The department code to filter by
  - `dateFrom` - Start date in format "ddMMyyyy" (e.g., "01062023" for June 1, 2023)
  - `dateTo` - End date in format "ddMMyyyy" (e.g., "30062023" for June 30, 2023)

### Response
- **Type**: `AttendanceListDTO`
- **Content-Type**: application/json

#### AttendanceListDTO Structure
```csharp
public class AttendanceListDTO
{
    public List<AttendanceDTO> Attendances { get; set; }    // List of attendance records by employee
    public string ErrorMessage { get; set; }                // Error message if any
}
```

#### AttendanceDTO Structure
```csharp
public class AttendanceDTO
{
    public EmployeeDTO Employee { get; set; }                // Employee information
    public List<AttendanceLogDTO> Logs { get; set; }         // List of attendance logs
    public string ErrorMessage { get; set; }                 // Error message if any
}
```

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    // Other employee properties...
}
```

#### AttendanceLogDTO Structure
```csharp
public class AttendanceLogDTO
{
    public DateTime Date { get; set; }          // Date of the attendance record
    public DateTime? CheckIn { get; set; }      // Check-in time
    public DateTime? CheckOut { get; set; }     // Check-out time
    public TimeSpan? WorkHours { get; set; }    // Total work hours
    public string Status { get; set; }          // Attendance status (e.g., Present, Absent, Late)
    public string Notes { get; set; }           // Additional notes
    // Other attendance properties...
}
```

### Implementation Logic
The implementation performs the following operations:
1. Logs the request with `ftaInt.LogRequest()`
2. Parses the date strings to DateTime objects using the format "ddMMyyyy"
3. Calls the `GetEmployeesAttendance` method with the department code, date range, and `FilterKeyType.DeptCode`
4. The `GetEmployeesAttendance` method:
   - Creates an object space for database operations
   - Retrieves employees filtered by department code
   - For each employee, retrieves attendance records within the date range
   - Maps each employee's data to an EmployeeDTO
   - Maps each attendance record to an AttendanceLogDTO
   - Creates an AttendanceDTO for each employee with their logs
   - Adds each AttendanceDTO to the Attendances list in the AttendanceListDTO
   - Returns the AttendanceListDTO

### Example Request
```
GET /GetEmployeesAttendanceByDeptCode?deptCode=IT001&dateFrom=01062023&dateTo=30062023
```

### Example Response
```json
{
  "Attendances": [
    {
      "Employee": {
        "EmpNo": "EMP001",
        "UserName": "john.doe",
        "ArabicName": "جون دو",
        "EnglishName": "John Doe",
        "Email": "<EMAIL>",
        "DeptEnglishName": "IT Department"
      },
      "Logs": [
        {
          "Date": "2023-06-01T00:00:00",
          "CheckIn": "2023-06-01T08:30:00",
          "CheckOut": "2023-06-01T17:15:00",
          "WorkHours": "08:45:00",
          "Status": "Present",
          "Notes": null
        },
        {
          "Date": "2023-06-02T00:00:00",
          "CheckIn": "2023-06-02T08:45:00",
          "CheckOut": "2023-06-02T17:30:00",
          "WorkHours": "08:45:00",
          "Status": "Present",
          "Notes": null
        }
      ],
      "ErrorMessage": null
    },
    {
      "Employee": {
        "EmpNo": "EMP003",
        "UserName": "bob.johnson",
        "ArabicName": "بوب جونسون",
        "EnglishName": "Bob Johnson",
        "Email": "<EMAIL>",
        "DeptEnglishName": "IT Department"
      },
      "Logs": [
        {
          "Date": "2023-06-01T00:00:00",
          "CheckIn": "2023-06-01T08:15:00",
          "CheckOut": "2023-06-01T17:00:00",
          "WorkHours": "08:45:00",
          "Status": "Present",
          "Notes": null
        }
      ],
      "ErrorMessage": null
    }
  ],
  "ErrorMessage": null
}
```

### Error Response
```json
{
  "Attendances": [],
  "ErrorMessage": "Error: Invalid date format"
}
```

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept GET requests with URL parameters
- Parse the department code and date range from the request
- Validate date format and convert to datetime objects

### Response DTO
- Create models to represent the AttendanceListDTO, AttendanceDTO, EmployeeDTO, and AttendanceLogDTO
- Include error message fields for reporting issues

### Logic
- Retrieve employees filtered by department code
- For each employee, retrieve attendance records within the date range
- Map each employee's data to an EmployeeDTO
- Map each attendance record to an AttendanceLogDTO
- Calculate work hours for each day
- Determine attendance status based on business rules
- Create an AttendanceDTO for each employee with their logs
- Return the complete attendance data for filtered employees

### Response Format
- Return JSON representation of AttendanceListDTO
- Include employee information for each employee
- Include all attendance logs within the date range for each employee
- Set ErrorMessage field if an error occurs

### Error Handling
- Handle invalid date formats
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Notes
- This endpoint is used to retrieve attendance records for employees in a specific department within a date range
- The department is identified by its code
- The date range must be specified in the format "ddMMyyyy"
- The response includes both employee information and attendance logs for each employee
- Work hours should be calculated based on check-in and check-out times
- Attendance status should be determined based on company policies (e.g., late threshold)
- The comparison should be exact for department codes
- The endpoint should return an empty list if no employees are found in the specified department
- This endpoint is similar to GetEmployeesAttendanceByDeptName but uses department code instead of name
