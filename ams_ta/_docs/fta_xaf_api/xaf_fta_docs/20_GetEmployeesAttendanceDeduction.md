# GetEmployeesAttendanceDeduction Endpoint

## Endpoint Information
- **URL**: `/GetEmployeesAttendanceDeduction`
- **Method**: GET
- **Description**: Retrieve attendance deduction information for an employee within a date range

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebGet(BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
AttendanceDeductionListDTO GetEmployeesAttendanceDeduction(String empNo, String dateFrom, String dateTo);
```

### Request
- **Type**: URL Parameters
- **Parameters**:
  - `empNo` - The employee number (HRCode) to search for
  - `dateFrom` - Start date in format "ddMMyyyy" (e.g., "01062023" for June 1, 2023)
  - `dateTo` - End date in format "ddMMyyyy" (e.g., "30062023" for June 30, 2023)

### Response
- **Type**: `AttendanceDeductionListDTO`
- **Content-Type**: application/json

#### AttendanceDeductionListDTO Structure
```csharp
public class AttendanceDeductionListDTO
{
    public EmployeeDTO Employee { get; set; }                        // Employee information
    public List<AttendanceDeductionDTO> Deductions { get; set; }     // List of attendance deductions
    public string ErrorMessage { get; set; }                         // Error message if any
}
```

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    // Other employee properties...
}
```

#### AttendanceDeductionDTO Structure
```csharp
public class AttendanceDeductionDTO
{
    public DateTime Date { get; set; }          // Date of the deduction
    public string DeductionType { get; set; }   // Type of deduction (e.g., Late, Early Leave, Absence)
    public TimeSpan DeductionTime { get; set; } // Amount of time deducted
    public decimal DeductionAmount { get; set; } // Monetary amount deducted
    public string Reason { get; set; }          // Reason for the deduction
    public string Status { get; set; }          // Status of the deduction (e.g., Pending, Approved, Rejected)
    // Other deduction properties...
}
```

### Implementation Logic
The implementation performs the following operations:
1. Logs the request with `ftaInt.LogRequest()`
2. Parses the date strings to DateTime objects using the format "ddMMyyyy"
3. Calls the `GetEmployeesAttendanceDeduction` method with the employee number and date range
4. The `GetEmployeesAttendanceDeduction` method:
   - Creates an object space for database operations
   - Finds the employee by employee number
   - Retrieves attendance records for the employee within the date range
   - Calculates deductions based on attendance policies
   - Maps the employee data to an EmployeeDTO
   - Maps each deduction to an AttendanceDeductionDTO
   - Adds the AttendanceDeductionDTOs to the Deductions list in the AttendanceDeductionListDTO
   - Returns the AttendanceDeductionListDTO

### Example Request
```
GET /GetEmployeesAttendanceDeduction?empNo=EMP001&dateFrom=01062023&dateTo=30062023
```

### Example Response
```json
{
  "Employee": {
    "EmpNo": "EMP001",
    "UserName": "john.doe",
    "ArabicName": "جون دو",
    "EnglishName": "John Doe",
    "Email": "<EMAIL>"
  },
  "Deductions": [
    {
      "Date": "2023-06-05T00:00:00",
      "DeductionType": "Late",
      "DeductionTime": "00:30:00",
      "DeductionAmount": 25.50,
      "Reason": "Late arrival",
      "Status": "Approved"
    },
    {
      "Date": "2023-06-12T00:00:00",
      "DeductionType": "Early Leave",
      "DeductionTime": "01:00:00",
      "DeductionAmount": 50.00,
      "Reason": "Early departure",
      "Status": "Approved"
    },
    {
      "Date": "2023-06-20T00:00:00",
      "DeductionType": "Absence",
      "DeductionTime": "08:00:00",
      "DeductionAmount": 200.00,
      "Reason": "Unauthorized absence",
      "Status": "Pending"
    }
  ],
  "ErrorMessage": null
}
```

### Error Response
```json
{
  "Employee": null,
  "Deductions": null,
  "ErrorMessage": "Error: Employee EMP001 doesn't exist!!"
}
```

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept GET requests with URL parameters
- Parse the employee number and date range from the request
- Validate date format and convert to datetime objects

### Response DTO
- Create models to represent the AttendanceDeductionListDTO, EmployeeDTO, and AttendanceDeductionDTO
- Include error message fields for reporting issues

### Logic
- Find the employee by employee number
- Retrieve attendance records for the employee within the date range
- Calculate deductions based on attendance policies:
  - Late arrivals
  - Early departures
  - Absences
  - Other policy violations
- Map the employee data to the response DTO
- Map each deduction to an AttendanceDeductionDTO
- Return the complete deduction data

### Response Format
- Return JSON representation of AttendanceDeductionListDTO
- Include employee information
- Include all deductions within the date range
- Set ErrorMessage field if an error occurs

### Error Handling
- Handle case where employee is not found
- Handle invalid date formats
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Notes
- This endpoint is used to retrieve attendance deduction information for a specific employee within a date range
- The employee is identified by their employee number (HRCode)
- The date range must be specified in the format "ddMMyyyy"
- The response includes both employee information and deduction details
- Deduction calculations should be based on company attendance policies
- Different types of deductions (late, early leave, absence) should be calculated separately
- Monetary amounts should be calculated based on the employee's salary and company policy
