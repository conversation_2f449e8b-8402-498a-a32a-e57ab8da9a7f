# GetAttByEmpNo Endpoint

## Endpoint Information
- **URL**: `/GetAttByEmpNo`
- **Method**: GET
- **Description**: Retrieve attendance data for SharePoint integration by employee number

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebGet(BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
AttendanceDTO GetAttByEmpNo(string EmpNo, string dateFrom, string dateTo);
```

### Request
- **Type**: URL Parameters
- **Parameters**:
  - `EmpNo` - The employee number (HRCode) to search for
  - `dateFrom` - Start date in format "ddMMyyyy" (e.g., "01062023" for June 1, 2023)
  - `dateTo` - End date in format "ddMMyyyy" (e.g., "30062023" for June 30, 2023)

### Response
- **Type**: `AttendanceDTO`
- **Content-Type**: application/json

#### AttendanceDTO Structure
```csharp
public class AttendanceDTO
{
    public EmployeeDTO Employee { get; set; }                // Employee information
    public List<AttendanceLogDTO> Logs { get; set; }         // List of attendance logs
    public string ErrorMessage { get; set; }                 // Error message if any
}
```

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    // Other employee properties...
}
```

#### AttendanceLogDTO Structure
```csharp
public class AttendanceLogDTO
{
    public DateTime Date { get; set; }          // Date of the attendance record
    public DateTime? CheckIn { get; set; }      // Check-in time
    public DateTime? CheckOut { get; set; }     // Check-out time
    public TimeSpan? WorkHours { get; set; }    // Total work hours
    public string Status { get; set; }          // Attendance status (e.g., Present, Absent, Late)
    public string Notes { get; set; }           // Additional notes
    // Other attendance properties...
}
```

### Implementation Logic
The implementation performs the following operations:
1. Parses the date strings to DateTime objects using the format "ddMMyyyy"
2. Calls the `GetEmployeeAttendance` method with the employee number and date range
3. The `GetEmployeeAttendance` method:
   - Creates an object space for database operations
   - Finds the employee by employee number
   - Retrieves attendance records for the employee within the date range
   - Maps the employee data to an EmployeeDTO
   - Maps each attendance record to an AttendanceLogDTO
   - Adds the AttendanceLogDTOs to the Logs list in the AttendanceDTO
   - Returns the AttendanceDTO

### Example Request
```
GET /GetAttByEmpNo?EmpNo=EMP001&dateFrom=01062023&dateTo=30062023
```

### Example Response
```json
{
  "Employee": {
    "EmpNo": "EMP001",
    "UserName": "john.doe",
    "ArabicName": "جون دو",
    "EnglishName": "John Doe",
    "Email": "<EMAIL>"
  },
  "Logs": [
    {
      "Date": "2023-06-01T00:00:00",
      "CheckIn": "2023-06-01T08:30:00",
      "CheckOut": "2023-06-01T17:15:00",
      "WorkHours": "08:45:00",
      "Status": "Present",
      "Notes": null
    },
    {
      "Date": "2023-06-02T00:00:00",
      "CheckIn": "2023-06-02T08:45:00",
      "CheckOut": "2023-06-02T17:30:00",
      "WorkHours": "08:45:00",
      "Status": "Present",
      "Notes": null
    }
  ],
  "ErrorMessage": null
}
```

### Error Response
```json
{
  "Employee": null,
  "Logs": null,
  "ErrorMessage": "Employee EMP001 doesn't exist!!"
}
```

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept GET requests with URL parameters
- Parse the employee number and date range from the request
- Validate date format and convert to datetime objects

### Response DTO
- Create models to represent the AttendanceDTO, EmployeeDTO, and AttendanceLogDTO
- Include error message fields for reporting issues

### Logic
- Find the employee by employee number
- Retrieve attendance records for the employee within the date range
- Map the employee data to the response DTO
- Map each attendance record to an AttendanceLogDTO
- Calculate work hours for each day
- Determine attendance status based on business rules
- Return the complete attendance data

### Response Format
- Return JSON representation of AttendanceDTO
- Include employee information
- Include all attendance logs within the date range
- Set ErrorMessage field if an error occurs

### Error Handling
- Handle case where employee is not found
- Handle invalid date formats
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Notes
- This endpoint is specifically designed for SharePoint integration
- It is similar to GetEmployeeAttendanceByEmpNo but with a simplified error handling approach
- The employee is identified by their employee number (HRCode)
- The date range must be specified in the format "ddMMyyyy"
- The response includes both employee information and attendance logs
- Work hours should be calculated based on check-in and check-out times
- Attendance status should be determined based on company policies (e.g., late threshold)
- This endpoint should be optimized for integration with SharePoint workflows
