# MAuthenticate Endpoint

## Endpoint Information
- **URL**: `/MAuthenticate`
- **Method**: POST
- **Description**: Authenticate mobile user and return employee details

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebInvoke(Method = "POST", BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
EmployeeDTO MAuthenticate(AuthDTO auth);
```

### Request
- **Type**: `AuthDTO`
- **Content-Type**: application/json

#### AuthDTO Structure
```csharp
public class AuthDTO
{
    public string Username { get; set; }        // Username for authentication
    public string Password { get; set; }        // Password for authentication
    public string DeviceID { get; set; }        // Unique identifier for the mobile device
    public string DeviceName { get; set; }      // Name of the mobile device
    public string AppVersion { get; set; }      // Version of the mobile application
    public string Platform { get; set; }        // Mobile platform (e.g., iOS, Android)
    // Other authentication properties...
}
```

### Response
- **Type**: `EmployeeDTO`
- **Content-Type**: application/json

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    public string DeptArabicName { get; set; }  // Department name in Arabic
    public string DeptEnglishName { get; set; } // Department name in English
    public string BGArabicName { get; set; }    // Business group name in Arabic
    public string BGEnglishName { get; set; }   // Business group name in English
    public string BranchArabicName { get; set; } // Branch name in Arabic
    public string BranchEnglishName { get; set; } // Branch name in English
    public string AreaArabicName { get; set; }   // Area name in Arabic
    public string AreaEnglishName { get; set; }  // Area name in English
    public string ErrorMessage { get; set; }     // Error message if any
}
```

### Implementation Logic
The implementation performs the following operations:
1. Calls the `MAuthenticate` method with the authentication data
2. The `MAuthenticate` method:
   - Validates the username and password
   - Verifies the device information
   - Logs the authentication attempt
   - If successful, retrieves the employee information
   - Maps the employee data to an EmployeeDTO
   - Returns the EmployeeDTO with employee details
   - If unsuccessful, returns an EmployeeDTO with an error message

### Example Request
```json
{
  "Username": "john.doe",
  "Password": "password123",
  "DeviceID": "DEVICE001",
  "DeviceName": "iPhone 13",
  "AppVersion": "1.2.3",
  "Platform": "iOS"
}
```

### Example Response
```json
{
  "EmpNo": "EMP001",
  "UserName": "john.doe",
  "ArabicName": "جون دو",
  "EnglishName": "John Doe",
  "Email": "<EMAIL>",
  "DeptArabicName": "قسم تكنولوجيا المعلومات",
  "DeptEnglishName": "IT Department",
  "BGArabicName": "تكنولوجيا",
  "BGEnglishName": "Technology",
  "BranchArabicName": "الفرع الرئيسي",
  "BranchEnglishName": "Main Branch",
  "AreaArabicName": "المقر الرئيسي",
  "AreaEnglishName": "Headquarters",
  "ErrorMessage": null
}
```

### Error Response
```json
{
  "EmpNo": null,
  "UserName": null,
  "ArabicName": null,
  "EnglishName": null,
  "Email": null,
  "DeptArabicName": null,
  "DeptEnglishName": null,
  "BGArabicName": null,
  "BGEnglishName": null,
  "BranchArabicName": null,
  "BranchEnglishName": null,
  "AreaArabicName": null,
  "AreaEnglishName": null,
  "ErrorMessage": "Invalid username or password"
}
```

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept POST requests with JSON body
- Parse the authentication data from the request body

### Request DTO
- Create a model to represent the AuthDTO with all required fields
- Include validation for required fields

### Response DTO
- Create a model to represent the EmployeeDTO with all fields
- Include error message field for reporting issues

### Logic
- Validate the username and password against Odoo's authentication system
- Verify the device information
- Log the authentication attempt
- If successful, retrieve the employee information
- Map the employee data to the response DTO
- Include related entity information (department, business group, branch, area)
- Return the complete employee data
- If unsuccessful, return an error message

### Response Format
- Return JSON representation of EmployeeDTO
- Include all fields, even if null
- Set ErrorMessage field if an error occurs

### Security Considerations
- Implement proper authentication mechanisms
- Validate device information
- Log authentication attempts for security auditing
- Consider implementing rate limiting to prevent brute force attacks
- Ensure secure transmission of credentials (HTTPS)

### Notes
- This endpoint is specifically designed for mobile application authentication
- It returns detailed employee information upon successful authentication
- The response includes all related entity information for the employee
- This endpoint should be secured with HTTPS to protect credentials
- Consider implementing token-based authentication for subsequent requests
- Device information should be stored for security auditing purposes
