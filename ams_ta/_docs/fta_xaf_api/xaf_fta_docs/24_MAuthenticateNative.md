# MAuthenticateNative Endpoint

## Endpoint Information
- **URL**: `/MAuthenticateNative`
- **Method**: POST
- **Description**: Authenticate mobile user for native apps

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebInvoke(Method = "POST", BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
String MAuthenticateNative(AuthDTO auth);
```

### Request
- **Type**: `AuthDTO`
- **Content-Type**: application/json

#### AuthDTO Structure
```csharp
public class AuthDTO
{
    public string Username { get; set; }        // Username for authentication
    public string Password { get; set; }        // Password for authentication
    public string DeviceID { get; set; }        // Unique identifier for the mobile device
    public string DeviceName { get; set; }      // Name of the mobile device
    public string AppVersion { get; set; }      // Version of the mobile application
    public string Platform { get; set; }        // Mobile platform (e.g., iOS, Android)
    // Other authentication properties...
}
```

### Response
- **Type**: `string`
- **Format**: Plain text
- **Success Response**: String containing employee information or success token
- **Error Response**: String starting with "Error:" followed by error details

### Implementation Logic
The implementation performs the following operations:
1. Calls the `Authenticate` method with the authentication data and `true` for native mode
2. The `Authenticate` method:
   - Validates the username and password
   - Verifies the device information
   - Logs the authentication attempt
   - If successful, returns a string with employee information or success token
   - If unsuccessful, returns an error message

### Example Request
```json
{
  "Username": "john.doe",
  "Password": "password123",
  "DeviceID": "DEVICE001",
  "DeviceName": "Samsung Galaxy S21",
  "AppVersion": "1.2.3",
  "Platform": "Android"
}
```

### Example Response
- Success: `"EMP001|John Doe|IT Department"`
- Error: `"Error: Invalid username or password"`

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept POST requests with JSON body
- Parse the authentication data from the request body

### Request DTO
- Create a model to represent the AuthDTO with all required fields
- Include validation for required fields

### Logic
- Validate the username and password against Odoo's authentication system
- Verify the device information
- Log the authentication attempt
- If successful, format a string with employee information
- If unsuccessful, return an error message

### Response Format
- Return a simple string response
- For success, include key employee information in a pipe-delimited format
- For errors, prefix with "Error:" followed by the error message

### Security Considerations
- Implement proper authentication mechanisms
- Validate device information
- Log authentication attempts for security auditing
- Consider implementing rate limiting to prevent brute force attacks
- Ensure secure transmission of credentials (HTTPS)

### Notes
- This endpoint is specifically designed for native mobile applications
- It returns a simplified string response instead of a JSON object
- The response format is optimized for native app parsing
- This endpoint should be secured with HTTPS to protect credentials
- Consider implementing token-based authentication for subsequent requests
- Device information should be stored for security auditing purposes
- This endpoint is similar to MAuthenticate but with a different response format for native apps
