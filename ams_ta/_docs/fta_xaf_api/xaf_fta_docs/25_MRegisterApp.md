# MRegisterApp Endpoint

## Endpoint Information
- **URL**: `/MRegisterApp`
- **Method**: POST
- **Description**: Register a mobile device/app

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebInvoke(Method = "POST", BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
String MRegisterApp(DeviceDTO device);
```

### Request
- **Type**: `DeviceDTO`
- **Content-Type**: application/json

#### DeviceDTO Structure
```csharp
public class DeviceDTO
{
    public string DeviceID { get; set; }        // Unique identifier for the mobile device
    public string DeviceName { get; set; }      // Name of the mobile device
    public string AppVersion { get; set; }      // Version of the mobile application
    public string Platform { get; set; }        // Mobile platform (e.g., iOS, Android)
    public string OSVersion { get; set; }       // Operating system version
    public string UserName { get; set; }        // Username associated with the device
    public string PushToken { get; set; }       // Push notification token
    public string ErrorMessage { get; set; }    // Error message if any
    // Other device properties...
}
```

### Response
- **Type**: `string`
- **Format**: Plain text
- **Success Response**: "1" indicating success
- **Error Response**: String starting with "Error:" followed by error details

### Implementation Logic
The implementation performs the following operations:
1. Calls the `RegisterApp` method with the device data
2. The `RegisterApp` method:
   - Validates the device information
   - Creates or updates a device registration record
   - Associates the device with the specified user
   - Stores the push notification token
   - Returns "1" if successful
   - Returns an error message if unsuccessful

### Example Request
```json
{
  "DeviceID": "DEVICE001",
  "DeviceName": "iPhone 13",
  "AppVersion": "1.2.3",
  "Platform": "iOS",
  "OSVersion": "15.4",
  "UserName": "john.doe",
  "PushToken": "examplePushNotificationToken123456789"
}
```

### Example Response
- Success: `"1"`
- Error: `"Error: Invalid device information"`

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept POST requests with JSON body
- Parse the device data from the request body

### Request DTO
- Create a model to represent the DeviceDTO with all required fields
- Include validation for required fields

### Logic
- Validate the device information
- Create or update a device registration record
- Associate the device with the specified user
- Store the push notification token
- Return success or error message

### Response Format
- Return a simple string response
- Return "1" for success
- Return error message with details for failures

### Error Handling
- Validate input data
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Security Considerations
- Validate device information
- Ensure the user exists before associating a device
- Consider implementing device verification mechanisms
- Log device registrations for security auditing

### Notes
- This endpoint is used to register mobile devices for the application
- It stores device information for push notifications and security purposes
- The device is associated with a specific user
- Push notification tokens should be stored securely
- Device information should be updated when the app is reinstalled or updated
- This endpoint is typically called during the initial setup of the mobile application
- Consider implementing a limit on the number of devices per user for security reasons
