# MAddAttendanceNative Endpoint

## Endpoint Information
- **URL**: `/MAddAttendanceNative`
- **Method**: POST
- **Description**: Record attendance from native mobile app

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebInvoke(Method = "POST", BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
String MAddAttendanceNative(EmpAttendanceLogDTO log);
```

### Request
- **Type**: `EmpAttendanceLogDTO`
- **Content-Type**: application/json

#### EmpAttendanceLogDTO Structure
```csharp
public class EmpAttendanceLogDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public DateTime LogTime { get; set; }       // Time of the attendance log
    public string LogType { get; set; }         // Type of log (e.g., "IN", "OUT")
    public string DeviceID { get; set; }        // ID of the device that recorded the attendance
    public string DeviceName { get; set; }      // Name of the device that recorded the attendance
    public string Location { get; set; }        // Location where the attendance was recorded
    public double Latitude { get; set; }        // Latitude coordinate of the attendance location
    public double Longitude { get; set; }       // Longitude coordinate of the attendance location
    public string Notes { get; set; }           // Additional notes
    public string ErrorMessage { get; set; }    // Error message if any
}
```

### Response
- **Type**: `string`
- **Format**: Plain text
- **Success Response**: "1" indicating success
- **Error Response**: String starting with "Error:" followed by error details

### Implementation Logic
The implementation performs the following operations:
1. Calls the `AddAttendanceLog` method with the attendance log data and `true` for native mode
2. The `AddAttendanceLog` method:
   - Validates the attendance log data
   - Finds the employee by username or employee number
   - Creates a new attendance log record
   - Sets the log time, type, device information, location, and other details
   - Commits the changes to the database
   - Returns "1" if successful
   - Returns an error message if unsuccessful

### Example Request
```json
{
  "EmpNo": "EMP001",
  "UserName": "john.doe",
  "LogTime": "2023-06-15T08:30:00",
  "LogType": "IN",
  "DeviceID": "DEVICE001",
  "DeviceName": "Samsung Galaxy S21",
  "Location": "Office Entrance",
  "Latitude": 24.7136,
  "Longitude": 46.6753,
  "Notes": "Regular check-in"
}
```

### Example Response
- Success: `"1"`
- Error: `"Error: Employee not found"`

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept POST requests with JSON body
- Parse the attendance log data from the request body

### Request DTO
- Create a model to represent the EmpAttendanceLogDTO with all required fields
- Include validation for required fields

### Logic
- Validate the attendance log data
- Find the employee by username or employee number
- Create a new attendance log record
- Set the log time, type, device information, location, and other details
- Commit the changes to the database
- Return success or error message

### Response Format
- Return a simple string response
- Return "1" for success
- Return error message with details for failures

### Error Handling
- Validate input data
- Handle case where employee is not found
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Location Handling
- Store latitude and longitude coordinates
- Consider implementing geofencing to validate attendance location
- Calculate distance from designated work locations
- Flag attendance records that are outside of approved locations

### Notes
- This endpoint is specifically designed for native mobile applications
- It is similar to MAddAttendance but optimized for native app integration
- It includes location data (latitude and longitude) for location verification
- Either Username or EmpNo must be provided to identify the employee
- The LogTime should be in a standard datetime format
- The LogType typically indicates whether this is a check-in or check-out event
- Device information helps track where the attendance was recorded
- The endpoint should validate that the employee exists before creating the log
- Consider implementing additional validation for location-based attendance
