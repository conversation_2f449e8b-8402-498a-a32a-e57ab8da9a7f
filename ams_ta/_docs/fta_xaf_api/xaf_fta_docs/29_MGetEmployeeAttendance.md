# MGetEmployeeAttendance Endpoint

## Endpoint Information
- **URL**: `/MGetEmployeeAttendance`
- **Method**: POST
- **Description**: Get detailed attendance data for mobile app

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebInvoke(Method = "POST", BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
AttendanceDTO MGetEmployeeAttendance(AttendanceRequestDTO request);
```

### Request
- **Type**: `AttendanceRequestDTO`
- **Content-Type**: application/json

#### AttendanceRequestDTO Structure
```csharp
public class AttendanceRequestDTO
{
    public string UserName { get; set; }        // Username for authentication
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string DateFrom { get; set; }        // Start date in format "ddMMyyyy"
    public string DateTo { get; set; }          // End date in format "ddMMyyyy"
    public string DeviceID { get; set; }        // ID of the device making the request
    public string ErrorMessage { get; set; }    // Error message if any
}
```

### Response
- **Type**: `AttendanceDTO`
- **Content-Type**: application/json

#### AttendanceDTO Structure
```csharp
public class AttendanceDTO
{
    public EmployeeDTO Employee { get; set; }                // Employee information
    public List<AttendanceLogDTO> Logs { get; set; }         // List of attendance logs
    public string ErrorMessage { get; set; }                 // Error message if any
}
```

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    // Other employee properties...
}
```

#### AttendanceLogDTO Structure
```csharp
public class AttendanceLogDTO
{
    public DateTime Date { get; set; }          // Date of the attendance record
    public DateTime? CheckIn { get; set; }      // Check-in time
    public DateTime? CheckOut { get; set; }     // Check-out time
    public TimeSpan? WorkHours { get; set; }    // Total work hours
    public string Status { get; set; }          // Attendance status (e.g., Present, Absent, Late)
    public string Notes { get; set; }           // Additional notes
    public string Location { get; set; }        // Location where the attendance was recorded
    public double Latitude { get; set; }        // Latitude coordinate of the attendance location
    public double Longitude { get; set; }       // Longitude coordinate of the attendance location
    // Other attendance properties...
}
```

### Implementation Logic
The implementation performs the following operations:
1. Parses the date strings to DateTime objects using the format "ddMMyyyy"
2. Determines whether to use username or employee number based on the provided values
3. Calls the `GetEmployeeAttendance` method with the appropriate identifier, date range, and filter type
4. The `GetEmployeeAttendance` method:
   - Creates an object space for database operations
   - Finds the employee by the specified identifier
   - Retrieves attendance records for the employee within the date range
   - Maps the employee data to an EmployeeDTO
   - Maps each attendance record to an AttendanceLogDTO
   - Adds the AttendanceLogDTOs to the Logs list in the AttendanceDTO
   - Returns the AttendanceDTO

### Example Request
```json
{
  "UserName": "john.doe",
  "EmpNo": "",
  "DateFrom": "01062023",
  "DateTo": "30062023",
  "DeviceID": "DEVICE001"
}
```

### Example Response
```json
{
  "Employee": {
    "EmpNo": "EMP001",
    "UserName": "john.doe",
    "ArabicName": "جون دو",
    "EnglishName": "John Doe",
    "Email": "<EMAIL>"
  },
  "Logs": [
    {
      "Date": "2023-06-01T00:00:00",
      "CheckIn": "2023-06-01T08:30:00",
      "CheckOut": "2023-06-01T17:15:00",
      "WorkHours": "08:45:00",
      "Status": "Present",
      "Notes": null,
      "Location": "Office Entrance",
      "Latitude": 24.7136,
      "Longitude": 46.6753
    },
    {
      "Date": "2023-06-02T00:00:00",
      "CheckIn": "2023-06-02T08:45:00",
      "CheckOut": "2023-06-02T17:30:00",
      "WorkHours": "08:45:00",
      "Status": "Present",
      "Notes": null,
      "Location": "Office Entrance",
      "Latitude": 24.7136,
      "Longitude": 46.6753
    }
  ],
  "ErrorMessage": null
}
```

### Error Response
```json
{
  "Employee": null,
  "Logs": null,
  "ErrorMessage": "Error: Employee john.doe doesn't exist!!"
}
```

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept POST requests with JSON body
- Parse the attendance request data from the request body
- Validate date format and convert to datetime objects

### Request DTO
- Create a model to represent the AttendanceRequestDTO with all required fields
- Include validation for required fields

### Response DTO
- Create models to represent the AttendanceDTO, EmployeeDTO, and AttendanceLogDTO
- Include error message fields for reporting issues

### Logic
- Determine whether to use username or employee number based on the provided values
- Find the employee by the specified identifier
- Retrieve attendance records for the employee within the date range
- Map the employee data to the response DTO
- Map each attendance record to an AttendanceLogDTO
- Include location data (latitude, longitude) in the response
- Calculate work hours for each day
- Determine attendance status based on business rules
- Return the complete attendance data

### Response Format
- Return JSON representation of AttendanceDTO
- Include employee information
- Include all attendance logs within the date range
- Include location data for each attendance record
- Set ErrorMessage field if an error occurs

### Error Handling
- Handle case where employee is not found
- Handle invalid date formats
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Notes
- This endpoint is specifically designed for mobile applications
- It is similar to MGetAtt but uses POST method and accepts a request DTO
- The employee can be identified by either username or employee number
- The date range must be specified in the format "ddMMyyyy"
- The response includes both employee information and attendance logs
- Work hours should be calculated based on check-in and check-out times
- Attendance status should be determined based on company policies (e.g., late threshold)
- Location data should be included for each attendance record
- The endpoint should be optimized for mobile app consumption
- Device information should be validated for security purposes
