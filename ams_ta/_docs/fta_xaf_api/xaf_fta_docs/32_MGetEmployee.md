# MGetEmployee Endpoint

## Endpoint Information
- **URL**: `/MGetEmployee`
- **Method**: POST
- **Description**: Get employee details for mobile app

## Implementation Details

### Service Method Signature
```csharp
[OperationContract]
[WebInvoke(Method = "POST", BodyStyle = WebMessageBodyStyle.Bare, ResponseFormat = WebMessageFormat.Json, RequestFormat = WebMessageFormat.Json)]
EmployeeDTO MGetEmployee(EmployeeRequestDTO request);
```

### Request
- **Type**: `EmployeeRequestDTO`
- **Content-Type**: application/json

#### EmployeeRequestDTO Structure
```csharp
public class EmployeeRequestDTO
{
    public string UserName { get; set; }        // Username for authentication
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string DeviceID { get; set; }        // ID of the device making the request
    public string ErrorMessage { get; set; }    // Error message if any
}
```

### Response
- **Type**: `EmployeeDTO`
- **Content-Type**: application/json

#### EmployeeDTO Structure
```csharp
public class EmployeeDTO
{
    public string EmpNo { get; set; }           // Employee number (HRCode)
    public string UserName { get; set; }        // Username for authentication
    public string ArabicName { get; set; }      // Employee name in Arabic
    public string EnglishName { get; set; }     // Employee name in English
    public string Email { get; set; }           // Employee email address
    public string DeptArabicName { get; set; }  // Department name in Arabic
    public string DeptEnglishName { get; set; } // Department name in English
    public string BGArabicName { get; set; }    // Business group name in Arabic
    public string BGEnglishName { get; set; }   // Business group name in English
    public string BranchArabicName { get; set; } // Branch name in Arabic
    public string BranchEnglishName { get; set; } // Branch name in English
    public string AreaArabicName { get; set; }   // Area name in Arabic
    public string AreaEnglishName { get; set; }  // Area name in English
    public string ErrorMessage { get; set; }     // Error message if any
}
```

### Implementation Logic
The implementation performs the following operations:
1. Determines whether to use username or employee number based on the provided values
2. Calls the `GetEmployeeInfo` method with the appropriate identifier and filter type
3. The `GetEmployeeInfo` method:
   - Creates an object space for database operations
   - Finds the employee by the specified identifier
   - Maps the employee data to an EmployeeDTO
   - Returns the EmployeeDTO with employee details
   - If the employee is not found, returns an EmployeeDTO with an error message

### Example Request
```json
{
  "UserName": "john.doe",
  "EmpNo": "",
  "DeviceID": "DEVICE001"
}
```

### Example Response
```json
{
  "EmpNo": "EMP001",
  "UserName": "john.doe",
  "ArabicName": "جون دو",
  "EnglishName": "John Doe",
  "Email": "<EMAIL>",
  "DeptArabicName": "قسم تكنولوجيا المعلومات",
  "DeptEnglishName": "IT Department",
  "BGArabicName": "تكنولوجيا",
  "BGEnglishName": "Technology",
  "BranchArabicName": "الفرع الرئيسي",
  "BranchEnglishName": "Main Branch",
  "AreaArabicName": "المقر الرئيسي",
  "AreaEnglishName": "Headquarters",
  "ErrorMessage": null
}
```

### Error Response
```json
{
  "EmpNo": null,
  "UserName": null,
  "ArabicName": null,
  "EnglishName": null,
  "Email": null,
  "DeptArabicName": null,
  "DeptEnglishName": null,
  "BGArabicName": null,
  "BGEnglishName": null,
  "BranchArabicName": null,
  "BranchEnglishName": null,
  "AreaArabicName": null,
  "AreaEnglishName": null,
  "ErrorMessage": "Error: Employee john.doe doesn't exist!!"
}
```

## Odoo Implementation Requirements

### Controller
- Create a controller with a route that matches the endpoint
- Accept POST requests with JSON body
- Parse the employee request data from the request body

### Request DTO
- Create a model to represent the EmployeeRequestDTO with all required fields
- Include validation for required fields

### Response DTO
- Create a model to represent the EmployeeDTO with all fields
- Include error message field for reporting issues

### Logic
- Determine whether to use username or employee number based on the provided values
- Find the employee by the specified identifier
- Map the employee data to the response DTO
- Include related entity information (department, business group, branch, area)
- Return the complete employee data
- If the employee is not found, return an error message

### Response Format
- Return JSON representation of EmployeeDTO
- Include all fields, even if null
- Set ErrorMessage field if an error occurs

### Error Handling
- Handle case where employee is not found
- Handle database errors
- Return descriptive error messages
- Log errors for debugging

### Security Considerations
- Validate device information
- Log access attempts for security auditing
- Consider implementing rate limiting to prevent abuse

### Notes
- This endpoint is specifically designed for mobile applications
- It allows retrieving employee information by either username or employee number
- The response includes all related entity information for the employee
- Device information should be validated for security purposes
- This endpoint is similar to GetEmployeeByEmpNo and GetEmployeeByUserName but uses POST method and accepts a request DTO
- The endpoint should be optimized for mobile app consumption
