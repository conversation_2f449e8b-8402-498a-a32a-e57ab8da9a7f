{"info": {"_postman_id": "5e519d31-6d67-45de-8a4b-51d8fb7be0d3", "name": "Employee Attendance API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12899274"}, "item": [{"name": "Get Employee Attendance by Employee Number", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "frontend_lang=en_US", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{amsBaseUrl}}/GetEmployeeAttendanceByEmpNo?empNo=12345&dateFrom=01012025&dateTo=01012026", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeeAttendanceByEmpNo"], "query": [{"key": "empNo", "value": "12345"}, {"key": "dateFrom", "value": "01012025"}, {"key": "dateTo", "value": "01012026"}]}}, "response": []}, {"name": "Get Employee Attendance by Username", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "frontend_lang=en_US", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{amsBaseUrl}}/GetEmployeeAttendanceByUserName?userName=emp1&dateFrom=01012025&dateTo=01012026", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeeAttendanceByUserName"], "query": [{"key": "userName", "value": "emp1"}, {"key": "dateFrom", "value": "01012025"}, {"key": "dateTo", "value": "01012026"}]}}, "response": []}, {"name": "Get All Employees Attendance", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "frontend_lang=en_US", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{amsBaseUrl}}/GetEmployeesAttendance?dateFrom=01012025&dateTo=01022026", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeesAttendance"], "query": [{"key": "dateFrom", "value": "01012025"}, {"key": "dateTo", "value": "01022026"}]}}, "response": []}, {"name": "Get Employees Attendance by Department", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "frontend_lang=en_US", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{amsBaseUrl}}/GetEmployeesAttendanceByDeptName?deptName=1.2.3dept3&dateFrom=01012025&dateTo=01012026", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeesAttendanceByDeptName"], "query": [{"key": "deptName", "value": "1.2.3dept3"}, {"key": "dateFrom", "value": "01012025"}, {"key": "dateTo", "value": "01012026"}]}}, "response": []}, {"name": "GetEmployeesAttendanceDeduction", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployeesAttendanceDeduction?dateFrom=01012024&dateTo=01012026&empNo=12345", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeesAttendanceDeduction"], "query": [{"key": "dateFrom", "value": "01012024"}, {"key": "dateTo", "value": "01012026"}, {"key": "empNo", "value": "12345"}]}}, "response": []}], "auth": {"type": "apikey", "apikey": [{"key": "value", "value": "frontend_lang=en_US", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Add pre-request logic here if needed"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Add test scripts here if needed"], "type": "text/javascript"}}]}