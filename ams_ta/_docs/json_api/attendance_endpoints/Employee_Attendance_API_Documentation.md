<h1 align="center">Employee Attendance API Documentation</h1>

<div align="center">

![Version](https://img.shields.io/badge/Version-1.0.0-blue)
![Status](https://img.shields.io/badge/Status-Stable-success)
![Last Updated](https://img.shields.io/badge/Last%20Updated-2025--05--08-informational)
![Company](https://img.shields.io/badge/Company-Laplace%20Software-orange)

</div>

## 📋 Overview

<div style="padding: 10px 0;">
This document provides comprehensive documentation for the Employee Attendance API endpoints. The API allows clients to retrieve attendance records for employees, search for attendance records by various criteria, and access attendance deduction information.
</div>

## 🔗 Base URL
All API endpoints are relative to the base URL: `{{amsBaseUrl}}`

## 🔐 Authentication

<div style="padding: 5px 0;">
The API uses public authentication with CORS enabled. No specific authentication tokens are required for these endpoints.
</div>

## 📦 Response Format
All responses are in JSON format with consistent field naming using `CamelCase`. Each response includes:

- **ResponseCode**: Status code of the operation (e.g., "200" for success)
- **ResponseMessage**: English description of the operation result
- **ResponseMessageAR**: Arabic description of the operation result
- **ErrorMessage**: Error details if applicable (empty string if no errors)

<div style="margin-top: 10px;"></div>

## 🔍 API Endpoints

<details open>
<summary><strong>Table of Contents</strong></summary>

1. [Get Employee Attendance by Username](#1-get-employee-attendance-by-username-)
2. [Get Employee Attendance by Employee Number](#2-get-employee-attendance-by-employee-number-)
3. [Get All Employees Attendance](#3-get-all-employees-attendance-)
4. [Get Employees Attendance by Department Name](#4-get-employees-attendance-by-department-name-)
5. [Get Employees Attendance Deduction](#5-get-employees-attendance-deduction-)

</details>

<h3 id="1-get-employee-attendance-by-username-">1. Get Employee Attendance by Username 👤</h3>
Retrieves attendance records for a specific employee by their username.

- **Endpoint**: `/GetEmployeeAttendanceByUserName`
- **Method**: `GET`
- **Parameters**:
  - `userName` (required): The username of the employee
  - `dateFrom` (required): Start date in format "ddMMyyyy" (e.g., "01012023" for January 1, 2023)
  - `dateTo` (required): End date in format "ddMMyyyy" (e.g., "31122023" for December 31, 2023)

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeeAttendanceByUserName?userName=emp1&dateFrom=01012023&dateTo=31122025
```

#### Success Response

```json
{
  "ResponseCode": "200",
  "ResponseMessage": "Success",
  "ResponseMessageAR": "تم بنجاح",
  "Emp": {
    "ResponseCode": "200",
    "ResponseMessage": "Success",
    "ResponseMessageAR": "تم بنجاح",
    "EmpNo": "12345",
    "UserName": "emp1",
    "EnglishName": "EMP1",
    "ArabicName": "EMP1",
    "Email": "emp1.com",
    "PhoneNo": "******-555-5556",
    "DeptArabicName": "1.2.3dept3",
    "DeptEnglishName": "1.2.3dept3",
    "BGArabicName": "All Users",
    "BGEnglishName": "All Users",
    "AreaArabicName": "",
    "AreaEnglishName": "",
    "BranchArabicName": "",
    "BranchEnglishName": "",
    "ErrorMessage": ""
  },
  "Logs": [
    {
      "ResponseCode": "200",
      "ResponseMessage": "Success",
      "ResponseMessageAR": "تم بنجاح",
      "Date": "/Date(1741824000000+0000)/",
      "Status": 1,
      "InTime": "15:00:00",
      "OutTime": "15:50:59",
      "RequiredTime": "00:00:00",
      "WorkingTime": "04:00:00",
      "Delay": "00:00:00",
      "Shortage": "00:00:00",
      "Comment": "vacation test",
      "DateString": "13/03/2025 00:00:00",
      "ErrorMessage": ""
    },
    // Additional attendance logs...
  ],
  "ErrorMessage": ""
}
```

#### Error Response - Employee Not Found

```json
{
  "ResponseCode": "404",
  "ResponseMessage": "Employee not found",
  "ResponseMessageAR": "الموظف غير موجود",
  "Emp": null,
  "Logs": [],
  "ErrorMessage": "Error: Employee emp123 doesn't exist!!"
}
```

#### Error Response - Invalid Date Range

```json
{
  "ResponseCode": "100",
  "ResponseMessage": "ValidationError",
  "ResponseMessageAR": "خطأ في التحقق",
  "ErrorMessage": "End Date must be greater than Start Date."
}
```

<h3 id="2-get-employee-attendance-by-employee-number-">2. Get Employee Attendance by Employee Number 🔢</h3>
Retrieves attendance records for a specific employee by their employee number.

- **Endpoint**: `/GetEmployeeAttendanceByEmpNo`
- **Method**: `GET`
- **Parameters**:
  - `empNo` (required): The employee number
  - `dateFrom` (required): Start date in format "ddMMyyyy" (e.g., "01012023" for January 1, 2023)
  - `dateTo` (required): End date in format "ddMMyyyy" (e.g., "31122023" for December 31, 2023)

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeeAttendanceByEmpNo?empNo=12345&dateFrom=01012023&dateTo=31122025
```

#### Success Response
Same format as `/GetEmployeeAttendanceByUserName` endpoint.

<h3 id="3-get-all-employees-attendance-">3. Get All Employees Attendance 👥</h3>
Retrieves attendance records for all employees within a date range.

- **Endpoint**: `/GetEmployeesAttendance`
- **Method**: `GET`
- **Parameters**:
  - `dateFrom` (required): Start date in format "ddMMyyyy" (e.g., "01012023" for January 1, 2023)
  - `dateTo` (required): End date in format "ddMMyyyy" (e.g., "31122023" for December 31, 2023)

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeesAttendance?dateFrom=01012023&dateTo=31122025
```

#### Success Response

```json
{
  "ResponseCode": "200",
  "ResponseMessage": "Successfully retrieved attendance records",
  "ResponseMessageAR": "تم استرجاع سجلات الحضور بنجاح",
  "AttList": [
    {
      "ResponseCode": "200",
      "ResponseMessage": "Success",
      "ResponseMessageAR": "تم بنجاح",
      "Emp": {
        // Employee details
      },
      "Logs": [
        // Attendance logs for this employee
      ],
      "ErrorMessage": ""
    },
    // Additional employees with their attendance logs
  ],
  "ErrorMessage": ""
}
```

<h3 id="4-get-employees-attendance-by-department-name-">4. Get Employees Attendance by Department Name 🏢</h3>
Retrieves attendance records for all employees in a specific department.

- **Endpoint**: `/GetEmployeesAttendanceByDeptName`
- **Method**: `GET`
- **Parameters**:
  - `deptName` (required): The department name to filter by
  - `dateFrom` (required): Start date in format "ddMMyyyy" (e.g., "01012023" for January 1, 2023)
  - `dateTo` (required): End date in format "ddMMyyyy" (e.g., "31122023" for December 31, 2023)

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeesAttendanceByDeptName?deptName=1.2.3dept3&dateFrom=01012023&dateTo=31122025
```

#### Success Response

```json
{
  "ResponseCode": "200",
  "ResponseMessage": "Successfully retrieved attendance records for department 1.2.3dept3",
  "ResponseMessageAR": "تم استرجاع سجلات الحضور بنجاح لقسم 1.2.3dept3",
  "AttList": [
    {
      "ResponseCode": "200",
      "ResponseMessage": "Success",
      "ResponseMessageAR": "تم بنجاح",
      "Emp": {
        // Employee details
      },
      "Logs": [
        // Attendance logs for this employee
      ],
      "ErrorMessage": ""
    },
    // Additional employees with their attendance logs
  ],
  "ErrorMessage": ""
}
```

<h3 id="5-get-employees-attendance-deduction-">5. Get Employees Attendance Deduction 📊</h3>
Retrieves attendance deduction information for employees within a date range.

- **Endpoint**: `/GetEmployeesAttendanceDeduction`
- **Method**: `GET`
- **Parameters**:
  - `dateFrom` (required): Start date in format "ddMMyyyy" (e.g., "01012023" for January 1, 2023)
  - `dateTo` (required): End date in format "ddMMyyyy" (e.g., "31122023" for December 31, 2023)
  - `empNo` (optional): Employee number to filter by

#### Request Example - For Specific Employee

```http
# Request
GET {{amsBaseUrl}}/GetEmployeesAttendanceDeduction?empNo=12345&dateFrom=01012023&dateTo=31122025
```

#### Success Response - For Specific Employee

```json
{
  "ResponseCode": "1",
  "ResponseMessage": "OK",
  "ResponseMessageAR": "تم بنجاح",
  "DeductionList": [
    {
      "ResponseCode": "1",
      "ResponseMessage": "OK",
      "ResponseMessageAR": "تم بنجاح",
      "EmpNo": "12345",
      "DeductionMinutes": 1197,
      "DeductionHours": 19,
      "RequiredMinutes": 1440,
      "ErrorMessage": ""
    }
  ],
  "ErrorMessage": ""
}
```

#### Request Example - For All Employees

```http
# Request
GET {{amsBaseUrl}}/GetEmployeesAttendanceDeduction?dateFrom=01012023&dateTo=31122025
```

#### Success Response - For All Employees

```json
{
  "ResponseCode": "1",
  "ResponseMessage": "OK",
  "ResponseMessageAR": "تم بنجاح",
  "DeductionList": [
    {
      "ResponseCode": "1",
      "ResponseMessage": "OK",
      "ResponseMessageAR": "تم بنجاح",
      "EmpNo": "12345",
      "DeductionMinutes": 1197,
      "DeductionHours": 19,
      "RequiredMinutes": 1440,
      "ErrorMessage": ""
    },
    {
      "ResponseCode": "1",
      "ResponseMessage": "OK",
      "ResponseMessageAR": "تم بنجاح",
      "EmpNo": "67890",
      "DeductionMinutes": 720,
      "DeductionHours": 12,
      "RequiredMinutes": 1440,
      "ErrorMessage": ""
    },
    // Additional employee deduction records...
  ],
  "ErrorMessage": ""
}
```

## 📚 Data Models

The API uses the following data models:

<h3 id="attendancedto">AttendanceDTO</h3>

Represents attendance information for an employee.

```json
{
  "ResponseCode": "200",          // Status code of the operation
  "ResponseMessage": "Success",    // English description of the operation result
  "ResponseMessageAR": "تم بنجاح", // Arabic description of the operation result
  "Emp": {                       // Employee information (EmployeeDTO)
    // See EmployeeDTO model
  },
  "Logs": [                      // Array of attendance logs (AttendanceLogDTO)
    // See AttendanceLogDTO model
  ],
  "ErrorMessage": ""             // Error details if applicable
}
```

<h3 id="attendancelogdto">AttendanceLogDTO</h3>

Represents an individual attendance record.

```json
{
  "ResponseCode": "200",          // Status code of the operation
  "ResponseMessage": "Success",    // English description of the operation result
  "ResponseMessageAR": "تم بنجاح", // Arabic description of the operation result
  "Date": "/Date(1741824000000+0000)/", // Date of the attendance record in .NET format
  "Status": 1,                   // Status code (0=Working Day, 1=Day Off, 2=Absent, 3=Vacation)
  "InTime": "15:00:00",          // Check-in time in HH:mm:ss format
  "OutTime": "15:50:59",         // Check-out time in HH:mm:ss format
  "RequiredTime": "00:00:00",    // Required working time in HH:mm:ss format
  "WorkingTime": "04:00:00",     // Actual working time in HH:mm:ss format
  "Delay": "00:00:00",           // Delay time in HH:mm:ss format
  "Shortage": "00:00:00",        // Shortage time in HH:mm:ss format
  "Comment": "vacation test",    // Comments or notes about the attendance
  "DateString": "13/03/2025 00:00:00", // Human-readable date in dd/MM/yyyy HH:mm:ss format
  "ErrorMessage": ""             // Error details if applicable
}
```

<h3 id="attendancelistdto">AttendanceListDTO</h3>

Represents a list of attendance records for multiple employees.

```json
{
  "ResponseCode": "200",          // Status code of the operation
  "ResponseMessage": "Successfully retrieved attendance records", // English description
  "ResponseMessageAR": "تم استرجاع سجلات الحضور بنجاح", // Arabic description
  "AttList": [                   // Array of AttendanceDTO objects
    // See AttendanceDTO model
  ],
  "ErrorMessage": ""             // Error details if applicable
}
```

<h3 id="attendancedeductiondto">AttendanceDeductionDTO</h3>

Represents attendance deduction information for an employee.

```json
{
  "ResponseCode": "1",           // Status code of the operation
  "ResponseMessage": "OK",       // English description of the operation result
  "ResponseMessageAR": "تم بنجاح", // Arabic description of the operation result
  "EmpNo": "12345",              // Employee number
  "DeductionMinutes": 1197,      // Total deduction in minutes
  "DeductionHours": 19,          // Total deduction in hours
  "RequiredMinutes": 1440,       // Required working minutes
  "ErrorMessage": ""             // Error details if applicable
}
```

<h3 id="attendancedeductionlistdto">AttendanceDeductionListDTO</h3>

Represents a list of attendance deduction records.

```json
{
  "ResponseCode": "1",           // Status code of the operation
  "ResponseMessage": "OK",       // English description of the operation result
  "ResponseMessageAR": "تم بنجاح", // Arabic description of the operation result
  "DeductionList": [             // Array of AttendanceDeductionDTO objects
    // See AttendanceDeductionDTO model
  ],
  "ErrorMessage": ""             // Error details if applicable
}
```

<h3 id="employeedto">EmployeeDTO</h3>

Represents an individual employee record.

```json
{
  "ResponseCode": "200",          // Status code of the operation
  "ResponseMessage": "Success",    // English description of the operation result
  "ResponseMessageAR": "تم بنجاح", // Arabic description of the operation result
  "EmpNo": "12345",              // Employee number/ID
  "UserName": "emp1",            // Employee username
  "EnglishName": "John Doe",      // Employee name in English
  "ArabicName": "جون دو",         // Employee name in Arabic
  "Email": "<EMAIL>", // Employee email address
  "PhoneNo": "+966501234567",    // Employee phone number
  "DeptArabicName": "قسم المبيعات", // Department name in Arabic
  "DeptEnglishName": "Sales",     // Department name in English
  "BGArabicName": "المجموعة أ",    // Business group name in Arabic
  "BGEnglishName": "Group A",     // Business group name in English
  "AreaArabicName": "الرياض",      // Area name in Arabic
  "AreaEnglishName": "Riyadh",    // Area name in English
  "BranchArabicName": "الفرع الرئيسي", // Branch name in Arabic
  "BranchEnglishName": "Main Branch", // Branch name in English
  "ErrorMessage": ""              // Error details if applicable
}
```

<hr style="margin-top: 30px; margin-bottom: 30px;">

<div align="center">

# End of Document

<p style="font-size: 12px; color: #555;">Copyright © 2025 Laplace Software. All rights reserved.</p>

</div>
