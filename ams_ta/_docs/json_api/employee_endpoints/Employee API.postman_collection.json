{"info": {"_postman_id": "7dd1e2a3-87ad-4029-8eb5-ca4d07d9e071", "name": "Employee API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12899274"}, "item": [{"name": "GetEmployeeByUserName", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{amsBaseUrl}}/GetEmployeeByUserName?userName=emp13", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeeByUserName"], "query": [{"key": "userName", "value": "emp13"}]}}, "response": []}, {"name": "/GetEmployees", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployees", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployees"]}}, "response": []}, {"name": "AddEmployee", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"EmpNo\": \"1234567\",\n  \"UserName\": \"sabreen52\",\n  \"EnglishName\": \"sabreen hassan\",\n  \"ArabicName\": \"جون دو\",\n  \"Email\": \"<EMAIL>\",\n  \"PhoneNo\": \"+966501234567\",\n  \"DeptArabicName\": \"الموارد البشرية\",\n  \"DeptEnglishName\": \"Human Resources\",\n  \"BGArabicName\": \"الإدارة العامة\",\n  \"BGEnglishName\": \"General Directorate\",\n  \"AreaArabicName\": \"منطقة الرياض\",\n  \"AreaEnglishName\": \"Riyadh Area\",\n  \"BranchArabicName\": \"فرع العليا\",\n  \"BranchEnglishName\": \"Olaya Branch\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{amsBaseUrl}}/AddEmployee", "host": ["{{amsBaseUrl}}"], "path": ["AddEmployee"], "query": [{"key": "dateFrom", "value": "01012025", "disabled": true}, {"key": "dateTo", "value": "01012026", "disabled": true}]}}, "response": []}, {"name": "/GetEmployeeByEmpNo", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployeeByEmpNo?empNo=12345", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeeByEmpNo"], "query": [{"key": "empNo", "value": "12345"}]}}, "response": []}, {"name": "/GetEmployeesByDeptName", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployeesByDeptName?deptName=1.2.3dept39", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeesByDeptName"], "query": [{"key": "deptName", "value": "1.2.3dept39"}]}}, "response": []}]}