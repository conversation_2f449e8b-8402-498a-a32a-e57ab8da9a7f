<h1 align="center">Employee API Documentation</h1>

<div align="center">

![Version](https://img.shields.io/badge/Version-1.0.0-blue)
![Status](https://img.shields.io/badge/Status-Stable-success)
![Last Updated](https://img.shields.io/badge/Last%20Updated-2025--04--28-informational)
![Company](https://img.shields.io/badge/Company-Laplace%20Software-orange)

</div>

## 📋 Overview

<div style="padding: 10px 0;">
This document provides comprehensive documentation for the Employee API endpoints. The API allows clients to retrieve employee information, search for employees by various criteria, and add new employee records.

</div>

## 🔗 Base URL
All API endpoints are relative to the base URL: `{{amsBaseUrl}}`

## 🔐 Authentication

<div style="padding: 5px 0;">
The API uses public authentication with CORS enabled. No specific authentication tokens are required for these endpoints.

</div>

## 📦 Response Format
All responses are in JSON format with consistent field naming using `CamelCase`. Each response includes:

- **ResponseCode**: Status code of the operation (e.g., "200" for success)
- **ResponseMessage**: English description of the operation result
- **ResponseMessageAR**: Arabic description of the operation result
- **ErrorMessage**: Error details if applicable (empty string if no errors)

<div style="margin-top: 10px;"></div>

## 🔍 API Endpoints

<details open>
<summary><strong>Table of Contents</strong></summary>

1. [Get Employee by Username](#1-get-employee-by-username-)
2. [Get Employee by Employee Number](#2-get-employee-by-employee-number-)
3. [Get Employees by Department Name](#3-get-employees-by-department-name-)
4. [Get All Employees](#4-get-all-employees-)
5. [Add Employee](#5-add-employee-)

</details>

<h3 id="1-get-employee-by-username-">1. Get Employee by Username 👤</h3>
Retrieves employee details by their username.

- **Endpoint**: `/GetEmployeeByUserName`
- **Method**: `GET`
- **Parameters**:
  - `userName` (required): The username of the employee to retrieve

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeeByUserName?userName=emp13
```



#### Success Response



```json
{
  // Success Response
  "ResponseCode": "200",
  "ResponseMessage": "Success",
  "ResponseMessageAR": "تم بنجاح",
  "EmpNo": "12345",
  "UserName": "emp1",
  "EnglishName": "EMP1",
  "ArabicName": "EMP1",
  "Email": "emp1.com",
  "PhoneNo": "******-555-5556",
  "DeptArabicName": "1.2.3dept3",
  "DeptEnglishName": "1.2.3dept3",
  "BGArabicName": "All Users",
  "BGEnglishName": "All Users",
  "AreaArabicName": "",
  "AreaEnglishName": "",
  "BranchArabicName": "",
  "BranchEnglishName": "",
  "ErrorMessage": ""
}
```



#### Error Response



```json
{
  // Error Response
  "ResponseCode": "404",
  "ResponseMessage": "Employee not found",
  "ResponseMessageAR": "الموظف غير موجود",
  "EmpNo": "",
  "UserName": "",
  "EnglishName": "",
  "ArabicName": "",
  "Email": "",
  "PhoneNo": "",
  "DeptArabicName": "",
  "DeptEnglishName": "",
  "BGArabicName": "",
  "BGEnglishName": "",
  "AreaArabicName": "",
  "AreaEnglishName": "",
  "BranchArabicName": "",
  "BranchEnglishName": "",
  "ErrorMessage": "No employee found with username: emp13"
}
```

<h3 id="2-get-employee-by-employee-number-">2. Get Employee by Employee Number 🔢</h3>
Retrieves employee details by their employee number.

- **Endpoint**: `/GetEmployeeByEmpNo`
- **Method**: `GET`
- **Parameters**:
  - `empNo` (required): The employee number to retrieve

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeeByEmpNo?empNo=12345
```

#### Success Response
Same format as `/GetEmployeeByUserName` endpoint.

<h3 id="3-get-employees-by-department-name-">3. Get Employees by Department Name 🏢</h3>
Retrieves all employees under a specific department.

- **Endpoint**: `/GetEmployeesByDeptName`
- **Method**: `GET`
- **Parameters**:
  - `deptName` (required): The department name to filter by

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeesByDeptName?deptName=1.2.3dept39
```



#### Success Response



```json
{
  // Success Response with Employee List
  "ResponseCode": "200",
  "ResponseMessage": "Success",
  "ResponseMessageAR": "تم بنجاح",
  "Employees": [
    {
      "ResponseCode": "200",
      "ResponseMessage": "Success",
      "ResponseMessageAR": "تم بنجاح",
      "EmpNo": "",
      "UserName": "",
      "EnglishName": "Eli Lambert",
      "ArabicName": "Eli Lambert",
      "Email": "<EMAIL>",
      "PhoneNo": "(*************",
      "DeptArabicName": "Sales",
      "DeptEnglishName": "Sales",
      "BGArabicName": "",
      "BGEnglishName": "",
      "AreaArabicName": "",
      "AreaEnglishName": "",
      "BranchArabicName": "",
      "BranchEnglishName": "",
      "ErrorMessage": ""
    },
    // Additional employees...
  ]
}
```



#### Error Response - Department Not Found



```json
{
  // Department Not Found Error
  "ResponseCode": "404",
  "ResponseMessage": "Department not found",
  "ResponseMessageAR": "القسم غير موجود",
  "Employees": [],
  "ErrorMessage": "Department not found"
}
```

<h3 id="4-get-all-employees-">4. Get All Employees 👥</h3>
Retrieves information for all employees in the system.

- **Endpoint**: `/GetEmployees`
- **Method**: `GET`
- **Parameters**: None

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployees
```



#### Success Response



```json
{
  // Success Response with Employee List
  "ResponseCode": "200",
  "ResponseMessage": "Success",
  "ResponseMessageAR": "تم بنجاح",
  "Employees": [
    {
      "ResponseCode": "200",
      "ResponseMessage": "Success",
      "ResponseMessageAR": "تم بنجاح",
      "EmpNo": "",
      "UserName": "",
      "EnglishName": "Abigail Peterson",
      "ArabicName": "Abigail Peterson",
      "Email": "<EMAIL>",
      "PhoneNo": "(*************",
      "DeptArabicName": "Professional Services",
      "DeptEnglishName": "Professional Services",
      "BGArabicName": "",
      "BGEnglishName": "",
      "AreaArabicName": "",
      "AreaEnglishName": "",
      "BranchArabicName": "",
      "BranchEnglishName": "",
      "ErrorMessage": ""
    },
    // Additional employees...
  ]
}
```

<h3 id="5-add-employee-">5. Add Employee ➕</h3>
Creates a new employee record or updates an existing one.

- **Endpoint**: `/AddEmployee`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Request Body**: JSON object with employee details

#### Request Example

```http
# Request
POST {{amsBaseUrl}}/AddEmployee
Content-Type: application/json

{
  "EmpNo": "1234567",
  "UserName": "sabreen52",
  "EnglishName": "sabreen hassan",
  "ArabicName": "جون دو",
  "Email": "<EMAIL>",
  "PhoneNo": "+966501234567",
  "DeptArabicName": "الموارد البشرية",
  "DeptEnglishName": "Human Resources",
  "BGArabicName": "الإدارة العامة",
  "BGEnglishName": "General Directorate",
  "AreaArabicName": "منطقة الرياض",
  "AreaEnglishName": "Riyadh Area",
  "BranchArabicName": "فرع العليا",
  "BranchEnglishName": "Olaya Branch"
}
```



#### Success Response



```json
""
```



#### Error Responses



- **Invalid Input Data**
```json
// Invalid Input Data Error
"Error: invalid input data expected EmployeeDTO"
```

- **Missing Required Fields**
```json
// Missing Required Fields Error
"Error: Missing required fields in EmployeeDTO (emp_no, user_name, or email)"
```

## 📚 Data Models

The API uses the following data models:

<h3 id="employeedto">EmployeeDTO</h3>

Represents an individual employee record.

```json
{
  "ResponseCode": "200",          // Status code of the operation
  "ResponseMessage": "Success",    // English description of the operation result
  "ResponseMessageAR": "تم بنجاح", // Arabic description of the operation result
  "EmpNo": "12345",              // Employee number/ID
  "UserName": "emp1",            // Employee username
  "EnglishName": "John Doe",      // Employee name in English
  "ArabicName": "جون دو",         // Employee name in Arabic
  "Email": "<EMAIL>", // Employee email address
  "PhoneNo": "+966501234567",    // Employee phone number
  "DeptArabicName": "قسم المبيعات", // Department name in Arabic
  "DeptEnglishName": "Sales",     // Department name in English
  "BGArabicName": "المجموعة أ",    // Business group name in Arabic
  "BGEnglishName": "Group A",     // Business group name in English
  "AreaArabicName": "الرياض",      // Area name in Arabic
  "AreaEnglishName": "Riyadh",    // Area name in English
  "BranchArabicName": "الفرع الرئيسي", // Branch name in Arabic
  "BranchEnglishName": "Main Branch", // Branch name in English
  "ErrorMessage": ""              // Error details if applicable
}
```

<h3 id="employeelistdto">EmployeeListDTO</h3>

Represents a list of employee records.

```json
{
  "ResponseCode": "200",          // Status code of the operation
  "ResponseMessage": "Success",    // English description of the operation result
  "ResponseMessageAR": "تم بنجاح", // Arabic description of the operation result
  "Employees": [                  // Array of EmployeeDTO objects
    {
      // EmployeeDTO object 1
    },
    {
      // EmployeeDTO object 2
    }
  ],
  "ErrorMessage": ""              // Error details if applicable
}
```

<hr style="margin-top: 30px; margin-bottom: 30px;">

<div align="center">

# End of Document

<p style="font-size: 12px; color: #555;">Copyright © 2025 Laplace Software. All rights reserved.</p>

</div>
