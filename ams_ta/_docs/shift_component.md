
# Shift
### Steps to apply employee shift
 - create shift rule 
 - create shift unit and link with shift rule
 - create schedule 
 - generate schedule days based on weekly or cycle days
 - link schedule days with shift units
 - create shift and link with schedule and implement it in specified dates
 - create employee group (user group) and link with primary shift and secondary shift

### Shift Rule
    define basic rule to apply on shift units like grace in time , grace out time, half work checkin criteria and half work checkout criteria

### Shift Unit
    * Normal Shift:
        - is normal working time have static start time and end time applied on all employees

    * Flexible Shift:
        - is flexible in checkin time like checkin time is between [start time (10:00 am) , start limit (10:30 am)]
        - should determined duration by hours to calculate end time dynamic on transaction
        - example 
            - if checkin time is 10:00 and duration 8 hours
            - end time should be 18:00 calculated per employee on transaction


    * Open Shift:
        - is open in 24 hours with required working duration 



