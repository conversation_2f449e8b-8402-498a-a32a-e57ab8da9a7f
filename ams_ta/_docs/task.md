
@LP - <PERSON><PERSON><PERSON> 
shift_rule , python constrains to check 
- grace in time >=0 , grace out time >=0
- overtime factor >=0

@LP-a<PERSON><PERSON> 
shift_unit: implement _compute_duration
in case shift_type in [open,normal] & end > start
duration = end - start , is_overnight = False
end < start is_overnight = True

 @api.depends('start_time', 'end_time', 'shift_type')
    def _compute_duration(self):

@a.khiry
from odoo.addons.ta.helper.helper import *
schedule_day: implement method _compute_name
based on its index, week_day, and schedule_id.
If a schedule_id exists, it checks the schedule_type.
If it's 'weekly', it sets the name to weekday (e.g., Monday, Tuesday, etc.).
Otherwise, it sets the name to "Day " followed by the index.



schedule: implement action action_generate_days



    employee: implement methods
    def get_available_employee_shift(self, date):
        """ return shift which date located and return shift based on priority 
        exception  shift >> secondary shift >> primary shift >> default shift"""
        ...


    def _get_schedule_day_index(self, start_date, current_date, cycle_days):  # employee_id, schedule_id ):
        """detect day index in schedule template"""
        ...

    def _get_schedule_day(self, schedule, start_date):
        """detect day record in schedule template"""
        ...


    ams_ta/base_timesheet : implement _compute_required_time_min
    to calculate all fields need to convert to minutes  (8) fields

    required_time_min = fields.Float(string="Required Time (Min)", help="Required Time by minutes",
                                     compute='_compute_time_min',store=True)
    @api.depends('required_time', 'working_time', 'delay_time', 'shortage_time', 'overtime')
    def _compute_time_min(self):
        ...