
class PunchLog(models.Model):
    _inherit = "ams_ta.punch_log"
    
    is_within_geo_fence = fields.Bo<PERSON>an(string="Within Geo-Fence", default=True)
    geo_fence_distance = fields.Float(string="Distance from Geo-Fence (m)", default=0.0)
    nearest_location_id = fields.Many2one('ams_ta.geo_fence_location', string="Nearest Location")
    geo_fence_status = fields.Selection([
        ('valid', 'Valid'),
        ('outside', 'Outside Boundary'),
        ('no_fence', 'No Geo-Fence')
    ], string="Geo-Fence Status", default='valid')





def calculate_distance(lat1, lon1, lat2, lon2):
    """
    Calculate the Haversine distance between two points in meters.
    """
    from math import radians, sin, cos, sqrt, atan2
    
    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    distance = 6371000 * c  # Earth radius is 6371 km, result in meters
    
    return distance

def action_validate_punch_log(self):
    """
    Validate the punch log and update the geo-fence status.
    """
        self._validate_geo_fence_punch_log()

def _validate_geo_fence_punch_log(self):
    """
    Process the punch log with geo-fence validation.
    """
    is_valid_punch = True
    for log in self:
        # Skip geo-fence validation if coordinates are not provided
        if log.latitude == 0 and log.longitude == 0:
            log.geo_fence_status = 'no_fence'
            continue
            
        # Get user group and check if geo-fencing is enabled
        user_group = log.employee_id.user_group_id
        if not user_group or not user_group.apply_geo_fencing:
            log.geo_fence_status = 'no_fence'
        else:
            # Validate against geo-fence locations
            log._validate_geo_fence()
            
            # Check if punch should be rejected based on user group settings
            if log.geo_fence_status == 'outside' and user_group.geo_fence_action == 'reject':
                is_valid_punch = False

        return is_valid_punch

        # Continue with regular punch log processing
        #super(PunchLog, log).action_execute_punch_log()

def _validate_geo_fence(self):
    """
    Validate if the punch location is within any of the allowed geo-fence locations.
    """
    user_group = self.employee_id.user_group_id
    locations = user_group.geo_fence_location_ids.filtered(lambda l: l.is_active)
    
    if not locations:
        self.geo_fence_status = 'no_fence'
        return
    
    min_distance = float('inf')
    nearest_location = None
    is_within_fence = False
    
    # Check distance to each location
    for location in locations:
        distance = calculate_distance(
            self.latitude, self.longitude,
            location.latitude, location.longitude
        )
        
        # Update nearest location if this one is closer
        if distance < min_distance:
            min_distance = distance
            nearest_location = location
            
        # Check if within this location's radius
        if distance <= location.radius:
            is_within_fence = True
            break
    
    # Update punch log with geo-fence validation results
    self.is_within_geo_fence = is_within_fence
    self.geo_fence_distance = min_distance
    self.nearest_location_id = nearest_location.id if nearest_location else False
    self.geo_fence_status = 'valid' if is_within_fence else 'outside'


