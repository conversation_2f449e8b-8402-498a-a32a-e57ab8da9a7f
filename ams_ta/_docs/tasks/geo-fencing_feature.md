# Geo-Fencing Feature Implementation Task

## Overview

This document outlines the implementation plan for adding geo-fencing capabilities to the `ams_ta` module. The feature will allow administrators to define geographical boundaries for each user group and validate whether employee punch logs are made from within authorized locations.

## Business Requirements

1. **Location Validation**: Validate that employees are punching in/out from authorized locations
2. **Configurable Boundaries**: Allow setting multiple geo-fence locations per user group
3. **Flexible Enforcement**: Provide options for handling out-of-bounds punches (warn, reject, flag)
4. **Mobile Integration**: Seamless integration with the mobile app for location capture
5. **Visual Management**: Map-based interface for defining and viewing geo-fence locations
6. **Reporting**: Insights on geo-fence compliance and violations

## System Architecture

```mermaid
flowchart TD
    A[Mobile App] -->|Send Location Data| B[API Endpoints]
    B --> C[Punch Log Processing]
    C --> D{Geo-Fence Validation}
    D -->|Within Boundary| E[Create Timesheet]
    D -->|Outside Boundary| F{Action Policy}
    F -->|Warn| G[Create with Warning]
    F -->|Reject| H[Reject Punch]
    F -->|Flag| I[Flag for Review]

    J[Admin UI] -->|Define| K[Geo-Fence Locations]
    K --> L[User Group Settings]
    L --> D
```

## Data Model Extensions

### 1. User Group Model Extension

Extend the `ams.user_group` model to include:

- Boolean field to enable/disable geo-fencing
- One-to-many relationship to geo-fence locations
- Configuration for tolerance distance
- Selection field for action when outside geo-fence

```mermaid
classDiagram
    class UserGroup {
        +Boolean apply_geo_fencing
        +Float geo_fence_tolerance
        +Selection geo_fence_action
        +One2many geo_fence_location_ids
    }

    class GeoFenceLocation {
        +Char name
        +Float latitude
        +Float longitude
        +Float radius
        +Char address
        +Boolean is_active
        +Char color
        +Many2one user_group_id
    }

    UserGroup "1" -- "many" GeoFenceLocation : has
```

### 2. New Geo-Fence Location Model

Create a new model `ams_ta.geo_fence_location` with:

- Name and address fields
- Latitude and longitude coordinates
- Radius for circular geo-fence
- Active status flag
- Color for visualization
- Relation to user group

### 3. Punch Log Model Extension

Extend the `ams_ta.punch_log` model to include:

- Boolean field for geo-fence compliance
- Float field for distance from nearest location
- Relation to nearest geo-fence location
- Selection field for geo-fence status

```mermaid
classDiagram
    class PunchLog {
        +Boolean is_within_geo_fence
        +Float geo_fence_distance
        +Many2one nearest_location_id
        +Selection geo_fence_status
    }

    class GeoFenceLocation {
        +Char name
        +Float latitude
        +Float longitude
        +Float radius
    }

    PunchLog "many" -- "0..1" GeoFenceLocation : nearest to
```

## Implementation Tasks

### Phase 1: Data Model Implementation

1. **Create Database Structure**
   - Add fields to `ams.user_group` model
   - Create new `ams_ta.geo_fence_location` model
   - Add fields to `ams_ta.punch_log` model
   - Set up proper relations between models

2. **Security Configuration**
   - Define access rights for the new model
   - Create record rules for multi-company support
   - Update existing security groups

### Phase 2: Business Logic Implementation

1. **Distance Calculation**
   - Implement Haversine formula for calculating distance between coordinates
   - Create helper methods for geo-fence validation

2. **Punch Log Processing**
   - Extend punch log creation/execution to include geo-fence validation
   - Implement different actions based on validation results
   - Add proper error handling and messaging

```mermaid
sequenceDiagram
    participant Mobile as Mobile App
    participant API as API Endpoint
    participant Punch as Punch Log
    participant Geo as Geo-Fence Validator
    participant TS as Timesheet

    Mobile->>API: Send punch with coordinates
    API->>Punch: Create punch log
    Punch->>Geo: Validate location

    alt Within geo-fence
        Geo->>Punch: Mark as valid
        Punch->>TS: Create/update timesheet
    else Outside geo-fence
        Geo->>Punch: Mark as outside

        alt Action = Warn
            Punch->>TS: Create/update with warning
        else Action = Reject
            Geo->>API: Return rejection
            API->>Mobile: Show error message
        else Action = Flag
            Punch->>TS: Create/update with flag
        end
    end

    API->>Mobile: Return result
```

### Phase 3: User Interface Development

1. **User Group Form Extension**
   - Add geo-fencing tab to user group form
   - Create fields for enabling and configuring geo-fencing
   - Add embedded view for managing geo-fence locations

2. **Map View for Geo-Fence Locations**
   - Create map view for visualizing locations
   - Implement drawing tools for defining geo-fences
   - Add address lookup functionality

3. **Punch Log Form Extension**
   - Add geo-fencing information to punch log form
   - Display validation results with appropriate styling
   - Show distance and nearest location information

```mermaid
flowchart TD
    A[User Group Form] --> B{Enable Geo-Fencing}
    B -->|Yes| C[Configure Settings]
    C --> D[Manage Locations]
    D --> E[Map View]
    E --> F[Draw Geo-Fence]
    F --> G[Save Location]

    H[Punch Log Form] --> I[Show Geo-Fence Tab]
    I --> J[Display Status]
    J --> K[Show Distance]
    K --> L[Link to Location]
```

### Phase 4: Mobile Integration

1. **API Extension**
   - Update API endpoints to accept location data
   - Return geo-fence validation results
   - Handle rejection scenarios

2. **Mobile App Requirements**
   - Document location permission requirements
   - Specify location capture implementation
   - Define UI for displaying geo-fence validation results

```mermaid
sequenceDiagram
    participant User as User
    participant App as Mobile App
    participant Backend as Odoo Backend

    User->>App: Open attendance screen
    App->>User: Request location permission
    User->>App: Grant permission
    App->>App: Capture GPS coordinates

    User->>App: Punch in/out
    App->>Backend: Send punch with coordinates

    alt Valid Location
        Backend->>App: Confirm success
        App->>User: Show success message
    else Invalid Location
        Backend->>App: Return geo-fence status
        App->>User: Show warning/error
    end
```

### Phase 5: Reporting and Analytics

1. **Compliance Reports**
   - Create report for geo-fence compliance
   - Add filters for date range, department, user group
   - Include visualizations for compliance rates

2. **Violation Analysis**
   - Implement tools for analyzing violation patterns
   - Create heatmap for violation locations
   - Add employee-specific violation reports

## Testing Requirements

1. **Unit Tests**
   - Test distance calculation accuracy
   - Validate geo-fence boundary detection
   - Test different action policies

2. **Integration Tests**
   - Test API with various location scenarios
   - Validate mobile app integration
   - Test multi-location scenarios

3. **User Acceptance Testing**
   - Define test scenarios for administrators
   - Create test plan for mobile users
   - Document expected behaviors for different configurations

## Deployment Considerations

1. **Database Migration**
   - Create migration scripts for existing installations
   - Handle default values for new fields

2. **Mobile App Updates**
   - Coordinate with mobile app development team
   - Plan for app store submission timeline

3. **User Training**
   - Create documentation for administrators
   - Prepare user guides for employees
   - Plan training sessions for HR staff

## Timeline and Milestones

```mermaid
gantt
    title Geo-Fencing Implementation Timeline
    dateFormat  YYYY-MM-DD

    section Planning
    Requirements Analysis    :a1, 2023-11-01, 7d
    Technical Design         :a2, after a1, 7d

    section Development
    Data Model Implementation :b1, after a2, 5d
    Business Logic           :b2, after b1, 10d
    User Interface           :b3, after b2, 7d
    Mobile Integration       :b4, after b3, 7d

    section Testing
    Unit Testing             :c1, after b2, 5d
    Integration Testing      :c2, after b4, 7d
    UAT                      :c3, after c2, 10d

    section Deployment
    Documentation            :d1, after c2, 7d
    Training                 :d2, after d1, 5d
    Production Deployment    :d3, after c3, 3d
```

## Success Criteria

1. Employees can only punch in/out from authorized locations
2. Administrators can easily define and manage geo-fence locations
3. The system provides clear feedback on geo-fence validation
4. Reports provide actionable insights on compliance
5. The feature works seamlessly on both web and mobile interfaces

## Future Enhancements

1. **Advanced Geo-Fencing**
   - Support for polygon-shaped geo-fences
   - Time-based geo-fence activation
   - Dynamic geo-fences based on job assignments

2. **Integration with Maps**
   - Real-time employee location tracking
   - Route optimization for mobile workers
   - Traffic-aware attendance policies

3. **Advanced Analytics**
   - Predictive analysis for compliance issues
   - Machine learning for detecting unusual patterns
   - Integration with performance metrics