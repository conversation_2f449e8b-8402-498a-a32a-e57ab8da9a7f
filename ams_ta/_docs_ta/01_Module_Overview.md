# AMS Time Attendance (ams_ta) Module Overview

## Introduction

The AMS Time Attendance (`ams_ta`) module is a comprehensive solution for managing employee attendance, timesheets, and work schedules. It provides functionality for tracking check-ins and check-outs, calculating working hours, managing shifts, and generating attendance reports.

## Module Architecture Diagram

```mermaid
flowchart TD
    subgraph Core Components
        direction TB
        Models[Data Models]-->BusinessLogic[Business Logic]
        BusinessLogic-->Presentation[Presentation Layer]
        BusinessLogic-->API[API Layer]
    end

    subgraph Data Models
        direction TB
        Timesheet[Timesheet]-->TimeUnit[Timesheet Time Unit]
        PunchLog[Punch Log]-->Timesheet
        Shift[Shift]-->Timesheet
        TimeUnitRule[Time Unit Rule]-->TimeUnit
    end

    subgraph Business Logic
        direction TB
        TimeCalculation[Time Calculation]
        ShiftManagement[Shift Management]
        AttendanceProcessing[Attendance Processing]
    end

    subgraph Presentation Layer
        direction TB
        Reports[Reports]
        Wizards[Wizards]
        Views[Views]
    end

    subgraph API Layer
        direction TB
        Controllers[Controllers]
        ExternalIntegration[External Integration]
    end

    subgraph External Systems
        direction TB
        BiometricDevices[Biometric Devices]
        MobileApps[Mobile Apps]
        ThirdPartyApps[Third-Party Applications]
    end

    ExternalSystems-->API
    API-->BusinessLogic
    Models-->BusinessLogic
    BusinessLogic-->Presentation
```

## Core Components

### 1. Data Models

The module is built around several key data models:

- **Timesheet (`ams_ta.timesheet`)**: Records daily attendance data for employees
- **Punch Log (`ams_ta.punch_log`)**: Captures individual check-in/check-out events
- **Timesheet Time Unit (`ams_ta.timesheet_time_unit`)**: Manages time segments within a timesheet
- **Shift (`ams_ta.shift`)**: Defines employee work schedules
- **Time Unit (`ams.time_unit`)**: Defines time periods within a schedule
- **Time Unit Rule (`ams_ta.time_unit_rule`)**: Configures rules for time units

### 2. Reports

The module includes reporting capabilities:

- **Attendance Details Report**: Provides detailed attendance information by employee
- **Attendance Summary Report**: Summarizes attendance data by department and employee

### 3. Wizards

User interface wizards for common operations:

- **Report Generator Wizard**: Allows users to generate attendance reports with various filters
- **Manual Attendance Wizard**: Enables manual entry of attendance records

## Key Features

1. **Flexible Time Tracking**:
   - Support for various shift types (normal, flexible, open)
   - Handling of overnight shifts
   - Grace periods for check-ins and check-outs

2. **Attendance Calculations**:
   - Working time calculation
   - Delay and shortage tracking
   - Overtime calculation with configurable factors
   - Absence detection

3. **Reporting**:
   - Detailed and summary reports
   - Filtering by employee, department, date range
   - Export capabilities

4. **Integration**:
   - Integration with HR module for employee data
   - API endpoints for external systems

## Module Architecture

The module follows a layered architecture:

1. **Data Layer**: Models for storing attendance and configuration data
2. **Business Logic Layer**: Methods for calculations and data processing
3. **Presentation Layer**: Reports and wizards for user interaction
4. **API Layer**: Endpoints for external system integration

## Dependencies

- `hr`: For employee data
- `ams_base`: For base functionality and follow-up models
- `mail`: For activity tracking and notifications
