# AMS Time Attendance Data Model Architecture

## Core Models Hierarchy

The `ams_ta` module is built on a hierarchical model structure that enables flexible and comprehensive time tracking:

```
ams_base.abstract_model
└── ams_ta.base_timesheet
    ├── ams_ta.timesheet
    └── ams_ta.timesheet_time_unit
```

## Class Diagram

```mermaid
classDiagram
    class AbstractModel {
        <<ams_base.abstract_model>>
    }

    class BaseTimesheet {
        <<ams_ta.base_timesheet>>
        +name: Char
        +date: Date
        +employee_id: Many2one
        +first_checkin_datetime: Datetime
        +last_checkout_datetime: Datetime
        +required_time: Float
        +working_time: Float
        +delay_time: Float
        +shortage_time: Float
        +overtime: Float
        +is_checked_in: Boolean
        +is_checked_out: Boolean
        +is_delayed: Boolean
        +is_shortage: Boolean
        +is_overtime: Boolean
        +is_absent: Boolean
        +_compute_checkin_dates_time()
        +_compute_checkout_dates_time()
    }

    class Timesheet {
        <<ams_ta.timesheet>>
        +is_dayoff: Boolean
        +is_weekend: Boolean
        +is_vacation: Boolean
        +is_permission: Boolean
        +is_public_vacation: Boolean
        +is_absent: Boolean
        +is_attend_day: Boolean
        +is_working_day: Boolean
        +shift_id: Many2one
        +ts_time_units_ids: One2many
        +calculate_ts_times()
        +_aggregate_common_fields()
        +_process_dayoff()
        +_process_working_day()
        +_set_computed_fields()
        +_update_counters()
    }

    class TimesheetTimeUnit {
        <<ams_ta.timesheet_time_unit>>
        +timesheet_id: Many2one
        +apply_min_max: Boolean
        +apply_half_work: Boolean
        +min_checkin_time: Float
        +max_checkout_time: Float
        +grace_in_time: Float
        +grace_out_time: Float
        +start_time: Float
        +end_time: Float
        +unit_type: Selection
        +calculate_times()
    }

    class PunchLog {
        <<ams_ta.punch_log>>
        +name: Char
        +date_time: Datetime
        +employee_id: Many2one
        +timesheet_id: Many2one
        +state: Selection
        +latitude: Float
        +longitude: Float
        +from_mobile: Boolean
        +action_execute_punch_log()
        +_handle_checkin_checkout()
        +_get_day_time_unit()
    }

    class Shift {
        <<ams_ta.shift>>
        +name: Char
        +employee_id: Many2one
        +schedule_id: Many2one
        +start_date: Date
        +end_date: Date
        +state: Selection
        +is_default: Boolean
        +action_execute()
        +_recalculate_shift_punch_logs()
    }

    class TimeUnitRule {
        <<ams_ta.time_unit_rule>>
        +name: Char
        +grace_in_time: Float
        +grace_out_time: Float
        +apply_half_work: Boolean
        +overtime_factor: Float
    }

    AbstractModel <|-- BaseTimesheet
    BaseTimesheet <|-- Timesheet
    BaseTimesheet <|-- TimesheetTimeUnit
    Timesheet "1" *-- "many" TimesheetTimeUnit : contains
    PunchLog --> Timesheet : updates
    PunchLog --> TimesheetTimeUnit : updates
    Shift --> Timesheet : configures
    TimeUnitRule --> TimesheetTimeUnit : applies rules to
```

## Base Models

### Base Timesheet (`ams_ta.base_timesheet`)

The foundation model that provides common fields and functionality for timesheet-related models.

**Key Inherited Models:**
- `ams_base.abstract_model`: Provides basic fields and methods
- `ams_base.follow_up_model`: Adds follow-up functionality
- `mail.thread`, `mail.activity.mixin`: Enables communication and activity tracking

**Key Fields:**
- `name`: Identifier for the timesheet record
- `date`: Date of the timesheet
- `employee_id`: Related employee
- `first_checkin_datetime`, `last_checkout_datetime`: Check-in/out timestamps
- `required_time`, `working_time`: Expected and actual work hours
- `delay_time`, `shortage_time`: Late arrival and early departure times
- `overtime`: Extra hours worked
- Various boolean flags (`is_checked_in`, `is_delayed`, etc.)

**Key Methods:**
- `_convert_datetime()`: Converts datetime to date and time components
- `_compute_checkin_dates_time()`, `_compute_checkout_dates_time()`: Calculate date and time fields

## Primary Models

### Timesheet (`ams_ta.timesheet`)

Records daily attendance data for an employee, aggregating data from timesheet time units.

**Key Fields:**
- Inherited from `base_timesheet`
- Status flags: `is_dayoff`, `is_weekend`, `is_vacation`, etc.
- Count fields: `delay_count`, `shortage_count`, etc.
- `ts_time_units_ids`: One-to-many relation to timesheet time units

**Key Methods:**
- `calculate_ts_times()`: Main calculation method for time statistics
- `_aggregate_common_fields()`: Aggregates data from time units
- `_process_dayoff()`, `_process_working_day()`: Different logic for day types
- `_set_computed_fields()`, `_update_counters()`: Update derived fields
- API methods for retrieving attendance data

### Timesheet Time Unit (`ams_ta.timesheet_time_unit`)

Represents a specific time segment within a timesheet, typically corresponding to a work period.

**Key Fields:**
- Inherited from `base_timesheet`
- `timesheet_id`: Related timesheet
- Time configuration: `min_checkin_time`, `max_checkout_time`, etc.
- `unit_type`: Type of time unit (normal, flexible, open)
- `grace_in_time`, `grace_out_time`: Allowed grace periods

**Key Methods:**
- `calculate_times()`: Calculates working time, delay, shortage, etc.
- Handles overnight shifts and different unit types

### Punch Log (`ams_ta.punch_log`)

Records individual check-in/check-out events from various sources (biometric devices, mobile apps, etc.).

**Key Fields:**
- `date_time`: Timestamp of the punch event
- `employee_id`: Related employee
- `state`: Status of the punch log (pending, executed)
- `latitude`, `longitude`: Geolocation data
- `timesheet_id`: Related timesheet

**Key Methods:**
- `action_execute_punch_log()`: Processes the punch log
- `_handle_checkin_checkout()`: Updates timesheet time unit with check-in/out data
- Logic for determining the appropriate time unit for a punch

### Shift (`ams_ta.shift`)

Defines a work schedule assignment for an employee.

**Key Fields:**
- `employee_id`: Related employee
- `schedule_id`: Related schedule
- `start_date`, `end_date`: Validity period
- `state`: Status (pending, running, finished)

**Key Methods:**
- `action_execute()`: Processes the shift
- `_recalculate_shift_punch_logs()`: Recalculates punch logs for the shift

### Time Unit Rule (`ams_ta.time_unit_rule`)

Configures rules for time units, such as grace periods and overtime factors.

**Key Fields:**
- `grace_in_time`, `grace_out_time`: Allowed grace periods
- `apply_half_work`: Whether to apply half-day rules
- `overtime_factor`: Multiplier for overtime hours

## Relationships

```
Timesheet (ams_ta.timesheet)
├── Employee (hr.employee) [Many2one]
├── Shift (ams_ta.shift) [Many2one]
└── Timesheet Time Units (ams_ta.timesheet_time_unit) [One2many]

Timesheet Time Unit (ams_ta.timesheet_time_unit)
└── Timesheet (ams_ta.timesheet) [Many2one]

Punch Log (ams_ta.punch_log)
├── Employee (hr.employee) [Many2one]
└── Timesheet (ams_ta.timesheet) [Many2one]

Shift (ams_ta.shift)
├── Employee (hr.employee) [Many2one]
└── Schedule (ams.schedule) [Many2one]
```

## Data Flow

1. **Punch Log Creation**:
   - Created when an employee checks in/out
   - Can come from biometric devices, mobile apps, or manual entry

2. **Punch Log Processing**:
   - `action_execute_punch_log()` is called
   - Determines the appropriate time unit
   - Creates or updates timesheet and timesheet time unit records

3. **Timesheet Calculation**:
   - Timesheet time units calculate working time, delay, shortage, etc.
   - Timesheet aggregates data from time units
   - Applies business rules for different day types

4. **Reporting**:
   - Reports query timesheet data
   - Group by employee, department, etc.
   - Calculate summaries and statistics
