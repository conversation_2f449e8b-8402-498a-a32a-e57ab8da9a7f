# Attendance Reporting System

## Overview

The AMS Time Attendance module includes a comprehensive reporting system that provides insights into employee attendance patterns, working hours, delays, absences, and other key metrics. The reporting system is designed to be flexible, allowing users to filter data by various criteria and generate both detailed and summary reports.

## Reporting System Architecture

```mermaid
flowchart TD
    subgraph User Interface
        ReportWizard[Report Generator Wizard]
    end

    subgraph Report Models
        ReportUtils[Report Utils Mixin]
        DetailedReport[Attendance Details Report]
        SummaryReport[Attendance Summary Report]
    end

    subgraph Data Sources
        Timesheet[Timesheet Records]
        Employee[Employee Data]
        Department[Department Data]
    end

    subgraph Output Formats
        PDF[PDF Report]
        HTML[HTML View]
        Excel[Excel Export]
    end

    ReportWizard -->|Configure| DetailedReport
    ReportWizard -->|Configure| SummaryReport

    ReportUtils -->|Shared Methods| DetailedReport
    ReportUtils -->|Shared Methods| SummaryReport

    Timesheet -->|Provides Data| DetailedReport
    Timesheet -->|Provides Data| SummaryReport
    Employee -->|Provides Data| DetailedReport
    Employee -->|Provides Data| SummaryReport
    Department -->|Provides Data| SummaryReport

    DetailedReport -->|Render| PDF
    DetailedReport -->|Render| HTML
    DetailedReport -->|Render| Excel
    SummaryReport -->|Render| PDF
    SummaryReport -->|Render| HTML
    SummaryReport -->|Render| Excel
```

## Report Types

### Attendance Details Report

The Attendance Details Report provides a comprehensive view of attendance data for individual employees over a specified period.

**Report Name:** `report_attendance_details`
**Model:** `report.ams_ta._report_attendance_details`

**Key Features:**
- Detailed daily attendance records
- Working hours, delay, shortage, and overtime calculations
- Absence and day-off tracking
- Grouping by employee

### Attendance Summary Report

The Attendance Summary Report provides an aggregated view of attendance data, grouped by department and employee.

**Report Name:** `report_attendance_summary`
**Model:** `report.ams_ta.report_attendance_summary`

**Key Features:**
- Summary statistics by department and employee
- Total working hours, delay, shortage, and overtime
- Absence and attendance counts
- Hierarchical grouping (department → employee)

## Report Generation Process

### Report Generator Wizard

The Report Generator Wizard (`ta.report_generator_wizard`) provides a user interface for configuring and generating reports.

**Key Fields:**
- `from_date`, `to_date`: Date range for the report
- `period_filter`: Predefined periods (current/previous week/month/year)
- `department_ids`, `employee_ids`: Filtering by department or specific employees
- `coach_ids`, `manager_ids`: Filtering by coach or manager

**Key Methods:**
- `_onchange_period_filter()`: Updates date range based on selected period
- `_compute_available_employees_domain()`: Dynamically filters available employees

### Report Data Preparation

The report models use a common utility mixin (`ReportUtilsMixin`) that provides shared functionality:

**Key Methods:**
- `_build_timesheet_domain()`: Constructs a search domain based on wizard inputs
- `_group_by_employee()`: Groups timesheet data by employee
- `_group_by_department_and_employee()`: Groups data hierarchically

### Report Rendering

Reports are rendered using QWeb templates defined in XML files. The report models prepare the data, and the templates handle the presentation.

**Data Flow:**
1. User configures report parameters in the wizard
2. Wizard calls the report action
3. Report model's `_get_report_values()` method prepares the data
4. QWeb template renders the data into HTML
5. Report is displayed or downloaded in the requested format (PDF, HTML, etc.)

## Data Sources

The reporting system primarily uses data from the following models:

- `ams_ta.timesheet`: Daily attendance records
- `hr.employee`: Employee information
- `hr.department`: Department information

## Filtering Capabilities

The reporting system supports filtering by:

- **Date Range**: Custom range or predefined periods
- **Organizational Structure**:
  - Department
  - Manager
  - Coach
- **Employee Selection**: Individual or multiple employees

## Time Calculation Logic

Reports display various time metrics calculated by the timesheet system:

- **Working Time**: Actual hours worked
- **Required Time**: Expected working hours
- **Delay Time**: Late arrival time
- **Shortage Time**: Early departure time
- **Overtime**: Extra hours worked
- **Total Deduction**: Time to be deducted for payroll purposes

## Implementation Details

### Report Utils Mixin

The `ReportUtilsMixin` provides common functionality for all reports:

```python
class ReportUtilsMixin:
    @api.model
    def _build_timesheet_domain(self, wizard):
        # Builds search domain based on wizard inputs
        # ...

    def _group_by_employee(self, timesheets):
        # Groups timesheets by employee
        # ...

    def _group_by_department_and_employee(self, timesheets):
        # Groups timesheets by department and then by employee
        # ...
```

### Report Models

Report models inherit from `models.AbstractModel` and implement the `_get_report_values` method:

```python
class ReportAttendanceDetails(models.AbstractModel, ReportUtilsMixin):
    _name = 'report.ams_ta._report_attendance_details'
    _description = 'Attendance Details Report'

    @api.model
    def _get_report_values(self, docids, data=None):
        # Prepare data for the report
        # ...
```

### Report Wizard

The report wizard provides a user interface for configuring reports:

```python
class ReportGenerator(models.TransientModel):
    _name = "ta.report_generator_wizard"
    _description = "Report Generator"

    # Fields for filtering and configuration
    # ...

    @api.onchange('period_filter')
    def _onchange_period_filter(self):
        # Update date range based on selected period
        # ...
```

## Integration Points

- **HR Module**: Employee and department data
- **Timesheet System**: Attendance data and calculations
- **Export Functionality**: Export to PDF, Excel, etc.
- **Access Control**: Report access based on user permissions
