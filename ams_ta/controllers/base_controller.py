from odoo import http
from odoo.http import request
import logging

_logger = logging.getLogger('ams_ta_api')


class BaseController(http.Controller):
    @property
    def ams_ta_request(self):
        ams_ta_request = request
        self.logger.info(f"{request.httprequest.url}")
        # TODO assign session or any business to share request
        # secy_request.session.db=''

        # update user timezone
        current_user = request.env.user
        if current_user and not current_user.tz:
            current_user.sudo().write({'tz': "Asia/Riyadh"})

        return ams_ta_request

    @property
    def logger(self):
        return _logger

    @property
    def employee_model(self):
        return self.ams_ta_request.env['hr.employee'].sudo()

    @property
    def timesheet_model(self):
        return self.ams_ta_request.env['ams_ta.timesheet'].sudo()

    # def _set_default_timezone(self):
    #     current_user = request.env.user
    #     if current_user and not current_user.tz:
    #         current_user.sudo().write({'tz': self.api_abstract_model.DEFAULT_API_TZ})
    #

    # region ---------------------- Test Methods -------------------------------------
    @http.route('/test_json', type='http', auth='public', methods=['POST'], csrf=False, cors="*")
    def test_method(self, **kwargs):
        # Your logic to process the request and get the result
        result = {
            "key1": "value1",
            "key2": "value2",
        }
        data = request.get_json_data()
        print(data)
        return request.make_json_response(result, status=200)
        # return self.alternative_json_response(result=result)

    @http.route('/test', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def test_get_method(self, **kwargs):
        # Your logic to process the request and get the result
        self.logger.info(f"Test method TODO {request.httprequest.url}")
        result = {
            "version": "17.0.0.1",
            "live": "Ok",
        }
        self.logger.info(f"Test API result {result} ")
        # data = request.get_json_data()

        return request.make_json_response(result, status=200)
        # return self.alternative_json_response(result=result)

    def handle_api_error(self, ex, response_code="100", status=200, response_message=None):
        """
        Universal API error handler with optional custom message.

        :param ex: Exception object or error string
        :param response_code: Business-level response code (default "100")
        :param status: HTTP status code (default 200)
        :param response_message: Optional message for API consumer. If not provided, uses exception type.
        """
        error_message = str(ex)
        message = response_message or f"{type(ex).__name__}"

        res_dict = {
            'response_code': response_code,
            'response_message': message,
            'ErrorMessage': error_message
        }

        self.logger.error(f"API error: {res_dict}")
        return request.make_json_response(res_dict, status=status)

    # endregion
