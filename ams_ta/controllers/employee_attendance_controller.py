from datetime import datetime
from typing import List

from odoo.addons.ams_ta.helper.helper import _parse_date_range
from odoo.addons.ams_ta.api.dto_attendance_deduction import AttendanceDeductionDTO
from odoo.addons.ams_ta.api.dto_attendance_deduction_list import AttendanceDeductionListDTO
from odoo.addons.ams_ta.api.dto_attendance_list import AttendanceListDTO
from odoo.addons.ams_ta.api.dto_attendance_log import AttendanceLogDTO
from odoo.addons.ams_ta.api.dto_employee import EmployeeDTO
from odoo.addons.ams_ta.api.dto_attendance import AttendanceDTO
from odoo.addons.ams_ta.controllers.base_controller import BaseController

from odoo import http
from odoo.http import request


class EmployeeAttendanceController(BaseController):

    @http.route('/GetEmployeeAttendanceByEmpNo', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employee_attendance_by_empno(self, empNo: str, dateFrom: str, dateTo: str, **kwargs):
        try:
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)
            result = self.timesheet_model.get_employee_attendance_by_identifier(empNo, dt_from, dt_to,filter_type="employee_number")
            res_dict= result.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeeAttendanceByUserName', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employee_attendance_by_username(self, userName: str, dateFrom:str, dateTo:str, **kwargs):
        try:
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)
            result = self.timesheet_model.get_employee_attendance_by_identifier(userName, dt_from, dt_to,filter_type="user_name")
            res_dict = result.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeesAttendance', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employees_attendance(self, dateFrom:str, dateTo:str, **kwargs):
        try:
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)
            # Pass None as department name to get all employees
            result = self.timesheet_model.get_employees_attendance(None, dt_from, dt_to,None)
            res_dict = result.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeesAttendanceByDeptName', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employees_attendance_by_dept_name(self, deptName:str, dateFrom:str, dateTo:str, **kwargs):
        try:
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)
            # Pass department name to the generic method
            result = self.timesheet_model.get_employees_attendance(deptName, dt_from, dt_to,filter_type="name")
            res_dict = result.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeesAttendanceDeduction', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employees_attendance_deduction(self, dateFrom:str, dateTo:str, empNo:str = None,**kwargs):
        try:
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)
            result = self.timesheet_model.get_employees_attendance_deduction( dt_from, dt_to ,empNo, filter_type="employee_number")
            res_dict = result.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)


