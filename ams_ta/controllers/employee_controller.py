from odoo import http
from odoo.http import request, Response
import json
from odoo.addons.ams_ta.controllers.base_controller import BaseController
from odoo.addons.ams_ta.api.dto_employee import *

class EmployeeController(BaseController):

    @http.route('/GetEmployeeByEmpNo', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employee_by_empno(self, empNo: str, **kwargs):
        try:
            result = self.employee_model.get_employee_info(empNo , filter_type="employee_number")
            res_dict= result.to_dict()
            self.logger.info(f"GetEmployeeByEmpNo return response EmployeeDTO: {res_dict} ")
            return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeeByUserName', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employee_by_username(self, userName: str, **kwargs):
        try:
            result = self.employee_model.get_employee_info(userName , filter_type="username")
            res_dict = result.to_dict()
            return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployees', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employees(self, **kwargs):
        try:
            result = self.employee_model.get_employees_info(filter_type="all")
            res_dict= result.to_dict()
            return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeesByDeptName', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employees_by_dept_name(self, deptName: str, **kwargs):
        try:
            result = self.employee_model.get_employees_info(deptName, filter_type="deptname")
            res_dict= result.to_dict()
            return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/AddEmployee', type='http', auth='public', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def add_employee(self):
        try:
            # Get the employee data from the request
            emp = request.get_json_data()
            # Call the model method to add the employee
            result = self.employee_model.add_employee_data(emp)
            return result
        except Exception as ex:
            return self.handle_api_error(ex)
