<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="ir_cron_generate_absences" model="ir.cron">
            <field name="name">Generate Employees Absences</field>
            <field name="model_id" ref="hr.model_hr_employee"/>
            <field name="state">code</field>
            <field name="code">model._cron_generate_absences()</field>
            <field name="active">False</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
        </record>
    </data>
</odoo>
