# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ams_ta
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-11-05 09:13+0000\n"
"PO-Revision-Date: 2025-11-05 09:13+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__available_employees_domain
msgid " Domain"
msgstr ""

#. module: ams_ta
#: model:ir.actions.act_window,name:ams_ta.exception_shift_action
msgid " Exception Shifts"
msgstr "الورديات الاستثنائية"

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "<strong>Employee Name:</strong>"
msgstr " اسم الموظف"

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "<strong>Employee No:</strong>"
msgstr "رقم الموظف"

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "<strong>From Date:</strong>"
msgstr " من تاريخ "

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "<strong>To Date:</strong>"
msgstr "الى تاريخ"

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "<strong>إلى تاريخ</strong>"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "<strong>اسم الموظف</strong>"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "<strong>رقم الموظف</strong>"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "<strong>من تاريخ</strong>"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__absent_alert_sent
msgid "Absent Alert Sent"
msgstr " "

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.js:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.js:0
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__absent_count
msgid "Absent Count"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__absent_time_criteria
msgid "Absent Time Criteria"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__message_needaction
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__message_needaction
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__message_needaction
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__message_needaction
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__message_needaction
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_user_group__geo_fence_action
msgid "Action When Outside Geo-Fence"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_form
msgid "Activate"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__is_active
msgid "Active"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__activity_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__activity_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__activity_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__activity_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__activity_ids
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__activity_state
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__activity_state
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__activity_state
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__activity_state
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__activity_state
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__activity_type_icon
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__activity_type_icon
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__activity_type_icon
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__activity_type_icon
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__activity_type_icon
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.view_manual_attendance_wizard_form
msgid "Add Attendance"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__address
msgid "Address"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_user_group__apply_geo_fencing
msgid "Apply Geo-Fencing"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__apply_half_work
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__apply_half_work
msgid "Apply Half Work"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__apply_min_max
msgid "Apply Min/Max"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__apply_working_hour_deduction
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__apply_working_hour_deduction
msgid "Apply Working Hour Deduction"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__message_attachment_count
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__message_attachment_count
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__message_attachment_count
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__message_attachment_count
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__message_attachment_count
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__attend_day_count
msgid "Attend Day Count"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.js:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.js:0
msgid "Attendance"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.js:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.js:0
#: model:ir.actions.client,name:ams_ta.action_client_ams_ta_dashboard
#: model:ir.ui.menu,name:ams_ta.ams_ta_combined_dashboard_menu
#: model:ir.ui.menu,name:ams_ta.ams_ta_dashboard_menu
msgid "Attendance Dashboard"
msgstr ""

#. module: ams_ta
#: model:ir.actions.report,name:ams_ta.action_report_attendance_details
#: model:ir.model,name:ams_ta.model_report_ams_ta__report_attendance_details
msgid "Attendance Details Report"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Attendance Details Report تقرير تفاصيل الحضور"
msgstr ""

#. module: ams_ta
#: model:ir.actions.report,name:ams_ta.action_report_attendance_summary
#: model:ir.model,name:ams_ta.model_report_ams_ta_report_attendance_summary
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "Attendance Summary Report"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet_form_view
msgid "Back"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__half_work_checkin_criteria
msgid ""
"Calculate half of working time if checkin > ( Start time + this count of "
"[hours:minutes] )"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__half_work_checkout_criteria
msgid ""
"Calculate half of working time if checkout < ( End time - this count of "
"[hours:minutes] )"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__calc_half_no_checkout_time
msgid "Calculate half of working time if no checkout"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.view_manual_attendance_wizard_form
msgid "Cancel"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_my_home_timesheet_views
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet_form_view
msgid "Check In"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_my_home_timesheet_views
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet_form_view
msgid "Check Out"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "Check your filters (date range, employee, etc.) and try again."
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__in_latitude
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__in_latitude
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__in_latitude
msgid "Check-in Latitude"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__in_longitude
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__in_longitude
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__in_longitude
msgid "Check-in Longitude"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__out_latitude
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__out_latitude
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__out_latitude
msgid "Check-out Latitude"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__out_longitude
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__out_longitude
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__out_longitude
msgid "Check-out Longitude"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__checkin_datetime
msgid "Checkin Datetime"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__checkin_time
msgid "Checkin Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__checkout_datetime
msgid "Checkout Datetime"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__checkout_time
msgid "Checkout Time"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.timesheet_time_unit_action
msgid "Click the Create button to add a new Timesheet time unit"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.timesheet_time_unit_action
msgid "Click to add a new Timesheet time unit"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__coach_ids
msgid "Coaches"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__color
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__color
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__color
msgid "Color"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__company_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__company_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__company_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__company_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__company_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__company_id
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__company_id
msgid "Company"
msgstr ""

#. module: ams_ta
#: model:ir.ui.menu,name:ams_ta.config_menu
msgid "Configurations"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__apply_working_hour_deduction
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__apply_working_hour_deduction
msgid "Consider Working Hour in calculating Deduction"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__last_dept_depth
#: model:ir.model.fields,help:ams_ta.field_ams_ta_punch_log__last_dept_depth
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__last_dept_depth
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__last_dept_depth
msgid "Contain hierarchy  depth of last department assigned to employee"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__dept_depth
#: model:ir.model.fields,help:ams_ta.field_ams_ta_punch_log__dept_depth
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__dept_depth
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__dept_depth
msgid "Contain hierarchy of department depth when create record"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__create_employee_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__create_employee_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__create_employee_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__create_employee_id
msgid "Create By Employee"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.timesheet_action
msgid "Create Timesheet"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.time_unit_rule_action
msgid "Create Unit rule"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.timesheet_action
msgid "Create a Timesheet"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.ams_ta_schedule_action
msgid "Create a schedule"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.shift_action
msgid "Create a shift"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.exception_shift_action
msgid "Create an Exception shift"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.ams_ta_schedule_action
msgid "Create schedule"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.ams_ta_time_unit_action
msgid "Create time unit"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__create_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__create_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__create_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__create_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__create_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__create_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__create_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__create_uid
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__create_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__create_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__create_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__create_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__create_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__create_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__create_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__create_date
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__create_date
msgid "Created on"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__current_employee_level
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__current_employee_level
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__current_employee_level
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__current_employee_level
msgid "Current Employee Level"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
#: model:ir.model.fields.selection,name:ams_ta.selection__ta_report_generator_wizard__period_filter__current_month
msgid "Current Month"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
#: model:ir.model.fields.selection,name:ams_ta.selection__ta_report_generator_wizard__period_filter__current_week
msgid "Current Week"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
#: model:ir.model.fields.selection,name:ams_ta.selection__ta_report_generator_wizard__period_filter__current_year
msgid "Current Year"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_form
msgid "Cycle Days"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__date
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__date
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_my_home_timesheet_views
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet_form_view
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_search
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Date"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__dayoff_count
msgid "Dayoff Count"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_form
msgid "Days"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.shift_view_search
msgid "Default Shifts"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_form
msgid "Default Time Unit"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__delay_alert_sent
msgid "Delay Alert Sent"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.js:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.js:0
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__delay_count
msgid "Delay Count"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Delay Hours التأخير"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__delay_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__delay_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__delay_time
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_my_home_timesheet_views
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet_form_view
msgid "Delay Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__delay_time_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__delay_time_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__delay_time_min
msgid "Delay Time (Minutes)"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Delayed"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__department_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__department_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__department_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__department_id
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__department_ids
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_search
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Department"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__dept_depth
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__dept_depth
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__dept_depth
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__dept_depth
msgid "Department Depth"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__dept_path_code
msgid "Department Path Code"
msgstr ""

#. module: ams_ta
#: model:ir.ui.menu,name:ams_ta.menu_hr_department
msgid "Departments"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__enroll_number
#: model:ir.model.fields,help:ams_ta.field_ams_ta_punch_log__enroll_number
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__enroll_number
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__enroll_number
msgid "Device Enroll Number"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__device_serial
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_lp_map
msgid "Device Serial"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__display_name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__display_name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__display_name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__display_name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__display_name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__display_name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__display_name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__display_name
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__geo_fence_distance
msgid "Distance from Geo-Fence (m)"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.js:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.js:0
msgid "Early Leave Count"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Early Leave التقصير"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_hr_employee
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__employee_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__employee_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__employee_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__employee_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__employee_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__employee_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__employee_id
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_search
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Employee"
msgstr "الموظف"

#. module: ams_ta
#. odoo-python
#: code:addons/addons_ams/ams_ta/models/timesheet.py:0
#: code:addons/ams_ta/models/timesheet.py:0
msgid "Employee %s has a delay of %.2f hours on %s."
msgstr ""

#. module: ams_ta
#. odoo-python
#: code:addons/addons_ams/ams_ta/models/timesheet.py:0
#: code:addons/ams_ta/models/timesheet.py:0
msgid "Employee %s has a shortage of %.2f hours on %s."
msgstr ""

#. module: ams_ta
#. odoo-python
#: code:addons/addons_ams/ams_ta/models/timesheet.py:0
#: code:addons/ams_ta/models/timesheet.py:0
msgid "Employee %s was absent on %s."
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__coach_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__coach_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__coach_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__coach_id
msgid "Employee Coach"
msgstr ""

#. module: ams_ta
#: model:ir.ui.menu,name:ams_ta.user_groups_menu
msgid "Employee Groups"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_form
msgid "Employee Info"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__manager_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__manager_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__manager_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__manager_id
msgid "Employee Manager"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__employee_number
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__employee_number
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__employee_number
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__employee_number
msgid "Employee Number"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__department_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_punch_log__department_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__department_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__department_id
msgid "Employee department on record creation"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_user_group__employee_ids
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__employee_ids
#: model:ir.ui.menu,name:ams_ta.employees_menu
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_user_group_view_form
msgid "Employees"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__end_date
msgid "End Date"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__end_time
msgid "End Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__enroll_number
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__enroll_number
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__enroll_number
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__enroll_number
msgid "Enroll Number"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_bs_event_log
msgid "Event Log"
msgstr ""

#. module: ams_ta
#: model:ir.ui.menu,name:ams_ta.exception_shifts
#: model:ir.ui.menu,name:ams_ta.exception_shifts_operations
msgid "Exception Shifts"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.shift_view_form
msgid "Exceptions"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.shift_view_form
msgid "Execute"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__execute_log_time
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_lp_map
msgid "Execute Log Time"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_list
msgid "Execute Punch Log"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_punch_log__state__executed
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_shift__status__executed
msgid "Executed"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.shift_view_search
msgid "Executed Shifts"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_shift__state__finished
msgid "Finished"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__first_checkin_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__first_checkin_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__first_checkin_date
msgid "First Check-in Date"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__first_checkin_datetime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__first_checkin_datetime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__first_checkin_datetime
msgid "First Check-in Datetime"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__first_checkin_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__first_checkin_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__first_checkin_time
msgid "First Check-in Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__first_checkin_time_char
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__first_checkin_time_char
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__first_checkin_time_char
msgid "First Check-in Time (Char)"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__first_log_checkin_datetime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__first_log_checkin_datetime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__first_log_checkin_datetime
msgid "First log Check-in Datetime"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_user_group__geo_fence_action__flag
msgid "Flag for Review"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_punch_log__geo_fence_action_taken__flag
msgid "Flagged"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_timesheet_time_unit__unit_type__flexible
msgid "Flexible"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__message_follower_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__message_follower_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__message_follower_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__message_follower_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__message_follower_ids
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__message_partner_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__message_partner_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__message_partner_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__message_partner_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__message_partner_ids
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__activity_type_icon
#: model:ir.model.fields,help:ams_ta.field_ams_ta_shift__activity_type_icon
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__activity_type_icon
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__activity_type_icon
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__activity_type_icon
#: model:ir.model.fields,help:ams_ta.field_hr_employee__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة من Font awesome مثال: fa-tasks "

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__from_date
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__from_date
msgid "From Date"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__from_mobile
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__from_mobile
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__from_mobile
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__from_mobile
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_lp_map
msgid "From Mobile"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_form
msgid "Generate Days"
msgstr ""

#. module: ams_ta
#: model:ir.actions.server,name:ams_ta.ir_cron_generate_absences_ir_actions_server
msgid "Generate Employees Absences"
msgstr ""

#. module: ams_ta
#: model:ir.actions.act_window,name:ams_ta.action_manual_attendance_wizard
msgid "Generate Manual Attendance"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_employee_view_list
msgid "Generate Test Punch Logs"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_employee_view_list
msgid "Generate Yesterday Absent"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_user_group_view_form
msgid "Geo Fence Config"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_form
msgid "Geo Fence Validation"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__latitude
msgid "Geo Latitude"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__longitude
msgid "Geo Longitude"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__geo_fence_action_taken
msgid "Geo-Fence Action Taken"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_ta_geo_fence_location
msgid "Geo-Fence Location"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_user_group__geo_fence_location_ids
msgid "Geo-Fence Locations"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__geo_fence_status
msgid "Geo-Fence Status"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_user_group__geo_fence_tolerance
msgid "Geo-Fence Tolerance (meters)"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__grace_in_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__grace_in_time
msgid "Grace In Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__grace_out_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__grace_out_time
msgid "Grace Out Time"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.time_unit_rule_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_form
msgid "Grace Time"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_search
#: model_terms:ir.ui.view,arch_db:ams_ta.shift_view_search
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Group By"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_my_home_timesheet_views
msgid "Group:"
msgstr ""

#. module: ams_ta
#: model:ir.ui.menu,name:ams_ta.menu_hr_root
msgid "HR"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.time_unit_rule_view_form
msgid "Half Working Day"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_form
msgid "Half Working Day :"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__has_message
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__has_message
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__has_message
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__has_message
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__has_message
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__id
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__id
msgid "ID"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__activity_exception_icon
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__activity_exception_icon
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__activity_exception_icon
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__activity_exception_icon
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__activity_exception_icon
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__activity_exception_icon
#: model:ir.model.fields,help:ams_ta.field_ams_ta_shift__activity_exception_icon
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__activity_exception_icon
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__activity_exception_icon
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__activity_exception_icon
#: model:ir.model.fields,help:ams_ta.field_hr_employee__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى النشاط المستثنى. "

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__half_work_checkin_criteria
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__half_work_checkin_criteria
msgid "If Checkin After"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__half_work_checkout_criteria
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__half_work_checkout_criteria
msgid "If Checkout Before"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__calc_half_no_checkout_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__calc_half_no_checkout_time
msgid "If No Checkout"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__message_needaction
#: model:ir.model.fields,help:ams_ta.field_ams_ta_shift__message_needaction
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__message_needaction
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__message_needaction
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__message_needaction
#: model:ir.model.fields,help:ams_ta.field_hr_employee__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__message_has_error
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__message_has_sms_error
#: model:ir.model.fields,help:ams_ta.field_ams_ta_shift__message_has_error
#: model:ir.model.fields,help:ams_ta.field_ams_ta_shift__message_has_sms_error
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__message_has_error
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__message_has_sms_error
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__message_has_error
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__message_has_sms_error
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__message_has_error
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__message_has_sms_error
#: model:ir.model.fields,help:ams_ta.field_hr_employee__message_has_error
#: model:ir.model.fields,help:ams_ta.field_hr_employee__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "In دخول"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__is_absent
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_absent
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__is_absent
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Is Absent"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_attend_day
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Is Attend Day"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__is_checked_in
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_checked_in
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__is_checked_in
msgid "Is Checked In"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__is_checked_out
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_checked_out
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__is_checked_out
msgid "Is Checked Out"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_dayoff
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Is Day Off"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__is_default
msgid "Is Default"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__is_delayed
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_delayed
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__is_delayed
msgid "Is Delayed"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__is_exception
msgid "Is Exception"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__message_is_follower
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__message_is_follower
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__message_is_follower
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__message_is_follower
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__message_is_follower
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__is_overnight
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_overnight
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__is_overnight
msgid "Is Overnight"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__is_overtime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_overtime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__is_overtime
msgid "Is Overtime"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_permission
msgid "Is Permission"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_public_vacation
msgid "Is Public Vacation"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__is_shortage
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_shortage
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__is_shortage
msgid "Is Shortage"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_vacation
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Is Vacation"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_weekend
msgid "Is Weekend"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_working_day
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Is Working Day"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__last_generate_absent_date
msgid "Last Absent Generation Date"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__last_checkout_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__last_checkout_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__last_checkout_date
msgid "Last Check-out Date"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__last_checkout_datetime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__last_checkout_datetime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__last_checkout_datetime
msgid "Last Check-out Datetime"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__last_checkout_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__last_checkout_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__last_checkout_time
msgid "Last Check-out Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__last_checkout_time_char
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__last_checkout_time_char
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__last_checkout_time_char
msgid "Last Check-out Time (Char)"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__last_department_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__last_department_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__last_department_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__last_department_id
msgid "Last Department"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__last_dept_depth
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__last_dept_depth
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__last_dept_depth
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__last_dept_depth
msgid "Last Department Depth"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__write_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__write_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__write_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__write_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__write_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__write_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__write_uid
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__write_uid
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__write_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__write_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__write_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__write_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__write_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__write_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__write_date
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__write_date
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__last_department_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_punch_log__last_department_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__last_department_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__last_department_id
msgid "Last department assign to employee"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__last_log_checkout_datetime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__last_log_checkout_datetime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__last_log_checkout_datetime
msgid "Last log Check-out Datetime"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__latitude
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_lp_map
msgid "Latitude"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__name
msgid "Location Name"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__date
msgid "Log Date"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__date_time
msgid "Log Date Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__time
msgid "Log Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__longitude
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_lp_map
msgid "Longitude"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.punch_log_action
msgid "Manage Punch Logs"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__manager_ids
msgid "Managers"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_ta_manual_attendance_wizard
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_employee_view_list
msgid "Manual Attendance"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__is_manual_edit
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__is_manual_edit
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__is_manual_edit
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Manual Edit"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_lp_map
msgid "Map Locations"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__max_checkout_time
msgid "Max Check-out Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_user_group__geo_fence_tolerance
msgid "Maximum allowed distance from defined locations in meters"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__max_checkout_time
msgid "Maximum time allowed to checkout"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__message_has_error
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__message_has_error
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__message_has_error
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__message_has_error
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__message_has_error
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__message_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__message_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__message_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__message_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__message_ids
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__min_checkin_time
msgid "Min Check-in Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__min_checkin_time
msgid "Minimum time allowed to checkin"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_form
msgid "Mobile Info"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "الموعد النهائي لنشاطاتي "

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__name
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__name
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__name
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_my_home_timesheet_views
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet_form_view
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_lp_map
msgid "Name"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__nearest_location_id
msgid "Nearest Location"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet_form_view
msgid "Next"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__activity_date_deadline
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__activity_date_deadline
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__activity_date_deadline
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__activity_date_deadline
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__activity_date_deadline
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__activity_summary
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__activity_summary
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__activity_summary
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__activity_summary
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__activity_summary
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__activity_type_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__activity_type_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__activity_type_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__activity_type_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__activity_type_id
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_punch_log__geo_fence_status__no_fence
msgid "No Geo-Fence"
msgstr ""

#. module: ams_ta
#. odoo-python
#: code:addons/addons_ams/ams_ta/models/employee.py:0
#: code:addons/ams_ta/models/employee.py:0
msgid "No employees selected. Please select employees to generate punch logs."
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "No matching records found for the selected filters."
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_punch_log__geo_fence_action_taken__none
msgid "None"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_timesheet_time_unit__unit_type__normal
msgid "Normal"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__notes
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__notes
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__notes
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__notes
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_form
msgid "Notes"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Notes ملاحظات"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__message_needaction_counter
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__message_needaction_counter
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__message_needaction_counter
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__message_needaction_counter
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__message_needaction_counter
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__message_has_error_counter
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__message_has_error_counter
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__message_has_error_counter
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__message_has_error_counter
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__message_has_error_counter
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__message_needaction_counter
#: model:ir.model.fields,help:ams_ta.field_ams_ta_shift__message_needaction_counter
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__message_needaction_counter
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__message_needaction_counter
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__message_needaction_counter
#: model:ir.model.fields,help:ams_ta.field_hr_employee__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__message_has_error_counter
#: model:ir.model.fields,help:ams_ta.field_ams_ta_shift__message_has_error_counter
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__message_has_error_counter
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__message_has_error_counter
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__message_has_error_counter
#: model:ir.model.fields,help:ams_ta.field_hr_employee__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_timesheet_time_unit__unit_type__open
msgid "Open"
msgstr ""

#. module: ams_ta
#: model:ir.ui.menu,name:ams_ta.op_menu
msgid "Operations"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_form
msgid "Other Info"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Out خروج"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_punch_log__geo_fence_status__outside
msgid "Outside Boundary"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__overtime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__overtime
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__overtime
msgid "Overtime"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__overtime_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__overtime_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__overtime_min
msgid "Overtime (Minutes)"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__overtime_day_count
msgid "Overtime Day Count"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__overtime_factor
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__overtime_factor
msgid "Overtime Factor"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__overtime_factored
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__overtime_factored
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__overtime_factored
msgid "Overtime Factored"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__overtime_factored_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__overtime_factored_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__overtime_factored_min
msgid "Overtime Factored (Minutes)"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_form
msgid "Overtime and Deduction"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Overtime الإضافي"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_punch_log__state__pending
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_shift__state__pending
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_shift__status__pending
msgid "Pending"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.shift_view_search
msgid "Pending Shifts"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__period_filter
msgid "Period"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Period الفترة"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__permission_count
msgid "Permission Count"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet_form_view
msgid "Previous"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
#: model:ir.model.fields.selection,name:ams_ta.selection__ta_report_generator_wizard__period_filter__previous_month
msgid "Previous Month"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
#: model:ir.model.fields.selection,name:ams_ta.selection__ta_report_generator_wizard__period_filter__previous_week
msgid "Previous Week"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
#: model:ir.model.fields.selection,name:ams_ta.selection__ta_report_generator_wizard__period_filter__previous_year
msgid "Previous Year"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_user_group__primary_shift_id
msgid "Primary shift"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Public Vacation"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_ta_punch_log
#: model:ir.ui.menu,name:ams_ta.punch_log_menu
msgid "Punch Log"
msgstr ""

#. module: ams_ta
#: model:ir.actions.act_window,name:ams_ta.punch_log_action
#: model:ir.actions.server,name:ams_ta.punch_log_server_action
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__punch_logs
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_list
msgid "Punch Logs"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_list
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_time_unit_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_time_unit_view_list
msgid "Purpose Type"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__radius
msgid "Radius (meters)"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__is_random
msgid "Random"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_user_group__geo_fence_action__reject
msgid "Reject Punch"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_punch_log__geo_fence_action_taken__reject
msgid "Rejected"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__remaining_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__remaining_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__remaining_time
msgid "Remaining Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__remaining_time_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__remaining_time_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__remaining_time_min
msgid "Remaining Time (Minutes)"
msgstr ""

#. module: ams_ta
#: model:ir.ui.menu,name:ams_ta.report_generator_menu
msgid "Report"
msgstr ""

#. module: ams_ta
#: model:ir.actions.act_window,name:ams_ta.action_report_generator_wizard
#: model:ir.model,name:ams_ta.model_ta_report_generator_wizard
#: model_terms:ir.ui.view,arch_db:ams_ta.view_report_generator_wizard_form
msgid "Report Generator"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__required_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__required_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__required_time
msgid "Required Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__required_time_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__required_time_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__required_time_min
msgid "Required Time (Minutes)"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_form
msgid "Reset"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__activity_user_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__activity_user_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__activity_user_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__activity_user_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__activity_user_id
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_time_unit__rule_id
msgid "Rule"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_shift__state__running
msgid "Running"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__message_has_sms_error
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__message_has_sms_error
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__message_has_sms_error
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__message_has_sms_error
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__message_has_sms_error
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__schedule_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__schedule_id
#: model_terms:ir.ui.view,arch_db:ams_ta.shift_view_search
msgid "Schedule"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__schedule_day_id
msgid "Schedule Day"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_list
msgid "Schedule Type"
msgstr ""

#. module: ams_ta
#: model:ir.actions.act_window,name:ams_ta.ams_ta_schedule_action
#: model:ir.ui.menu,name:ams_ta.ta_schedule_menu
msgid "Schedules"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_user_group__secondary_shift_id
msgid "Secondary shift"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__coach_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_punch_log__coach_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__coach_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__coach_id
msgid ""
"Select the \"Employee\" who is the coach of this employee.\n"
"The \"Coach\" has no specific rights or responsibilities by default."
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_time_unit_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.time_unit_rule_view_form
msgid "Setting"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_ta_shift
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__shift_id
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Shift"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.time_unit_rule_view_list
msgid "Shift Rule"
msgstr ""

#. module: ams_ta
#: model:ir.model.constraint,message:ams_ta.constraint_ams_ta_shift_name_unique
msgid "Shift name must be unique!"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Shift جدول الدوام"
msgstr ""

#. module: ams_ta
#: model:ir.actions.act_window,name:ams_ta.shift_action
#: model:ir.ui.menu,name:ams_ta.shift_menu
#: model_terms:ir.ui.view,arch_db:ams_ta.shift_view_list
msgid "Shifts"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Shortage"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__shortage_alert_sent
msgid "Shortage Alert Sent"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__shortage_count
msgid "Shortage Count"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__shortage_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__shortage_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__shortage_time
msgid "Shortage Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__shortage_time_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__shortage_time_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__shortage_time_min
msgid "Shortage Time (Minutes)"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.js:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.js:0
msgid "Show More"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__start_date
msgid "Start Date"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__start_limit
msgid "Start Limit"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__start_time
msgid "Start Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__state
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__state
#: model_terms:ir.ui.view,arch_db:ams_ta.shift_view_search
msgid "State"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__status
msgid "Status"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__activity_state
#: model:ir.model.fields,help:ams_ta.field_ams_ta_shift__activity_state
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__activity_state
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__activity_state
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__activity_state
#: model:ir.model.fields,help:ams_ta.field_hr_employee__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الأنشطة المعتمدة على الحالة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: ams_ta
#. odoo-python
#: code:addons/addons_ams/ams_ta/models/employee.py:0
#: code:addons/ams_ta/models/employee.py:0
msgid "Success"
msgstr ""

#. module: ams_ta
#: model:res.groups,name:ams_ta.group_ta_employee
msgid "TA Employee"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.shift_view_form
msgid "Technical Info"
msgstr ""

#. module: ams_ta
#: model:ir.ui.menu,name:ams_ta.top_menu
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_user_group_view_form
msgid "Time Attendance"
msgstr ""

#. module: ams_ta
#: model:ir.module.category,name:ams_ta.ams_ta_group_category
msgid "Time Attendance Groups"
msgstr ""

#. module: ams_ta
#: model:ir.module.category,description:ams_ta.ams_ta_group_category
msgid "Time Attendance Management"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_form
msgid "Time Detail"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__time_off_hours
msgid "Time Off Hours"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_form
msgid "Time Summary"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_time_unit
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_time_unit_view_list
msgid "Time Unit"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_form
msgid "Time Unit Info"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_ta_time_unit_rule
msgid "Time Unit Rule"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_form
msgid "Time Unit Rule Info"
msgstr ""

#. module: ams_ta
#: model:ir.actions.act_window,name:ams_ta.time_unit_rule_action
#: model:ir.ui.menu,name:ams_ta.shift_rule_menu
msgid "Time Unit Rules"
msgstr ""

#. module: ams_ta
#: model:ir.actions.act_window,name:ams_ta.ams_ta_time_unit_action
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__ts_time_units_ids
#: model:ir.ui.menu,name:ams_ta.ta_time_unit_menu
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_form
msgid "Time Units"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__grace_in_time
msgid ""
"Time allowed to checkin after start of time_unit without calculate delay"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__grace_out_time
msgid ""
"Time allowed to checkout before end of time_unit without calculate shortage"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Time-off"
msgstr ""

#. module: ams_ta
#: model:ir.actions.act_window,name:ams_ta.timesheet_action
#: model:ir.actions.server,name:ams_ta.timesheet_server_action
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__timesheet_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__timesheet_id
#: model:ir.ui.menu,name:ams_ta.employee_timesheet_menu_top_menu
#: model:ir.ui.menu,name:ams_ta.timesheet_menu
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_my_home_menu_timesheet
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_my_home_timesheet_views
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_lp_map
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_list
msgid "Timesheet"
msgstr ""

#. module: ams_ta
#. odoo-python
#: code:addons/addons_ams/ams_ta/models/timesheet.py:0
#: code:addons/ams_ta/models/timesheet.py:0
msgid "Timesheet Alert"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_pivot
msgid "Timesheet Analysis"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet_form_view
msgid "Timesheet Details"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_ta_timesheet
msgid "Timesheet for AMS TA Including Time Off Configuration"
msgstr ""

#. module: ams_ta
#: model:ir.actions.act_window,name:ams_ta.timesheet_time_unit_action
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_list
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_time_unit_view_search
msgid "Timesheet shift unit"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__to_date
#: model:ir.model.fields,field_description:ams_ta.field_ta_report_generator_wizard__to_date
msgid "To Date"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_search
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Today"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Total Absence Days"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__total_deduction
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__total_deduction
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__total_deduction
msgid "Total Deduction"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Total Delay and Early Leave Hours"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__total_delay_shortage
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__total_delay_shortage
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__total_delay_shortage
msgid "Total Delay and Shortage"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__total_delay_shortage_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__total_delay_shortage_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__total_delay_shortage_min
msgid "Total Delay and Shortage (Minutes)"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Total Permission Hours"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__public_vacation_count
msgid "Total Vacation Count"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Total Vacation Days"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Total الإجمالي"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__activity_exception_decoration
#: model:ir.model.fields,help:ams_ta.field_ams_ta_shift__activity_exception_decoration
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__activity_exception_decoration
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__activity_exception_decoration
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__activity_exception_decoration
#: model:ir.model.fields,help:ams_ta.field_hr_employee__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى في السجل. "

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__unit_type
msgid "Unit Type"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_schedule_view_form
msgid "Update Working Schedule"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_user_group
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__user_group_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_geo_fence_location__user_group_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__user_group_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__user_group_id
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__user_group_id
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__user_group_id
#: model_terms:ir.ui.view,arch_db:ams_ta.ams_ta_employee_view_form
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_search
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "User Group"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__vacation_count
msgid "Vacation Count"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_punch_log__geo_fence_status__valid
msgid "Valid"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_form
msgid "Validate Geo Fence"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_my_home_menu_timesheet
msgid "View Timesheets"
msgstr ""

#. module: ams_ta
#: model_terms:ir.actions.act_window,help:ams_ta.punch_log_action
msgid "View and manage punch logs for employees."
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_user_group__geo_fence_action__warn
msgid "Warn Only"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields.selection,name:ams_ta.selection__ams_ta_punch_log__geo_fence_action_taken__warn
msgid "Warning"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__website_message_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_shift__website_message_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_time_unit_rule__website_message_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__website_message_ids
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__website_message_ids
#: model:ir.model.fields,field_description:ams_ta.field_hr_employee__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__website_message_ids
#: model:ir.model.fields,help:ams_ta.field_ams_ta_shift__website_message_ids
#: model:ir.model.fields,help:ams_ta.field_ams_ta_time_unit_rule__website_message_ids
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__website_message_ids
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__website_message_ids
#: model:ir.model.fields,help:ams_ta.field_hr_employee__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__weekend_count
msgid "Weekend Count"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_manual_attendance_wizard__with_time
msgid "With Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_punch_log__is_within_geo_fence
msgid "Within Geo-Fence"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__working_day_count
msgid "Working Day Count"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "Working Hours ساعات العمل"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__working_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__working_time
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__working_time
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_my_home_timesheet_views
#: model_terms:ir.ui.view,arch_db:ams_ta.portal_timesheet_form_view
msgid "Working Time"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_base_timesheet__working_time_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet__working_time_min
#: model:ir.model.fields,field_description:ams_ta.field_ams_ta_timesheet_time_unit__working_time_min
msgid "Working Time (Minutes)"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.punch_log_view_search
#: model_terms:ir.ui.view,arch_db:ams_ta.timesheet_view_search
msgid "Yesterday"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_ta_base_timesheet
msgid "ams_ta.base_timesheet"
msgstr ""

#. module: ams_ta
#: model:ir.model,name:ams_ta.model_ams_ta_timesheet_time_unit
msgid "ams_ta.timesheet_time_unit"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ta_report_generator_wizard__available_employees_domain
msgid ""
"compute field to filter employees by department and coaches and managers"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
msgid "current_month"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
msgid "current_week"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
msgid "current_year"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
msgid "previous_month"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
msgid "previous_week"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
msgid "previous_year"
msgstr ""

#. module: ams_ta
#: model:ir.model.fields,help:ams_ta.field_ams_ta_base_timesheet__employee_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_punch_log__employee_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet__employee_id
#: model:ir.model.fields,help:ams_ta.field_ams_ta_timesheet_time_unit__employee_id
msgid "this record owned to this employee as follow up"
msgstr ""

#. module: ams_ta
#. odoo-javascript
#: code:addons/addons_ams/ams_ta/static/src/dashboard/dashboard.xml:0
#: code:addons/ams_ta/static/src/dashboard/dashboard.xml:0
msgid "to"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "إجمالي أيام الإجازات"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "إجمالي أيام العطلة"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "إجمالي أيام الغياب"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "إجمالي الإستئذان"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "إجمالي التأخير"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "إجمالي التأخير والتقصير"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "إجمالي التقصير"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "إجمالي ساعات الاستئذان"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "إجمالي ساعات العمل"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "إلى تاريخ:"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "الادارة:"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "الاسم"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
msgid "التاريخ"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "المجموع الكلى للإدارة:"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "تقرير الحضور والانصراف"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "عدد أيام الدوام الفعلى"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "عدد أيام الدوام الكلى"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "لا توجد نتائج مطابقة"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "من تاريخ:"
msgstr ""

#. module: ams_ta
#: model_terms:ir.ui.view,arch_db:ams_ta._report_attendance_details
#: model_terms:ir.ui.view,arch_db:ams_ta.report_attendance_summary
msgid "يرجى مراجعة الفلاتر المختارة مثل التاريخ أو الموظف."
msgstr ""
