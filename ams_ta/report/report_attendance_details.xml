<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <template id="_report_attendance_details">
        <t t-call="web.html_container">

            <t t-if="not grouped_data">
                <!-- Show a message for no matching records -->
                <t t-call="web.external_layout">
                    <div class="page">
                        <style>
                            .no-data-box {
                                width: 100%;
                                padding: 50px 20px;
                                border: 2px dashed #e74c3c;
                                text-align: center;
                                font-family: Arial, sans-serif;
                                color: #2c3e50;
                                margin-top: 100px;
                            }

                            .no-data-box h2 {
                                font-size: 20px;
                                color: #e74c3c;
                                margin-bottom: 10px;
                            }

                            .no-data-box p {
                                font-size: 14px;
                                color: #555;
                                margin: 5px 0;
                            }

                            .no-data-box .icon {
                                font-size: 40px;
                                color: #e74c3c;
                                margin-bottom: 10px;
                            }

                            .no-data-box hr {
                                margin: 15px auto;
                                border: 0;
                                border-top: 1px solid #e74c3c;
                                width: 40%;
                            }
                        </style>

                        <div class="no-data-box">
                            <div class="icon">✘</div>
                            <h2>لا توجد نتائج مطابقة</h2>
                            <hr/>
                            <p>No matching records found for the selected filters.</p>
                            <p>يرجى مراجعة الفلاتر المختارة مثل التاريخ أو الموظف.</p>
                            <p>Check your filters (date range, employee, etc.) and try again.</p>
                        </div>
                    </div>
                </t>
            </t>


            <t t-if="grouped_data">
                <!-- Show the report as normal -->
                <t t-foreach="docs" t-as="doc">
                    <t t-foreach="grouped_data.values()" t-as="group">
                        <t t-set="employee" t-value="group['employee']"/>
                        <!-- Inherit Odoo's standard external layout (header) -->
                        <t t-call="web.external_layout">
                            <div class="page">
                                <!-- Add custom CSS styles -->
                                <style>
                                    body {
                                        font-family: Arial, sans-serif;
                                        margin: 0;
                                        padding: 0;
                                        color: #333;
                                    }

                                    .report {
                                        border-collapse: collapse;
                                        width: 100%;
                                        background: #fff;
                                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                                    }

                                    .report th, .report td {
                                        border: 1px solid #ddd;
                                        padding: 6px;
                                        text-align: center;
                                    }

                                    .report th {
                                        background-color: #f4f4f4;
                                        color: #000;
                                    }

                                    .summation-row {
                                        background-color: #f2eeed;
                                    }

                                    .summation-row p {
                                        margin: 2px 0;
                                    }

                                    .bilingual {
                                        display: flex;
                                        align-items: center;
                                        gap: 20px;
                                    }

                                    .red-text {
                                        color: red;
                                    }
                                </style>
                                <!-- Report Title -->
                                <div class="text-center" style="margin-bottom: 40px;">
                                    <h3>Attendance Details Report تقرير تفاصيل الحضور</h3>
                                </div>
                                <!-- Employee and Date Information -->
                                <table class="table table-sm">
                                    <tr>
                                        <td>
                                            <div class="bilingual">
                                                <strong>Employee Name:</strong>
                                                <span t-esc="employee.name"/>
                                                <strong>اسم الموظف</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="bilingual">
                                                <strong>Employee No:</strong>
                                                <span t-esc="employee.id"/>
                                                <strong>رقم الموظف</strong>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="bilingual">
                                                <strong>From Date:</strong>
                                                <span t-esc="doc.from_date"/>
                                                <strong>من تاريخ</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="bilingual">
                                                <strong>To Date:</strong>
                                                <span t-esc="doc.to_date"/>
                                                <strong>إلى تاريخ</strong>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                                <!-- Attendance Table -->
                                <table class="report">
                                    <thead>
                                        <tr>
                                            <th rowspan="2" style="min-width:150px"><p>Date</p> التاريخ</th>
                                            <th colspan="2">Period الفترة</th>
                                            <th rowspan="2">Delay Hours التأخير</th>
                                            <th rowspan="2">Early Leave التقصير</th>
                                            <th rowspan="2">Overtime الإضافي</th>
                                            <th rowspan="2">Working Hours ساعات العمل</th>
                                            <th rowspan="2">Notes ملاحظات</th>
                                            <th rowspan="2">Shift جدول الدوام</th>
                                        </tr>
                                        <tr>
                                            <th>In دخول</th>
                                            <th>Out خروج</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-set="working_hours" t-value="0"/>
                                        <t t-set="total_delay_hours" t-value="0"/>
                                        <t t-set="total_early_leave" t-value="0"/>
                                        <t t-set="total_overtime" t-value="0"/>
                                        <t t-set="total_absence_days" t-value="0"/>
                                        <t t-set="total_vacation_days" t-value="0"/>
                                        <t t-set="total_permission_hours" t-value="0"/>
                                        <t t-foreach="group['timesheets']" t-as="timesheet">
                                            <tr>
                                                <td><span t-esc="timesheet.date"/></td>
                                                <td><span
                                                        t-esc="doc.convert_to_time_format(timesheet.first_checkin_time)"/></td>
                                                <td><span
                                                        t-esc="doc.convert_to_time_format(timesheet.last_checkout_time)"/></td>
                                                <td>
                                                    <span t-attf-class="{{ 'red-text' if timesheet.delay_time > 0 else '' }}">
                                                        <span t-esc="doc.convert_to_time_format(timesheet.delay_time)"/>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span t-attf-class="{{ 'red-text' if timesheet.shortage_time > 0 else '' }}">
                                                        <span t-esc="doc.convert_to_time_format(timesheet.shortage_time)"/>
                                                    </span>
                                                </td>
                                                <td><span t-esc="doc.convert_to_time_format(timesheet.overtime)"/></td>
                                                <td><span
                                                        t-esc="doc.convert_to_time_format(timesheet.working_time)"/></td>
                                                <td><span t-esc="timesheet.notes"/></td>
                                                <td><span t-esc="timesheet.shift_id.name"/></td>
                                            </tr>
                                            <t t-set="total_delay_hours"
                                               t-value="total_delay_hours + timesheet.delay_time"/>
                                            <t t-set="total_early_leave"
                                               t-value="total_early_leave + timesheet.shortage_time"/>
                                            <t t-set="total_overtime" t-value="total_overtime + timesheet.overtime"/>
                                            <t t-set="working_hours" t-value="working_hours + timesheet.working_time"/>
                                            <t t-if="timesheet.is_absent">
                                                <t t-set="total_absence_days" t-value="total_absence_days + 1"/>
                                            </t>
                                            <t t-if="timesheet.is_vacation or timesheet.is_public_vacation">
                                                <t t-set="total_vacation_days" t-value="total_vacation_days + 1"/>
                                            </t>
                                            <t t-set="total_permission_hours"
                                               t-value="total_permission_hours + timesheet.time_off_hours"/>
                                        </t>
                                        <!-- Summation Row for Main Table -->
                                        <tr>
                                            <td><p>Total الإجمالي</p></td>
                                            <td></td>
                                            <td></td>
                                            <td><span t-esc="doc.convert_to_time_format(total_delay_hours)"/></td>
                                            <td><span t-esc="doc.convert_to_time_format(total_early_leave)"/></td>
                                            <td><span t-esc="doc.convert_to_time_format(total_overtime)"/></td>
                                            <td><span t-esc="doc.convert_to_time_format(working_hours)"/></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <!-- Additional Summation Rows -->
                                        <tr class="summation-row">
                                            <td colspan="3">
                                                <p>Total Absence Days</p>
                                                <p>إجمالي أيام الغياب</p>
                                            </td>
                                            <td colspan="1"><span t-esc="total_absence_days"/></td>
                                            <td colspan="3">
                                                <p>Total Vacation Days</p>
                                                <p>إجمالي أيام العطلة</p>
                                            </td>
                                            <td colspan="2"><span t-esc="total_vacation_days"/></td>
                                        </tr>
                                        <tr class="summation-row">
                                            <td colspan="3">
                                                <p>Total Delay and Early Leave Hours</p>
                                                <p>إجمالي التأخير والتقصير</p>
                                            </td>
                                            <td colspan="1"><span
                                                    t-esc="doc.convert_to_time_format(total_delay_hours + total_early_leave)"/></td>
                                            <td colspan="3">
                                                <p>Total Permission Hours</p>
                                                <p>إجمالي ساعات الاستئذان</p>
                                            </td>
                                            <td colspan="2"><span
                                                    t-esc="doc.convert_to_time_format(total_permission_hours)"/></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </t>
                        <div class="page_break" style="page-break-after: 1;"></div>
                    </t>
                </t>
            </t>

        </t>
    </template>
</odoo>