<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <template id="report_attendance_summary">
            <t t-call="web.html_container">
                <t t-if="not grouped_data">
                    <!-- Show a message for no matching records -->
                    <t t-call="web.external_layout">
                        <div class="page">
                            <style>
                                .no-data-box {
                                    width: 100%;
                                    padding: 50px 20px;
                                    border: 2px dashed #e74c3c;
                                    text-align: center;
                                    font-family: Arial, sans-serif;
                                    color: #2c3e50;
                                    margin-top: 100px;
                                }

                                .no-data-box h2 {
                                    font-size: 20px;
                                    color: #e74c3c;
                                    margin-bottom: 10px;
                                }

                                .no-data-box p {
                                    font-size: 14px;
                                    color: #555;
                                    margin: 5px 0;
                                }

                                .no-data-box .icon {
                                    font-size: 40px;
                                    color: #e74c3c;
                                    margin-bottom: 10px;
                                }

                                .no-data-box hr {
                                    margin: 15px auto;
                                    border: 0;
                                    border-top: 1px solid #e74c3c;
                                    width: 40%;
                                }
                            </style>

                            <div class="no-data-box">
                                <div class="icon">✘</div>
                                <h2>لا توجد نتائج مطابقة</h2>
                                <hr/>
                                <p>No matching records found for the selected filters.</p>
                                <p>يرجى مراجعة الفلاتر المختارة مثل التاريخ أو الموظف.</p>
                                <p>Check your filters (date range, employee, etc.) and try again.</p>
                            </div>
                        </div>
                    </t>
                </t>

                <t t-if="grouped_data">
                    <!-- Show the report as normal -->
                    <t t-foreach="docs" t-as="doc">
                        <t t-foreach="grouped_data.values()" t-as="department_group">
                            <!-- Page for each department -->
                            <t t-call="web.external_layout">
                                <div class="page">
                                    <!-- Style Section -->
                                    <style>
                                        body {
                                            font-family: 'Arial', sans-serif;
                                        }

                                        h2 {
                                            text-align: center;
                                            font-weight: bold;
                                            margin-bottom: 20px;
                                        }

                                        h4 {
                                            margin: 5px 0;

                                            text-align: right;
                                            align_items: flex-center


                                        }

                                        h3 {
                                            margin: 5px 0;
                                            text-align: left;


                                        }

                                        table {
                                            width: 100%;
                                            border-collapse: collapse;
                                            margin-top: 20px;
                                            text-align: center;
                                            direction: rtl;
                                            text-align: right;

                                        }

                                        th {
                                            border: 1px solid #ddd;
                                            padding: 8px;
                                            background-color: #f2f2f2;
                                            font-weight: bold;
                                        }

                                        td {
                                            border: 1px solid #ddd;
                                            padding: 8px;

                                        }

                                        tr:nth-child(even) {
                                            background-color: #f9f9f9;
                                        }

                                        tr:nth-child(odd) {
                                            background-color: #ffffff;
                                        }

                                        .totals-row {
                                            font-weight: bold;
                                        }
                                    </style>
                                    <h3><t t-esc="doc.date or ''"/></h3>


                                    <!-- Arabic Title -->
                                    <div style="display: flex; justify-content: space-between;">

                                        <h3>Attendance Summary Report</h3>
                                        <h3> تقرير الحضور والانصراف</h3>
                                    </div>


                                    <!-- Company and Date Information -->
                                    <div style="display: flex; justify-content: space-between;">
                                        <h4>إلى تاريخ: <t t-esc="doc.to_date or ''"/></h4>

                                        <h4>من تاريخ: <t t-esc="doc.from_date or ''"/></h4>
                                    </div>

                                    <!-- Department on the Right Side -->
                                    <h3 style="text-align: right;">الادارة: <t
                                            t-esc="department_group['department'].name"/></h3>


                                    <!-- Table with Attendance Data -->
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>الاسم</th>
                                                <th>عدد أيام الدوام الكلى</th>
                                                <th>عدد أيام الدوام الفعلى</th>
                                                <th>إجمالي ساعات العمل</th>
                                                <th>إجمالي التأخير</th>
                                                <th>إجمالي التقصير</th>
                                                <th>إجمالي التأخير والتقصير</th>
                                                <th>إجمالي أيام الغياب</th>
                                                <th>إجمالي الإستئذان</th>
                                                <th>إجمالي أيام الإجازات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Timesheet Data -->
                                            <t t-set="department_timesheets" t-value="[]"/>
                                            <t t-foreach="department_group['employees'].values()" t-as="group">
                                                <t t-set="employee" t-value="group['employee']"/>
                                                <t t-foreach="group['timesheets']" t-as="timesheet">
                                                    <tr>
                                                        <td><t t-esc="employee.name"/></td>
                                                        <td><t t-esc="timesheet.working_day_count or 0"/></td>
                                                        <td><t t-esc="timesheet.attend_day_count or 0"/></td>
                                                        <td><t t-esc="doc.convert_to_time_format(timesheet.working_time_min or 0)"/></td>
                                                        <td><t t-esc="doc.convert_to_time_format(timesheet.delay_time_min or 0)"/></td>
                                                        <td><t t-esc="doc.convert_to_time_format(timesheet.shortage_time_min or 0)"/></td>
                                                        <td><t t-esc="doc.convert_to_time_format((timesheet.delay_time_min or 0) + (timesheet.shortage_time_min or 0))"/></td>
                                                        <td><t t-esc="timesheet.absent_count or 0"/></td>
                                                        <td><t t-esc="timesheet.permission_count or 0"/></td>
                                                        <td><t t-esc="timesheet.vacation_count or 0"/></td>
                                                    </tr>
                                                    <!-- Collect timesheet for totals -->
                                                    <t t-set="department_timesheets"
                                                       t-value="department_timesheets + [timesheet]"/>
                                                </t>
                                            </t>

                                            <!-- Totals Row -->
                                            <tr class="totals-row">
                                                <td>المجموع الكلى للإدارة:</td>
                                                <td><t t-esc="sum(t.working_day_count for t in department_timesheets) or 0"/></td>
                                                <td><t t-esc="sum(t.attend_day_count for t in department_timesheets) or 0"/></td>
                                                <td><t t-esc="doc.convert_to_time_format(sum(t.working_time_min for t in department_timesheets) or 0)"/></td>
                                                <td><t t-esc="doc.convert_to_time_format(sum(t.delay_time_min for t in department_timesheets) or 0)"/></td>
                                                <td><t t-esc="doc.convert_to_time_format(sum(t.shortage_time_min for t in department_timesheets) or 0)"/></td>
                                                <td><t t-esc="doc.convert_to_time_format(sum((t.delay_time_min or 0) + (t.shortage_time_min or 0) for t in department_timesheets) or 0)"/></td>
                                                <td><t t-esc="sum(t.absent_count for t in department_timesheets) or 0"/></td>
                                                <td><t t-esc="sum(t.permission_count for t in department_timesheets) or 0"/></td>
                                                <td><t t-esc="sum(t.vacation_count for t in department_timesheets) or 0"/></td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </t>
                            <div class="page_break" style="page-break-after: 1;"></div>

                        </t>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>

