id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink

access_base_timesheet_admin,access_base_timesheet_admin,model_ams_ta_base_timesheet,base.group_system,1,1,1,1
access_base_timesheet_manager,access_base_timesheet_manager,model_ams_ta_base_timesheet,ams_base.ams_group_manager,1,1,1,1
access_base_timesheet_user,access_base_timesheet_user,model_ams_ta_base_timesheet,ams_base.ams_group_user,1,1,1,0
access_base_timesheet_readonly,access_base_timesheet_readonly,model_ams_ta_base_timesheet,ams_base.ams_group_readonly,1,0,0,0
access_base_timesheet_employee,access_base_timesheet_employee,model_ams_ta_base_timesheet,group_ta_employee,1,0,0,0

access_timesheet_admin,access_timesheet_admin,model_ams_ta_timesheet,base.group_system,1,1,1,1
access_timesheet_manager,access_timesheet_manager,model_ams_ta_timesheet,ams_base.ams_group_manager,1,1,1,1
access_timesheet_user,access_timesheet_user,model_ams_ta_timesheet,ams_base.ams_group_user,1,1,1,0
access_timesheet_readonly,access_timesheet_readonly,model_ams_ta_timesheet,ams_base.ams_group_readonly,1,0,0,0
access_timesheet_employee,access_timesheet_employee,model_ams_ta_timesheet,group_ta_employee,1,0,0,0

access_timesheet_time_unit_admin,access_timesheet_time_unit_admin,model_ams_ta_timesheet_time_unit,base.group_system,1,1,1,1
access_timesheet_time_unit_manager,access_timesheet_time_unit_manager,model_ams_ta_timesheet_time_unit,ams_base.ams_group_manager,1,1,1,1
access_timesheet_time_unit_user,access_timesheet_time_unit_user,model_ams_ta_timesheet_time_unit,ams_base.ams_group_user,1,1,1,0
access_timesheet_time_unit_readonly,access_timesheet_time_unit_readonly,model_ams_ta_timesheet_time_unit,ams_base.ams_group_readonly,1,0,0,0

access_punch_log_admin,access_punch_log_admin,model_ams_ta_punch_log,base.group_system,1,1,1,1
access_punch_log_manager,access_punch_log_manager,model_ams_ta_punch_log,ams_base.ams_group_manager,1,1,1,1
access_punch_log_user,access_punch_log_user,model_ams_ta_punch_log,ams_base.ams_group_user,1,1,1,0
access_punch_log_readonly,access_punch_log_readonly,model_ams_ta_punch_log,ams_base.ams_group_readonly,1,0,0,0

access_shift_admin,access_shift_admin,model_ams_ta_shift,base.group_system,1,1,1,1
access_shift_manager,access_shift_manager,model_ams_ta_shift,ams_base.ams_group_manager,1,1,1,1
access_shift_user,access_shift_user,model_ams_ta_shift,ams_base.ams_group_user,1,1,1,0
access_shift_readonly,access_shift_readonly,model_ams_ta_shift,ams_base.ams_group_readonly,1,0,0,0

access_time_unit_rule_admin,access_time_unit_rule_admin,model_ams_ta_time_unit_rule,base.group_system,1,1,1,1
access_time_unit_rule_manager,access_time_unit_rule_manager,model_ams_ta_time_unit_rule,ams_base.ams_group_manager,1,1,1,1
access_time_unit_rule_user,access_time_unit_rule_user,model_ams_ta_time_unit_rule,ams_base.ams_group_user,1,1,1,0
access_time_unit_rule_readonly,access_time_unit_rule_readonly,model_ams_ta_time_unit_rule,ams_base.ams_group_readonly,1,0,0,0

access_user_group_admin,access_user_group_admin,model_ams_user_group,base.group_system,1,1,1,1
access_user_group_manager,access_user_group_manager,model_ams_user_group,ams_base.ams_group_manager,1,1,1,1
access_user_group_user,access_user_group_user,model_ams_user_group,ams_base.ams_group_user,1,1,1,0
access_user_group_readonly,access_user_group_readonly,model_ams_user_group,ams_base.ams_group_readonly,1,0,0,0

access_manual_attendance_wizard_admin,access_manual_attendance_wizard_admin,model_ams_ta_manual_attendance_wizard,base.group_system,1,1,1,1
access_manual_attendance_wizard_manager,access_manual_attendance_wizard_manager,model_ams_ta_manual_attendance_wizard,ams_base.ams_group_manager,1,1,1,1
access_manual_attendance_wizard_user,access_manual_attendance_wizard_user,model_ams_ta_manual_attendance_wizard,ams_base.ams_group_user,1,1,1,0
access_manual_attendance_wizard_readonly,access_manual_attendance_wizard_readonly,model_ams_ta_manual_attendance_wizard,ams_base.ams_group_readonly,1,0,0,0

access_report_generator_wizard_admin,access_report_generator_wizard_admin,model_ta_report_generator_wizard,base.group_system,1,1,1,1
access_report_generator_wizard_manager,access_report_generator_wizard_manager,model_ta_report_generator_wizard,ams_base.ams_group_manager,1,1,1,1
access_report_generator_wizard_user,access_report_generator_wizard_user,model_ta_report_generator_wizard,ams_base.ams_group_user,1,1,1,0
access_report_generator_wizard_readonly,access_report_generator_wizard_readonly,model_ta_report_generator_wizard,ams_base.ams_group_readonly,1,0,0,0

access_timesheet_portal,access_timesheet_portal,model_ams_ta_timesheet,base.group_portal,1,0,0,0

access_event_log_admin,access_event_log_admin,model_ams_bs_event_log,base.group_system,1,1,1,1
access_event_log_manager,access_event_log_manager,model_ams_bs_event_log,ams_base.ams_group_manager,1,1,1,1
access_event_log_user,access_event_log_user,model_ams_bs_event_log,ams_base.ams_group_user,1,1,1,0
access_event_log_readonly,access_event_log_readonly,model_ams_bs_event_log,ams_base.ams_group_readonly,1,0,0,0

access_geo_fence_admin,access_geo_fence_admin,model_ams_ta_geo_fence_location,base.group_system,1,1,1,1
access_geo_fence_manager,access_geo_fence_manager,model_ams_ta_geo_fence_location,ams_base.ams_group_manager,1,1,1,1
access_geo_fence_user,access_geo_fence_user,model_ams_ta_geo_fence_location,ams_base.ams_group_user,1,1,1,0
access_geo_fence_readonly,access_geo_fence_readonly,model_ams_ta_geo_fence_location,ams_base.ams_group_readonly,1,0,0,0