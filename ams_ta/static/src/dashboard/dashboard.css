
.lp-dashboard-item-count {
    background-color: var(--dashboard-bg-color);
    color: white;
    border-radius: 5px;
    margin-top: 1rem;
    margin-bottom: 2rem;
}

.lp-dashboard-item-count__badge-container {
    margin-top: 1rem;
}

.lp-dashboard-item-count__badge {
    font-size: 1.5rem;
    padding: 0.5rem;
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    color: white;
    border: 3px solid white;
    display: flex;
    justify-content: center;
    align-items: center;

}

.lp-dashboard-item-count__icon {
    font-size: 1.5rem;
}

.lp-dashboard-item-count__label-container {
    margin-top: 2rem;
}

.lp-dashboard-item-count__label {
    color: white;
    font-size: 1.3rem;
}

.lp-dashboard-item-count__count-container {
    font-size: 2rem;
}

.lp-dashboard-item-count__chart-icon {
    font-size: 1.3rem;
}

.lp-dashboard-item-count__button-container {
    border-top: 1px solid rgba(0, 0, 0, .125);
}

.lp-dashboard-item-count__button {
    width: 100%;
    background-color: rgba(0, 0, 0, .03);
    border: none;
    text-align: center;
    padding: 1rem;
    color: white;
}

.lp-dashboard-item-count__button:hover {
    background-color: rgba(0, 0, 0, 0.1);
    opacity: 0.9;
}

.lp-dashboard_scrollable_container {
    max-height: 100vh;
    /*overflow-y: auto;*/
    padding: 20px;
    box-sizing: border-box;
}




