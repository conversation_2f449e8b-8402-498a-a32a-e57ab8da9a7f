/** @odoo-module **/
import {Component,useState} from "@odoo/owl";
import {registry} from "@web/core/registry";
import {DashboardItemCount} from "./dashboard_item_count";
import {_t} from "@web/core/l10n/translation";

// ✅ Arabic to Western numeral converter
function toWesternDigits(str) {
    return str.replace(/[٠-٩]/g, d => "٠١٢٣٤٥٦٧٨٩".indexOf(d));
}

// Main dashboard component
class AMSTADashboard extends Component {
    static template = "ams_ta.AwesomeDashboard";  // Reference to the XML template
    static components = {DashboardItemCount};   // Register child components

    // Custom function to get start (Sunday) and end (Saturday) of week
    getSundayWeekRange(date) {
        const weekday = date.weekday; // 1 (Monday) to 7 (Sunday)
        const daysToSubtract = weekday % 7; // If Sunday => 0, Monday => 1, etc.
        const start = date.minus({days: daysToSubtract}).startOf("day"); // Go back to Sunday
        const end = start.plus({days: 6}).endOf("day"); // Saturday
        return {start, end};
    }

    // Generic function to get start and end of a given period
    getPeriodRange(period, date, offset = {}) {
        const targetDate = date.minus(offset);
        return {
            start: targetDate.startOf(period),
            end: targetDate.endOf(period),
        };
    }

    // Date range handlers for various periods
    periodHandlers = {
        current_week: (date) => this.getSundayWeekRange(date),
        previous_week: (date) => this.getSundayWeekRange(date.minus({weeks: 1})),
        current_month: (date) => this.getPeriodRange("month", date),
        previous_month: (date) => this.getPeriodRange("month", date, {months: 1}),
        current_year: (date) => this.getPeriodRange("year", date),
        previous_year: (date) => this.getPeriodRange("year", date, {years: 1}),
        default: (date) => ({start: date, end: date}),
    };

    // Setup function to initialize the state with default date range
    setup() {
        const today = luxon.DateTime.now();
        const {start, end} = this.periodHandlers.previous_month(today);
        this.state = useState({
            startDate: toWesternDigits(start.toFormat("yyyy-MM-dd")),
            endDate: toWesternDigits(end.toFormat("yyyy-MM-dd")),
        });
    }

    // Updates state when user changes a date manually
    onDateChange(ev) {
        this.state[ev.target.name] = ev.target.value;
    }

    // Updates state when user selects a predefined period
    onPeriodChange(ev) {
        const {
            start,
            end
        } = (this.periodHandlers[ev.target.value] || this.periodHandlers.default)(luxon.DateTime.now());
        this.state.startDate = toWesternDigits(start.toFormat("yyyy-MM-dd"));
        this.state.endDate = toWesternDigits(end.toFormat("yyyy-MM-dd"));
    }

    // Translated label for the "Show More" button
    get getTranslatedButtonLabel() {
        return _t("Show More");
    }

    // Translated title of the dashboard
    get translateAttendanceDashboardTitle() {
        return _t("Attendance Dashboard");
    }

    // Constructs the dashboard items list with filters and metadata
    getAttendanceDashboardItems() {
        const {startDate, endDate} = this.state;
        const dateDomain = [['date', '>=', startDate], ['date', '<=', endDate]];

        return [
            {
                label: _t("Attendance"),
                model: "ams_ta.timesheet",
                domain: [['is_attend_day', '=', true], ...dateDomain],
                icon: "fa fa-check-circle",
                chartIcon: "fa fa-pie-chart",
                color: "bg-success",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: _t("Absent Count"),
                model: "ams_ta.timesheet",
                domain: [['is_absent', '=', true], ...dateDomain],
                icon: "fa fa-times-circle",
                chartIcon: "fa fa-bar-chart",
                color: "bg-danger",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: _t("Delay Count"),
                model: "ams_ta.timesheet",
                domain: [['is_delayed', '=', true], ...dateDomain],
                icon: "fa fa-exclamation-circle",
                chartIcon: "fa fa-line-chart",
                color: "bg-info",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: _t("Early Leave Count"),
                model: "ams_ta.timesheet",
                domain: [['is_shortage', '=', true], ...dateDomain],
                icon: "fa fa-clock-o",
                chartIcon: "fa fa-area-chart",
                color: "bg-warning",
                views: [[false, "list"], [false, "form"]],
            },
            // TODO: Implement additional dashboard items (Over Time, Vacation) in future iterations
            // Additional widgets can be enabled below if needed:
            // {
            //     label: _t("Over Time Count"),
            //     model: "ams_ta.timesheet",
            //     domain: [['is_overtime', '=', true], ...dateDomain],
            //     icon: "fa fa-calendar-check-o",
            //     chartIcon: "fa fa-bar-chart",
            //     color: "bg-info",
            //     views: [[false, "list"], [false, "form"]],
            // },
            // {
            //     label: _t("Vacation Count"),
            //     model: "ams_ta.timesheet",
            //     domain: [['is_vacation', '=', true], ['date', '=', today]],
            //     icon: "fa fa-sun-o",
            //     chartIcon: "fa fa-pie-chart",
            //     color: "bg-primary",
            //     views: [[false, "list"], [false, "form"]],
            // },
        ];
    }
}

// Register the dashboard component as an Odoo client-side action
registry.category("actions").add("ams_ta.dashboard", AMSTADashboard);
