<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
    <t t-name="ams_ta.AwesomeDashboard">
    <div class="lp-dashboard_scrollable_container">
        <div class="container-fluid o_secy_container m-3">
<!--            &lt;!&ndash; Display Today's Date &ndash;&gt;-->
            <!--            <h3 style="display: flex; align-items: center; white-space: nowrap; font-size: 24px; color: #000;">-->
            <!--                <i class="fa fa-tachometer ms-2 text-primary mx-1"></i>-->
            <!--                <t t-esc="translateAttendanceDashboardTitle"/> - <t t-esc="new Date().toLocaleDateString()"/>-->
            <!--            </h3>-->
            <!-- Display Selected Date Range -->
            <h3 style="display: flex; align-items: center; white-space: nowrap; font-size: 24px; color: #000;">
                <i class="fa fa-tachometer ms-2 text-primary mx-1"></i>
                <t t-esc="translateAttendanceDashboardTitle"/>
                -
                <t t-esc="state.startDate"/>
                to
                <t t-esc="state.endDate"/>
            </h3>

            <!-- Date Filter Inputs -->
            <div class="row mb-4 mt-4">
                <!-- Add period selector with previous_month as default -->
                <div class="col-md-3">
                    <label class="form-label">Period</label>
                    <select class="form-control" t-on-change="onPeriodChange">
                        <option value="current_week">Current Week</option>
                        <option value="previous_week">Previous Week</option>
                        <option value="current_month">Current Month</option>
                        <option value="previous_month" selected="selected">Previous Month</option>
                        <option value="current_year">Current Year</option>
                        <option value="previous_year">Previous Year</option>
                    </select>
                </div>
                <!-- Existing date inputs remain unchanged -->
                <div class="col-md-3">
                    <label class="form-label">Start Date</label>
                    <input type="date" t-att-value="state.startDate" name="startDate" class="form-control"
                           t-on-change="onDateChange"/>
                </div>
                <div class="col-md-3">
                    <label class="form-label">End Date</label>
                    <input type="date" t-att-value="state.endDate" name="endDate" class="form-control"
                           t-on-change="onDateChange"/>
                </div>
            </div>


            <!-- Dashboard Items -->
            <div class="row justify-content-start mx-2 mt-5">
                <t t-foreach="getAttendanceDashboardItems()" t-as="attendanceItem" t-key="attendanceItem.label">
                    <DashboardItemCount
                            size="16"
                            label="attendanceItem.label"
                            icon="attendanceItem.icon"
                            chartIcon="attendanceItem.chartIcon"
                            buttonLabel="this.getTranslatedButtonLabel"
                            color="attendanceItem.color"
                            model="attendanceItem.model"
                            domain="attendanceItem.domain"
                            views="attendanceItem.views"
                    />
                </t>
            </div>
        </div>
    </div>
</t>
</templates>
