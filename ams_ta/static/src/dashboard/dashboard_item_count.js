/** @odoo-module **/

import {Component, useState, onWillStart, onWillUpdateProps, onWillDestroy} from "@odoo/owl";
import {useService} from "@web/core/utils/hooks";

export class DashboardItemCount extends Component {
    static template = "ams_ta.DashboardItemCount";

    static props = {
        size: {type: Number, default: 18, optional: true},
        label: {type: String, default: "Item Label"},
        icon: {type: String, optional: true},
        chartIcon: {type: String, optional: true},
        buttonLabel: {type: String, default: "Show More", optional: true},
        color: {type: String, default: "#ffffff", optional: true},
        model: {type: String, required: true},
        domain: {type: Array, default: [], required: true},
        views: {type: Array, required: true},
    };

    setup() {
        this.orm = useService("orm");
        this.action = useService("action");
        this.busService = this.env.services.bus_service;

        this.state = useState({
            count: 0,
        });

        onWillStart(async () => {
            await this.fetchCount();
        });

        onWillUpdateProps(async (nextProps) => {
            if (JSON.stringify(nextProps.domain) !== JSON.stringify(this.props.domain)) {
                await this.fetchCount(nextProps.domain);
            }
        });

        const refreshBusListener = async (payload) => {
            if (payload.model === this.props.model) {
                await this.fetchCount(this.props.domain);
            }
        };
        this.busService.subscribe("auto_refresh", refreshBusListener);
        this.busService.addChannel("auto_refresh");
        this._refreshStopBus = () => {
            this.busService.unsubscribe("auto_refresh", refreshBusListener);
            this.busService.deleteChannel("auto_refresh");
        };

        onWillDestroy(() => {
            this._refreshStopBus();
        });
    }

    async fetchCount(domain) {
        const context = {
            apply_followup_domain: 1, // 👈 This will trigger your `_search` logic
        };
        this.orm
            .searchCount(this.props.model, domain || this.props.domain, {context})
            .then((count) => (this.state.count = count));
    }

    openEntryView() {
        this.action.doAction({
            type: "ir.actions.act_window",
            name: `${this.props.label}`,
            res_model: this.props.model,
            views: this.props.views,
            domain: this.props.domain,
        });
    }
}