
### Steps to run a Test

    Before we know how to write test cases, we need to know how they run.

    To run a test, the --test-enable parameter and -i 
    parameters are used to indicate which you would like to test.
    Apply the given command if you are required to execute a test on your local odoo:


    ./odoo-bin -i {module_to_install} --test-enable -c {conf path}

    1) Launch odoo with the flag --test-enable

    1) Launch odoo with the flag -d [my_database]

    2) Launch odoo with the flag -i [modules_to_install]

    3) E.g: 

    -c ./config/ams18.conf -i ams_ta, --test-enable --log-level=error --test-tags ta_timesheet

    Linux:
    >> ./odoo-bin -i ta --test-enable -c /odoo.conf
   
     Windows:
    >> python E:\abadr\Work\01.laplace\026.EMR\src\odoo\odoo-bin -i ta --test-enable --log-level=error -c E:\abadr\Work\01.laplace\026.EMR\src\odoo\debian\ta15e.conf 

   >> python E:\abadr\Work\01.laplace\026.EMR\src\odoo\odoo-bin test --test-name=test_normal_timesheet --test-module=ta --test-download=E:\abadr\Work\01.laplace\026.EMR\src\odoo\unittest -c E:\abadr\Work\01.laplace\026.EMR\src\odoo\debian\ta15e.conf --test-download=E:\abadr\Work\01.laplace\026.EMR\src\odoo\unittest


> 
> 