<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: base_timesheet_view_form-->
    <record id="base_timesheet_view_form" model="ir.ui.view">
        <field name="name">ams_ta.base_timesheet.form</field>
        <field name="model">ams_ta.base_timesheet</field>
        <field name="arch" type="xml">
            <form>
                <header>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="employee_id"/>
                            <field name="date"/>
                            <field name="first_checkin_time"/>
                            <field name="last_checkout_time"/>

                        </group>
                        <group>
                            <field name="working_time"/>
                            <field name="delay_time"/>
                            <field name="shortage_time"/>
                            <field name="overtime"/>

                        </group>
                    </group>
                    <notebook>

                        <page string="Time Summary" name="time_summary">
                            <group>
                                <group>
                                    <field name="required_time"/>
                                    <field name="working_time"/>
                                    <field name="delay_time"/>
                                    <field name="shortage_time"/>
                                </group>
                                <group>
                                    <field name="total_delay_shortage"/>
                                    <field name="overtime"/>
                                    <field name="overtime_factored"/>

                                </group>
                            </group>
                        </page>
                        <page string="Employee Info">
                            <group>
                                <field name="employee_id"/>
                            </group>
                        </page>
                        <page string="Shift Histories">
                            <group>
                                <field name="name"/>
                            </group>
                        </page>
                        <page string="Other Info">
                            <group>
                                <field name="checked_in"/>
                                <field name="checked_out"/>

                            </group>
                        </page>

                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: base_timesheet_view_list-->
    <record id="base_timesheet_view_list" model="ir.ui.view">
        <field name="name">ams_ta.base_timesheet.list</field>
        <field name="model">ams_ta.base_timesheet</field>
        <field name="arch" type="xml">
            <list string="Base Timesheets">
                <field name="employee_id"/>
                <field name="date"/>
                <field name="working_time"/>
                <field name="overtime"/>
                <field name="delay_time"/>
                <field name="shortage_time"/>
                <field name="first_checkin_time"/>
                <field name="last_checkout_time"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: base_timesheet_view_search-->
    <record id="base_timesheet_view_search" model="ir.ui.view">
        <field name="name">ams_ta.base_timesheet.list</field>
        <field name="model">ams_ta.base_timesheet</field>
        <field name="arch" type="xml">
            <search>
            </search>
        </field>
    </record>


    <!--TODO[IMP]: base_timesheet_action-->
    <record id="base_timesheet_action" model="ir.actions.act_window">
        <field name="name">Base Timesheets</field>
        <field name="res_model">ams_ta.base_timesheet</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Timesheet
            </p>
            <p>
                Create Timesheet
            </p>
        </field>
    </record>
</odoo>
