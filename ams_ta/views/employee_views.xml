<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: employee_view_form-Inheritance -->
    <record id="ams_ta_employee_view_form" model="ir.ui.view">
        <field name="name">ams_ta.employee_form_inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="ams_base.ams_employee_view_form"/>
        <field name="mode">extension</field>
        <field name="arch" type="xml">
            <field name="employee_number" position="before">
                <field name="user_group_id" string="User Group"/>
                <field name="last_generate_absent_date" groups="base.group_no_one"/>
            </field>
            <!--            <xpath expr="//group[@name='access_col2']" position="inside">-->
            <!--                <field name="user_group_id" string="User Group"/>-->
            <!--            </xpath>-->
        </field>
    </record>
    <!--TODO[IMP]: employee_view_list-Inheritance-->
    <record id="ams_ta_employee_view_list" model="ir.ui.view">
        <field name="name">ams_ta.employee_list_inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="mode">extension</field>
        <field name="arch" type="xml">
            <xpath expr="//list/header" position="inside">
                <button
                        string="Manual Attendance"
                        type="action"
                        name="%(action_manual_attendance_wizard)d"
                        class="btn-primary"
                        icon="fa-calendar"
                />
                <button
                        string="Generate Yesterday Absent"
                        type="object"
                        name="action_generate_yesterday_absent"
                        class="btn-primary"
                        icon="fa-calendar"
                />
                <button
                        string="Generate Test Punch Logs"
                        type="object"
                        name="action_generate_test_punch_logs"
                        class="btn-primary"
                        icon="fa-map-marker"
                        groups="base.group_no_one"
                />
            </xpath>
        </field>
    </record>
</odoo>
