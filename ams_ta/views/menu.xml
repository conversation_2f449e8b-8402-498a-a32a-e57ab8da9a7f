<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <menuitem name="Attendance Dashboard" id="ams_ta_combined_dashboard_menu" action="action_client_ams_ta_dashboard"
                  sequence="25" parent="ams.ams_combined_dashboard_menu"/>
        <menuitem id="top_menu" name="Time Attendance" sequence="25" web_icon="ams_ta,static/description/icon.png"/>
        <menuitem name="Attendance Dashboard" id="ams_ta_dashboard_menu" action="action_client_ams_ta_dashboard"
                  parent="top_menu" sequence="25"/>
        <menuitem id="op_menu" name="Operations" sequence="30" parent="top_menu">
            <menuitem id="timesheet_menu" name="Timesheet" action="timesheet_action" sequence="10"/>
            <menuitem id="punch_log_menu" name="Punch Log" action="punch_log_action" sequence="20"/>
            <menuitem id="exception_shifts_operations" name="Exception Shifts" action="exception_shift_action"
                      sequence="50"/>

        </menuitem>

        <menuitem id="menu_hr_root" name="HR" sequence="35" parent="top_menu"
                  groups="base.group_system">
            <menuitem id="user_groups_menu" name="Employee Groups" action="ams.user_group_action" sequence="10"/>
            <menuitem id="menu_hr_department" name="Departments" action="hr.hr_department_kanban_action" sequence="20"/>
            <menuitem id="employees_menu" name="Employees" action="ams_base.employee_action" sequence="30"/>
        </menuitem>
        <menuitem id="config_menu" name="Configurations" sequence="40" parent="top_menu"
                  groups="base.group_system">
            <menuitem id="shift_rule_menu" name="Time Unit Rules" action="time_unit_rule_action" sequence="10"/>
            <menuitem id="ta_time_unit_menu" name="Time Units" action="ams_ta_time_unit_action" sequence="20"/>
            <menuitem id="ta_schedule_menu" name="Schedules" action="ams_ta_schedule_action" sequence="30"/>
            <menuitem id="shift_menu" name="Shifts" action="shift_action" sequence="40"/>
            <menuitem id="exception_shifts" name="Exception Shifts" action="exception_shift_action" sequence="50"/>
        </menuitem>
        <menuitem id="report_generator_menu" name="Report" action="action_report_generator_wizard" parent="top_menu"
                  sequence="45"/>
        <menuitem id="employee_timesheet_menu_top_menu" name="Timesheet" action="timesheet_action" sequence="50" groups="group_ta_employee" web_icon="ams_ta,static/description/icon.png"/>



    </data>

</odoo>