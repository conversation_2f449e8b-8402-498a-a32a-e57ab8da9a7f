<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add Timesheet menu to the portal breadcrumbs -->
    <template id="portal_timesheet" name="Timesheet" inherit_id="portal.portal_breadcrumbs" priority="30">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <li t-if="page_name == 'timesheet'" t-attf-class="breadcrumb-item #{'active ' if timesheet else ''}">
                <a t-if="timesheet" t-attf-href="/timesheet?{{ keep_query() }}">Timesheet</a>
                <t t-else="">Timesheet</t>
            </li>
        </xpath>
    </template>

    <!-- Add Timesheet menu to the portal home page -->
    <template id="portal_my_home_menu_timesheet" name="Timesheet" inherit_id="portal.portal_my_home"
              customize_show="True" priority="30">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="before">
            <t t-set="portal_client_category_enable" t-value="True"/>
        </xpath>
        <div id="portal_client_category" position="inside">
            <t t-call="portal.portal_docs_entry">
                <t t-set="icon" t-value="'/ams_ta/static/src/image/timesheet_image.jpg'"/>
                <t t-set="title">Timesheet</t>
                <t t-set="url" t-value="'/timesheet'"/>
                <t t-set="text">View Timesheets</t>
                <t t-set="placeholder_count" t-value="'portal_timesheet'"/>
            </t>
        </div>
    </template>

    <!-- Define the Timesheet view -->
    <template id="portal_my_home_timesheet_views" name="My Timesheet">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>
            <t t-call="portal.portal_searchbar">
                <t t-set="title">Timesheet</t>
            </t>
            <t t-call="portal.portal_table">
                <thead>
                    <tr class="active">
                        <th class="text-left">Name</th>
                        <th class="text-left">Date</th>
                        <th class="text-center">Check In</th>
                        <th class="text-center">Check Out</th>
                        <th class="text-center">Working Time</th>
                        <th class="text-end">Delay Time</th>
                    </tr>
                </thead>
                <t t-if="grouped">
                    <t t-foreach="timesheets" t-as="group">
                        <tr>
                            <td colspan="6" class="fw-bold">Group: <t t-esc="group.get(groupby, 'UnKnown')"/></td>
                        </tr>
                        <t t-foreach="group['records']" t-as="timesheet">
                            <tr t-att-onclick="'window.location.href=\'/timesheet/details/%s\'' % timesheet.id"
                                style="cursor: pointer;" class="text-left align-middle">
                                <td class='text-left'><t t-esc="timesheet.name"/></td>
                                <td class='text-center'><t t-esc="timesheet.date"/></td>
                                <td class='text-center'><t t-esc="timesheet.first_checkin_datetime"/></td>
                                <td class='text-end'><t t-esc="timesheet.last_checkout_datetime"/></td>
                                <td class='text-end'><t t-esc="timesheet.working_time"/></td>
                                <td class='text-end'><t t-esc="timesheet.delay_time"/></td>
                            </tr>
                        </t>
                    </t>
                </t>

                <t t-if="not grouped">
                    <t t-foreach="timesheets" t-as="timesheet">
                        <tr t-att-onclick="'window.location.href=\'/timesheet/details/%s\'' % timesheet.id"
                            style="cursor: pointer;" class="text-left align-middle">
                            <td class='text-left'><t t-esc="timesheet.name"/></td>
                            <td class='text-center'><t t-esc="timesheet.date"/></td>
                            <td class='text-center'><t t-esc="timesheet.first_checkin_datetime"/></td>
                            <td class='text-end'><t t-esc="timesheet.last_checkout_datetime"/></td>
                            <td class='text-end'><t t-esc="timesheet.working_time"/></td>
                            <td class='text-end'><t t-esc="timesheet.delay_time"/></td>
                        </tr>
                    </t>
                </t>
            </t>

        </t>
    </template>
    <!-- Form View for Timesheet -->
    <template id="portal_timesheet_form_view" name="Timesheet Form View">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>
            <t t-call="portal.portal_breadcrumbs">
                <t t-set="breadcrumbs" t-value="[('Timesheet', '/timesheet'), ('Form', False)]"/>
            </t>
            <div class="container mt-4">
                <h3>Timesheet Details</h3>
                <div class="card">
                    <div class="card-body">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th>Name</th>
                                    <td><span t-field="timesheet.name"/></td>
                                </tr>
                                <tr>
                                    <th>Date</th>
                                    <td><span t-field="timesheet.first_checkin_datetime"/></td>
                                </tr>
                                <tr>
                                    <th>Check In</th>
                                    <td><span t-field="timesheet.first_checkin_datetime"/></td>
                                </tr>
                                <tr>
                                    <th>Check Out</th>
                                    <td><span t-field="timesheet.last_checkout_datetime"/></td>
                                </tr>
                                <tr>
                                    <th>Working Time</th>
                                    <td><span t-field="timesheet.working_time"/></td>
                                </tr>
                                <tr>
                                    <th>Delay Time</th>
                                    <td><span t-field="timesheet.delay_time"/></td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="text-right mt-3">
                            <!-- Previous Button -->
                            <t t-if="prev_record">
                                <a t-att-href="prev_record" class="btn btn-secondary">Previous</a>
                            </t>
                            <!-- Next Button -->
                            <t t-if="next_record">
                                <a t-att-href="next_record" class="btn btn-secondary">Next</a>
                            </t>
                            <a t-attf-href="/timesheet" class="btn btn-secondary">Back</a>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>