<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Punch Log Form View -->
    <record id="punch_log_view_form" model="ir.ui.view">
        <field name="name">ams_ta.punch_log.form</field>
        <field name="model">ams_ta.punch_log</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button string="Execute Punch Log" type="object" name="action_execute_punch_log"
                            class="btn-primary"/>
                    <field name="state" widget="statusbar"/>
                    <button string="Validate Geo Fence" type="object" name="action_validate_geo_fence_punch_log"
                            class="btn-primary"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="longitude"/>
                            <field name="date_time"/>
                            <field name="notes"/>
                            <field name="from_mobile"/>
                        </group>
                        <group>
                            <field name="device_serial"/>
                            <field name="latitude"/>
                            <field name="date" widget="date"/>
                            <field name="time" widget="float_time"/>
                            <field name="execute_log_time"/>
                            <field name="timesheet_id"/>
                        </group>

                    </group>
                    <notebook>
                        <page string="Employee Info">
                            <group>
                                <group>
                                    <field name="employee_id" context="{'apply_followup_domain':1}"/>
                                    <field name="employee_number" readonly="True"/>
                                    <field name="enroll_number" readonly="True"/>
                                </group>
                                <group>
                                    <field name="department_id" readonly="True"/>
                                    <field name="dept_path_code" readonly="True" groups="base.group_no_one"/>
                                </group>
                            </group>
                        </page>
                       <page string="Geo Fence Validation">
                                <group>
                            <field name="is_within_geo_fence"/>
                            <field name="geo_fence_distance"/>
                            <field name="nearest_location_id"/>
                            <field name="geo_fence_status"/>
                        </group>
                        </page>

                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Punch Log List View -->
    <record id="punch_log_view_list" model="ir.ui.view">
        <field name="name">ams_ta.punch_log.list</field>
        <field name="model">ams_ta.punch_log</field>
        <field name="arch" type="xml">
            <list string="Punch Logs">
                <header>
                    <button string="Execute Punch Log" type="object" name="action_execute_punch_log"
                            class="btn-primary"/>
                </header>
                <field name="name"/>
                <field name="employee_id"/>
                <field name="date_time"/>
                <field name="date" optional="hide"/>
                <field name="time" optional="hide"/>
                <field name="state" decoration-success="state == 'executed'" decoration-info="state == 'pending'"/>
                <field name="timesheet_id" optional="hide"/>
                <field name="execute_log_time" optional="hide"/>
                <field name="employee_number" optional="hide"/>
                <field name="device_serial" optional="hide"/>
                <field name="dept_path_code" optional="hide" groups="base.group_no_one"/>
                <field name="notes" optional="hide"/>
                <field name="longitude" optional="hide"/>
                <field name="latitude" optional="hide"/>
                <field name="from_mobile" optional="hide"/>
            </list>
        </field>
    </record>

    <!-- Punch Log Search View -->
    <record id="punch_log_view_search" model="ir.ui.view">
        <field name="name">ams_ta.punch_log.search</field>
        <field name="model">ams_ta.punch_log</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="employee_id"/>
                <field name="employee_number"/>
                <field name="enroll_number"/>
                <field name="date"/>

                <!-- Date Filters -->
                <filter string="Today" name="filter_today" domain="[('date', '=', context_today())]"/>
                <filter string="Yesterday" name="filter_yesterday" domain="[('date', '=', (context_today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d'))]"/>

                <separator/>

                <!-- Filters for grouping -->
                <group expand="1" string="Group By">
                    <filter string="Employee" name="group_by_employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Department" name="group_by_department" context="{'group_by': 'department_id'}"/>
                    <filter string="User Group" name="group_by_user_group" context="{'group_by': 'user_group_id'}"/>
                    <filter string="Date" name="group_by_date" context="{'group_by': 'date'}"/>
                </group>

            </search>
        </field>
    </record>


    <!--    &lt;!&ndash; Leaflet Map View Definition &ndash;&gt;-->
    <!--    <record id="punch_log_view_map_location_leaflet" model="ir.ui.view">-->
    <!--        <field name="name">ams_ta.punch_log.leaflet</field>-->
    <!--        <field name="model">ams_ta.punch_log</field>-->
    <!--        <field name="type">leaflet_map</field>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <leaflet_map string="Map Locations"-->
    <!--                         field_latitude="latitude"-->
    <!--                         field_longitude="longitude"-->
    <!--                         field_title="name"-->
    <!--                         limit="100"-->
    <!--                         panel_title="Map Locations Panel"-->
    <!--                         hideAddress="True"-->
    <!--                         hideName="True"-->
    <!--                         hideTitle="True"-->
    <!--            >-->
    <!--                <field name="name"/>-->
    <!--                <field name="latitude"/>-->
    <!--                <field name="longitude"/>-->
    <!--            </leaflet_map>-->

    <!--        </field>-->
    <!--    </record>-->

    <!-- Map View Definition -->
    <record id="punch_log_view_lp_map" model="ir.ui.view">
        <field name="name">ams_ta.punch_log.lp_map</field>
        <field name="model">ams_ta.punch_log</field>
        <field name="type">lp_map</field>
        <field name="arch" type="xml">
            <lp_map string="Map Locations"
                    field_latitude="latitude"
                    field_longitude="longitude"
                    field_title="name"
                    limit="100"
                    panel_title="Map Locations Panel"
                    hide_address="False"
                    hide_name="False"
                    hide_title="False"
            >
                <field name="name" string="Name"/>
                <field name="longitude" string="Longitude"/>
                <field name="latitude" string="Latitude"/>
                <field name="from_mobile" string="From Mobile"/>
                <field name="device_serial" string="Device Serial"/>
                <field name="timesheet_id" string="Timesheet"/>
                <field name="execute_log_time" string="Execute Log Time"/>

            </lp_map>
        </field>
    </record>

    <record id="punch_log_server_action" model="ir.actions.server">
        <field name="name">Punch Logs</field>
        <field name="model_id" ref="model_ams_ta_punch_log"/>
        <field name="binding_model_id" ref="model_ams_ta_punch_log"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_ta.punch_log'].sudo().action_open_views(
            {
            'action_ref': 'ams_ta.punch_log_action',
            'use_domain_follow_up_visibility': False
            }
            )

        </field>
        <field name="type">ir.actions.server</field>
        <field name="groups_id" eval="[(4, ref('base.group_no_one'))]"/>
    </record>


    <!-- Punch Log Action -->
    <record id="punch_log_action" model="ir.actions.act_window">
        <field name="name">Punch Logs</field>
        <field name="res_model">ams_ta.punch_log</field>
        <field name="view_mode">list,form,search,lp_map</field>
        <field name="context">{'search_default_filter_today': 1, 'search_default_available': 1, 'apply_followup_domain': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Manage Punch Logs
            </p>
            <p>
                View and manage punch logs for employees.
            </p>
        </field>
    </record>
</odoo>
