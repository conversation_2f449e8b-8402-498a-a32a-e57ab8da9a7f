<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: schedule_view_form-->
    <record id="ams_ta_schedule_view_form" model="ir.ui.view">
        <field name="name">ams.schedule.form</field>
        <field name="model">ams.schedule</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button string="Generate Days" type="object" name="action_generate_days" class="btn-primary"/>
                <button name="action_update_working_schedule" type="object" string="Update Working Schedule"
                        class="btn-primary"/>
                <button name="action_activate" type="object" string="Activate" class="btn-primary"
                        invisible="activate"/>
                <button name="action_reset" type="object" string="Reset" class="btn-secondary"
                        invisible="not activate"/>
            </xpath>

            <xpath expr="//field[@name='description']" position="after">
                <field name="schedule_type" string="Schedule Type"/>
                <field name="resource_calendar_id" readonly="1" groups="base.group_no_one" required="0"/>
                <field name="cycle_days" string="Cycle Days" invisible="schedule_type=='weekly'"/>
            </xpath>
            <xpath expr="//field[@name='last_sync_date']" position="after">
                <field name="default_time_unit_id" string="Default Time Unit"
                       context="{
                                   'form_view_ref': 'ams_ta.ams_ta_time_unit_view_form',
                                   'search_view_ref': 'ams_ta.ams_ta_time_unit_view_search',
                                   'list_view_ref': 'ams_ta.ams_ta_time_unit_view_list'
                               }"/>
                <field name="purpose_type" string="Purpose Type" groups="base.group_no_one"/>
            </xpath>
            <xpath expr="//page[@name='basic_info']" position="replace">
                <page name="days" string="Days">
                    <field name="day_ids" string="Days" readonly="is_readonly">
                        <list editable="bottom">
                            <field name="name"/>
                            <field name="week_day" optional="hide"/>
                            <field name="is_day_off" optional="show" widget="boolean_toggle"/>
                            <field name="time_units_ids" widget="many2many_tags"/>
                            <field name="is_units_overlapped" optional="hide"/>
                            <field name="is_units_mixed_types" optional="hide"/>
                            <field name="index" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: schedule_view_list-->
    <record id="ams_ta_schedule_view_list" model="ir.ui.view">
        <field name="name">ams.schedule.list</field>
        <field name="model">ams.schedule</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="inside">
                <field name="schedule_type" string="Schedule Type"/>
                <field name="purpose_type" string="Purpose Type"/>

            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: schedule_view_search-->
    <record id="ams_ta_schedule_view_search" model="ir.ui.view">
        <field name="name">ams.schedule.search</field>
        <field name="model">ams.schedule</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">

            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: schedule_action-->
    <record id="ams_ta_schedule_action" model="ir.actions.act_window">
        <field name="name">Schedules</field>
        <field name="res_model">ams.schedule</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'default_purpose_type': 'ta', 'search_default_available': 1}</field>
        <field name="domain">[('purpose_type', 'in', ['ta', 'all'])]</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'list', 'view_id': ref('ams_ta_schedule_view_list')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('ams_ta_schedule_view_form')})]"/>
        <field name="target">current</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a schedule
            </p>
            <p>
                Create schedule
            </p>
        </field>
    </record>
</odoo>
