<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: shift_view_form-->
    <record id="shift_view_form" model="ir.ui.view">
        <field name="name">ams_ta.shift.form</field>
        <field name="model">ams_ta.shift</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="schedule_id" context="{
                                                           'form_view_ref': 'ams_ta.ams_ta_schedule_view_form',
                                                           'search_view_ref': 'ams_ta.ams_ta_schedule_view_search',
                                                           'list_view_ref': 'ams_ta.ams_ta_schedule_view_list'
                                                       }"/>
                            <field name="is_default" invisible="is_exception==True"/>
                        </group>
                        <group>
                            <field name="start_date"/>
                            <field name="end_date"/>
                        </group>
                    </group>
                    <group string="Exceptions" invisible="is_exception==False">
                        <group>
                            <field name="employee_id"/>
                            <field name="status"/>
                        </group>
                        <group>
                            <button name="action_execute" type="object" string="Execute" class="btn btn-secondary"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="technical" string="Technical Info" groups="base.group_no_one">
                            <group name="row1">
                                <group name="technical_col1">
                                    <field name="is_exception"/>
                                </group>
                                <group name="technical_col2">
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: shift_view_list-->
    <record id="shift_view_list" model="ir.ui.view">
        <field name="name">ams_ta.shift.tree</field>
        <field name="model">ams_ta.shift</field>
        <field name="arch" type="xml">
            <list string="Shifts">
                <field name="name"/>
                <field name="start_date"/>
                <field name="end_date"/>
                <field name="state"/>
                <field name="is_default" optional="hide"/>
                <field name="is_exception" optional="hide"/>
                <field name="status"/>
                <field name="schedule_id" optional="hide"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: shift_view_search-->
    <record id="shift_view_search" model="ir.ui.view">
        <field name="name">ams_ta.shift.search</field>
        <field name="model">ams_ta.shift</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="start_date"/>
                <field name="end_date"/>
                <field name="state"/>
                <field name="status"/>
                <field name="schedule_id"/>
                <filter string="Pending Shifts" name="pending_shifts" domain="[('status', '=', 'pending')]"/>
                <filter string="Executed Shifts" name="executed_shifts" domain="[('status', '=', 'executed')]"/>
                <filter string="Default Shifts" name="default_shifts" domain="[('is_default', '=', True)]"/>
                <group expand="1" string="Group By">
                    <filter string="Schedule" name="schedule" context="{'group_by': 'schedule_id'}"/>
                    <filter string="State" name="state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!--TODO[IMP]: shift_action-->
    <record id="shift_action" model="ir.actions.act_window">
        <field name="name">Shifts</field>
        <field name="res_model">ams_ta.shift</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="domain">[('is_exception', '=', False)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a shift
            </p>
            <p>
                Create a shift
            </p>
        </field>
    </record>

    <!--TODO[IMP]: Exception_shift_action-->
    <record id="exception_shift_action" model="ir.actions.act_window">
        <field name="name"> Exception Shifts</field>
        <field name="res_model">ams_ta.shift</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'default_is_exception': True, 'search_default_available': 1}</field>
        <field name="domain">[('is_exception', '=', True)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an Exception shift
            </p>
            <p>
                Create an Exception shift
            </p>
        </field>
    </record>
</odoo>
