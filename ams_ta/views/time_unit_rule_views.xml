<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: time_unit_rule_view_form-->
    <record id="time_unit_rule_view_form" model="ir.ui.view">
        <field name="name">ams_ta.time_unit_rule.form</field>
        <field name="model">ams_ta.time_unit_rule</field>
        <field name="arch" type="xml">
            <form>
                <header>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="overtime_factor"/>
                            <field name="apply_working_hour_deduction"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Setting">
                            <group>
                                <group string="Grace Time">
                                    <field name="grace_in_time" widget="float_time"/>
                                    <field name="grace_out_time" widget="float_time"/>
                                </group>
                                <group string="Half Working Day">
                                    <field name="apply_half_work"/>
                                    <field name="half_work_checkin_criteria" invisible="apply_half_work == False"
                                           widget="float_time"/>
                                    <field name="half_work_checkout_criteria" invisible="apply_half_work == False"
                                           widget="float_time"/>
                                    <field name="calc_half_no_checkout_time" invisible="apply_half_work == False"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>

            </form>
        </field>
    </record>

    <!--TODO[IMP]: time_unit_rule_view_list-->
    <record id="time_unit_rule_view_list" model="ir.ui.view">
        <field name="name">ams_ta.time_unit_rule.list</field>
        <field name="model">ams_ta.time_unit_rule</field>
        <field name="arch" type="xml">
            <list string="Shift Rule">
                <field name="name"/>
                <field name="grace_in_time" widget="float_time"/>
                <field name="grace_out_time" widget="float_time"/>
                <field name="apply_half_work"/>
                <field name="apply_working_hour_deduction"/>
                <field name="half_work_checkin_criteria" optional="hide" widget="float_time"/>
                <field name="half_work_checkout_criteria" optional="hide" widget="float_time"/>
                <field name="calc_half_no_checkout_time" optional="hide"/>
                <field name="overtime_factor" optional="hide"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: time_unit_rule_view_search-->
    <record id="time_unit_rule_view_search" model="ir.ui.view">
        <field name="name">ams_ta.time_unit_rule.search</field>
        <field name="model">ams_ta.time_unit_rule</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
            </search>
        </field>
    </record>

    <!--TODO[IMP]:time_unit_rule_action-->
    <record id="time_unit_rule_action" model="ir.actions.act_window">
        <field name="name">Time Unit Rules</field>
        <field name="res_model">ams_ta.time_unit_rule</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create Unit rule
            </p>
            <p>
                Create Unit rule
            </p>
        </field>
    </record>
</odoo>
