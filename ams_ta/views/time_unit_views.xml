<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: time_unit_view_form-->
    <record id="ams_ta_time_unit_view_form" model="ir.ui.view">
        <field name="name">ams_ta.time_unit.form</field>
        <field name="model">ams.time_unit</field>
        <field name="arch" type="xml">
            <form>
                <header>
                </header>
                <sheet>
                    <group>
                        <!--                        <group>-->
                        <!--                            <field name="code"/>-->
                        <!--                            <field name="en_name"/>-->
                        <!--                            <field name="ar_name"/>-->
                        <!--                        </group>-->
                        <group>
                            <field name="name"/>
                            <field name="unit_type"/>
                            <field name="rule_id" required="1"/>
                            <field name="color" widget="color"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Setting">
                            <group>
                                <group>
                                    <field name="start_time" widget="float_time" readonly="unit_type == 'open'"/>
                                    <field name="duration" widget="float_time"/>
                                    <field name="start_limit" invisible="unit_type != 'flexible'" widget="float_time"/>
                                    <field name="end_time" widget="float_time"
                                           readonly="unit_type in ['open','flexible']"/>
                                    <field name="end_limit" invisible="unit_type != 'flexible'" widget="float_time"/>
                                    <field name="is_overnight"/>
                                </group>
                                <group>
                                    <field name="absent_time_criteria" widget="float_time"/>
                                    <field name="apply_min_max" invisible="unit_type == 'open'"/>
                                    <field name="min_checkin_time" invisible="apply_min_max == False"
                                           widget="float_time"/>
                                    <field name="max_checkout_time" invisible="apply_min_max == False"
                                           widget="float_time"/>
                                    <field name="purpose_type" string="Purpose Type" groups="base.group_no_one"/>

                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>

            </form>
        </field>
    </record>

    <!--TODO[IMP]: time_unit_view_list-->
    <record id="ams_ta_time_unit_view_list" model="ir.ui.view">
        <field name="name">ams_ta.time_unit.list</field>
        <field name="model">ams.time_unit</field>
        <field name="arch" type="xml">
            <list string="Time Unit">
                <field name="color" widget="color"/>
                <field name="name"/>
                <field name="start_time"/>
                <field name="end_time"/>
                <field name="duration"/>
                <field name="unit_type" optional="hide"/>
                <field name="is_overnight" optional="hide"/>
                <field name="apply_min_max" optional="hide"/>
                <field name="purpose_type" string="Purpose Type" groups="base.group_no_one"/>
                <field name="rule_id" optional="hide"/>

            </list>
        </field>
    </record>

    <!--TODO[IMP]: time_unit_view_search-->
    <record id="ams_ta_time_unit_view_search" model="ir.ui.view">
        <field name="name">ams.time_unit.search</field>
        <field name="model">ams.time_unit</field>
        <field name="arch" type="xml">
            <search>
                <field name="unit_type"/>
                <field name="is_overnight"/>
            </search>
        </field>
    </record>

    <!--TODO[IMP]:time_unit_action-->
    <record id="ams_ta_time_unit_action" model="ir.actions.act_window">
        <field name="name">Time Units</field>
        <field name="res_model">ams.time_unit</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'default_purpose_type': 'ta', 'search_default_available': 1}</field>
        <field name="domain">[('purpose_type', 'in', ['ta', 'all'])]</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'list', 'view_id': ref('ams_ta_time_unit_view_list')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('ams_ta_time_unit_view_form')})]"/>
        <field name="target">current</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create time unit
            </p>
            <p>
                Create time unit
            </p>
        </field>
    </record>
</odoo>
