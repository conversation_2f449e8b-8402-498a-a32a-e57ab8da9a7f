<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- timesheet shift unit form view -->
    <record id="timesheet_time_unit_view_form" model="ir.ui.view">
        <field name="name">ams_ta.timesheet_time_unit.form</field>
        <field name="model">ams_ta.timesheet_time_unit</field>
        <field name="arch" type="xml">
            <form string="Timesheet shift unit">
                <header>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="first_checkin_datetime" readonly="context.get('manual_edit', 0)==0"
                                   decoration-danger="first_checkin_datetime != first_log_checkin_datetime"/>
                            <field name="last_checkout_datetime" readonly="context.get('manual_edit', 0)==0"
                                   decoration-danger="last_checkout_datetime != last_log_checkout_datetime"/>
                        </group>
                        <group>
                            <field name="working_time" widget="float_time"/>
                            <field name="delay_time" widget="float_time" decoration-danger="delay_time > 0"/>
                            <field name="shortage_time" widget="float_time" decoration-danger="shortage_time > 0"/>
                            <field name="overtime" widget="float_time" decoration-success="overtime > 0"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Time Detail">
                            <group>
                                <group>
                                    <field name="overtime_factored" widget="float_time"
                                           decoration-success="overtime_factored > 0"/>
                                    <field name="remaining_time" widget="float_time"
                                           decoration-danger="remaining_time > 0"/>
                                    <field name="total_delay_shortage" widget="float_time"
                                           decoration-danger="total_delay_shortage > 0"/>
                                    <field name="total_deduction" widget="float_time"
                                           decoration-danger=" total_deduction > 0"/>
                                    <field name="first_log_checkin_datetime" widget="datetime"/>
                                    <field name="last_log_checkout_datetime" widget="datetime"/>
                                </group>
                                <group>
                                    <field name="is_checked_in"/>
                                    <field name="is_checked_out"/>
                                    <field name="is_delayed"/>
                                    <field name="is_shortage"/>
                                    <field name="is_overtime"/>
                                    <field name="is_absent"/>
<!--                                    <field name="first_checkin_time" widget="float_time"/>-->
<!--                                    <field name="last_checkout_time" widget="float_time"/>-->
                                </group>
                            </group>
                        </page>
                        <page string="Time Unit Rule Info">
                            <group>
                                <group string="Grace Time">
                                    <field name="grace_in_time" widget="float_time"/>
                                    <field name="grace_out_time" widget="float_time"/>
                                </group>
                                <group string="Overtime and Deduction">
                                    <field name="overtime_factor"/>
                                    <field name="apply_working_hour_deduction"/>
                                </group>
                                <group string="Half Working Day :">
                                    <field name="apply_half_work"/>
                                    <field name="half_work_checkin_criteria" invisible="apply_half_work == False"
                                           widget="float_time"/>
                                    <field name="half_work_checkout_criteria" invisible="apply_half_work == False"
                                           widget="float_time"/>
                                    <field name="calc_half_no_checkout_time" invisible="apply_half_work == False"/>
                                </group>
                            </group>
                        </page>
                        <page string="Time Unit Info">
                            <group>
                                <group>
                                    <field name="start_time" widget="float_time"/>
                                    <field name="start_limit" invisible="unit_type != 'flexible'" widget="float_time"/>
                                    <field name="end_time" widget="float_time"/>
                                    <field name="required_time" widget="float_time"/>
                                    <field name="unit_type"/>
                                    <field name="is_overnight"/>
                                </group>

                                <group>
                                    <field name="absent_time_criteria" widget="float_time"/>
                                    <field name="apply_min_max"/>
                                    <field name="min_checkin_time" invisible="apply_min_max == False"
                                           widget="float_time"/>
                                    <field name="max_checkout_time" invisible="apply_min_max == False"
                                           widget="float_time"/>
                                </group>
                            </group>
                        </page>
                        <page string="Mobile Info">
                            <group>
                                <field name="from_mobile"/>
                                <field name="in_longitude"/>
                                <field name="in_latitude"/>
                                <field name="out_longitude"/>
                                <field name="out_latitude"/>
                            </group>
                        </page>
                        <page string="Notes">
                            <group>
                                <field name="notes"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form></field>
    </record>

    <!-- timesheet_time_unit_list_view -->
    <record id="timesheet_time_unit_view_list" model="ir.ui.view">
        <field name="name">ams_ta.timesheet_time_unit_view.list</field>
        <field name="model">ams_ta.timesheet_time_unit</field>
        <field name="arch" type="xml">
            <list string="Timesheet shift unit">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="date"/>
                <field name="first_checkin_time" widget="float_time"/>
                <field name="first_checkin_datetime" optional="hide" decoration-danger="first_checkin_datetime != first_log_checkin_datetime"/>
                <field name="last_checkout_time" widget="float_time"/>
                <field name="last_checkout_datetime" optional="hide" decoration-danger="last_checkout_datetime != last_log_checkout_datetime"/>
                <field name="working_time" widget="float_time"/>
                <field name="required_time" widget="float_time"/>
                <field name="remaining_time" optional="hide" widget="float_time"/>
                <field name="delay_time" widget="float_time"/>
                <field name="shortage_time" widget="float_time"/>
                <field name="total_delay_shortage" optional="hide" widget="float_time"/>
                <field name="overtime" optional="hide" widget="float_time"/>
                <field name="overtime_factor"/>
                <field name="is_delayed" optional="hide"/>
                <field name="is_shortage" optional="hide"/>
                <field name="is_overnight" optional="hide"/>
                <field name="timesheet_id" optional="hide"/>
                <field name="notes" optional="hide"/>
                <field name="from_mobile"/>
                <field name="in_longitude"/>
                <field name="in_latitude"/>
                <field name="out_longitude"/>
                <field name="out_latitude"/>
            </list></field>
    </record>

    <!-- timesheet_time_unit_search_view -->
    <record id="timesheet_time_unit_view_search" model="ir.ui.view">
        <field name="name">ams_ta_timesheet_time_unit</field>
        <field name="model">ams_ta.timesheet_time_unit</field>
        <field name="arch" type="xml">
            <search string="Timesheet shift unit">
            </search>
        </field>
    </record>

    <!-- timesheet_time_unit_action -->
    <record id="timesheet_time_unit_action" model="ir.actions.act_window">
        <field name="name">Timesheet shift unit</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">ams_ta.timesheet_time_unit</field>
        <field name="view_mode">list,form</field>
        <field name="context">{"search_default_fieldname": 1}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                Click to add a new Timesheet time unit
            </p>
            <p>
                Click the Create button to add a new Timesheet time unit
            </p>
        </field>
    </record>

</odoo>
