<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: user_group_view_form-Inheritance -->
    <record id="ams_ta_user_group_view_form" model="ir.ui.view">
        <field name="name">ams.user_group_form_inherit</field>
        <field name="model">ams.user_group</field>
        <field name="inherit_id" ref="ams.user_group_view_form"/>
        <field name="mode">extension</field>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='users']" position="before">
                <page string="Time Attendance">
                    <group>
                        <group>
                            <field name="primary_shift_id"/>
                            <field name="secondary_shift_id"/>
                        </group>
                    </group>
                </page>
                <page string="Employees">
                    <field name="employee_ids" readonly="1">
                        <list>
                            <field name="name"/>
                            <field name="work_email"/>
                            <field name="department_id"/>
                        </list>
                    </field>
                </page>
            </xpath>
              <xpath expr="//page[@name='users']" position="after">
                <page string="Geo Fence Config">
                    <group>
                            <field name="apply_geo_fencing"/>
                            <field name="geo_fence_tolerance" invisible="apply_geo_fencing == False"/>
                            <field name="geo_fence_action" invisible="apply_geo_fencing == False"/>
                        <field name="geo_fence_location_ids" invisible="apply_geo_fencing == False">
                            <list editable="bottom">
                                <field name="name"/>
                                <field name="latitude"/>
                                <field name="longitude"/>
                                <field name="radius"/>
                                <field name="address" optional="hide"/>
                                <field name="is_active"/>
                                <field name="color" widget="color"/>
                            </list>
                        </field>
                    </group>
                </page>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: user_group_view_list-Inheritance-->
    <record id="ams_ta_user_group_view_list" model="ir.ui.view">
        <field name="name">ams.user_group_view_list</field>
        <field name="model">ams.user_group</field>
        <field name="inherit_id" ref="ams.user_group_view_list"/>
        <field name="mode">extension</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='parent_id']" position="after">
                <!--                <field name="employee_ids"/>-->
                <field name="primary_shift_id"/>
                <field name="secondary_shift_id"/>
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: user_group_view_search-Inheritance-->
    <record id="ams_ta_user_group_view_search" model="ir.ui.view">
        <field name="name">ams.user_group_search_inherit</field>
        <field name="model">ams.user_group</field>
        <field name="inherit_id" ref="ams.user_group_view_search"/>
        <field name="mode">extension</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="primary_shift_id"/>
                <field name="secondary_shift_id"/>
            </xpath>
        </field>
    </record>

    <!--    &lt;!&ndash;TODO[IMP]: user_group_action&ndash;&gt;-->
    <!--    <record id="ams_ta_user_group_action" model="ir.actions.act_window">-->
    <!--        <field name="name">ams_ta_user_group</field>-->
    <!--        <field name="res_model">ams.user_group</field>-->
    <!--        <field name="view_mode">list,form,search</field>-->
    <!--        <field name="context">{'search_default_today': 1}</field>-->
    <!--        <field name="help" type="html">-->
    <!--            <p class="o_view_nocontent_smiling_face">Create user_group record</p>-->
    <!--            <p>Create user_group records</p>-->
    <!--        </field>-->
    <!--    </record>-->
</odoo>
