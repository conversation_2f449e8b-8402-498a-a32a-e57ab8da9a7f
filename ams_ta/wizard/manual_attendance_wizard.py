from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta
import random

class ManualAttendance(models.TransientModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_ta.manual_attendance_wizard"
    _description = "Manual Attendance"

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    from_date = fields.Date(default=fields.Date.today())
    to_date = fields.Date(default=fields.Date.today())

    with_time = fields.Boolean(default=False)
    checkin_time = fields.Float()
    checkout_time = fields.Float()

    checkin_datetime = fields.Datetime()
    checkout_datetime = fields.Datetime()

    is_random = fields.Boolean(string="Random", default=False)
    # endregion

    # region Special
    # endregion

    # region  Relational
    employee_ids = fields.Many2many('hr.employee')
    employee_id = fields.Many2one('hr.employee')
    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def generate_timesheet(self):
        """
        Generate timesheet records for selected employees based on the provided dates and times.
        Only main fields are populated: employee, first_checkin_time, last_checkout_time, working_time, delay_time, shortage_time.
        """
        self.ensure_one()

        timesheet = self.env['ams_ta.timesheet']
        timesheet_list = []
        for employee in self.employee_ids:
            current_date = self.from_date
            while current_date <= self.to_date:
                if self.is_random:
                    # Generate random values for main fields
                    first_checkin_time = random.uniform(8.0, 10.0)
                    last_checkout_time = random.uniform(16.0, 18.0)
                else:
                    # Use provided check-in and check-out times
                    first_checkin_time = self.checkin_time
                    last_checkout_time = self.checkout_time

                working_time = last_checkout_time - first_checkin_time
                delay_time = max(0, first_checkin_time - 9.0)
                shortage_time = max(0, 8.0 - working_time)

                # Create timesheet record with only main fields
                timesheet_list.append({
                    'employee_id': employee.id,
                    'date': current_date,
                    'first_checkin_time': first_checkin_time,
                    'last_checkout_time': last_checkout_time,
                    'working_time': working_time,
                    'delay_time': delay_time,
                    'shortage_time': shortage_time,
                })

                # Move to the next day
                current_date += timedelta(days=1)

        if timesheet_list:
            timesheet.create(timesheet_list)


    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Private Methods -------------------------------------
