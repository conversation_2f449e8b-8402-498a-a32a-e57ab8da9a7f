<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Wizard Form View -->
    <record id="view_manual_attendance_wizard_form" model="ir.ui.view">
        <field name="name">ams_ta.manual_attendance_wizard.form</field>
        <field name="model">ams_ta.manual_attendance_wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="employee_ids" widget="many2many_tags" options="{'no_create': True}" />
                            <field name="with_time" />
                            <field name="is_random" />
                        </group>
                        <group>
                            <field name="from_date" />
                            <field name="to_date" />
                            <field name="checkin_time" invisible="not with_time" widget="float_time"/>
                            <field name="checkout_time" invisible="not with_time" widget="float_time"/>
                        </group>
                    </group>

                </sheet>
                <footer>
                    <button string="Add Attendance" type="object" name="generate_timesheet" class="btn-primary" />
                    <button string="Cancel" class="btn-secondary" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

    <!-- Wizard Action -->
    <record id="action_manual_attendance_wizard" model="ir.actions.act_window">
        <field name="name">Generate Manual Attendance</field>
        <field name="res_model">ams_ta.manual_attendance_wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'default_employee_ids': active_ids}</field>
    </record>
</odoo>