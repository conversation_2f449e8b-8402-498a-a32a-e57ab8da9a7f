from odoo import models, fields, api
import json
from odoo.addons.ams_ta.helper.helper import get_date_range, float_str_to_hhmm


class ReportGenerator(models.TransientModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.report_generator_wizard"
    _description = "Report Generator"
    # endregion
    PERIOD_SELECTION = [
        ('current_week', 'Current Week'),
        ('previous_week', 'Previous Week'),
        ('current_month', 'Current Month'),
        ('previous_month', 'Previous Month'),
        ('current_year', 'Current Year'),
        ('previous_year', 'Previous Year'),
    ]

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(default='Report Generator', readonly=True)
    from_date = fields.Date()
    to_date = fields.Date()
    company_id = fields.Many2one('res.company', string='Company', required=True, default=lambda self: self.env.company)
    date = fields.Datetime(default=fields.Datetime.now)
    period_filter = fields.Selection(
        selection=PERIOD_SELECTION,
        string="Period",
        default='previous_month'
    )
    # endregion

    # region  Special
    # endregion

    # region  Relational
    department_ids = fields.Many2many('hr.department', 'department_ids',
                                      domain=lambda self: self.env['hr.department']._domain_follow_up())
    employee_ids = fields.Many2many('hr.employee',
                                    string="Employees")
    available_employees_domain = fields.Char(string=" Domain", compute='_compute_available_employees_domain',
                                             help="compute field to filter employees by department and coaches and managers")

    coach_ids = fields.Many2many(
        'hr.employee',
        'report_wizard_coach_rel',  # Unique relation table name
        'wizard_id',
        'employee_id',
        string="Coaches",
        domain=lambda self: self.env['hr.employee']._domain_follow_up()
    )

    manager_ids = fields.Many2many(
        'hr.employee',
        'report_wizard_manager_rel',  # Unique relation table name
        'wizard_id',
        'employee_id',
        string="Managers",
        domain=lambda self: self.env['hr.employee']._domain_follow_up()
    )

    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('department_ids', 'coach_ids', 'manager_ids')
    def _compute_available_employees_domain(self):
        for wizard in self:
            base_domain = self.env['hr.employee']._domain_follow_up()
            if wizard.department_ids:
                dept_domain = [('department_id', 'in', wizard.department_ids.ids)]
                final_domain = base_domain + dept_domain
            else:
                final_domain = base_domain

            if wizard.coach_ids:
                coach_domain = [('coach_id', 'in', wizard.coach_ids.ids)]
                final_domain += coach_domain

            if wizard.manager_ids:
                manager_domain = [('parent_id', 'in', wizard.manager_ids.ids)]
                final_domain += manager_domain

            wizard.available_employees_domain = json.dumps(final_domain)

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.onchange('period_filter')
    def _onchange_period_filter(self):
        """Update date range fields based on the selected period filter."""
        if not self.period_filter:
            # self.from_date = False
            # self.to_date = False
            return

        # Use the reusable function to get the date range
        start_date, end_date = get_date_range(self.period_filter)
        self.from_date = start_date
        self.to_date = end_date

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def convert_to_time_format(self, float_time):
        return float_str_to_hhmm(float_time)
    # endregion
