# FTA Integration API Technical Documentation

## Overview
Attendance RESTful web services are lightweight, highly scalable and maintainable APIs designed for integration between ERP and FTA systems.

### Purpose
Build API for integration between ERP and FTA systems

### Integration Modules
- Employees
- Attendance  
- Permissions
- Vacations

### Version
V.03

### Service URLs
**Test Environment**: `https://base_url/`  
**Production Environment**: `https://myattendance.stcsc.sa/ftaint/FTAService.svc/`

> **Note**: Test server uses HTTPS protocol

---

## Table of Contents
- [Fast Start](#fast-start)
- [Employees Module](#employees-module)
- [Attendance Module](#attendance-module)
- [Permissions Module](#permissions-module)
- [Vacations Module](#vacations-module)

---

## Fast Start

Quick start examples using test server:

### Employees
```bash
# Service health check
https://base_url/isalive

# Get employee by employee number
https://base_url/GetEmployeeByEmpNo?empNo=00762

# Get employee by username
https://base_url/GetEmployeeByusername?username=moataz.migled

# Get all employees
https://base_url/GetEmployees
```

### Attendance
```bash
# Get attendance by employee number
https://base_url/GetEmployeeAttendanceByEmpNo?empNo=00762&dateFrom=01012019&dateTo=31012019

# Get attendance by username
https://base_url/GetEmployeeAttendanceByUserName?username=moataz.migled&dateFrom=01012019&dateTo=31012019

# Get all employees attendance
https://base_url/GetEmployeesAttendance?dateFrom=01012019&dateTo=31012019
```

### Deduction
```bash
# Get all employees attendance deduction
https://base_url/GetEmployeesAttendanceDeduction?dateFrom=01012019&dateTo=31012019

# Get specific employee attendance deduction
https://base_url/GetEmployeesAttendanceDeduction?empNo=00762&dateFrom=01012019&dateTo=31012019
```

---

## Employees Module

### Service Methods
1. `AddEmployee` - Add or update employee data
2. `GetEmployeeByEmpNo` - Get employee by employee number
3. `GetEmployeeByUserName` - Get employee by username
4. `GetEmployees` - Get all employees
5. `GetEmployeesByDeptName` - Get employees by department name

### BaseURL/AddEmployee
**Type**: POST  
**Description**: Add employee data or update if employee exists

**Request JSON Body**:
```json
{
    "ArabicName": "String content",
    "AreaArabicName": "String content",
    "AreaEnglishName": "String content",
    "BGArabicName": "String content",
    "BGEnglishName": "String content",
    "BranchArabicName": "String content",
    "BranchEnglishName": "String content",
    "DeptArabicName": "String content",
    "DeptEnglishName": "String content",
    "Email": "String content",
    "EmpNo": "String content",
    "EnglishName": "String content",
    "ErrorMessage": "String content",
    "PhoneNo": "String content",
    "UserName": "String content"
}
```

**Response**:  
Returns "1" for success or error message

### BaseURL/GetEmployeeByEmpNo?empNo={EMPNO}
**Type**: GET  
**Description**: Return employee data by employee number

**Response JSON Body**:
```json
{
    "ArabicName": "String content",
    "AreaArabicName": "String content",
    "AreaEnglishName": "String content",
    "BGArabicName": "String content",
    "BGEnglishName": "String content",
    "BranchArabicName": "String content",
    "BranchEnglishName": "String content",
    "DeptArabicName": "String content",
    "DeptEnglishName": "String content",
    "Email": "String content",
    "EmpNo": "String content",
    "EnglishName": "String content",
    "ErrorMessage": "String content",
    "PhoneNo": "String content",
    "UserName": "String content"
}
```

### BaseURL/GetEmployeeByUserName?userName={USERNAME}
**Type**: GET  
**Description**: Return employee data by username

**Response JSON Body**: Same structure as GetEmployeeByEmpNo

### BaseURL/GetEmployees
**Type**: GET  
**Description**: Return all employee data

**Response JSON Body**:
```json
{
    "Employees": [
        {
            "ArabicName": "String content",
            "AreaArabicName": "String content",
            "AreaEnglishName": "String content",
            "BGArabicName": "String content",
            "BGEnglishName": "String content",
            "BranchArabicName": "String content",
            "BranchEnglishName": "String content",
            "DeptArabicName": "String content",
            "DeptEnglishName": "String content",
            "Email": "String content",
            "EmpNo": "String content",
            "EnglishName": "String content",
            "ErrorMessage": "String content",
            "PhoneNo": "String content",
            "UserName": "String content"
        }
    ]
}
```

### BaseURL/GetEmployeesByDeptName?deptName={DEPTNAME}
**Type**: GET  
**Description**: Return employees data by department name

**Response JSON Body**: Same structure as GetEmployees

---

## Attendance Module

### Service Methods
1. `GetEmployeeAttendanceByEmpNo` - Get attendance by employee number
2. `GetEmployeeAttendanceByUserName` - Get attendance by username
3. `GetEmployeesAttendance` - Get all employees attendance
4. `GetEmployeesAttendanceByDeptName` - Get attendance by department
5. `GetEmployeesAttendanceDeduction` - Get attendance deductions

### BaseURL/GetEmployeeAttendanceByEmpNo?empNo={EMPNO}&dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return employee attendance data by employee number

**Response JSON Body**:
```json
{
    "Emp": {
        "ArabicName": "String content",
        "AreaArabicName": "String content",
        "AreaEnglishName": "String content",
        "BGArabicName": "String content",
        "BGEnglishName": "String content",
        "BranchArabicName": "String content",
        "BranchEnglishName": "String content",
        "DeptArabicName": "String content",
        "DeptEnglishName": "String content",
        "Email": "String content",
        "EmpNo": "String content",
        "EnglishName": "String content",
        "ErrorMessage": "String content",
        "PhoneNo": "String content",
        "UserName": "String content"
    },
    "ErrorMessage": "String content",
    "Logs": [
        {
            "Comment": "String content",
            "Date": "/Date(928138800000+0300)/",
            "DateString": "String content",
            "Delay": "String content",
            "InTime": "String content",
            "OutTime": "String content",
            "RequiredTime": "String content",
            "Shortage": "String content",
            "Status": 0,
            "WorkingTime": "String content"
        }
    ]
}
```

### BaseURL/GetEmployeeAttendanceByUserName?userName={USERNAME}&dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return employee attendance data by username

**Response JSON Body**: Same structure as GetEmployeeAttendanceByEmpNo

### BaseURL/GetEmployeesAttendance?dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return all employees attendance data

**Response JSON Body**:
```json
{
    "AttList": [
        {
            "Emp": {
                // Employee object
            },
            "ErrorMessage": "String content",
            "Logs": [
                {
                    // Attendance log object
                }
            ]
        }
    ],
    "ErrorMessage": "String content"
}
```

### BaseURL/GetEmployeesAttendanceByDeptName?deptName={DEPTNAME}&dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return employees attendance data by department

**Response JSON Body**: Same structure as GetEmployeesAttendance

### BaseURL/GetEmployeesAttendanceDeduction?dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return employees deduction data

**Response JSON Body**:
```json
{
    "DeductionList": [
        {
            "DeductionHours": 0,
            "DeductionMinutes": 0,
            "EmpNo": "String content"
        }
    ]
}
```

---

## Permissions Module

### Service Methods
1. `AddPermissionRequest` - Add permission request
2. `GetEmployeePermissionsByEmpNo` - Get permissions by employee number
3. `GetEmployeePermissionsByUserName` - Get permissions by username
4. `GetEmployeesPermissions` - Get all employees permissions
5. `GetEmployeesPermissionsByDeptName` - Get permissions by department

### BaseURL/AddPermissionRequest
**Type**: POST  
**Description**: Add employee permission request

**Request JSON Body**:
```json
{
    "EmpNo": "String content",
    "EndDateString": "String content",
    "ErrorMessage": "String content",
    "Reason": "String content",
    "ReplyComment": "String content",
    "RequestName": "String content",
    "RequestNo": "String content",
    "StartDateString": "String content",
    "Status": 0,
    "TotalMinutes": 0,
    "TransactionType": 0,
    "UserName": "String content"
}
```

**Response JSON Body**: Same structure as request

### BaseURL/GetEmployeePermissionsByEmpNo?empNo={EMPNO}&dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return employee permissions by employee number

**Response JSON Body**:
```json
{
    "Emp": {
        // Employee object
    },
    "ErrorMessage": "String content",
    "Logs": [
        {
            "EmpNo": "String content",
            "EndDateString": "String content",
            "ErrorMessage": "String content",
            "Reason": "String content",
            "ReplyComment": "String content",
            "RequestName": "String content",
            "RequestNo": "String content",
            "StartDateString": "String content",
            "Status": 0,
            "TotalMinutes": 0,
            "TransactionType": 0,
            "UserName": "String content"
        }
    ]
}
```

### BaseURL/GetEmployeePermissionsByUserName?userName={USERNAME}&dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return employee permissions by username

**Response JSON Body**: Same structure as GetEmployeePermissionsByEmpNo

### BaseURL/GetEmployeesPermissions?dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return all employees permissions

**Response JSON Body**:
```json
{
    "ErrorMessage": "String content",
    "Requests": [
        {
            "Emp": {
                // Employee object
            },
            "ErrorMessage": "String content",
            "Logs": [
                {
                    // Permission log object
                }
            ]
        }
    ]
}
```

### BaseURL/GetEmployeesPermissionsByDeptName?deptName={DEPTNAME}&dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return employees permissions by department

**Response JSON Body**: Same structure as GetEmployeesPermissions

---

## Vacations Module

### Service Methods
1. `AddVacationRequest` - Add vacation request
2. `GetEmployeeVacationsByEmpNo` - Get vacations by employee number
3. `GetEmployeeVacationsByUserName` - Get vacations by username
4. `GetEmployeesVacations` - Get all employees vacations
5. `GetEmployeesVacationsByDeptName` - Get vacations by department

### BaseURL/AddVacationRequest
**Type**: POST  
**Description**: Add employee vacation request

**Request JSON Body**:
```json
{
    "EmpNo": "String content",
    "EndDateString": "String content",
    "ErrorMessage": "String content",
    "Reason": "String content",
    "ReplyComment": "String content",
    "RequestName": "String content",
    "RequestNo": "String content",
    "StartDateString": "String content",
    "Status": 0,
    "TotalMinutes": 0,
    "TransactionType": 0,
    "UserName": "String content"
}
```

**Response JSON Body**: Same structure as request

### BaseURL/GetEmployeeVacationsByEmpNo?empNo={EMPNO}&dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return employee vacations by employee number

**Response JSON Body**:
```json
{
    "Emp": {
        // Employee object
    },
    "ErrorMessage": "String content",
    "Logs": [
        {
            "EmpNo": "String content",
            "EndDateString": "String content",
            "ErrorMessage": "String content",
            "Reason": "String content",
            "ReplyComment": "String content",
            "RequestName": "String content",
            "RequestNo": "String content",
            "StartDateString": "String content",
            "Status": 0,
            "TotalMinutes": 0,
            "TransactionType": 0,
            "UserName": "String content"
        }
    ]
}
```

### BaseURL/GetEmployeeVacationsByUserName?userName={USERNAME}&dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return employee vacations by username

**Response JSON Body**: Same structure as GetEmployeeVacationsByEmpNo

### BaseURL/GetEmployeesVacations?dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return all employees vacations

**Response JSON Body**:
```json
{
    "ErrorMessage": "String content",
    "Requests": [
        {
            "Emp": {
                // Employee object
            },
            "ErrorMessage": "String content",
            "Logs": [
                {
                    // Vacation log object
                }
            ]
        }
    ]
}
```

### BaseURL/GetEmployeesVacationsByDeptName?deptName={DEPTNAME}&dateFrom={DATEFROM}&dateTo={DATETO}
**Type**: GET  
**Description**: Return employees vacations by department

**Response JSON Body**: Same structure as GetEmployeesVacations

---

## Notes
- All dates should be in format `DDMMYYYY` (e.g., 01012019 for January 1, 2019)
- All service methods use JSON format
- Error handling should check the `ErrorMessage` field in responses
- Status codes: 0 typically indicates pending, but specific values should be verified with the API provider