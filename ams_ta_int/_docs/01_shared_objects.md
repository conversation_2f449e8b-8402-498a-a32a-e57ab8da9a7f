# Updated Shared Object Definitions

## Employee Object
**Used in**: All employee-related endpoints, attendance, permissions, and vacations

```json
{
    "ArabicName": "string",
    "AreaArabicName": "string",
    "AreaEnglishName": "string",
    "BGArabicName": "string",
    "BGEnglishName": "string",
    "BranchArabicName": "string",
    "BranchEnglishName": "string",
    "DeptArabicName": "string",
    "DeptEnglishName": "string",
    "Email": "string",
    "EmpNo": "string",
    "EnglishName": "string",
    "ErrorMessage": "string",
    "PhoneNo": "string",
    "UserName": "string"
}
```

**Field Descriptions**:
- `ArabicName`: Employee name in Arabic
- `AreaArabicName`: Area/Region name in Arabic
- `AreaEnglishName`: Area/Region name in English
- `BGArabicName`: Business Group name in Arabic
- `BGEnglishName`: Business Group name in English
- `BranchArabicName`: Branch name in Arabic
- `BranchEnglishName`: Branch name in English
- `DeptArabicName`: Department name in Arabic
- `DeptEnglishName`: Department name in English
- `Email`: Employee email address
- `EmpNo`: Employee identification number
- `EnglishName`: Employee name in English
- `ErrorMessage`: Error message if any issues occur
- `PhoneNo`: Employee phone number
- `UserName`: Employee username/login ID

---

## Attendance Log Object
**Used in**: Attendance endpoints

```json
{
    "Comment": "string",
    "Date": "/Date(928138800000+0300)/",
    "DateString": "string",
    "Delay": "string",
    "InTime": "string",
    "OutTime": "string",
    "RequiredTime": "string",
    "Shortage": "string",
    "Status": "integer",
    "WorkingTime": "string"
}
```

**Field Descriptions**:
- `Comment`: Additional comments or notes
- `Date`: Date in .NET JSON date format (milliseconds since epoch with timezone)
- `DateString`: Date as string representation
- `Delay`: Delay time in attendance
- `InTime`: Employee check-in time
- `OutTime`: Employee check-out time
- `RequiredTime`: Required working hours
- `Shortage`: Shortage in working hours
- `Status`: **Attendance status code: 0=WorkingDay, 1=DayOff, 2=Absent, 3=Vacation**
- `WorkingTime`: Actual working hours

---

## Request Object (Permissions & Vacations)
**Used in**: Permission and Vacation endpoints

```json
{
    "EmpNo": "string",
    "EndDateString": "string",
    "ErrorMessage": "string",
    "Reason": "string",
    "ReplyComment": "string",
    "RequestName": "string",
    "RequestNo": "string",
    "StartDateString": "string",
    "Status": "integer",
    "TotalMinutes": "integer",
    "TransactionType": "integer",
    "UserName": "string"
}
```

**Field Descriptions**:
- `EmpNo`: Employee identification number
- `EndDateString`: End date of the request (format: DDMMYYYY)
- `ErrorMessage`: Error message if any issues occur
- `Reason`: Reason for the request
- `ReplyComment`: Manager's reply or comments
- `RequestName`: Name/type of the request
- `RequestNo`: Unique request identifier
- `StartDateString`: Start date of the request (format: DDMMYYYY)
- `Status`: **Request status code: 0=Waiting, 1=Accepted, 2=Rejected, 3=Canceled** (ignore when adding request)
- `TotalMinutes`: Total minutes requested
- `TransactionType`: Type of transaction
- `UserName`: Employee username

---

## Deduction Object
**Used in**: Attendance deduction endpoints

```json
{
    "DeductionHours": "integer",
    "DeductionMinutes": "integer",
    "EmpNo": "string"
}
```

**Field Descriptions**:
- `DeductionHours`: Hours deducted
- `DeductionMinutes`: Minutes deducted
- `EmpNo`: Employee identification number

---

## Response Wrapper Objects

### Employee List Wrapper
```json
{
    "Employees": [
        { /* Employee Object */ }
    ]
}
```

### Attendance Wrapper
```json
{
    "Emp": { /* Employee Object */ },
    "ErrorMessage": "string",
    "Logs": [
        { /* Attendance Log Object */ }
    ]
}
```

### Attendance List Wrapper
```json
{
    "AttList": [
        { /* Attendance Wrapper */ }
    ],
    "ErrorMessage": "string"
}
```

### Permission/Vacation Wrapper
```json
{
    "Emp": { /* Employee Object */ },
    "ErrorMessage": "string",
    "Logs": [
        { /* Request Object */ }
    ]
}
```

### Permission/Vacation List Wrapper
```json
{
    "ErrorMessage": "string",
    "Requests": [
        { /* Permission/Vacation Wrapper */ }
    ]
}
```

### Deduction List Wrapper
```json
{
    "DeductionList": [
        { /* Deduction Object */ }
    ]
}
```

---

## Status Code Definitions

### 📅 Attendance Status Codes
| Code | Status | Description |
|------|--------|-------------|
| **0** | **WorkingDay** | Normal working day with attendance records |
| **1** | **DayOff** | Official day off (weekend, holiday) |
| **2** | **Absent** | Employee absent without approval |
| **3** | **Vacation** | Approved vacation day |

### 📋 Request Status Codes (Permissions/Vacations)
| Code | Status | Description |
|------|--------|-------------|
| **0** | **Waiting** | Request pending approval |
| **1** | **Accepted** | Request approved |
| **2** | **Rejected** | Request denied |
| **3** | **Canceled** | Request canceled by employee |

> **Important Note**: When adding new requests (Permissions/Vacations), the `Status` field should be **ignored** as the system will automatically set it to `0` (Waiting).

---

## Date Format Specifications

- **API Parameters**: `DDMMYYYY` (e.g., 01012019 for January 1, 2019)
- **JSON Response Dates**: .NET JSON format `/Date(epoch+offset)/`
- **DateString**: Human-readable date string format

---

## Usage Examples

### Adding a Vacation Request (Status ignored)
```json
{
    "EmpNo": "00762",
    "StartDateString": "01012024",
    "EndDateString": "05012024",
    "Reason": "Family vacation",
    "UserName": "moataz.migled",
    "TotalMinutes": 7200
    // Status field is ignored when adding new request
}
```

### Reading Attendance Response
```json
{
    "Status": 0, // WorkingDay - has attendance records
    "InTime": "08:15",
    "OutTime": "17:30",
    "WorkingTime": "8:15"
}
```

```json
{
    "Status": 3, // Vacation - no attendance records expected
    "InTime": "",
    "OutTime": "",
    "WorkingTime": "0:00"
}
```