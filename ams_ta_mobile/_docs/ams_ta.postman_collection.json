{"info": {"_postman_id": "c85ec292-4b24-484a-be23-fd21807b878f", "name": "ams_ta", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12899274"}, "item": [{"name": "MAuthenticate", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"AppId\": \"12344\",\n  \n \n  \"Username\": \"emp1\",\n \n  \n \n  \"Password\": \"123456\"\n \n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8025/MAuthenticate", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MAuthenticate"]}}, "response": []}, {"name": "MAddAttendance", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"AppId\": \"12344\",\n    \"AppVersion\": \"1.0.0\",\n    \"DeviceInfo\": \"Android 11, Samsung Galaxy S21\",\n    \"EmpNo\": \"12345\",\n    \"HRCode\": \"678901\",\n    \"Username\": \"emp1\",\n    \"DateTimeString\": \"2023/07/07 01:00:00\",\n    \"Latitude\": 23.0,\n    \"Longitude\": 34.7,\n    \"Time\": \"01:00\",\n    \"Type\": 1\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8025/MAddAttendance", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MAddAttendance"]}}, "response": []}, {"name": "MGetEmployee", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"AppId\": \"app123\",\n  \"AppVersion\": \"1.0.0\",\n  \"DeviceInfo\": \"iPhone 12\",\n  \"EmpNo\": \"12345\",\n   \"HRCode\": \"678901\",\n  \n  \"Username\": \"emp1\",\n  \"ArabicName\": \"جون دو\",\n  \"AreaArabicName\": \"المنطقة العربية\",\n  \"AreaEnglishName\": \"Arabian Area\",\n  \"BGArabicName\": \"الاسم العربي\",\n  \"BGEnglishName\": \"English BG Name\",\n  \"BranchArabicName\": \"الفرع العربي\",\n  \"BranchEnglishName\": \"English Branch\",\n  \"DeptArabicName\": \"القسم العربي\",\n  \"DeptEnglishName\": \"English Department\",\n  \"Email\": \"<EMAIL>\",\n  \"EnglishName\": \"John Doe\",\n  \"ErrorMessage\": \"No Error\",\n  \"PhoneNo\": \"+1234567890\",\n  \"UserName\": \"emp1\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8025/MGetEmployee", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MGetEmployee"]}}, "response": []}, {"name": "MGetEmployeeAttendance", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"AppId\": \"app123\",\n  \"AppVersion\": \"1.0.0\",\n  \"DeviceInfo\": \"iPhone 12\",\n  \"EmpNo\": \"12345\",\n  \"HRCode\": \"678901\",\n  \"Username\": \"emp1\",\n  \"Action\": \"CheckIn\",\n  \"DateFrom\": \"2025-01-01\",\n  \"DateTo\": \"2025-01-31\"\n  \n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8025/MGetEmployeeAttendance", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MGetEmployeeAttendance"]}}, "response": []}]}