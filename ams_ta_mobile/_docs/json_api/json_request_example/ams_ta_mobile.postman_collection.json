{"info": {"_postman_id": "c85ec292-4b24-484a-be23-fd21807b878f", "name": "ams_ta_mobile", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12899274"}, "item": [{"name": "mobile_attendance", "item": [{"name": "MGetEmployeeAttendance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"AppId\": \"2490fc8b5517fcf4\",\n//   \"AppVersion\": \"1.0.0\",\n//   \"DeviceInfo\": \"iPhone 12\",\n//   \"EmpNo\": \"12345\",\n//   \"HRCode\": \"678901\",\n  \"Username\": \"empm\",\n  \"Action\": \"0\",\n  \"DateFrom\": \"2025-01-01\",\n  \"DateTo\": \"2025-04-18\"\n  \n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8025/MGetEmployeeAttendance", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MGetEmployeeAttendance"]}}, "response": []}, {"name": "MAddAttendance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"AppId\": \"2490fc8b5517fcf4\",\n    \"AppVersion\": \"1.0.0\",\n    \"DeviceInfo\": \"Android 11, Samsung Galaxy S21\",\n    // \"EmpNo\": \"12345\",\n    // \"HRCode\": \"678901\",\n    \"Username\": \"emp1\",\n    \"Latitude\": 23.0,\n    \"Longitude\": 34.7,\n    \"Type\": 1\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8025/MAddAttendance", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MAddAttendance"]}}, "response": []}]}, {"name": "mobile_auth", "item": [{"name": "MAuthenticate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"AppId\": \"2490fc8b5517fcf4\",\n  \"Username\": \"emp1\",\n  \"Password\": \"123456\"\n \n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8025/MAuthenticate", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MAuthenticate"]}}, "response": []}, {"name": "MGetEmployee", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"AppId\": \"app123\",\n  \"AppVersion\": \"1.0.0\",\n  \"DeviceInfo\": \"iPhone 12\",\n  \"EmpNo\": \"12345\",\n  \"HRCode\": \"678901\",\n  \"Username\": \"emp1\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8025/MGetEmployee", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MGetEmployee"]}}, "response": []}]}, {"name": "mobile_time_off", "item": [{"name": "MGetTimeOffTypes", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8025/MGetTimeOffTypes?time_off_category=1", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MGetTimeOffTypes"], "query": [{"key": "time_off_category", "value": "1"}]}}, "response": []}, {"name": "MGetTime_off", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    // \"AppId\": \"123\",\n    // \"AppVersion\": \"1.0.0\",\n    // \"DeviceInfo\": \"Android 11, Samsung Galaxy S21\",\n    \"Username\": \"emp1\",\n    \"TimeOffCategory\" : 2,\n    \"DateFrom\": \"2024-03-05\",\n    \"DateTo\": \"2026-03-27\" \n   \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8025/MGetTime_off", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MGetTime_off"]}}, "response": []}, {"name": "MAddTimeOffLog", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n//   \"AppId\": \"123\",\n//   \"AppVersion\": \"1.0.0\",\n//   \"DeviceInfo\": \"Android 11, Samsung Galaxy S21\",\n//   \"EmpNo\": \"12345\",\n//   \"HRCode\": \"678901\",\n//   \"Token\": \"YourTokenHere\",\n  \"Username\": \"emp1\",\n  \"TimeOffTypeID\": 12,\n//   \"DateTimeFrom\": \"2025-02-12 15:00:00\",\n//   \"DateTimeTo\": \"2025-02-13 16:00:00\",\n    \"DateFrom\": \"2025-07-20\",\n  \"DateTo\": \"2025-07-20\",\n  \"HourFrom\":\"9.30\",\n  \"HourTo\":13.0,\n  \"TimeOffCategory\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8025/MAddTimeOffLog", "protocol": "http", "host": ["localhost"], "port": "8025", "path": ["MAddTimeOffLog"]}}, "response": []}]}]}