# **View All Mobile Authenticate  API JSON Response**

### **Endpoint: `/MAuthenticate`**

```json
{
    "ResponseCode": "1",
    "ResponseMessage": "OK",
    "ResponseMessageAR": "تم تسجيل الدخول بنجاح",
    "AppId": "2490fc8b5517fcf4",
    "AppVersion": "",
    "DeviceInfo": "",
    "EmpNo": "12345",
    "HRCode": "67890",
    "Token": "2486b546555f7d142001a8ce5cd2c411483d1617",
    "Username": "emp1",
    "ArabicName": "EMP1",
    "AreaArabicName": "",
    "AreaEnglishName": "",
    "BGArabicName": "All Users",
    "BGEnglishName": "All Users",
    "BranchArabicName": "",
    "BranchEnglishName": "",
    "DeptArabicName": "1.2.3dept3",
    "DeptEnglishName": "1.2.3dept3",
    "Email": "emp1.com",
    "EnglishName": "EMP1",
    "ErrorMessage": "",
    "PhoneNo": "******-555-5556",
    "UserName": "emp1"
}

```
-------------------------------------------
### **Endpoint: `/MGetEmployee`**

```json
{
    "ResponseCode": "1",
    "ResponseMessage": "OK",
    "ResponseMessageAR": "تمت العملية بنجاح",
    "AppId": "2490fc8b5517fcf4",
    "AppVersion": "",
    "DeviceInfo": "iPhone 12",
    "EmpNo": "12345",
    "HRCode": "67890",
    "Token": "2486b546555f7d142001a8ce5cd2c411483d1617",
    "Username": "emp1",
    "ArabicName": "EMP1",
    "AreaArabicName": "",
    "AreaEnglishName": "",
    "BGArabicName": "All Users",
    "BGEnglishName": "All Users",
    "BranchArabicName": "",
    "BranchEnglishName": "",
    "DeptArabicName": "1.2.3dept3",
    "DeptEnglishName": "1.2.3dept3",
    "Email": "emp1.com",
    "EnglishName": "EMP1",
    "ErrorMessage": "",
    "PhoneNo": "******-555-5556",
    "UserName": "emp1"
}

```
-------------------------------------------
