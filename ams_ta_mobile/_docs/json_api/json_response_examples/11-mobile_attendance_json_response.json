# **View All Mobile Attendance  API JSON Response**

### **Endpoint: `/MGetEmployeeAttendance`**

```json
{
    "Attendance": [
        {
            "CheckInTimeString": "16:00",
            "CheckOutTimeString": "15:50",
            "DateString": "2025-03-13",
            "DayLength": 0.0,
            "DelayMinutes": 0.0,
            "DisplayDelayMinutes": "00:00",
            "DepartmentNameAr": "",
            "DepartmentNameEn": "",
            "DifferenceMinutes": 0.0,
            "EmployeeID": "21",
            "NoteAr": "vacation test",
            "NoteEn": "vacation test",
            "OvertimeMinutes": 240.0,
            "PermissionMinutes": 0.0,
            "ShiftNameAr": "Normal Shift[08_16]",
            "ShiftNameEn": "Normal Shift[08_16]",
            "ShortageMinutes": 0.0,
            "Status": 1,
            "WorkingMinutes": 240,
            "DisplayWorkingMinutes": "04:00"
        },
        {
            "CheckInTimeString": "14:59",
            "CheckOutTimeString": "00:00",
            "DateString": "2025-03-18",
            "DayLength": 0.0,
            "DelayMinutes": 0.0,
            "DisplayDelayMinutes": "00:00",
            "DepartmentNameAr": "",
            "DepartmentNameEn": "",
            "DifferenceMinutes": 0.0,
            "EmployeeID": "21",
            "NoteAr": "vacation test",
            "NoteEn": "vacation test",
            "OvertimeMinutes": 0.0,
            "PermissionMinutes": 0.0,
            "ShiftNameAr": "Normal Shift[08_16]",
            "ShiftNameEn": "Normal Shift[08_16]",
            "ShortageMinutes": 0.0,
            "Status": 1,
            "WorkingMinutes": 0,
            "DisplayWorkingMinutes": "00:00"
        },
        {
            "CheckInTimeString": "15:17",
            "CheckOutTimeString": "15:20",
            "DateString": "2025-03-23",
            "DayLength": 480.0,
            "DelayMinutes": 436.79999999999995,
            "DisplayDelayMinutes": "07:16",
            "DepartmentNameAr": "",
            "DepartmentNameEn": "",
            "DifferenceMinutes": 0.0,
            "EmployeeID": "21",
            "NoteAr": "",
            "NoteEn": "",
            "OvertimeMinutes": 0.0,
            "PermissionMinutes": 0.0,
            "ShiftNameAr": "Normal Shift[08_16]",
            "ShiftNameEn": "Normal Shift[08_16]",
            "ShortageMinutes": 40.199999999999996,
            "Status": 0,
            "WorkingMinutes": 3,
            "DisplayWorkingMinutes": "00:03"
        }
    ],
    "DeviceInfo": "iPhone 12",
    "Permissions": [],
    "ResponseCode": "1",
    "ResponseMessage": "OK",
    "ResponseMessageAr": "تم العملية بنجاح",
    "Summary": {
        "TotalAbsent": 0,
        "TotalDelayAndShortageMinutes": 476.99999999999994,
        "TotalDelayAndShortageMinutesString": "476.99999999999994",
        "TotalDelayMinutes": 436.79999999999995,
        "TotalDelayMinutesString": "436.79999999999995",
        "TotalOvertimeMinutes": 240.0,
        "TotalOvertimeMinutesString": "240.0",
        "TotalPermissionMinutes": 0.0,
        "TotalPermissionMinutesString": "0",
        "TotalShortageMinutes": 40.199999999999996,
        "TotalShortageMinutesString": "40.199999999999996",
        "TotalVacation": 2.0,
        "TotalWorkingMinutes": 243.00000000000006,
        "TotalWorkingMinutesString": "243.00000000000006"
    },
    "Vacations": []
}

```
---------------------------------------

### **Endpoint: `/MAddAttendance`**

```json
        {
    "ResponseCode": "1",
    "ResponseMessage": "OK",
    "ResponseMessageAr": "تم التسجيل بنجاح",
    "AppId": "2490fc8b5517fcf4",
    "AppVersion": "1.0.0",
    "DeviceInfo": "Android 11, Samsung Galaxy S21",
    "EmpNo": "",
    "HRCode": "",
    "Token": "",
    "Username": "emp1",
    "DateTimeString": "",
    "Latitude": 23.0,
    "Longitude": 34.7,
    "Time": "",
    "Type": 1
}

```
---------------------------------------

### **Endpoint: `/GetEmployees`**

```json

```
---------------------------------------

### **Endpoint: `/GetEmployees`**

```json

```
---------------------------------------

### **Endpoint: `/GetEmployees`**

```json

```
---------------------------------------

