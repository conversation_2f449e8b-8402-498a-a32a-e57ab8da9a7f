# **View All Mobile TimeOff  API JSON Response**

### **Endpoint: `/MGetTimeOffTypes`**

```json
{
    "TimeOffType": [
        {
            "id": 13,
            "name": "vacation test"
        }
    ],
    "ResponseCode": "1",
    "ResponseMessage": "OK",
    "ResponseMessageAr": "تمت العملية بنجاح"
}

```
---------------------------------------

### **Endpoint: `/MGetTime_off`**

```json
{
    "TimeOff": [
        {
            "holiday_status_id": 12,
            "holiday_status_name": "test permission",
            "description": "Mobile Time Off Request: EMP1",
            "date_from": "2025-03-30",
            "date_to": "2025-03-30",
            "hour_from": "14:07",
            "hour_to": "15:07",
            "duration_display": "1:00 hours",
            "state": "validate",
            "state_display": "Approved"
        },
        {
            "holiday_status_id": 14,
            "holiday_status_name": "permission",
            "description": "Mobile Time Off Request: EMP1",
            "date_from": "2025-03-10",
            "date_to": "2025-03-10",
            "hour_from": "13:22",
            "hour_to": "14:22",
            "duration_display": "1:00 hours",
            "state": "validate",
            "state_display": "Approved"
        }
    ],
    "ResponseCode": "1",
    "ResponseMessage": "OK",
    "ResponseMessageAr": "تمت العملية بنجاح"
}

```
---------------------------------------

### **Endpoint: `/MAddTimeOffLog`**

```json
{
    "ResponseCode": "1",
    "ResponseMessage": "OK",
    "ResponseMessageAr": "تمت العملية بنجاح",
    "AppId": "0",
    "AppVersion": "",
    "DeviceInfo": "",
    "EmpNo": "",
    "HRCode": "",
    "Token": "",
    "Username": "emp1",
    "TimeOffTypeID": 12,
    "DateTimeFrom": "",
    "DateTimeTo": "",
    "TimeOffCategory": 2,
    "DateFrom": "2025-07-20",
    "DateTo": "2025-07-20",
    "HourFrom": "9.30",
    "HourTo": 13.0,
    "HourFromDisplay": "",
    "HourToDisplay": ""
}

```
--------------------------------

``` json
        {
    "ResponseCode": "117",
    "ResponseMessage": "An employee already booked time off which overlaps with this period: Mobile Time Off Request: EMP1 - from 2025-07-20 to 2025-07-20",
    "ResponseMessageAr": "الموظف حجز إجازة مسبقًا تتداخل مع هذه الفترة: Mobile Time Off Request: EMP1 - من 2025-07-20 إلى 2025-07-20",
    "AppId": "0",
    "AppVersion": "",
    "DeviceInfo": "",
    "EmpNo": "",
    "HRCode": "",
    "Token": "",
    "Username": "emp1",
    "TimeOffTypeID": 12,
    "DateTimeFrom": "",
    "DateTimeTo": "",
    "TimeOffCategory": 2,
    "DateFrom": "2025-07-20",
    "DateTo": "2025-07-20",
    "HourFrom": "9.30",
    "HourTo": 13.0,
    "HourFromDisplay": "",
    "HourToDisplay": ""
}

```



---------------------------------------

