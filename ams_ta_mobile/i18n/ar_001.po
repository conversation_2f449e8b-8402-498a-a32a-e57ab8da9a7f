# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ams_ta_mobile
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-11-05 09:30+0000\n"
"PO-Revision-Date: 2025-11-05 09:30+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: ams_ta_mobile
#: model:ir.model.fields,help:ams_ta_mobile.field_hr_employee__mobile_attendance_allow
msgid "Allow attendance with mobile app"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,help:ams_ta_mobile.field_hr_employee__mobile_app_allow
msgid "Allow using mobile app to review the attendance"
msgstr ""

#. module: ams_ta_mobile
#: model_terms:ir.ui.view,arch_db:ams_ta_mobile.ams_ta_mobile_employee_view_form
msgid "App Version"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,field_description:ams_ta_mobile.field_hr_employee__mobile_attendance_allow
msgid "Attendance Allow"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,field_description:ams_ta_mobile.field_hr_leave__name
msgid "Description"
msgstr "الوصف"

#. module: ams_ta_mobile
#: model:ir.model,name:ams_ta_mobile.model_hr_employee
msgid "Employee"
msgstr "الموظف"

#. module: ams_ta_mobile
#: model:ir.ui.menu,name:ams_ta_mobile.employees_menu
msgid "Employees"
msgstr ""

#. module: ams_ta_mobile
#: model_terms:ir.ui.view,arch_db:ams_ta_mobile.ams_ta_mobile_employee_view_form
msgid "Last Login Date"
msgstr ""

#. module: ams_ta_mobile
#: model_terms:ir.ui.view,arch_db:ams_ta_mobile.ams_ta_mobile_employee_view_form
msgid "Mobile App"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,field_description:ams_ta_mobile.field_hr_employee__mobile_app_allow
msgid "Mobile App Allow"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,field_description:ams_ta_mobile.field_hr_employee__mobile_app_version
msgid "Mobile App Version"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.ui.menu,name:ams_ta_mobile.top_menu
#: model_terms:ir.ui.view,arch_db:ams_ta_mobile.ams_ta_mobile_employee_view_form
msgid "Mobile Attendance"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,field_description:ams_ta_mobile.field_hr_employee__mobile_last_login_date
msgid "Mobile Last Login Date"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,field_description:ams_ta_mobile.field_hr_employee__mobile_register_id
msgid "Mobile Register"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,field_description:ams_ta_mobile.field_hr_employee__mobile_register_date
msgid "Mobile Register Date"
msgstr ""

#. module: ams_ta_mobile
#: model_terms:ir.ui.view,arch_db:ams_ta_mobile.ams_ta_mobile_employee_view_form
msgid "Mobile Time Attendance"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,field_description:ams_ta_mobile.field_hr_employee__mobile_token
msgid "Mobile Token"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,help:ams_ta_mobile.field_hr_employee__mobile_register_id
msgid "Mobile application ID"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.actions.act_window,name:ams_ta_mobile.punch_log_map_action
msgid "Punch Logs Map"
msgstr ""

#. module: ams_ta_mobile
#: model_terms:ir.ui.view,arch_db:ams_ta_mobile.ams_ta_mobile_employee_view_form
msgid "Register Date"
msgstr ""

#. module: ams_ta_mobile
#: model_terms:ir.ui.view,arch_db:ams_ta_mobile.ams_ta_mobile_employee_view_form
msgid "Register ID"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model,name:ams_ta_mobile.model_hr_leave_type
msgid "Time Off Type"
msgstr "نوع الإجازة "

#. module: ams_ta_mobile
#: model:ir.model,name:ams_ta_mobile.model_hr_leave
msgid "Time off Request"
msgstr "الإجازات "

#. module: ams_ta_mobile
#: model:ir.model.fields,field_description:ams_ta_mobile.field_hr_leave_type__time_off_category
msgid "Timeoff Category"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model,name:ams_ta_mobile.model_ams_ta_timesheet
msgid "Timesheet for AMS TA Including Time Off Configuration"
msgstr ""

#. module: ams_ta_mobile
#: model_terms:ir.ui.view,arch_db:ams_ta_mobile.ams_ta_mobile_employee_view_form
msgid "Token"
msgstr ""

#. module: ams_ta_mobile
#: model:ir.ui.menu,name:ams_ta_mobile.punch_logs_menu
msgid "Tracking Map"
msgstr ""

#. module: ams_ta_mobile
#: model_terms:ir.actions.act_window,help:ams_ta_mobile.punch_log_map_action
msgid "View Punch Logs on Map"
msgstr ""

#. module: ams_ta_mobile
#: model_terms:ir.actions.act_window,help:ams_ta_mobile.punch_log_map_action
msgid "View and track punch logs for employees on a map."
msgstr ""

#. module: ams_ta_mobile
#: model:ir.model.fields,help:ams_ta_mobile.field_hr_employee__mobile_token
msgid "When user (employee) success login save token"
msgstr ""
