<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- TODO[IMP]: employee_view_form-Inheritance -->
    <record id="ams_ta_mobile_employee_view_form" model="ir.ui.view">
        <field name="name">ams_base.mobile_employee_form_inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="ams_base.ams_employee_view_form"/>
        <field name="mode">extension</field>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page name="mobile_time_attendance" string="Mobile Time Attendance">
                    <group>
                        <field name="mobile_app_allow" string="Mobile App"/>
                        <field name="mobile_attendance_allow" string="Mobile Attendance"/>
                        <field name="mobile_app_version" string="App Version"/>
                        <field name="mobile_register_id" string="Register ID"/>
                        <field name="mobile_token" string="Token"/>
                        <field name="mobile_last_login_date" string="Last Login Date"/>
                        <field name="mobile_register_date" string="Register Date"/>
                    </group>
                </page>
            </xpath>
        </field>
    </record>

    <!--    &lt;!&ndash; TODO[IMP]: employee_view_list-Inheritance &ndash;&gt;-->
    <!--    <record id="ams_ta_mobile_employee_view_list" model="ir.ui.view">-->
    <!--        <field name="name">ams_base.mobile_employee_list_inherit</field>-->
    <!--        <field name="model">hr.employee</field>-->
    <!--        <field name="inherit_id" ref="hr.view_employee_tree"/>-->
    <!--        <field name="mode">extension</field>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <xpath expr="//list" position="inside">-->
    <!--                <field name="mobile_app_allow" string="Mobile App"/>-->
    <!--                <field name="mobile_attendance_allow" string="Mobile Attendance"/>-->
    <!--                <field name="mobile_app_version" string="App Version"/>-->
    <!--                <field name="mobile_register_id" string="Register ID"/>-->
    <!--                <field name="mobile_token" string="Token"/>-->
    <!--                <field name="mobile_last_login_date" string="Last Login Date"/>-->
    <!--                <field name="mobile_register_date" string="Register Date"/>-->
    <!--            </xpath>-->
    <!--        </field>-->
    <!--    </record>-->
</odoo>
