<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
            <record id="ams_ta_mobile_hr_leave_type_form" model="ir.ui.view">
            <field name="name">ams_base.hr_leave_type_form_inherit</field>
            <field name="model">hr.leave.type</field>
            <field name="inherit_id" ref="hr_holidays.edit_holiday_status_form"/>
    <!--        <field name="mode">extension</field>-->
            <field name="arch" type="xml">
                <xpath expr="//field[@name='request_unit']" position="after" >

                    <field name="time_off_category" groups="base.group_no_one"/>

                </xpath>
            </field>
        </record>

        <record id="ams_ta_mobile_hr_leave_type_tree" model="ir.ui.view">
            <field name="name">ams_base.hr_leave_type_tree_inherit</field>
            <field name="model">hr.leave.type</field>
            <field name="inherit_id" ref="hr_holidays.view_holiday_status_normal_tree"/>
    <!--        <field name="mode">extension</field>-->
            <field name="arch" type="xml">
                <xpath expr="//field[@name='allocation_validation_type']" position="after" >
                    <field name="time_off_category" groups="base.group_no_one"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>