{"info": {"_postman_id": "d3d9a638-1c14-4125-ac62-30e234a34ea5", "name": "Employee Permission", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12899274"}, "item": [{"name": "GetEmployeePermissionsByUserName", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployeePermissionsByUserName?userName=emp1&dateFrom=01012025&dateTo=31122026", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeePermissionsByUserName"], "query": [{"key": "userName", "value": "emp1"}, {"key": "dateFrom", "value": "01012025"}, {"key": "dateTo", "value": "31122026"}]}}, "response": []}, {"name": "AddPermissionRequest", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"EmpNo\": \"12345\",\n  \"UserName\":\"emp1\",\n  \"RequestName\":\"test validation\",\n  \"RequestNo\":\"55454\",\n  \"Status\":1,\n  \"StartDateString\": \"11/06/2025 9:00:00\",\n  \"EndDateString\": \"11/06/2025 11:00:00\",\n  \"Reason\": \"Professor appointment\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{amsBaseUrl}}/AddPermissionRequest", "host": ["{{amsBaseUrl}}"], "path": ["AddPermissionRequest"]}}, "response": []}, {"name": "GetEmployeePermissionsByEmpNo", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployeePermissionsByEmpNo?empNo=12345&dateFrom=01012025&dateTo=01012026", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeePermissionsByEmpNo"], "query": [{"key": "empNo", "value": "12345"}, {"key": "dateFrom", "value": "01012025"}, {"key": "dateTo", "value": "01012026"}]}}, "response": []}, {"name": "GetEmployeesPermissions", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployeesPermissions?dateFrom=01012025&dateTo=31122025", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeesPermissions"], "query": [{"key": "dateFrom", "value": "01012025"}, {"key": "dateTo", "value": "31122025"}]}}, "response": []}, {"name": "GetEmployeesPermissionsByDeptName", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployeesPermissionsByDeptName?deptName=1.2.3dept3&dateFrom=01012025&dateTo=31122026", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeesPermissionsByDeptName"], "query": [{"key": "deptName", "value": "1.2.3dept3"}, {"key": "dateFrom", "value": "01012025"}, {"key": "dateTo", "value": "31122026"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}