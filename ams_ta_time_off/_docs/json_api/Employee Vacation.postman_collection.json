{"info": {"_postman_id": "8a577156-7623-4cf9-94e5-35b27bc93ca0", "name": "Employee Vacation", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12899274"}, "item": [{"name": "GetEmployeeVacationsByUserName", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployeeVacationsByUserName?userName=emp1&dateFrom=01012023&dateTo=31122025", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeeVacationsByUserName"], "query": [{"key": "userName", "value": "emp1"}, {"key": "dateFrom", "value": "01012023"}, {"key": "dateTo", "value": "31122025"}]}}, "response": []}, {"name": "AddVacationRequest", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"EmpNo\": \"12345\",\n  \"UserName\":\"emp1\",\n  \"RequestName\":\"Paid Time Off\",\n  \"RequestNo\":\"66789\",\n  \"Status\":1,\n  \"StartDateString\": \"12/07/2025 9:00:00\",\n  \"EndDateString\": \"13/07/2025 11:00:00\",\n  \"Reason\": \"Professor appointment\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{amsBaseUrl}}/AddVacationRequest", "host": ["{{amsBaseUrl}}"], "path": ["AddVacationRequest"]}}, "response": []}, {"name": "GetEmployeeVacationsByEmpNo", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployeeVacationsByEmpNo?empNo=12345&dateFrom=01012023&dateTo=31122025", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeeVacationsByEmpNo"], "query": [{"key": "empNo", "value": "12345"}, {"key": "dateFrom", "value": "01012023"}, {"key": "dateTo", "value": "31122025"}]}}, "response": []}, {"name": "GetEmployeesVacations", "request": {"method": "GET", "header": [], "url": {"raw": "{{amsBaseUrl}}/GetEmployeesVacations?dateFrom=01012023&dateTo=31122025", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeesVacations"], "query": [{"key": "dateFrom", "value": "01012023"}, {"key": "dateTo", "value": "31122025"}]}}, "response": []}, {"name": "GetEmployeesVacationsByDeptName", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{amsBaseUrl}}/GetEmployeesVacationsByDeptName?deptName=1.2.3dept3&dateFrom=01012023&dateTo=31122025", "host": ["{{amsBaseUrl}}"], "path": ["GetEmployeesVacationsByDeptName"], "query": [{"key": "deptName", "value": "1.2.3dept3"}, {"key": "dateFrom", "value": "01012023"}, {"key": "dateTo", "value": "31122025"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}