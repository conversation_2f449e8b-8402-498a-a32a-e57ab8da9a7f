<h1 align="center">Employee Vacation API Documentation</h1>

<div align="center">

![Version](https://img.shields.io/badge/Version-1.0.0-blue)
![Status](https://img.shields.io/badge/Status-Stable-success)
![Last Updated](https://img.shields.io/badge/Last%20Updated-2025--05--08-informational)
![Company](https://img.shields.io/badge/Company-Laplace%20Software-orange)

</div>

## 📋 Overview

<div style="padding: 10px 0;">
This document provides comprehensive documentation for the Employee Vacation API endpoints. The API allows clients to retrieve vacation requests for employees, search for vacation requests by various criteria, and add new vacation requests.
</div>

## 🔗 Base URL
All API endpoints are relative to the base URL: `{{amsBaseUrl}}`

## 🔐 Authentication

<div style="padding: 5px 0;">
The API uses public authentication with CORS enabled. No specific authentication tokens are required for these endpoints.
</div>

## 📦 Response Format
All responses are in JSON format with consistent field naming using `CamelCase`. Each response includes:

- **ResponseCode**: Status code of the operation (e.g., "200" for success)
- **ResponseMessage**: English description of the operation result
- **ResponseMessageAR**: Arabic description of the operation result
- **ErrorMessage**: Error details if applicable (empty string if no errors)

<div style="margin-top: 10px;"></div>

## 🔍 API Endpoints

<details open>
<summary><strong>Table of Contents</strong></summary>

1. [Get Employee Vacations by Username](#1-get-employee-vacations-by-username-)
2. [Get Employee Vacations by Employee Number](#2-get-employee-vacations-by-employee-number-)
3. [Get All Employees Vacations](#3-get-all-employees-vacations-)
4. [Get Employees Vacations by Department Name](#4-get-employees-vacations-by-department-name-)
5. [Add Vacation Request](#5-add-vacation-request-)

</details>

<h3 id="1-get-employee-vacations-by-username-">1. Get Employee Vacations by Username 👤</h3>
Retrieves vacation requests for a specific employee by their username.

- **Endpoint**: `/GetEmployeeVacationsByUserName`
- **Method**: `GET`
- **Parameters**:
  - `userName` (required): The username of the employee
  - `dateFrom` (required): Start date in format "ddMMyyyy" (e.g., "01012023" for January 1, 2023)
  - `dateTo` (required): End date in format "ddMMyyyy" (e.g., "31122023" for December 31, 2023)

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeeVacationsByUserName?userName=emp1&dateFrom=01012023&dateTo=31122025
```

#### Success Response

```json
{
  "ResponseCode": "1",
  "ResponseMessage": "OK",
  "ResponseMessageAR": "تمت العملية بنجاح",
  "Emp": {
    "ResponseCode": "200",
    "ResponseMessage": "Success",
    "ResponseMessageAR": "تم بنجاح",
    "EmpNo": "12345",
    "UserName": "emp1",
    "EnglishName": "EMP1",
    "ArabicName": "EMP1",
    "Email": "emp1.com",
    "PhoneNo": "******-555-5556",
    "DeptArabicName": "1.2.3dept3",
    "DeptEnglishName": "1.2.3dept3",
    "BGArabicName": "All Users",
    "BGEnglishName": "All Users",
    "AreaArabicName": "",
    "AreaEnglishName": "",
    "BranchArabicName": "",
    "BranchEnglishName": "",
    "ErrorMessage": ""
  },
  "Logs": [
    {
      "ResponseCode": "1",
      "ResponseMessage": "OK",
      "ResponseMessageAR": "تمت العملية بنجاح",
      "EmpNo": "12345",
      "UserName": "emp1",
      "RequestNo": "176",
      "RequestDateString": "03/04/2025 17:44:12",
      "StartDateString": "02/04/2025 08:00:00",
      "EndDateString": "03/04/2025 17:00:00",
      "RequestName": "vacation test",
      "Reason": "Mobile Request - Category: Vacation",
      "ReplyComment": "Mobile Request - Category: Vacation",
      "Status": 1,
      "ErrorMessage": ""
    },
    // Additional vacation requests...
  ],
  "ErrorMessage": ""
}
```

#### Error Response - Employee Not Found

```json
{
  "ResponseCode": "404",
  "ResponseMessage": "Employee not found",
  "ResponseMessageAR": "الموظف غير موجود",
  "Emp": {},
  "Logs": [],
  "ErrorMessage": "Error: Employee not found"
}
```

#### Error Response - Invalid Date Range

```json
{
  "ResponseCode": "400",
  "ResponseMessage": "The start date must be earlier than the end date",
  "ResponseMessageAR": "يجب أن يكون تاريخ البداية قبل تاريخ النهاية",
  "Emp": { /* Employee details */ },
  "Logs": [],
  "ErrorMessage": "Error: The start date must be earlier than the end date"
}
```

<h3 id="2-get-employee-vacations-by-employee-number-">2. Get Employee Vacations by Employee Number 🔢</h3>
Retrieves vacation requests for a specific employee by their employee number.

- **Endpoint**: `/GetEmployeeVacationsByEmpNo`
- **Method**: `GET`
- **Parameters**:
  - `empNo` (required): The employee number
  - `dateFrom` (required): Start date in format "ddMMyyyy" (e.g., "01012023" for January 1, 2023)
  - `dateTo` (required): End date in format "ddMMyyyy" (e.g., "31122023" for December 31, 2023)

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeeVacationsByEmpNo?empNo=12345&dateFrom=01012023&dateTo=31122025
```

#### Success Response
Same format as `/GetEmployeeVacationsByUserName` endpoint.

<h3 id="3-get-all-employees-vacations-">3. Get All Employees Vacations 👥</h3>
Retrieves vacation requests for all employees within a date range.

- **Endpoint**: `/GetEmployeesVacations`
- **Method**: `GET`
- **Parameters**:
  - `dateFrom` (required): Start date in format "ddMMyyyy" (e.g., "01012023" for January 1, 2023)
  - `dateTo` (required): End date in format "ddMMyyyy" (e.g., "31122023" for December 31, 2023)

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeesVacations?dateFrom=01012023&dateTo=31122025
```

#### Success Response

```json
{
  "ResponseCode": "1",
  "ResponseMessage": "OK",
  "ResponseMessageAR": "تمت العملية بنجاح",
  "Requests": [
    {
      "ResponseCode": "1",
      "ResponseMessage": "OK",
      "ResponseMessageAR": "تمت العملية بنجاح",
      "Emp": {
        // Employee details
      },
      "Logs": [
        // Vacation requests for this employee
      ],
      "ErrorMessage": ""
    },
    // Additional employees with their vacation requests
  ],
  "ErrorMessage": ""
}
```

<h3 id="4-get-employees-vacations-by-department-name-">4. Get Employees Vacations by Department Name 🏢</h3>
Retrieves vacation requests for all employees in a specific department.

- **Endpoint**: `/GetEmployeesVacationsByDeptName`
- **Method**: `GET`
- **Parameters**:
  - `deptName` (required): The department name to filter by
  - `dateFrom` (required): Start date in format "ddMMyyyy" (e.g., "01012023" for January 1, 2023)
  - `dateTo` (required): End date in format "ddMMyyyy" (e.g., "31122023" for December 31, 2023)

#### Request Example

```http
# Request
GET {{amsBaseUrl}}/GetEmployeesVacationsByDeptName?deptName=1.2.3dept3&dateFrom=01012023&dateTo=31122025
```

#### Success Response

```json
{
  "ResponseCode": "1",
  "ResponseMessage": "OK",
  "ResponseMessageAR": "تمت العملية بنجاح",
  "Requests": [
    {
      "ResponseCode": "1",
      "ResponseMessage": "OK",
      "ResponseMessageAR": "تمت العملية بنجاح",
      "Emp": {
        // Employee details
      },
      "Logs": [
        // Vacation requests for this employee
      ],
      "ErrorMessage": ""
    },
    // Additional employees with their vacation requests
  ],
  "ErrorMessage": ""
}
```

<h3 id="5-add-vacation-request-">5. Add Vacation Request ➕</h3>
Creates or updates a vacation request for an employee.

- **Endpoint**: `/AddVacationRequest`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Request Body**: JSON object with vacation request details

#### Request Example

```http
# Request
POST {{amsBaseUrl}}/AddVacationRequest
Content-Type: application/json

{
  "EmpNo": "12345",
  "UserName": "emp1",
  "RequestNo": "66789",
  "StartDateString": "12/07/2025 9:00:00",
  "EndDateString": "13/07/2025 11:00:00",
  "RequestName": "Paid Time Off",
  "Reason": "Professor appointment",
  "ReplyComment": "",
  "Status": 1
}
```

#### Success Response

```json
{
  "ResponseCode": "200",
  "ResponseMessage": "Leave request created or updated successfully",
  "ResponseMessageAR": "",
  "EmpNo": "12345",
  "UserName": "emp1",
  "RequestNo": "66789",
  "RequestDateString": "",
  "StartDateString": "12/07/2025 9:00:00",
  "EndDateString": "13/07/2025 11:00:00",
  "RequestName": "Paid Time Off",
  "Reason": "Professor appointment",
  "ReplyComment": "",
  "Status": 1,
  "ErrorMessage": ""
}
```

#### Error Response - Invalid Date Range

```json
{
  "ResponseCode": "0",
  "ResponseMessage": "Error: End date must be greater than or equal to start date",
  "ResponseMessageAR": "",
  "EmpNo": "12345",
  "UserName": "emp1",
  "RequestNo": "66789",
  "RequestDateString": "",
  "StartDateString": "12/07/2025 9:00:00",
  "EndDateString": "13/07/2024 11:00:00",
  "RequestName": "Paid Time Off",
  "Reason": "Professor appointment",
  "ReplyComment": "",
  "Status": 0,
  "ErrorMessage": "Error: End date must be greater than or equal to start date",
  "TransactionType": 0,
  "TotalMinutes": 0
}
```

## 📚 Data Models

The API uses the following data models:

<h3 id="requestdto">RequestDTO</h3>

Represents a collection of vacation requests for an employee.

```json
{
  "ResponseCode": "1",           // Status code of the operation
  "ResponseMessage": "OK",       // English description of the operation result
  "ResponseMessageAR": "تمت العملية بنجاح", // Arabic description of the operation result
  "Emp": {                       // Employee information (EmployeeDTO)
    // See EmployeeDTO model
  },
  "Logs": [                      // Array of vacation requests (RequestLogDTO)
    // See RequestLogDTO model
  ],
  "ErrorMessage": ""             // Error details if applicable
}
```

<h3 id="requestlogdto">RequestLogDTO</h3>

Represents an individual vacation request.

```json
{
  "ResponseCode": "1",           // Status code of the operation
  "ResponseMessage": "OK",       // English description of the operation result
  "ResponseMessageAR": "تمت العملية بنجاح", // Arabic description of the operation result
  "EmpNo": "12345",              // Employee number
  "UserName": "emp1",            // Employee username
  "RequestNo": "176",            // Request identifier
  "RequestDateString": "03/04/2025 17:44:12", // Date when request was created
  "StartDateString": "02/04/2025 08:00:00",   // Start date of vacation
  "EndDateString": "03/04/2025 17:00:00",     // End date of vacation
  "RequestName": "vacation test", // Type of vacation request
  "Reason": "Mobile Request - Category: Vacation", // Reason for vacation
  "ReplyComment": "Mobile Request - Category: Vacation", // Comments from approver
  "Status": 1,                   // Status code (0=pending, 1=approved, 2=rejected, 3=cancelled)
  "ErrorMessage": ""             // Error details if applicable
}
```

<h3 id="requestlistdto">RequestListDTO</h3>

Represents a list of vacation requests for multiple employees.

```json
{
  "ResponseCode": "1",           // Status code of the operation
  "ResponseMessage": "OK",       // English description of the operation result
  "ResponseMessageAR": "تمت العملية بنجاح", // Arabic description of the operation result
  "Requests": [                  // Array of RequestDTO objects
    // See RequestDTO model
  ],
  "ErrorMessage": ""             // Error details if applicable
}
```

<h3 id="employeedto">EmployeeDTO</h3>

Represents an individual employee record.

```json
{
  "ResponseCode": "200",          // Status code of the operation
  "ResponseMessage": "Success",    // English description of the operation result
  "ResponseMessageAR": "تم بنجاح", // Arabic description of the operation result
  "EmpNo": "12345",              // Employee number/ID
  "UserName": "emp1",            // Employee username
  "EnglishName": "John Doe",      // Employee name in English
  "ArabicName": "جون دو",         // Employee name in Arabic
  "Email": "<EMAIL>", // Employee email address
  "PhoneNo": "+966501234567",    // Employee phone number
  "DeptArabicName": "قسم المبيعات", // Department name in Arabic
  "DeptEnglishName": "Sales",     // Department name in English
  "BGArabicName": "المجموعة أ",    // Business group name in Arabic
  "BGEnglishName": "Group A",     // Business group name in English
  "AreaArabicName": "الرياض",      // Area name in Arabic
  "AreaEnglishName": "Riyadh",    // Area name in English
  "BranchArabicName": "الفرع الرئيسي", // Branch name in Arabic
  "BranchEnglishName": "Main Branch", // Branch name in English
  "ErrorMessage": ""              // Error details if applicable
}
```

<hr style="margin-top: 30px; margin-bottom: 30px;">

<div align="center">

# End of Document

<p style="font-size: 12px; color: #555;">Copyright © 2025 Laplace Software. All rights reserved.</p>

</div>
