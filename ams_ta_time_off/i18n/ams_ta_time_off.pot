# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ams_ta_time_off
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-11-05 09:11+0000\n"
"PO-Revision-Date: 2025-11-05 09:11+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.resource_calendar_leaves_tree_timeoff
msgid "Activate"
msgstr ""

#. module: ams_ta_time_off
#: model:ir.model.fields,field_description:ams_ta_time_off.field_hr_leave__company_id
msgid "Company"
msgstr ""

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.resource_calendar_leaves_tree_timeoff
msgid "Deactivate"
msgstr ""

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.view_hr_holidays_filter_inherit
msgid "Employee"
msgstr ""

#. module: ams_ta_time_off
#: model:ir.model.fields,field_description:ams_ta_time_off.field_resource_calendar_leaves__is_active
msgid "Is Active"
msgstr ""

#. module: ams_ta_time_off
#: model:ir.model.fields,field_description:ams_ta_time_off.field_hr_leave__leave_manager_id
msgid "Leave Manager"
msgstr ""

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.ta_timesheet_view_form_timeoff
msgid "Permission"
msgstr ""

#. module: ams_ta_time_off
#: model:ir.model,name:ams_ta_time_off.model_resource_calendar_leaves
msgid "Public Holidays"
msgstr ""

#. module: ams_ta_time_off
#: model:ir.model.fields,field_description:ams_ta_time_off.field_ams_ta_timesheet__public_vacation_id
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.ta_timesheet_view_form_timeoff
msgid "Public Vacation"
msgstr ""

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.ta_timesheet_view_search_timeoff
msgid "Public Vacation ID"
msgstr ""

#. module: ams_ta_time_off
#: model:ir.model.fields,field_description:ams_ta_time_off.field_hr_leave__record_id
msgid "Record"
msgstr ""

#. module: ams_ta_time_off
#: model:ir.model.fields,field_description:ams_ta_time_off.field_ams_ta_timesheet__time_off_id
msgid "Time Off"
msgstr ""

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.ta_timesheet_view_form_timeoff
msgid "Time Off Info"
msgstr ""

#. module: ams_ta_time_off
#: model:ir.model,name:ams_ta_time_off.model_hr_leave
msgid "Time off Request"
msgstr ""

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.ta_timesheet_view_search_timeoff
msgid "Time-off ID"
msgstr ""

#. module: ams_ta_time_off
#: model:ir.model,name:ams_ta_time_off.model_ams_ta_timesheet
msgid "Timesheet for AMS TA Including Time Off Configuration"
msgstr ""

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.ta_timesheet_view_form_timeoff
msgid "Vacation"
msgstr ""

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.ta_timesheet_view_form_timeoff
msgid "time off for Permission"
msgstr ""

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.ta_timesheet_view_form_timeoff
msgid "time off for Public Vacation"
msgstr ""

#. module: ams_ta_time_off
#: model_terms:ir.ui.view,arch_db:ams_ta_time_off.ta_timesheet_view_form_timeoff
msgid "time off for Vacation"
msgstr ""
