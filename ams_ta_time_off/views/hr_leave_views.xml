<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <model_name> _view_form-->
    <record id="leave_view_form_with_manager_id" model="ir.ui.view">
        <field name="name">hr.leave.form</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='holiday_status_id']" position="after">
                <field name="leave_manager_id"/>
            </xpath>
<!--            <xpath expr="//field[@name='request_unit_half']" position="before">-->
<!--                <field invisible="leave_type_request_unit == 'day'" name="manual_from_to"-->
<!--                       readonly="state not in ('confirm', 'validate')"/>-->
<!--                <label for="manual_from_to" invisible="leave_type_request_unit == 'day'" string="Manual Duration"/>-->
<!--            </xpath>-->
<!--            <xpath expr="//label[@for='request_unit_half']" position="replace">-->
<!--                <label for="request_unit_half" invisible="1" string="Period Start/End"/>-->
<!--            </xpath>-->
<!--            <xpath expr="//field[@name='request_unit_half']" position="replace">-->
<!--                <field invisible="1" name="request_unit_half"/>-->
<!--            </xpath>-->
<!--            <xpath expr="//label[@for='request_unit_hours']" position="replace">-->
<!--                <label for="request_unit_hours" invisible="1"/>-->
<!--            </xpath>-->
<!--            <xpath expr="//field[@name='request_unit_hours']" position="replace">-->
<!--                <field class="ml-5" invisible="1" name="request_unit_hours"/>-->
<!--            </xpath>-->
<!--            <xpath expr="//field[@name='request_hour_from']" position="replace">-->
<!--                <div class="o_row" invisible="manual_from_to == False">-->
<!--                    <field class="oe_inline" name="manual_from_to_duration" nolabel="1" widget="float_time_selection"/>-->
<!--                    <span>-->
<!--                        hours-->
<!--                    </span>-->
<!--                </div>-->
<!--                <div class="o_row" invisible="manual_from_to == True or leave_type_request_unit != 'hour'">-->
<!--                    <field class="oe_inline" name="number_of_hours" nolabel="1"/>-->
<!--                </div>-->
<!--            </xpath>-->
        </field>
    </record>
    <record id="view_hr_holidays_filter_inherit" model="ir.ui.view">
        <field name="name">hr.holidays.filter.inherit</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.view_hr_holidays_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//group" position="inside">
                <filter context="{'group_by':'employee_id'}" name="group_employee" string="Employee"/>
            </xpath></field>
    </record>
</odoo>
