<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- form view -->
        <record id="ta_timesheet_view_form_timeoff" model="ir.ui.view">
            <field name="name">ta_timesheet_form</field>
            <field name="model">ams_ta.timesheet</field>
            <field name="type">form</field>
            <field name="inherit_id" ref="ams_ta.timesheet_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='notes']" position="after">
                    <field name="time_off_id" invisible="is_vacation == False"
                           string="Vacation" help="time off for Vacation"/>
                    <field name="time_off_id" invisible="is_permission == False"
                           string="Permission" help="time off for Permission"/>
                    <field name="public_vacation_id" invisible="is_public_vacation == False" string="Public Vacation"
                           help="time off for Public Vacation"/>
                </xpath>
                <xpath expr="//page[@name='other_info']" position="before">
                    <page string="Time Off Info">
                        <group>
                            <group>
                                <field name="is_weekend"/>
                                <field name="is_vacation"/>
                                <field name="is_public_vacation"/>
                                <field name="is_permission"/>
                            </group>
                            <group>
                                <field name="is_dayoff"/>
                                <field name="time_off_hours"/>
                            </group>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- list view -->
        <record id="ta_timesheet_view_list_timeoff" model="ir.ui.view">
            <field name="name">ta_timesheet_tree</field>
            <field name="model">ams_ta.timesheet</field>
            <field name="type">list</field>
            <field name="inherit_id" ref="ams_ta.timesheet_view_list"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='notes']" position="after">
                    <field name="time_off_id" optional="hide"/>
                    <!--                    <field name="time_off_id2" optional="hide"/>-->
                    <field name="public_vacation_id" optional="hide"/>
                </xpath></field>
        </record>

        <!-- search -->
        <record id="ta_timesheet_view_search_timeoff" model="ir.ui.view">
            <field name="name">ta_timesheet</field>
            <field name="model">ams_ta.timesheet</field>
            <field name="inherit_id" ref="ams_ta.timesheet_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='shift']">
                    <filter context="{'group_by':'time_off_id'}" name="timeoff_id" string="Time-off ID"/>
                    <filter context="{'group_by':'public_vacation_id'}" name="public_vacation_id"
                            string="Public Vacation ID"/>
                </xpath></field>
        </record>
    </data>
</odoo>
