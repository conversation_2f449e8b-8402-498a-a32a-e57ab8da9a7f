# Email Notification System for Invitation Workflow

## Overview
This document describes the email notification system implemented for the AMS Visit Management module. The system automatically sends email notifications to invitation requesters when the invitation state changes to 'approved', 'confirmed', or 'rejected'.

## Implementation Details

### 1. Email Template
- **File**: `data/mail_template_data.xml`
- **Template ID**: `mail_template_invitation_state_change`
- **Model**: `ams_vm.invitation` (inherits from `ams_vm.base_request`)
- **Features**:
  - Professional HTML email layout
  - Dynamic content based on invitation state
  - Color-coded status indicators
  - Comprehensive invitation details table
  - Responsive design

### 2. Email Notification Logic
- **File**: `models/base_visitor_request.py`
- **Method**: `write()` override
- **Trigger**: State changes to 'approved', 'confirmed', or 'rejected'
- **Recipient**: `create_uid` (invitation requester)
- **Features**:
  - Automatic email sending via mail queue
  - Error handling and logging
  - Chatter integration for audit trail
  - Performance optimized (non-blocking)

### 3. Email Content
The email template includes:
- **Header**: Professional styling with company branding
- **Status Update**: Clear indication of the new state
- **Invitation Details**:
  - Invitation name
  - Current status (color-coded)
  - Visitor information
  - Start and end dates
  - Next steps based on state
- **Footer**: Company information and contact details

### 4. State-Specific Messaging
- **Approved**: "Your invitation has been approved. Please wait for confirmation."
- **Confirmed**: "Your invitation has been confirmed. You can now proceed with your visit."
- **Rejected**: "Your invitation request has been rejected. Please contact the administrator for more information."

## Technical Features

### Error Handling
- Graceful handling of missing email addresses
- Exception catching for email sending failures
- Comprehensive logging in the invitation chatter

### Performance Optimization
- Uses Odoo's mail queue system (`force_send=False`)
- Non-blocking email sending
- Batch processing support

### Security
- Uses `sudo()` for template access to ensure proper permissions
- Validates email addresses before sending
- Secure template rendering with proper escaping

### Audit Trail
- All email notifications are logged in the invitation chatter
- Success and failure messages are recorded
- Timestamp and recipient information included

## Configuration

### Prerequisites
- Odoo mail module must be installed and configured
- SMTP server must be configured in Odoo settings
- Users must have valid email addresses

### Template Customization
The email template can be customized by:
1. Navigating to Settings > Technical > Email Templates
2. Finding "Invitation State Change Notification"
3. Modifying the subject, body, or styling as needed

### Disabling Notifications
To disable email notifications:
1. Archive the email template, or
2. Modify the `_send_state_change_notifications` method to return early

## Testing

### Manual Testing
1. Create an invitation with a user that has an email address
2. Change the invitation state to 'approved', 'confirmed', or 'rejected'
3. Check the mail queue in Settings > Technical > Email > Emails
4. Verify the email content and recipient
5. Check the invitation chatter for notification logs

### Automated Testing
Consider adding unit tests to verify:
- Email template rendering
- State change detection
- Email sending logic
- Error handling scenarios

## Troubleshooting

### Common Issues
1. **Emails not sending**: Check SMTP configuration and mail queue
2. **Template not found**: Verify template installation and XML ID
3. **Missing recipient**: Ensure users have valid email addresses
4. **Permission errors**: Check user access rights for mail templates

### Debugging
- Enable developer mode to access technical menus
- Check the mail queue for pending/failed emails
- Review invitation chatter for error messages
- Check Odoo logs for detailed error information

## Future Enhancements

### Possible Improvements
1. **Multi-language support**: Translate email templates
2. **Custom templates**: Allow different templates per company/user
3. **Email preferences**: Let users opt-in/out of notifications
4. **Rich notifications**: Add attachments or embedded images
5. **SMS integration**: Alternative notification channels
6. **Batch notifications**: Summary emails for multiple state changes

### Integration Opportunities
1. **Calendar integration**: Add calendar invites for confirmed visits
2. **Mobile notifications**: Push notifications via mobile app
3. **Slack/Teams integration**: Workplace messaging notifications
4. **Dashboard widgets**: Real-time notification counters

## Maintenance

### Regular Tasks
- Monitor email delivery rates
- Review and clean up mail queue periodically
- Update email templates as business requirements change
- Test email functionality after system updates

### Performance Monitoring
- Track email sending performance
- Monitor mail queue size and processing time
- Review error logs for recurring issues
- Optimize template rendering if needed