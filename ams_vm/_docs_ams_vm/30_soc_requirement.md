# Source Code Review Prerequisites

## Prerequisite SCR for the Visitor Management System

### 1. Access to the Source Code

The source code review cannot be conducted without access to the source code, so the application owner needs to provide the service provider with the necessary access.

### 2. Code Documentation

The application owner needs to provide any documentation related to the code, such as design documents, architecture documents, and user manuals.

### 3. Code History

The application owner should provide the service provider with access to the code repository and the history of the code changes.

### 4. List of Third-Party Libraries and Frameworks

The application owner should provide a list of any third-party libraries and frameworks used in the application.

### 5. List of Application Features

The application owner should provide a list of the application features and their associated code modules.

### 6. Threat History

The application owner should provide a list of the historical security threats for the application, which will help the service provider to prioritize the review.

### 7. Contact Information

The respective team needs to provide contact information for key personnel who can provide assistance or answer questions during the SCA assessment.

## Source Code Review Request Form

| Field | Information |
|-------|-------------|
| Application Name | Visitor Management System (VMS) |
| Repository Name | addons_ams |
| Source Code Review Type | Comprehensive SCA |
| SOC Team | Ministry of Economy and Planning Security Operations Center |
| Brief Description about the Application/Change | The Visitor Management System is a custom Odoo-based application that manages government buildings' visitor registration, access control, and tracking. It integrates with BioStar 2 for biometric access control and LDAP for user authentication and includes QR code-based visitor identification. The system consists of multiple interconnected modules that handle different aspects of visitor management and access control. |
| Technical SPOC detail to provide application walkthrough | Abdelrazek Badr  <br>Lead Developer  <br>Email: <EMAIL>  <br>Phone: +966 59 277 2339 |
| Application/change relative documentation | - Technical architecture diagrams in the documentation folder  <br>- API documentation for BioStar 2 integration  <br>- User manuals for the Visitor Management System |
| Number of code lines | Custom modules about Total: 20,658 lines of code  <br>- Python: 14,329 lines  <br>- XML: 6,329 lines  <br><br>Module breakdown:  <br>- ams_base: 2,538 lines  <br>- ams: 3,071 lines  <br>- ams_bs: 10,277 lines  <br>- ams_vm: 3,750 lines  <br>- ams_mep: 1,022 lines |
