---
title: "Technical Documentation for SOC Review"
subtitle: "Visitor Management System"
author: "Ministry of Economy and Planning"
date: "June 2023"
geometry: "margin=2.5cm"
toc: true
toc-depth: 3
numbersections: true
colorlinks: true
---
# Technical Documentation for SOC Review

## Table of Contents

1. [System Architecture Overview](#1-system-architecture-overview)
   1. [Module Structure and Dependencies](#11-module-structure-and-dependencies)
   2. [Module Descriptions](#12-module-descriptions)
2. [Technology Stack](#2-technology-stack)
   1. [Core Technologies](#21-core-technologies)
   2. [Deployment Architecture](#22-deployment-architecture)
3. [Data Models and Relationships](#3-data-models-and-relationships)
   1. [Core Models](#31-core-models)
4. [System Workflow](#4-system-workflow)
5. [Security Mechanisms](#5-security-mechanisms)
   1. [Authentication and Authorization](#51-authentication-and-authorization)
   2. [Data Protection](#52-data-protection)
6. [Third-Party Dependencies](#6-third-party-dependencies)
   1. [Odoo Modules](#61-odoo-modules)
   2. [External Libraries](#62-external-libraries)
7. [Integration Points](#7-integration-points)
   1. [BioStar 2 Integration](#71-biostar-2-integration)
   2. [Email Integration](#72-email-integration)
   3. [LDAP/Active Directory](#73-ldapactive-directory)
8. [Source Code Access and References](#8-source-code-access-and-references)
   1. [Source Code Access](#81-source-code-access)
   2. [Odoo References](#82-odoo-references)
9. [Testing Environment](#9-testing-environment)
   1. [Test Server Information](#91-test-server-information)
   2. [External System Integration Points](#92-external-system-integration-points)
10. [Source Code Review Request](#10-source-code-review-request)
    1. [Application Information](#101-application-information)
    2. [Application Description](#102-application-description)
    3. [Technical Contact](#103-technical-contact)
    4. [Documentation](#104-documentation)
    5. [Code Statistics](#105-code-statistics)
11. [Contact Information](#11-contact-information)

---

## 1. System Architecture Overview

This document provides technical information about the Visitor Management System built on Odoo 18, focusing on the architecture, module structure, and security aspects as required for the SOC review.

### 1.1 Module Structure and Dependencies

The system consists of several interconnected modules that build upon each other to provide the complete visitor management functionality.

```mermaid
graph BT;
    ams_base[ams_base <br>Base Module]
    ams_bs[ams_bs <br> Biostar Access Control]
    ams[ams Core <br>  Access Management System]
    ams_vm[ams_vm <br> Visitor Management]
    ams_mep[ams_mep <br> Ministry of Economy and Planning <br> Customization]
    hr[Employees Management]

    ams_base --> hr
    ams --> ams_base
    ams_bs --> ams
    ams_vm --> ams
    ams_mep --> ams_vm
```

### 1.2 Module Descriptions

- **ams_base**: Foundation module providing abstract models and shared functionality
- **ams**: Core Access Management System handling access control fundamentals
- **ams_bs**: BioStar integration for biometric access control
- **ams_vm**: Visitor Management module for handling visitor registration and access
- **ams_mep**: Ministry-specific customizations for the Visitor Management System

## 2. Technology Stack

### 2.1 Core Technologies

- **Odoo 18**: Base application framework
- **Python 3.12+**: Backend programming language
- **PostgreSQL**: Database system
- **JavaScript/XML**: Frontend technologies
- **Docker**: Containerization for deployment

### 2.2 Deployment Architecture

The system is deployed across multiple virtual machines as illustrated below:

```mermaid
graph TD
    subgraph Production Environment
        subgraph VM1[Production VM 1 Odoo Web Application]
            direction TB
            pgAdmin4[Docker: PgAdmin4 <br> DB Management <br> Ports:5050]
            Odoo[Docker: Odoo Web <br> Visitor Management System <br> Ports:8069,7072]
            Nginx[Docker: Nginx Proxy Manager <br> Ports:80,443,81]
            Portainer[Docker: Portainer <br> Ports:9000,9443]

        end

        subgraph VM2[Production VM 2]
            direction TB
            PostgreSQL[PostgreSQL Database <BR> Ports: 5432]
        end

        Odoo -->|Connects to| PostgreSQL
        pgAdmin4 -->|Connects to| PostgreSQL

    end


    subgraph External Integrations
        BioStar2[BioStar 2 Server] -->|Access Control| Odoo
        LDAP[LDAP/Active Directory <BR> Ports: 389] -->|User Authentication or Sync| Odoo
        SMTP[SMTP Server] -->|Email Notifications| Odoo
    end

    subgraph Repositories
        GitHub[GitHub] -->|Clone or pull updates| Odoo
        DockerHub[Docker Hub] -->|Pull Docker Images| Odoo
    end

    style VM1 fill:#f9f,stroke:#333,stroke-width:2px
    style VM2 fill:#bbf,stroke:#333,stroke-width:2px
    style Repositories fill:#99f,stroke:#333,stroke-width:2px
```

## 3. Data Models and Relationships

The system is built on a hierarchical model structure with inheritance patterns typical of Odoo applications.

### 3.1 Core Models

```mermaid
classDiagram
    class BaseAbstractModel {
        - _name = 'ams_base.abstract_model'
    }

    class BaseRequest {
        - _name = 'ams_base.request'
        - _inherit = 'ams_base.abstract_model'
        - name: Char
        - description: Char
        - state: Selection(pending, approved, rejected,cancelled)
        - status: Selection(pending, running, finished)
        - start_date: DateTime
        - end_date: DateTime
        - request_uid:User // res.users
        - request_date: DateTime
        - response_uid: User // res.users
        - response_date: DateTime
        - commit : Char
    }

    class Visitor {
        - _name = 'ams_vm.visitor'
        - _inherit = 'ams_base.abstract_model'
        - _inherits = 'res.partner': 'partner_id'
        - partner_id: Many2one
    }

    class BaseVisitorRequest {
        - _name = 'ams_vm.base_request'
        - _inherit = 'ams_base.request'
        - visitor_id: Many2one

        + action_approve()
        + action_cancel()
        + action_reject()
    }

    class Invitation {
        - _name = 'ams_vm.invitation'
        - _inherit = 'ams_vm.base_request'

        + action_approve()
        + action_reject()
    }

    class Visit {
        - _name = 'ams_vm.visit'
        - _inherit = 'ams_vm.base_request'
    }

    BaseAbstractModel <|--  BaseRequest :_inherit
    BaseRequest <|--  BaseVisitorRequest :_inherit
    BaseVisitorRequest <|--  Invitation :_inherit
    BaseVisitorRequest <|--  Visit :_inherit

    BaseVisitorRequest --o Visitor
```

## 4. System Workflow

The visitor management process follows a defined workflow from registration to access:

```mermaid
sequenceDiagram
    Employee ->> VMS: Register Visitor
    Employee ->> VMS: Create Invitation Request
    VMS ->> Visitor Email: Send QR Code
    Visitor ->> Security: Presents ID and QR Code
    Security ->> VMS: Verify QR Code and ID
    VMS ->> Security: Confirm Visit
    VMS ->> Biometric Device: Grant Access
    Visitor ->> Biometric Device: Scan QR Code
    Biometric Device ->> Gate/Door: Open (if valid)
```

## 5. Security Mechanisms

### 5.1 Authentication and Authorization

- **User Authentication**: Leverages Odoo's built-in authentication system with optional LDAP integration
- **Role-Based Access Control**: Implemented through Odoo security groups:
  - `ams_vm.group_employee_request`: Regular employees who can register visitors
  - `ams_vm.group_security_approval`: Security personnel who approve visitor access
  - `ams_vm.group_event_request`: Users who can create event invitations
  - `ams_base.ams_group_manager`: System administrators

### 5.2 Data Protection

- **Data Encryption**: Sensitive data stored in encrypted format
- **Secure Communications**: HTTPS for all web traffic
- **Access Logging**: All system access and changes are logged

## 6. Third-Party Dependencies

### 6.1 Odoo Modules

- base
- mail
- hr (for employee management)

### 6.2 External Libraries

- Python libraries for API communication
- Libraries for QR code generation and processing

## 7. Integration Points

### 7.1 BioStar 2 Integration

- API-based integration for access control
- Secure credential exchange for door access

### 7.2 Email Integration

- SMTP server integration for visitor notifications
- Template-based email generation

### 7.3 LDAP/Active Directory

- User authentication and synchronization
- Role mapping between directory services and application roles

## 8. Source Code Access and References

### 8.1 Source Code Access

The complete source code for the Visitor Management System is available on the test server for review. The code follows Odoo's standard module structure and can be accessed directly on the Oracle Linux virtual machine server under the `opc` user account.

#### Module Locations on Test Server

- **Core Modules**: `/usr/lib/python3/dist-packages/odoo/addons/`
- **Custom Modules**: `/opt/addons_ams/`
  - `ams_base`: Base module with abstract models
  - `ams`: Core Access Management System
  - `ams_bs`: BioStar integration module
  - `ams_vm`: Visitor Management module
  - `ams_mep`: Ministry customizations

### 8.2 Odoo References

As the system is built on Odoo 18 Community Edition, the following official resources can provide additional context:

- **Odoo Source Code**: [https://github.com/odoo/odoo](https://github.com/odoo/odoo)
- **Odoo Downloads**: [https://www.odoo.com/page/download](https://www.odoo.com/page/download)
- **Odoo Website**: [https://www.odoo.com](https://www.odoo.com)
- **Odoo GDPR Compliance**: [https://www.odoo.com/gdpr](https://www.odoo.com/gdpr)

## 9. Testing Environment

### 9.1 Test Server Information

A dedicated test environment is available for security review with identical architecture to production but isolated from production systems.

#### Test Server Access Points

- **Web Application**: [http://visitor-test.mep.gov.sa](http://visitor-test.mep.gov.sa) or [http://************:8069](http://************:8069)
- **Nginx Admin Console**: [http://************:81](http://************:81)
- **Portainer Container Management**: [http://************:9000](http://************:9000)

### 9.2 External System Integration Points

- **BioStar API**: [https://************](https://************)
- **SMTP Server**: `smtpgrn.mep.gov.sa`
- **LDAP Server**: `************`
  - **Base DN**: OU=MEP-Employees,OU=Users,OU=MEP,OU=Units,DC=planning,DC=gov,DC=sa
  - **Filter**: userPrincipalName=%s

*Note: For security reasons, authentication credentials are not included in this document. Proper credentials will be provided separately through secure channels to authorized security team members.*

## 10. Source Code Review Request

### 10.1 Application Information

- **Application Name:** Visitor Management System (VMS)
- **Repository Name:** addons_ams
- **Source Code Review Type:** Comprehensive SCA
- **SOC Team:** Ministry of Economy and Planning Security Operations Center

### 10.2 Application Description

The Visitor Management System is a custom Odoo-based application that manages government buildings' visitor registration, access control, and tracking. It integrates with BioStar 2 for biometric access control and LDAP for user authentication and includes QR code-based visitor identification. The system consists of multiple interconnected modules that handle different aspects of visitor management and access control.

### 10.3 Technical Contact

- **Name:** Abdelrazek Badr
- **Role:** Lead Developer
- **Email:** [<EMAIL>](mailto:<EMAIL>)
- **Phone:** +966 59 277 2339

### 10.4 Documentation

- Technical architecture documentation
- User manuals for the Visitor Management System
  - 01_VMS_Employee_Guide_V1.PDF
  - 02_VMS_Security_Employee_Guide_V1.PDF
  - 03_VMS_Event_Employee_Guide_V1.PDF

### 10.5 Code Statistics

**Total:** 20,658 lines of code

- Python: 14,329 lines
- XML: 6,329 lines

**Module breakdown:**

- ams_base: 2,538 lines
- ams: 3,071 lines
- ams_bs: 10,277 lines
- ams_vm: 3,750 lines
- ams_mep: 1,022 lines

## 11. Contact Information

For technical inquiries related to this security review, please contact:

- **Technical Lead**: Abdelrazek Badr  [+966 59 277 2339]
- **Project Manager**: Ahmed Fouad [+966 56 927 1503]

---

*Note: This document provides a high-level technical overview for security assessment purposes. Detailed implementation information is available through code review on the test server.*
