classDiagram
    class BaseAbstractModel {
        - _name = 'ams_base.abstract_model'
    }

    class BaseRequest {
        - _name = 'ams_base.request'
        - _inherit = 'ams_base.abstract_model'
        - name: Char
        - description: Char
        - state: Selection(pending, approved, rejected,cancelled)
        - status: Selection(pending, running, finished)
        - start_date: DateTime
        - end_date: DateTime
        - request_uid:User // res.users
        - request_date: DateTime
        - response_uid: User // res.users
        - response_date: DateTime
        - commit : Char
    }

    class Visitor {
        - _name = 'ams_vm.visitor'
        - _inherit = 'ams_base.abstract_model'
        - _inherits = 'res.partner': 'partner_id'
        - partner_id: Many2one
    }

    class BaseVisitorRequest {
        - _name = 'ams_vm.base_request'
        - _inherit = 'ams_base.request'
        - visitor_id: Many2one
            
        + action_approve()
        + action_cancel()
        + action_reject()
    }

    class Invitation {
        - _name = 'ams_vm.invitation'
        - _inherit = 'ams_vm.base_request'
        
        + action_approve()
        + action_reject()
    }

    class Visit {
        - _name = 'ams_vm.visit'
        - _inherit = 'ams_vm.base_request'
    }

    BaseAbstractModel <|--  BaseRequest :_inherit
    BaseRequest <|--  BaseVisitorRequest :_inherit
    BaseVisitorRequest <|--  Invitation :_inherit
    BaseVisitorRequest <|--  Visit :_inherit
    
    BaseVisitorRequest --o Visitor
