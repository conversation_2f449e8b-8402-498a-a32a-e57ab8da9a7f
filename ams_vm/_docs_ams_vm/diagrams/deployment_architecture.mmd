graph TD
    subgraph Production Environment
        subgraph VM1[Production VM 1 Odoo Web Application]
            direction TB
            pgAdmin4[Docker: PgAdmin4 <br> DB Management <br> Ports:5050]
            Odoo[Docker: Odoo Web <br> Visitor Management System <br> Ports:8069,7072]
            Nginx[Docker: Nginx Proxy Manager <br> Ports:80,443,81]
            Portainer[Docker: Portainer <br> Ports:9000,9443]

        end

        subgraph VM2[Production VM 2]
            direction TB
            PostgreSQL[PostgreSQL Database <BR> Ports: 5432]
        end

        Odoo -->|Connects to| PostgreSQL
        pgAdmin4 -->|Connects to| PostgreSQL

    end


    subgraph External Integrations
        BioStar2[BioStar 2 Server] -->|Access Control| Odoo
        LDAP[LDAP/Active Directory <BR> Ports: 389] -->|User Authentication or Sync| Odoo
        SMTP[SMTP Server] -->|Email Notifications| Odoo
    end

    subgraph Repositories
        GitHub[GitHub] -->|Clone or pull updates| Odoo
        DockerHub[Docker Hub] -->|Pull Docker Images| Odoo
    end

    style VM1 fill:#f9f,stroke:#333,stroke-width:2px
    style VM2 fill:#bbf,stroke:#333,stroke-width:2px
    style Repositories fill:#99f,stroke:#333,stroke-width:2px
