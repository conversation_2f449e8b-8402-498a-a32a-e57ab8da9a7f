# PDF Conversion Instructions

This directory contains a technical documentation file that needs to be converted to PDF format with an index on the first page.

## Files

- `31_technical_documentation_for_soc.md` - The original Markdown file with Mermaid diagrams
- `31_technical_documentation_for_soc_pdf_ready.md` - A PDF-ready version with image placeholders
- `extract_diagrams.py` - A script to extract Mermaid diagrams for conversion

## Converting to PDF

### Option 1: Using Pandoc and LaTeX (Recommended)

1. Install Pandoc and LaTeX if not already installed:
   ```
   brew install pandoc
   brew install --cask mactex-no-gui
   ```

2. Convert the Markdown to PDF:
   ```
   pandoc 31_technical_documentation_for_soc.md -o technical_documentation_for_soc.pdf --toc --toc-depth=3 --pdf-engine=xelatex -V geometry:margin=1in
   ```

### Option 2: Using Online Converters

1. Use an online Markdown to PDF converter such as:
   - [Markdown to PDF](https://www.markdowntopdf.com/)
   - [Pandoc Online](https://pandoc.org/try/)
   - [Dillinger](https://dillinger.io/)

2. Upload the `31_technical_documentation_for_soc_pdf_ready.md` file to the converter
3. Download the generated PDF

### Option 3: Using Visual Studio Code

1. Install the "Markdown PDF" extension in VS Code
2. Open the `31_technical_documentation_for_soc_pdf_ready.md` file
3. Right-click in the editor and select "Markdown PDF: Export (pdf)"

## Handling Mermaid Diagrams

For proper rendering of Mermaid diagrams:

1. Run the extraction script:
   ```
   python extract_diagrams.py
   ```

2. Convert the extracted `.mmd` files to images using:
   - [Mermaid Live Editor](https://mermaid.live/)
   - The Mermaid CLI tool (`mmdc`)
   - Any other Mermaid rendering tool

3. Replace the image placeholders in the PDF-ready file with the actual images

## Final Steps

1. Review the generated PDF for formatting issues
2. Ensure the table of contents is properly displayed on the first page
3. Check that all diagrams are correctly rendered
