#!/usr/bin/env python3
import re
import os

def extract_mermaid_diagrams(markdown_file):
    with open(markdown_file, 'r') as f:
        content = f.read()
    
    # Find all Mermaid diagrams
    mermaid_pattern = r'```mermaid\n(.*?)\n```'
    diagrams = re.findall(mermaid_pattern, content, re.DOTALL)
    
    # Create a directory for the diagrams if it doesn't exist
    os.makedirs('diagrams', exist_ok=True)
    
    # Save each diagram to a file
    for i, diagram in enumerate(diagrams):
        diagram_type = diagram.strip().split('\n')[0].split(' ')[0]
        filename = f"diagrams/{diagram_type}_{i+1}.mmd"
        with open(filename, 'w') as f:
            f.write(diagram)
        print(f"Saved diagram {i+1} to {filename}")

if __name__ == "__main__":
    extract_mermaid_diagrams("31_technical_documentation_for_soc.md")
