# Lines of Code Summary for Visitor Management System

This document provides a summary of the lines of code (LOC) for each module in the Visitor Management System.

## Module Breakdown

| Module | Python LOC | XML LOC | Total LOC |
|--------|------------|---------|-----------|
| ams_base | 2,015 | 523 | 2,538 |
| ams | 1,612 | 1,459 | 3,071 |
| ams_bs | 8,937 | 1,340 | 10,277 |
| ams_vm | 1,403 | 2,347 | 3,750 |
| ams_mep | 362 | 660 | 1,022 |
| **Total** | **14,329** | **6,329** | **20,658** |

## Analysis

- **ams_base**: Foundation module with abstract models (12.3% of total code)
- **ams**: Core Access Management System (14.9% of total code)
- **ams_bs**: BioStar integration module (49.7% of total code)
- **ams_vm**: Visitor Management module (18.2% of total code)
- **ams_mep**: Ministry customizations (4.9% of total code)

## Notes

- Python files include models, controllers, and business logic
- XML files include views, data files, and security definitions
- The BioStar integration module (ams_bs) contains the largest portion of code due to the complexity of integrating with the external access control system
- The Visitor Management module (ams_vm) has more XML code than Python code, indicating a focus on user interface and data definitions

This information can be included in the technical documentation for the SOC review to provide an overview of the system's size and complexity.
