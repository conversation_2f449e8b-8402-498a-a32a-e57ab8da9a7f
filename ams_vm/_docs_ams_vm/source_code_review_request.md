# Source Code Review Request

## Application Information

**Application Name:** Visitor Management System (VMS)

**Repository Name:** addons_ams

**Source Code Review Type:** Comprehensive SCA

**SOC Team:** Ministry of Economy and Planning Security Operations Center

## Application Description

The Visitor Management System is a custom Odoo-based application that manages government buildings' visitor registration, access control, and tracking. It integrates with BioStar 2 for biometric access control and LDAP for user authentication and includes QR code-based visitor identification. The system consists of multiple interconnected modules that handle different aspects of visitor management and access control.

## Technical Contact

**Name:** Abdelrazek Badr  
**Role:** Lead Developer  
**Email:** <EMAIL>  
**Phone:** +966 59 277 2339

## Documentation

- Technical architecture diagrams in the documentation folder
- API documentation for BioStar 2 integration
- User manuals for the Visitor Management System

## Code Statistics

**Total:** 20,658 lines of code
- Python: 14,329 lines
- XML: 6,329 lines

**Module breakdown:**
- ams_base: 2,538 lines
- ams: 3,071 lines
- ams_bs: 10,277 lines
- ams_vm: 3,750 lines
- ams_mep: 1,022 lines
