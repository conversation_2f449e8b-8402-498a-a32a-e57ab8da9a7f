<odoo>
    <data noupdate="1">
        <!-- Tomorrow Filter -->
        <record id="tomorrow_filter" model="ir.filters">
            <field name="name">Tomorrow</field>
            <field name="model_id">ams_vm.invitation</field>
            <field name="domain">[("start_date", "&gt;=", (context_today() + relativedelta(days = 1)).strftime("%Y-%m-%d 00:00:00")),
                ("start_date", "&lt;=", (context_today() + relativedelta(days = 1)).strftime("%Y-%m-%d 23:59:59"))]</field>
        </record>

        <!-- Next 7 Days Filter -->
        <record id="next_seven_days_filter" model="ir.filters">
            <field name="name">Next 7 Days</field>
            <field name="model_id">ams_vm.invitation</field>
            <field name="domain">[("start_date", "&gt;=", context_today().strftime("%Y-%m-%d 00:00:00")),
                ("start_date", "&lt;=", (context_today() + relativedelta(days = 7)).strftime("%Y-%m-%d 23:59:59"))]</field>
        </record>

        <!-- Next 14 Days Filter -->
        <record id="next_fourteen_days_filter" model="ir.filters">
            <field name="name">Next 14 Days</field>
            <field name="model_id">ams_vm.invitation</field>
            <field name="domain">[("start_date", "&gt;=", context_today().strftime("%Y-%m-%d 00:00:00")),
                ("start_date", "&lt;=", (context_today() + relativedelta(days = 14)).strftime("%Y-%m-%d 23:59:59"))]</field>
        </record>

        <!-- Next 30 Days Filter -->
        <record id="next_thirty_days_filter" model="ir.filters">
            <field name="name">Next 30 Days</field>
            <field name="model_id">ams_vm.invitation</field>
            <field name="domain">[("start_date", "&gt;=", context_today().strftime("%Y-%m-%d 00:00:00")),
                ("start_date", "&lt;=", (context_today() + relativedelta(days = 30)).strftime("%Y-%m-%d 23:59:59"))]</field>
        </record>

        <!-- Yesterday Filter -->
        <record id="yesterday_filter" model="ir.filters">
            <field name="name">Yesterday</field>
            <field name="model_id">ams_vm.invitation</field>
            <field name="domain">[("start_date", "&gt;=", (context_today() - relativedelta(days = 1)).strftime("%Y-%m-%d 00:00:00")),
                ("start_date", "&lt;=", (context_today() - relativedelta(days = 1)).strftime("%Y-%m-%d 23:59:59"))]</field>
        </record>

        <!-- Last 7 Days Filter -->
        <record id="last_seven_days_filter" model="ir.filters">
            <field name="name">Last 7 Days</field>
            <field name="model_id">ams_vm.invitation</field>
            <field name="domain">[("start_date", "&gt;=", (context_today() - relativedelta(days = 7)).strftime("%Y-%m-%d 00:00:00")),
                ("start_date", "&lt;=", context_today().strftime("%Y-%m-%d 23:59:59"))]</field>
        </record>

        <!-- Last 14 Days Filter -->
        <record id="last_fourteen_days_filter" model="ir.filters">
            <field name="name">Last 14 Days</field>
            <field name="model_id">ams_vm.invitation</field>
            <field name="domain">[("start_date", "&gt;=", (context_today() - relativedelta(days = 14)).strftime("%Y-%m-%d 00:00:00")),
                ("start_date", "&lt;=", context_today().strftime("%Y-%m-%d 23:59:59"))]</field>
        </record>

        <!-- Last 30 Days Filter -->
        <record id="last_thirty_days_filter" model="ir.filters">
            <field name="name">Last 30 Days</field>
            <field name="model_id">ams_vm.invitation</field>
            <field name="domain">[("start_date", "&gt;=", (context_today() - relativedelta(days = 30)).strftime("%Y-%m-%d 00:00:00")),
                ("start_date", "&lt;=", context_today().strftime("%Y-%m-%d 23:59:59"))]</field>
        </record>
    </data>
</odoo>