<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Email Template for Invitation State Change Notifications -->
        <record id="mail_template_invitation_state_change_notification" model="mail.template">
            <field name="name">Visitors System - Invitation State Change Notification</field>
            <field name="model_id" ref="model_ams_vm_invitation"/>
            <field name="subject">Visitors System: Invitation Status Update - {{ object.name }}</field>
            <field name="email_from">{{ object.company_id.email or user.email or 'noreply@localhost' }}</field>
            <field name="email_to">{{ object.create_uid.email }}</field>
            <field name="lang">{{ object.create_uid.lang }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
                <html>
                    <body>
                        <div style="font-family: 'Cairo', sans-serif; background-color: #f3f4f6; color: #1a1a1a; line-height: 1.8; margin: 0; padding: 0;">
                            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);">
                                <div style="background-color: #163c4a; background: linear-gradient(135deg, #163c4a 0%, #163c4a 100%); padding: 40px 20px; text-align: center;">
                                    <h1 style="color: #ffffff; font-size: 28px; font-weight: 700; margin: 0;">تحديث حالة الطلب - Request Status Update</h1>
                                </div>
                                
                                <!-- Main Content -->
                                <div style="padding: 40px 30px; direction: ltr; text-align: left;">
                                    <h2 style="font-size: 24px; font-weight: 600; color: #163c4a; margin-bottom: 20px;">Dear <t t-esc="object.create_uid.name"/>,</h2>
                                    <p style="font-size: 16px; margin-bottom: 20px;">Your invitation request has been updated. Please find the details below:</p>
                                    
                                    <div style="background-color: #f9fafb; border-radius: 12px; padding: 25px; margin: 30px 0; border: 1px solid #dfe3e8;">
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Request Number:</strong> <t t-esc="object.name"/>
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Subject:</strong> <t t-esc="object.subject or 'N/A'"/>
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Main Visitor Name:</strong> <t t-if="object.visitor_id" t-esc="object.visitor_id.name"/> 
                                             
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Visit Date:</strong> <t t-esc="object._localized_start_date or 'N/A'"/>
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Building:</strong> <t t-esc="object.building_id.name or 'N/A'"/>
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Meeting Room:</strong> <t t-esc="object.room_name or 'N/A'"/>
                                         </div>
                                        <div style="margin-bottom: 0px;">
                                            <strong style="color: #163c4a;">• Current Status:</strong> 
                                            <t t-if="object.state == 'approved'">
                                                <span style="background-color: #d4edda; color: #155724; padding: 4px 12px; border-radius: 20px; font-weight: bold; font-size: 14px;">✓ Approved</span>
                                            </t>
                                            <t t-elif="object.state == 'confirmed'">
                                                <span style="background-color: #d1ecf1; color: #0c5460; padding: 4px 12px; border-radius: 20px; font-weight: bold; font-size: 14px;">✓ Confirmed</span>
                                            </t>
                                            <t t-elif="object.state == 'rejected'">
                                                <span style="background-color: #f8d7da; color: #721c24; padding: 4px 12px; border-radius: 20px; font-weight: bold; font-size: 14px;">✗ Rejected</span>
                                            </t>
                                            <t t-elif="object.state == 'pending'">
                                                <span style="background-color: #fff3cd; color: #856404; padding: 4px 12px; border-radius: 20px; font-weight: bold; font-size: 14px;">⏳ Pending</span>
                                            </t>
                                            <t t-else="">
                                                <span style="background-color: #e2e3e5; color: #6c757d; padding: 4px 12px; border-radius: 20px; font-weight: bold; font-size: 14px;"><t t-esc="object.state.title()"/></span>
                                            </t>
                                        </div>
                                    </div>
                                    
                                    <t t-if="object.state == 'approved'">
                                        <div style="background-color: #e7f4ed; padding: 20px; border-left: 6px solid #163c4a; border-radius: 8px; margin: 20px 0;">
                                            <p style="font-size: 16px; margin: 0;"><strong>📋 Next Steps:</strong></p>
                                            <ul style="list-style: disc inside; padding: 0; margin-top: 10px;">
                                                <li style="font-size: 14px;">Your invitation request has been approved.</li>
                                                <li style="font-size: 14px;">Please wait for security confirmation when your visitor arrives.</li>
                                                <li style="font-size: 14px;">Ensure your visitor brings valid identification documents.</li>
                                            </ul>
                                        </div>
                                    </t>
                                    <t t-elif="object.state == 'confirmed'">
                                        <div style="background-color: #e7f4ed; padding: 20px; border-left: 6px solid #163c4a; border-radius: 8px; margin: 20px 0;">
                                            <p style="font-size: 16px; margin: 0;"><strong>✅ Visit Confirmed:</strong></p>
                                            <ul style="list-style: disc inside; padding: 0; margin-top: 10px;">
                                                <li style="font-size: 14px;">Your visitor has been successfully checked in.</li>
                                                <li style="font-size: 14px;">The visit is now active and in progress.</li>
                                                <li style="font-size: 14px;">Please ensure to escort your visitor as needed.</li>
                                            </ul>
                                        </div>
                                    </t>
                                    <t t-elif="object.state == 'rejected'">
                                        <div style="background-color: #f8d7da; padding: 20px; border-left: 6px solid #dc3545; border-radius: 8px; margin: 20px 0;">
                                            <p style="font-size: 16px; margin: 0;"><strong>❌ Request Rejected:</strong></p>
                                            <ul style="list-style: disc inside; padding: 0; margin-top: 10px;">
                                                <li style="font-size: 14px;">Your invitation request has been rejected.</li>
                                                <li style="font-size: 14px;">Please contact the system administrator for more information.</li>
                                                <li style="font-size: 14px;">You may submit a new request with updated information if needed.</li>
                                            </ul>
                                        </div>
                                    </t>
                                    <t t-elif="object.state == 'pending'">
                                        <div style="background-color: #fff3cd; padding: 20px; border-left: 6px solid #ffc107; border-radius: 8px; margin: 20px 0;">
                                            <p style="font-size: 16px; margin: 0;"><strong>⏳ Pending Review:</strong></p>
                                            <ul style="list-style: disc inside; padding: 0; margin-top: 10px;">
                                                <li style="font-size: 14px;">Your invitation request is currently under review.</li>
                                                <li style="font-size: 14px;">You will be notified once a decision has been made.</li>
                                                <li style="font-size: 14px;">Please ensure all required information has been provided.</li>
                                            </ul>
                                        </div>
                                    </t>
                                    
                                    <!-- Action Button -->
                                    <div style="text-align: center; margin: 30px 0;">
                                        <a t-attf-href="/web#id={{object.id}}&amp;model=ams_vm.invitation&amp;view_type=form" 
                                           style="background-color: #163c4a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; font-size: 16px;">
                                            📋 View Request Details
                                        </a>
                                    </div>
                                    
                                    <p style="font-size: 16px; margin-top: 30px;">If you have any questions or need assistance, please don't hesitate to contact the system administrator.</p>
                                    
                                    <p style="margin: 20px 0 0 0; font-size: 16px;">
                                        Best regards,<br/>
                                        <strong>"Ministry of Economy and Planning"</strong>
                                    </p>
                                </div>
                                
                                <div style="background-color: #f9fafb; padding: 30px; text-align: center; border-top: 1px solid #dfe3e8;">
                                    <div style="margin-top: 20px;">
                                        <a href="https://mep.gov.sa/ar" style="color: #163c4a; text-decoration: none; font-size: 14px; font-weight: 600; margin: 0 15px;">🌐 موقعنا الإلكتروني - Our Website</a>
                                        <br/><br/>
                                        <span style="color: #163c4a; font-size: 14px; font-weight: 600;">📍 الرياض، المملكة العربية السعودية - Riyadh, Saudi Arabia</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </body>
                </html>
            </field>
        </record>
        
        <!-- Email Template for Manager Approval Request -->
        <record id="mail_template_manager_approval_request" model="mail.template">
            <field name="name">Visitors System - Approval Request</field>
            <field name="model_id" ref="model_ams_vm_invitation"/>
            <field name="subject">Visitors System: Approval Required - {{ object.name }}</field>
            <field name="email_from">{{ object.company_id.email or user.email or 'noreply@localhost' }}</field>
            <field name="email_to">{{ object.approval_uid.email }}</field>
            <field name="lang">{{ object.approval_uid.lang }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
                <html>
                    <body>
                        <div style="font-family: 'Cairo', sans-serif; background-color: #f3f4f6; color: #1a1a1a; line-height: 1.8; margin: 0; padding: 0;">
                            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);">
                                <div style="background-color: #163c4a; background: linear-gradient(135deg, #163c4a 0%, #163c4a 100%); padding: 40px 20px; text-align: center;">
                                    <h1 style="color: #ffffff; font-size: 28px; font-weight: 700; margin: 0;">طلب موافقة - Approval Required</h1>
                                </div>
                                
                                <!-- Main Content -->
                                <div style="padding: 40px 30px; direction: ltr; text-align: left;">
                                    <h2 style="font-size: 24px; font-weight: 600; color: #163c4a; margin-bottom: 20px;">Dear <t t-esc="object.approval_uid.name"/>,</h2>
                                    <p style="font-size: 16px; margin-bottom: 20px;">A new visitor invitation request requires your approval. Please review the details below and take appropriate action:</p>
                                    
                                    <div style="background-color: #f9fafb; border-radius: 12px; padding: 25px; margin: 30px 0; border: 1px solid #dfe3e8;">
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Request Number:</strong> <t t-esc="object.name"/>
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Requested By:</strong> <t t-esc="object.create_uid.name"/>
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Subject:</strong> <t t-esc="object.subject or 'N/A'"/>
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Main Visitor Name:</strong> <t t-if="object.visitor_id" t-esc="object.visitor_id.name"/> 
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Visit Date:</strong> <t t-esc="object._localized_start_date or 'N/A'"/>
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Building:</strong> <t t-esc="object.building_id.name or 'N/A'"/>
                                         </div>
                                         <div style="margin-bottom: 15px;">
                                             <strong style="color: #163c4a;">• Meeting Room:</strong> <t t-esc="object.room_name or 'N/A'"/>
                                         </div>

                                        <div style="margin-bottom: 0px;">
                                            <strong style="color: #163c4a;">• Current Status:</strong> 
                                            <span style="background-color: #fff3cd; color: #856404; padding: 4px 12px; border-radius: 20px; font-weight: bold; font-size: 14px;">⏳ Need Approval</span>
                                        </div>
                                    </div>
                                    
                                    <div style="background-color: #fff3cd; padding: 20px; border-left: 6px solid #163c4a; border-radius: 8px; margin: 20px 0;">
                                        <p style="font-size: 16px; margin: 0;"><strong>📋 Action Required:</strong></p>
                                        <ul style="list-style: disc inside; padding: 0; margin-top: 10px;">
                                            <li style="font-size: 14px;">Please review the visitor invitation request details above.</li>
                                            <li style="font-size: 14px;">Click the button below to access the system and approve or reject the request.</li>
                                            <li style="font-size: 14px;">Ensure all security protocols are followed for visitor access.</li>
                                        </ul>
                                    </div>
                                    
                                    <!-- Action Buttons -->
                                    <div style="text-align: center; margin: 30px 0;">
                                        <a t-attf-href="/web#id={{object.id}}&amp;model=ams_vm.invitation&amp;view_type=form" 
                                           style="background-color: #163c4a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; font-size: 16px; margin: 0 10px;">
                                            ✅ Review &amp; Approve
                                        </a>
                                    </div>
                                    
                                    <p style="font-size: 16px; margin-top: 30px;">If you have any questions about this request, please contact the requester directly or the system administrator.</p>
                                    
                                    <p style="margin: 20px 0 0 0; font-size: 16px;">
                                        Best regards,<br/>
                                        <strong>"Ministry of Economy and Planning"</strong>
                                    </p>
                                </div>
                                
                                <div style="background-color: #f9fafb; padding: 30px; text-align: center; border-top: 1px solid #dfe3e8;">
                                    <div style="margin-top: 20px;">
                                        <a href="https://mep.gov.sa/ar" style="color: #163c4a; text-decoration: none; font-size: 14px; font-weight: 600; margin: 0 15px;">🌐 موقعنا الإلكتروني - Our Website</a>
                                        <br/><br/>
                                        <span style="color: #163c4a; font-size: 14px; font-weight: 600;">📍 الرياض، المملكة العربية السعودية - Riyadh, Saudi Arabia</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </body>
                </html>
            </field>
        </record>
        
    </data>
</odoo>