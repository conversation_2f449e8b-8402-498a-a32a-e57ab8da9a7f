# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ams_vm
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-11 04:04+0000\n"
"PO-Revision-Date: 2025-03-11 23:45+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n"
"%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"X-Generator: Poedit 3.5\n"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__event_count
msgid "# Events"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__meeting_count
msgid "# Meetings"
msgstr ""

#. module: ams_vm
#: model:mail.template,subject:ams_vm.ams_vm_default_email_template
msgid "({{ object.name }}) - {{ object.invitation_id.subject }}"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "/&gt;"
msgstr ""

#. module: ams_vm
#: model:mail.template,body_html:ams_vm.ams_vm_default_email_template
msgid ""
"<html>\n"
"                    <body>\n"
"                        <div style=\"font-family: 'Cairo', sans-serif; direction: rtl; "
"background-color: #f3f4f6; color: #1a1a1a; line-height: 1.8; margin: 0; padding: 0;\">\n"
"                            <div style=\"max-width: 600px; margin: 0 auto; background-"
"color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 6px 10px rgba(0, 0, "
"0, 0.15);\">\n"
"                                <div style=\"background: linear-gradient(135deg, #004F23 "
"0%, #007A3B 100%); padding: 40px 20px; text-align: center;\">\n"
"                                    <h1 style=\"color: #ffffff; font-size: 28px; font-"
"weight: 700; margin: 0;\">تصريح زيارة</h1>\n"
"                                </div>\n"
"                                <div style=\"padding: 40px 30px;\">\n"
"                                    <h2 style=\"font-size: 24px; font-weight: 600; color: "
"#004F23; margin-bottom: 20px;\">مرحباً <t t-out=\"object.visitor_id.partner_id.name or "
"''\"></t>،</h2>\n"
"                                    <p style=\"font-size: 16px;\">يسرنا أن نقدم لكم "
"تفاصيل زيارتكم كما يلي:</p>\n"
"                                    <div style=\"background-color: #f9fafb; border-"
"radius: 12px; padding: 25px; margin: 30px 0; border: 1px solid #dfe3e8;\">\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">رقم التصريح:"
"</strong> <t t-esc=\"object.name\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">قاعة "
"الاجتماع:</strong> <t t-esc=\"object.invitation_id.room_name\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">الدور:</"
"strong> <t t-esc=\"object.invitation_id.location_address\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">وقت الزيارة:"
"</strong> <t t-esc=\"object.invitation_id._localized_start_date\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">مدة الزيارة:"
"</strong> <t t-esc=\"object.duration_display\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">وقت الخروج:"
"</strong> <t t-esc=\"object.invitation_id._localized_end_date\"></t>\n"
"                                        </div>\n"
"                                    </div>\n"
"                                    <p style=\"font-size: 16px; margin-top: 30px;"
"\">نشكركم على اهتمامكم. لا تترددوا في التواصل معنا إذا كانت لديكم أي استفسارات.</p>\n"
"                                    <p style=\"font-size: 16px; margin-top: 20px;\">مع "
"أطيب التحيات،<br><strong>إدارة الأمن والسلامة</strong></p>\n"
"                                </div>\n"
"                                <div style=\"background-color: #f9fafb; padding: 30px; "
"text-align: center; border-top: 1px solid #dfe3e8;\">\n"
"                                    <a href=\"https://companywebsite.com\" style=\"color: "
"#004F23; text-decoration: none; font-size: 14px; font-weight: 600; margin: 0 10px;"
"\">زيارة موقعنا</a>\n"
"                                    <a href=\"https://companywebsite.com/contact\" style="
"\"color: #004F23; text-decoration: none; font-size: 14px; font-weight: 600; margin: 0 "
"10px;\">تواصل معنا</a>\n"
"                                    <p style=\"color: #6c757d; font-size: 12px; margin-"
"top: 20px;\">جميع الحقوق محفوظة ©2024</p>\n"
"                                </div>\n"
"                            </div>\n"
"                        </div>\n"
"                    </body>\n"
"                </html>\n"
"            "
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.view_invitation_kanban
msgid "<i class=\"fa fa-bookmark mx-1\" title=\"Subject\"/>"
msgstr "<i class=\"fa fa-bookmark mx-1\" title=\"الموضوع\"/>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-calendar  mx-2\" title=\"Last Response Date\"/>"
msgstr "<i class=\"fa fa-calendar  mx-2\" title=\"تاريخ الرد السابق\"/>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-calendar  mx-2\" title=\"Request Date\"/>"
msgstr "<i class=\"fa fa-calendar  mx-2\" title=\"تاريخ الطلب\"/>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.view_invitation_kanban
msgid "<i class=\"fa fa-calendar mx-1\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar mx-1\" title=\"التاريخ\"/>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-check text-primary me-2\" title=\"Response By\"/>"
msgstr "<i class=\"fa fa-check text-primary me-2\" title=\"الرد بواسطة\"/>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-envelope text-primary mx-2\" title=\"Email Status\"/>"
msgstr "<i class=\"fa fa-envelope text-primary mx-2\" title=\"حالة البريد\"/>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-hashtag text-primary me-2\" title=\"Request Number\"/>"
msgstr "<i class=\"fa fa-hashtag text-primary me-2\" title=\"رقم الطلب\"/>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid ""
"<i class=\"fa fa-id-card-o\" style=\"margin-left: 10px;\"/>\n"
"                                    تصريح زيارة"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-tasks text-primary me-2\" title=\"Status\"/>"
msgstr "<i class=\"fa fa-tasks text-primary me-2\" title=\"الحالة\"/>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.view_invitation_kanban
msgid "<i class=\"fa fa-user mx-1\" title=\"Visitor\"/>"
msgstr "<i class=\"fa fa-user mx-1\" title=\"الزائر\"/>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-user text-primary me-2\" title=\"Request By\"/>"
msgstr "<i class=\"fa fa-user text-primary me-2\" title=\"طلب بواسطة\"/>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">الدور:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">رقم التصريح:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">قاعة الاجتماع:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">مدة الزيارة:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">وقت الخروج:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">وقت الزيارة:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">محذوف</span>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid ""
"<strong><span><i class=\"fa fa-exclamation-triangle\" title=\"Info\"/></span></strong> "
"This request\n"
"                        need to be approved from manager or supervisor <br/> because "
"request time outside allowed time\n"
"                        or approval forced required from manager !!\n"
"                        <span invisible=\"not is_current_user_requester or approval_uid"
"\"><br/> Please select\n"
"                            your <strong>Approval Manager</strong> below and click on "
"button Request\n"
"                            Approval.</span>"
msgstr ""
"<strong><span><i class=\"fa fa-exclamation-triangle\" title=\"Info\"/></span></strong> "
"هذا الطلب\n"
"                        يجب ان يتم الموافقة من قبل المدير او المشرف\n"
"                        <br/> لان وقت الطلب خارج من الوقت المسموح به\n"
"                        او الموافقة مطلوبة من قبل المدير !!\n"
"                        <span invisible=\"not is_current_user_requester or approval_uid"
"\"><br/> يرجى تحديد\n"
"                            <strong>مدير الموافقة</strong> الخاص بك\n"
"                            وضغط على زر طلب الموافقة.</span>"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<strong>ملاحظات هامة:</strong>"
msgstr ""

#. module: ams_vm
#: model:mail.template,name:ams_vm.ams_vm_default_email_template
msgid "AMS VM Default Email Template"
msgstr "النموذج الافتراضي لقالب البريد الالكترونى"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__api_type
msgid "API Type"
msgstr "نوع API"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_needaction
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_needaction
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_needaction
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_needaction
msgid "Action Needed"
msgstr "التحكم المطلوب"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activate
msgid "Activate"
msgstr "تفعيل"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__active
msgid "Active"
msgstr "مفعل"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__active_lang_count
msgid "Active Lang Count"
msgstr "عدد اللغات المفعلة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_ids
msgid "Activities"
msgstr "النشاطات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "تسمية النشاط الاستثناء"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_type_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_type_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_type_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_type_icon
msgid "Activity Type Icon"
msgstr "صورة نوع النشاط"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__additional_info
msgid "Additional info"
msgstr "معلومات اضافية"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__type
msgid "Address Type"
msgstr "نوع العنوان"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__dashboard_up_coming_domain__all
msgid "All"
msgstr "الكل"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__lang
msgid ""
"All the emails and documents sent to this contact will be translated in this language."
msgstr "كل البريدات والمستندات المرسلة لهذا الاتصال سيتم ترجمتها في هذه اللغة."

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__required_manager_approval
msgid "Always needs manager approval"
msgstr "يحتاج دائما لموافقة المدير"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__approval_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__approval_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__approval_uid
msgid "Approval Manager"
msgstr "مدير الموافقة"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Approve"
msgstr "موافقة"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__approved
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__approved
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__approved
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__approved
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Approved"
msgstr "تم الموافقة"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Approved Invitations"
msgstr "الدعوات الموافق عليها"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_attachment_count
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_attachment_count
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_attachment_count
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__auto_generate_qr_code
msgid "Auto Generate QR Code"
msgstr "توليد QR كود بشكل تلقائي"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__auto_generate_qr_code
msgid "Auto generate QR code Per Request"
msgstr "توليد QR كود بشكل تلقائي لكل طلب"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__avatar_1920
msgid "Avatar"
msgstr "الصورة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__avatar_1024
msgid "Avatar 1024"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__avatar_128
msgid "Avatar 128"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__avatar_256
msgid "Avatar 256"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__avatar_512
msgid "Avatar 512"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__badge_report_id
msgid "Badge Report Attachment"
msgstr "مرفق تقرير البطاقة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__bank_ids
msgid "Banks"
msgstr "البنوك"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__barcode
msgid "Barcode"
msgstr "الباركود"

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_base_request
msgid "Base Visitor Request"
msgstr "طلب زيارة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__is_blacklisted
msgid "Blacklist"
msgstr "قائمة سوداء"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "الهاتف الموجود في القائمة السوداء هو الجوال"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "الهاتف الموجود في القائمة السوداء هو الهاتف"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_bounce
msgid "Bounce"
msgstr "الرسائل المرسلة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__building_id
msgid "Building"
msgstr "المبنى"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__building_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__building_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__building_id
msgid "Building"
msgstr "المبنى"



#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__location_address
msgid "Building-Floor"
msgstr "مبنى-الطابق"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Cancel"
msgstr "إلغاء"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__cancelled
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__cancelled
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__cancelled
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__cancelled
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Cancelled"
msgstr "ملغى"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__card_id
msgid "Card"
msgstr "البطاقة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__channel_ids
msgid "Channels"
msgstr "القنوات"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr "تحقق من إذا كان الاتصال هو شركة ، قد لا يكون شخص"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__employee
msgid "Check this box if this contact is an Employee."
msgstr "تحقق من هذه الخانة إذا كانت جهة الاتصال هذه موظف."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__city
msgid "City"
msgstr "المدينة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__color
msgid "Color"
msgstr "اللون"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__commit
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__commit
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__commit
msgid "Comments"
msgstr "التعليقات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__commercial_partner_id
msgid "Commercial Entity"
msgstr "الكائن التجاري"

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_res_company
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__company_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__company_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__company_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__company_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__company_id
#: model:ir.ui.menu,name:ams_vm.company_menu
msgid "Company"
msgstr "الشركات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__company_registry
msgid "Company ID"
msgstr "الرقم التعريفي للشركة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__company_name
msgid "Company Name"
msgstr "اسم الشركة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__commercial_company_name
msgid "Company Name Entity"
msgstr "اسم الكائن التجاري"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__company_type
msgid "Company Type"
msgstr "نوع الشركة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__partner_gid
msgid "Company database ID"
msgstr "رقم قاعدة البيانات للشركة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__contact_address
msgid "Complete Address"
msgstr "العنوان بالكامل"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__complete_name
msgid "Complete Name"
msgstr "الاسم بالكامل"

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.config_menu
msgid "Configurations"
msgstr "الإعدادات"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Confirm"
msgstr "تأكيد"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Confirm Visit"
msgstr "تأكيد الزيارة"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Duration (Days)"
msgstr " المدة (بالايام)"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Duration (Hours)"
msgstr " المدة (بالساعات)"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__confirmed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__confirmed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__confirmed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__confirmed
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Confirmed"
msgstr "تم التأكيد"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Confirmed Invitations"
msgstr "الدعوات المؤكدة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__child_ids
msgid "Contact"
msgstr "الاتصال"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_form
msgid "Contact Information"
msgstr "معلومات جهة الاتصال"

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.res_partner_menu
msgid "Contacts"
msgstr "الاتصالات"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__last_dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__last_dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__last_dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__last_dept_depth
msgid "Contain hierarchy  depth of last department assigned to employee"
msgstr "يحتوي على التسلسل الهرمي للقسم الأخير المعين للموظف"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__dept_depth
msgid "Contain hierarchy of department depth when create record"
msgstr "يحتوي على التسلسل الهرمي للقسم عند انشاء سجل"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "عدد رسائل البريد المرسلة لهذا الاتصال"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__country_id
msgid "Country"
msgstr "الدولة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__country_code
msgid "Country Code"
msgstr "كود الدولة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__create_employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__create_employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__create_employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__create_employee_id
msgid "Create By Employee"
msgstr "تم الانشاء بواسطة الموظف"

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.visit_action
msgid "Create a visit record"
msgstr "انشاء سجل زيارة"

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.event_invitation_action
msgid "Create an event invitation"
msgstr "إنشاء دعوة للحدث"

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.visit_action
msgid "Create visit records"
msgstr "انشاء سجلات زيارة"

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.visitor_action
msgid "Create visitor"
msgstr "انشاء زائر"

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.invitation_action
msgid "Create your first invitation!"
msgstr "إنشاء الدعوة الأولى!"

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.visitor_action
msgid "Create your first visitor!"
msgstr "إنشاء الزائر الأول الخاص بك!"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__create_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__create_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__create_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__create_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__create_uid
msgid "Created by"
msgstr "انشئ بواسطة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__create_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__create_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__create_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__create_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__create_date
msgid "Created on"
msgstr "انشئ في"

#. module: ams_vm
#: model:ir.actions.client,name:ams_vm.action_client_dashboard
#: model:ir.ui.menu,name:ams_vm.ams_dashboard_menu
msgid "Dashboard"
msgstr "لوحة التحكم"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__dashboard_up_coming_domain
msgid "Dashboard Upcoming Domain"
msgstr "لوحة التحكم القادمة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__date
msgid "Date"
msgstr "التاريخ"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__department_id
msgid "Department"
msgstr "القسم"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__dept_depth
msgid "Department Depth"
msgstr "عمق القسم"

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.menu_hr_department
msgid "Departments"
msgstr "الاقسام"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__display_name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__display_name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__display_name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__display_name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__display_name
msgid "Display Name"
msgstr "الاسم"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__duration
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__duration
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__duration
msgid "Duration"
msgstr "المدة"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__duration
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__duration
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__duration
msgid "Duration in hours"
msgstr "المدة بالساعات"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__partner_share
msgid ""
"Either customer (not a user), either shared user. Indicated the current partner is a "
"customer without access or with a limited access created for sharing data."
msgstr ""
"العميل (ليس مستخدم)، أو مستخدم مشترك. يعرض المستخدم الحالي هو عميل بدون دخول أو مع دخول "
"محدد للمشاركة بالبيانات."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__email
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__email_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__email_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__email_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__email_state
msgid "Email State"
msgstr "حالة البريد الإلكتروني"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__email_template_id
msgid "Email Template"
msgstr "قالب البريد الإلكتروني"

#. module: ams_vm
#: model:ir.model.constraint,message:ams_vm.constraint_ams_vm_visitor_email_unique
msgid "Email must be unique!"
msgstr "يجب أن يكون البريد الإلكتروني فريد!"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/invitation_visitor.py:0
#: code:addons/ams_vm/models/invitation_visitor.py:0
msgid "Email template not found"
msgstr "لم يتم العثور على قالب البريد الألكتروني"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__employee
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__employee_id
msgid "Employee"
msgstr "الموظف"

#. module: ams_vm
#: model:res.groups,name:ams_vm.group_employee_request
msgid "Employee Request"
msgstr "طلب الموظف"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__department_id
msgid "Employee department on record creation"
msgstr "قسم الموظف عند إنشاء السجل"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__employee_ids
#: model:ir.ui.menu,name:ams_vm.menu_hr_employee
msgid "Employees"
msgstr "الموظفين"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__employees_count
msgid "Employees Count"
msgstr "عدد الموظفين"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__end_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__end_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__end_date
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__enroll_number
msgid "Enroll Number"
msgstr "رقم التسجيل"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Event"
msgstr "الفعاليات"

#. module: ams_vm
#: model:ir.actions.act_window,name:ams_vm.event_invitation_action
msgid "Event Invitation"
msgstr "دعوة فعالية"

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.event_invitation_menu
msgid "Event Invitations"
msgstr "دعوات فعالية"

#. module: ams_vm
#: model:res.groups,name:ams_vm.group_event_manager
msgid "Event Manager"
msgstr "مدير فعالية"

#. module: ams_vm
#: model:res.groups,name:ams_vm.group_event_request
msgid "Event Request"
msgstr "طلب فعالية"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__email_state__failed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__email_state__failed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__email_state__failed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__email_state__failed
msgid "Failed"
msgstr "فشل"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and comparisons."
msgstr "حقل يستخدم لتخزين رقم الهاتف . يساعد على سرعة البحث والمقارنات."

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__status__finished
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__status__finished
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__status__finished
msgid "Finished"
msgstr "تم الانتهاء"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__first_name
msgid "First Name"
msgstr " الاسم الاول"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__middle_name
msgid "Middle Name"
msgstr "الاسم الأوسط"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_follower_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_follower_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_follower_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_partner_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_partner_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_partner_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء)"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__activity_type_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__activity_type_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__activity_type_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة فونت آويما . مثل fa-tasks"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr "تنسيق البريد الإلكتروني \"الاسم <البريد الإلكتروني@النطاق>\""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__email_formatted
msgid "Formatted Email"
msgstr "البريد الإلكتروني المنسق"

#. module: ams_vm
#: model:ir.actions.report,name:ams_vm.general_invitation_badge_report_action
msgid "General Invitation Badge"
msgstr "دعوة عامة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__partner_latitude
msgid "Geo Latitude"
msgstr "خط العرض الجغرافي"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__partner_longitude
msgid "Geo Longitude"
msgstr "طول الخريطة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__date_localization
msgid "Geolocation Date"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Group By"
msgstr ""

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.menu_hr_root
msgid "HR"
msgstr "الموارد البشرية"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__has_approval_access
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__has_approval_access
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__has_approval_access
msgid "Has Approval Access"
msgstr "لديه حق الوصول للموافقة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__has_message
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__has_message
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__has_message
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__has_message
msgid "Has Message"
msgstr "لديه رسالة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__host_work_phone
msgid "Host Work Extension"
msgstr "رقم تحويلة المستضيف"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__host_work_mobile
msgid "Host Work Mobile"
msgstr "جوال المستضيف"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__id
msgid "ID"
msgstr "ID"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__id_number
msgid "ID Number"
msgstr "رقم الهوية"

#. module: ams_vm
#: model:ir.model.constraint,message:ams_vm.constraint_ams_vm_visitor_unique_id_number
msgid "ID Number must be unique!"
msgstr "رقم الهوية يجب أن يكون فريدًا!"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__id_type_id
msgid "ID Type"
msgstr "نوع الهوية"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__im_status
msgid "IM Status"
msgstr "IM Status"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_exception_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_exception_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_exception_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_exception_icon
msgid "Icon"
msgstr "أيقونة"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__activity_exception_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__activity_exception_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__activity_exception_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icon to indicate an exception activity."

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__message_needaction
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__message_needaction
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__message_needaction
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_needaction
msgid "If checked, new messages require your attention."
msgstr "اذا تم تحديد هذا الحقل، سوف يحتاج الرسائل الجديدة إلى مراجعتك."

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__message_has_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__message_has_sms_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__message_has_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__message_has_sms_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__message_has_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__message_has_sms_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_has_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "اذا تم تحديد هذا الحقل، سوف يحتاج بعض الرسائل إلى خطأ في تسليمها."

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass mailing anymore, "
"from any list"
msgstr ""
"إذا كان عنوان البريد الإلكتروني مدرجًا في القائمة السوداء، فلن يتلقى جهة الاتصال رسائل "
"بريدية جماعية بعد الآن، من أي قائمة"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive mass mailing "
"sms anymore, from any list"
msgstr ""
"إذا كان رقم الهاتف المطهر موجودًا في القائمة السوداء، فلن يتلقى جهة الاتصال رسائل نصية "
"قصيرة جماعية بعد الآن، من أي قائمة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__image_1920
msgid "Image"
msgstr "صورة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__image_1024
msgid "Image 1024"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__image_128
msgid "Image 128"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__image_256
msgid "Image 256"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__image_512
msgid "Image 512"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__id_attachment
msgid "ID Attachment"
msgstr "مرفق الهوية"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps distinguish "
"which number is blacklisted             when there is both a mobile and phone field in a "
"model."
msgstr ""
"يشير إلى ما إذا كان رقم الهاتف المعقم الموجود في القائمة السوداء هو رقم جوال. يساعد في "
"التمييز بين الرقم الموجود في القائمة السوداء عندما يكون هناك حقل جوال وحقل هاتف في الطراز."

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps distinguish "
"which number is blacklisted             when there is both a mobile and phone field in a "
"model."
msgstr ""
"يشير إلى ما إذا كان رقم الهاتف المعقم الموجود في القائمة السوداء هو رقم هاتف. يساعد في "
"التمييز بين الرقم الموجود في القائمة السوداء عندما يكون هناك حقل جوال وحقل هاتف في الطراز."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__industry_id
msgid "Industry"
msgstr " صناعة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__contact_address_inline
msgid "Inlined Complete Address"
msgstr "العنوان الكامل المضمن"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/visitor.py:0 code:addons/ams_vm/models/visitor.py:0
msgid "Invalid email address. Please enter a valid email in the correct format."
msgstr "عنوان البريد الإلكتروني غير صالح. يرجى إدخال بريد إلكتروني صالح بالتنسيق الصحيح."

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_invitation
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__invitation_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__invitation_id
msgid "Invitation"
msgstr "دعوة"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_list
msgid "Invitation List"
msgstr "قائمة الدعوات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__invitation_qr_code
msgid "Invitation QR Code"
msgstr " رمز QR للدعوة"

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_invitation_visitor
msgid "Invitation Visitor Assignment"
msgstr "تعيين الزائر للدعوة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__invitation_visitor_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__invitation_visitor_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__invitation_visitor_ids
msgid "Invitation Visitors"
msgstr "دعوة الزائرين"

#. module: ams_vm
#: model:ir.actions.act_window,name:ams_vm.invitation_action
#: model:ir.actions.server,name:ams_vm.invitation_server_action
#: model:ir.ui.menu,name:ams_vm.invitation_menu
#: model:ir.ui.menu,name:ams_vm.single_invitation_top_menu
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_list
msgid "Invitations"
msgstr "الدعوات"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_calendar
msgid "Invitations Calendar"
msgstr "تقويم الدعوات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__is_current_user_requester
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__is_current_user_requester
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__is_current_user_requester
msgid "Is Current User Requester"
msgstr "هل المستخدم الحالي هو مقدم الطلب؟"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__is_event
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__is_event
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__is_event
msgid "Is Event"
msgstr " حدث"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_is_follower
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_is_follower
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_is_follower
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_is_follower
msgid "Is Follower"
msgstr "هل هو متابع؟"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__is_public
msgid "Is Public"
msgstr "هل هو عام؟"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__is_readonly
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__is_readonly
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__is_readonly
msgid "Is Readonly"
msgstr "هل هو فقط قراءة؟"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__is_company
msgid "Is a Company"
msgstr "هل هو شركة؟"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__function
msgid "Job Position"
msgstr "وظيفة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__lang
msgid "Language"
msgstr "اللغة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__last_department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__last_department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__last_department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__last_department_id
msgid "Last Department"
msgstr "القسم الأخير"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__last_dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__last_dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__last_dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__last_dept_depth
msgid "Last Department Depth"
msgstr "عمق القسم الأخير"

 #. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__last_name
msgid "Last Name"
msgstr " الاسم الاخير"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__write_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__write_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__write_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__write_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث من"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__write_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__write_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__write_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__write_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__last_department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__last_department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__last_department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__last_department_id
msgid "Last department assign to employee"
msgstr "    القسم الأخير المعين للموظف"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__access_groups_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__access_groups_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__location_address
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__access_groups_ids
msgid "Floor"
msgstr "الدور"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__visitor_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__visitor_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__visitor_id
msgid "Main Visitor"
msgstr "    الزائر الرئيسي"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__visitor_id_number
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__visitor_id_number
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__visitor_id_number
msgid "Main Visitor ID"
msgstr "رقم هوية الزائر الرئيسي"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__request_max_days
msgid "Maximum number of days for which request can be raised"
msgstr "    الحد الأقصى لعدد الأيام التي يمكن خلالها رفع الطلب"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__visit_max_hours
msgid "Maximum number of hours for which visit can be raised"
msgstr "الحد الأقصى لعدد الساعات التي يمكن زيادة الزيارة خلالها"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__meeting_ids
msgid "Meetings"
msgstr "الاجتماعات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_has_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_has_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_has_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_has_error
msgid "Message Delivery error"
msgstr "    خطا في تسليم الرسائل"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_ids
msgid "Messages"
msgstr "    الرسائل"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__visitor_mobile
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__mobile
msgid "Mobile"
msgstr "    الموبايل"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Multiple Days"
msgstr "أيام متعددة"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/base_visitor_request.py:0
#: code:addons/ams_vm/models/base_visitor_request.py:0
msgid ""
"Multiple Schedule Duration Exceeded: The schedule spans {duration} days, which exceeds "
"the maximum allowed {max_days} days."
msgstr ""
"تم تجاوز مدة الجدول الزمني المتعددة: يمتد الجدول الزمني لمدة {duration} يومًا، وهو ما "
"يتجاوز الحد الأقصى المسموح به {max_days} يومًا."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "    موعد النشاط الخاص بي"

#. module: ams_vm
#: model:ir.actions.server,name:ams_vm.event_invitation_server_action
msgid "My Event Invitations"
msgstr "    دعواتي للحدث"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_search
msgid "My Visitors"
msgstr "الزوار الخاصة بي"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__name
msgid "Name"
msgstr "    الاسم"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__nationality
msgid "Nationality"
msgstr "    الجنسية"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__need_approval
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__need_approval
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__need_approval
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__need_approval
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__need_approval
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__need_approval
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__need_approval
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Need Approval"
msgstr "تحتاج للموافقة"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Need Approval Invitations"
msgstr "    دعوات تحتاج للموافقة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "موعد النشاط القادم"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_summary
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_summary
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_summary
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_summary
msgid "Next Activity Summary"
msgstr "    ملخص النشاط القادم"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_type_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_type_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_type_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_type_id
msgid "Next Activity Type"
msgstr "    نوع النشاط القادم"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__dashboard_up_coming_domain__nextmonth
msgid "Next Month"
msgstr "    الشهر القادم"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__dashboard_up_coming_domain__nextweek
msgid "Next Week"
msgstr "    الاسبوع القادم"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search

msgid "No Show"
msgstr "عدم حضور الزيارات"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/invitation.py:0
#: code:addons/ams_vm/models/invitation.py:0

msgid "No visitors found for the selected invitations."
msgstr "لا يوجد زائر موجود للدعوات المحددة."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__email_normalized
msgid "Normalized Email"
msgstr "    البريد الالكتروني المعادل"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__comment
msgid "Notes"
msgstr "    ملاحظات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_needaction_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_needaction_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_needaction_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_needaction_counter
msgid "Number of Actions"
msgstr "    عدد الإجراءات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_has_error_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_has_error_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_has_error_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_has_error_counter
msgid "Number of errors"
msgstr "    عدد الأخطاء"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__message_needaction_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__message_needaction_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__message_needaction_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "    عدد الرسائل التي تتطلب الإجراء"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__message_has_error_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__message_has_error_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__message_has_error_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "    عدد الرسائل التي تحتوي على خطأ في التوصيل"

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.op_menu
msgid "Operations"
msgstr "    العمليات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__organization
msgid "Organization"
msgstr "اسم الجهة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__parent_name
msgid "Parent name"
msgstr "    اسم الوالد"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__same_company_registry_partner_id
msgid "Partner with same Company Registry"
msgstr "    الشركة المشابهة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__same_vat_partner_id
msgid "Partner with same Tax ID"
msgstr "    شريك بنفس رقم التعريف الضريبي"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__payment_token_count
msgid "Payment Token Count"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__payment_token_ids
msgid "Payment Tokens"
msgstr ""


#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__email_state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__email_state__pending

msgid "Pending"
msgstr "قيد الانتظار"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__pending

msgid "Draft"
msgstr "مسودة"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__status__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__status__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__status__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__status__pending
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Pending"
msgstr "مجدولة"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Draft Invitations"
msgstr "مسودة الدعوات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__visitor_phone
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__phone
msgid "Phone"
msgstr "    الهاتف"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "    الهاتف محظور"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__phone_mobile_search
msgid "Phone/Mobile"
msgstr "    الهاتف/الجوال"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/base_visitor_request.py:0
#: code:addons/ams_vm/models/base_visitor_request.py:0
msgid "Please select an approval manager."
msgstr "يرجى تحديد مدير الموافقة."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__property_product_pricelist
msgid "Pricelist"
msgstr ""

#. module: ams_vm
#: model:ir.actions.server,name:ams_vm.action_server_print_all_visitor_badges
msgid "Print All Visitor Badges"
msgstr "    طباعة كل بطاقات الزوار"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Print Invitation"
msgstr "طباعة الدعوة"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "Profile Photo"
msgstr "صورة الملف الشخصي"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__qr_code
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__qr_code
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__qr_code
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__qr_code
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__qr_code
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "QR Code"
msgstr "    كود QR"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__qr_codes
msgid "Qr Codes"
msgstr "    اكواد QR"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__rating_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__rating_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__rating_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__rating_ids
msgid "Ratings"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__rec_name
msgid "Rec Name"
msgstr "   اسم التسجيل"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__ref
msgid "Reference"
msgstr "    الرقم التعريفي"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_form
msgid "Regenerate QR Code"
msgstr "إعادة إنشاء كود QR"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Reject"
msgstr "رفض"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__rejected
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__rejected
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__rejected
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__rejected
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Rejected"
msgstr " تم الرفض"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Rejected Invitations"
msgstr "    الدعوات المرفوضة"

#. module: ams_vm
#. odoo-javascript
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "No Show Invitations"
msgstr "عدم حضور الزيارات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__parent_id
msgid "Related Company"
msgstr "    اسم الجهة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__related_company
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__related_company
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__related_company
msgid "Related Organization"
msgstr "    اسم الجهة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__partner_id
msgid "Related Partner"
msgstr "    الشريك المرتبط"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__employee_ids
msgid "Related employees based on their private address"
msgstr "    الموظفون المرتبطون بناء على عنوانهم الخاص"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Request Approval"
msgstr "طلب الموافقة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__request_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__request_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__request_date
msgid "Request Date"
msgstr "    تاريخ الطلب"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Request Info"
msgstr "معلومات الطلب"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__request_max_days
msgid "Request Max Days"
msgstr "    أقصى عدد أيام الطلب"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/res_company.py:0
#: code:addons/ams_vm/models/res_company.py:0
msgid "Request Max Days must be greater than 0."
msgstr "        يجب ان يكون عدد يوم الطلب اكبر من 0."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__name
msgid "Request No"
msgstr "    رقم الطلب"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__request_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__request_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__request_uid
msgid "Requested By"
msgstr "    طلب من"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__required_approval_after_time
msgid "Required Approval After Time"
msgstr "  الموافقة المطلوبة بعد مرور الوق"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/res_company.py:0
#: code:addons/ams_vm/models/res_company.py:0
msgid "Required Approval After Time must be between 0 and 23.59"
msgstr "        يجب ان يكون عدد ساعات الموافقة بين 0 و 23.59."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__required_manager_approval
msgid "Required Manager Approval"
msgstr "  مطلوب موافقة المدير"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Reset"
msgstr "إعادة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__response_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__response_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__response_uid
msgid "Responded By"
msgstr " تم الرد من خلال "

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__response_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__response_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__response_date
msgid "Response Date"
msgstr " تاريخ الرد"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_user_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_user_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_user_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_user_id
msgid "Responsible User"
msgstr " المستخدم المسؤول"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__room_name
msgid "Meeting Room"
msgstr "غرفة الاجتماعات"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__status__running
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__status__running
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__status__running
msgid "Running"
msgstr "جاري"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_has_sms_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_has_sms_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_has_sms_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_has_sms_error
msgid "SMS Delivery error"
msgstr " خطأ في تسليم الرسالة SMS"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__user_id
msgid "Salesperson"
msgstr " مندوب مبيعات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__phone_sanitized
msgid "Sanitized Number"
msgstr " رقم مصفوف"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__schedule_type
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__schedule_type
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__schedule_type
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Visit Type"
msgstr " نوع الزيارة"

#. module: ams_vm
#: model:res.groups,name:ams_vm.group_security_approval
msgid "Security Approval"
msgstr "الموافقة الأمنية"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__badge_report_id
msgid "Select default badge report to include in email attachment "
msgstr " حدد تقرير الشارة الافتراضي لتضمينه في مرفق البريد الإلكتروني"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__email_template_id
msgid "Select email template to be used when sending email"
msgstr " حدد قالب البريد الإلكتروني الذي سيتم استخدامه عند ارسال البريد الإلكتروني"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__visitors_group_id
msgid "Select group to be used for visitors"
msgstr " حدد المجموعة التي سيتم استخدامها للزائرين"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__self
msgid "Self"
msgstr " self"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Send Email"
msgstr "ارسال البريد الإلكتروني"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Send Invitation Email"
msgstr "ارسال البريد الإلكتروني للدعوة"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__email_state__sent
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__email_state__sent
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__email_state__sent
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__email_state__sent
msgid "Sent"
msgstr " تم الارسال"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__sex
msgid "Sex"
msgstr "الجنس"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__partner_share
msgid "Share Partner"
msgstr " شريك المشاركة"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Show More"
msgstr " اظهار المزيد"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__signup_type
msgid "Signup Token Type"
msgstr " نوع رمز التسجيل"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Single Day"
msgstr "يوم واحد"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/base_visitor_request.py:0
#: code:addons/ams_vm/models/base_visitor_request.py:0
msgid ""
"Single Schedule Duration Exceeded: The schedule spans {duration:.2f} hours, which exceeds "
"the maximum allowed {max_hours} hours."
msgstr ""
" وقت الجدول يتجاوز {duration:.2f} ساعة, وهو ما يتجاوز الحد الأقصى المسموح به {max_hours} "
"ساعة."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__specific_property_product_pricelist
msgid "Specific Property Product Pricelist"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Standard"
msgstr "قياسى"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__starred_message_ids
msgid "Starred Message"
msgstr " الرسالة المميزة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__start_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__start_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__start_date
msgid "Start Date"
msgstr " تاريخ البدء"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__state_id
msgid "State"
msgstr " الحالة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__static_map_url
msgid "Static Map Url"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__static_map_url_is_valid
msgid "Static Map Url Is Valid"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__status
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__status
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__status
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_list
msgid "Status"
msgstr "حالة الزيارة"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__activity_state
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__activity_state
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__activity_state
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
" حالة حسب النشاطات\n"
"متاخرة: تم تجاوز تاريخ الانتهاء\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"مخطط: النشاطات المستقبلية."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__street
msgid "Street"
msgstr " الشارع"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__street2
msgid "Street2"
msgstr " الشارع 2"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__subject
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__subject
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__subject
msgid "Subject"
msgstr " الموضوع"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Submit Request"
msgstr "تقديم الطلب"

#. module: ams_vm
#: code:addons/ams_vm/static/src/components/time_picker_component_field/time_picker_field.xml:0
msgid "Done"
msgstr "تم"

#. module: ams_vm
#: code:addons/ams_vm/static/src/components/time_picker_component_field/time_picker_field.xml:0
msgid "Minutes"
msgstr "دقائق"

#. module: ams_vm
#: code:addons/ams_vm/static/src/components/time_picker_component_field/time_picker_field.xml:0
msgid "Hours"
msgstr "ساعات"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__api_type__suprema
msgid "Suprema"
msgstr " سوبريما"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__synced
msgid "Synced"
msgstr " متزامنة"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__category_id
msgid "Tags"
msgstr " الملاحظات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__vat
msgid "Tax ID"
msgstr " الرقم الضريبي"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_form
msgid "Technical Info"
msgstr "المعلومات التقنية"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
" الرمز الدولي في اثناء حروف\n"
"يمكن استخدام هذا الحقل للبحث سريع."

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the country format. "
"You can use '/' to indicate that the partner is not subject to tax."
msgstr ""
" الرقم الضريبي. قيم هذه سيتم التحقق منها بواسطة الدولة. يمكنك استخدام '/' للوضح ان الشركة "
"ليست مرتبطة بالضرائب."

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__user_id
msgid "The internal user in charge of this contact."
msgstr " هذه المستخدم الداخلي المسئول عن جهة الاتصال."

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__company_registry
msgid ""
"The registry number of the company. Use it if it is different from the Tax ID. It must be "
"unique across all partners of a same country"
msgstr ""
"قم تسجيل الشركة. استخدمه إذا كان مختلفًا عن رقم التعريف الضريبي. يجب أن يكون فريدًا بين "
"جميع الشركاء في نفس البلد"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can contain more "
"than strictly an email address."
msgstr ""
" هذا الحقل يستخدم للبحث على عنوان البريد الإلكتروني كما يحتوي هذا الحقل على عنوان البريد "
"الإلكتروني."

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the current partner"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__required_approval_after_time
msgid "Time after which manager approval is required"
msgstr " الوقت الذي يتطلب موافقة المدير بعده"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__tz
msgid "Timezone"
msgstr " المنطقة الزمنية"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__tz_offset
msgid "Timezone offset"
msgstr " إزاحة المنطقة الزمنية"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__title
msgid "Title"
msgstr " العنوان"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__dashboard_up_coming_domain__today
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Today"
msgstr " اليوم"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__dashboard_up_coming_domain__tomorrow
msgid "Tomorrow"
msgstr " غدا"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__total_visits
msgid "Total Visits"
msgstr " مجموع الزيارات"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__activity_exception_decoration
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__activity_exception_decoration
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__activity_exception_decoration
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr " نوع النشاط الاستثنائي المسجل.."

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Upcoming Invitations:"
msgstr " الدعوات القادمة"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Visitor Management System"
msgstr "نظام إدارة  الزوّار"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Add New Invitation"
msgstr "إضافة دعوة جديدة"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
msgid "View All"
msgstr "عرض الكل"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Hi %(user_name)s, welcome to your dashboard to manage your invitations!"
msgstr "مرحباً %(user_name)s، أهلاً بك في لوحة التحكم الخاصة بك لإدارة دعواتك!"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Just now"
msgstr "الآن"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
msgid "%s minutes ago"
msgstr "منذ %s دقيقة"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
msgid "%s hours ago"
msgstr "منذ %s ساعة"

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Last updated"
msgstr "آخر تحديث"

#. module: ams_vm
#: model:ir.actions.server,name:ams_vm.ir_cron_update_visitor_request_status_ir_actions_server
msgid "Update Visitor Request Status"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__barcode
msgid "Use a barcode to identify this contact."
msgstr ""

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.view_user_form
msgid "User Classification"
msgstr "تصنيف المستخدم"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__user_livechat_username
msgid "User Livechat Username"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__user_ids
msgid "Users"
msgstr " المستخدمين"

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_visit
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__visit_id
msgid "Visit"
msgstr " زيارة"

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.top_menu
msgid "Visit Management"
msgstr " إدارة الزيارات"

#. module: ams_vm
#: model:ir.module.category,description:ams_vm.ams_vm_group_category
#: model:ir.module.category,name:ams_vm.ams_vm_group_category
msgid "Visit Management GROUPS"
msgstr "إدارة الزيارات GROUPS"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__visit_max_hours
msgid "Visit Max Hours"
msgstr " اقصى ساعات الزيارة"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/res_company.py:0
#: code:addons/ams_vm/models/res_company.py:0
msgid "Visit Max Hours must be between 1 and 24."
msgstr "يجب أن تكون ساعات الزيارة القصوى بين 1 و 24."

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__name
msgid "Visit No"
msgstr " رقم الزيارة"

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_visitor
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__visitor_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__is_visitor
#: model:ir.ui.menu,name:ams_vm.visitor_menu
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_list
msgid "Visitor"
msgstr "الزائر"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__visitor_id_number
msgid "Visitor ID"
msgstr "رقم هوية الزائر"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/base_visitor_request.py:0
#: code:addons/addons_ams/ams_vm/models/invitation_visitor.py:0
#: code:addons/ams_vm/models/base_visitor_request.py:0
#: code:addons/ams_vm/models/invitation_visitor.py:0
msgid "Visitor Not Found"
msgstr "الزائر غير موجود"

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/base_visitor_request.py:0
#: code:addons/ams_vm/models/base_visitor_request.py:0
msgid "Main Visitor Not Found , please check main visitor id"
msgstr "الزائر الرئيسي غير موجود من فضلك تحقق من رقم هوية الزائر الرئيسي"

#. module: ams_vm
#: model:ir.actions.act_window,name:ams_vm.visitor_action
#: model:ir.actions.server,name:ams_vm.visitor_server_action
#: model:ir.ui.menu,name:ams_vm.visitor_top_menu
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Visitors"
msgstr "قائمة الزوار"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__visitors_count
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__visitors_count
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__visitors_count
msgid "Visitors Count"
msgstr " عدد الزوار"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__visitors_group_id
msgid "Visitors Group"
msgstr " مجموعة الزوار"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.view_res_company_form
msgid "Visitors Management Settings"
msgstr "إعدادات إدارة الزيارات"

#. module: ams_vm
#: model:ir.actions.act_window,name:ams_vm.visit_action
#: model:ir.ui.menu,name:ams_vm.visit_menu
msgid "Visits"
msgstr " الزيارات"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Visits Count"
msgstr "عدد الزيارات"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "Wave Footer"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__website
msgid "Website Link"
msgstr " رابط الموقع"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__website_message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__website_message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__website_message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__website_message_ids
msgid "Website Messages"
msgstr " رسائل الموقع"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__website_message_ids
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__website_message_ids
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__website_message_ids
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__website_message_ids
msgid "Website communication history"
msgstr " تاريخ الاتصال بالموقع"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__tz
msgid ""
"When printing documents and exporting/importing data, time values are computed according "
"to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your web client."
msgstr ""
"عند طباعة المستندات وتصدير/استيراد البيانات، يتم حساب قيم الوقت وفقًا لهذه المنطقة "
"الزمنية.\n"
"إذا لم يتم تعيين المنطقة الزمنية، فسيتم استخدام UTC (التوقيت العالمي المنسق).\n"
"في أي مكان آخر، يتم حساب قيم الوقت وفقًا للإزاحة الزمنية لعميل الويب الخاص بك. "

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__api_type__zk
msgid "ZKTeco"
msgstr " ZKTeco"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__zip
msgid "Zip"
msgstr " الرقم البريدي"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_kanban
msgid "at"
msgstr "في"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__employee_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__employee_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__employee_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__employee_id
msgid "this record owned to this employee as follow up"
msgstr "هذا السجل مملوك بواسطة هذا الموظف كمتابعة"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "الاسم :"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "الموقع:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "بطاقة دعوة"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "تم إرفاق تصريح الزيارة مع هذا البريد الإلكتروني"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "تواصل معنا"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "جميع الحقوق محفوظة لوزارة الاقتصاد والتخطيط ©2024"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "رقم المرجع:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "قاعة الاجتماع:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "مرحباً"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid ""
"مع أطيب التحيات،<br/>\n"
"                                    <strong>إدارة الأمن والسلامة</strong><br/>\n"
"                                    وزارة الاقتصاد والتخطيط"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "موقعنا الإلكتروني"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "نتطلع لاستقبالكم ونتمنى لكم زيارة موفقة."
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "نرحب بكم في وزارة الاقتصاد والتخطيط. فيما يلي تفاصيل تصريح زيارتكم:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "وقت الخروج:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "وقت الزيارة:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "يرجى إبراز هذا التصريح عند نقطة الأمن"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "يرجى الالتزام بتعليمات الأمن والسلامة"
msgstr ""

#. module: ams_vm
#: model:ir.actions.act_window,name:ams_vm.action_invitation_visitor_import_wizard
msgid "Import Visitors from Excel"
msgstr "استيراد الزوار من ملف Excel"

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_invitation_visitor_import_wizard
msgid "Import Invitation Visitors from Excel"
msgstr "استيراد زوار الدعوة من ملف Excel"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__excel_file
msgid "Excel File"
msgstr "ملف Excel"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__filename
msgid "Filename"
msgstr "اسم الملف"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__update_existing
msgid "Update Existing Visitors"
msgstr "تحديث الزوار الموجودين"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__create_missing
msgid "Create Missing Visitors"
msgstr "إنشاء الزوار المفقودين"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__import_summary
msgid "Import Summary"
msgstr "ملخص الاستيراد"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__total_rows
msgid "Total Rows"
msgstr "إجمالي الصفوف"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__created_count
msgid "Created Visitors"
msgstr "الزوار المُنشأون"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__updated_count
msgid "Updated Visitors"
msgstr "الزوار المُحدثون"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__error_count
msgid "Errors"
msgstr "الأخطاء"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__gender
msgid "Gender"
msgstr "الجنس"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visitor__gender__male
msgid "Male"
msgstr "ذكر"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visitor__gender__female
msgid "Female"
msgstr "أنثى"

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visitor__gender__other
msgid "Other"
msgstr "آخر"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Import Visitors from Excel File"
msgstr "استيراد الزوار من ملف Excel"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Import Visitors"
msgstr "استيراد الزوار"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Download Template"
msgstr "تحميل النموذج"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Close"
msgstr "إغلاق"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "File Upload"
msgstr "رفع الملف"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Import Options"
msgstr "خيارات الاستيراد"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Import Results"
msgstr "نتائج الاستيراد"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Import Summary"
msgstr "ملخص الاستيراد"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Excel File Format:"
msgstr "تنسيق ملف Excel:"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "The Excel file should contain the following columns (case insensitive):"
msgstr "يجب أن يحتوي ملف Excel على الأعمدة التالية (غير حساس لحالة الأحرف):"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "first_name"
msgstr "الاسم الأول"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "middle_name"
msgstr "الاسم الأوسط"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "last_name"
msgstr "اسم العائلة"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "id_number"
msgstr "رقم الهوية"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "email"
msgstr "البريد الإلكتروني"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "organization"
msgstr "المؤسسة"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "nationality"
msgstr "الجنسية"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "mobile"
msgstr "الجوال"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "sex"
msgstr "الجنس"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "(required) - Visitor's first name"
msgstr "(مطلوب) - الاسم الأول للزائر"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "(required) - Visitor's last name"
msgstr "(مطلوب) - اسم العائلة للزائر"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "(required) - National ID or identification number"
msgstr "(مطلوب) - رقم الهوية الوطنية أو رقم التعريف"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "(required) - Email address"
msgstr "(مطلوب) - عنوان البريد الإلكتروني"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "(optional) - Company or organization"
msgstr "(اختياري) - الشركة أو المؤسسة"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "(optional) - Country name"
msgstr "(اختياري) - اسم البلد"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "(optional) - Mobile phone number"
msgstr "(اختياري) - رقم الهاتف المحمول"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "(optional) - Gender"
msgstr "(اختياري) - الجنس"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Notes:"
msgstr "ملاحظات:"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "• The first row should contain column headers"
msgstr "• يجب أن يحتوي الصف الأول على عناوين الأعمدة"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "• Visitors will be matched by ID number"
msgstr "• سيتم مطابقة الزوار برقم الهوية"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "• Empty rows will be skipped"
msgstr "• سيتم تجاهل الصفوف الفارغة"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "• Existing visitors can be updated if the option is enabled"
msgstr "• يمكن تحديث الزوار الموجودين إذا تم تفعيل الخيار"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Import from Excel"
msgstr "استيراد من Excel"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Visitors List"
msgstr "قائمة الزوار"

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "openpyxl library is not installed. Please install it to use Excel import."
msgstr "مكتبة openpyxl غير مثبتة. يرجى تثبيتها لاستخدام استيراد Excel."

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Please upload an Excel file."
msgstr "يرجى رفع ملف Excel."

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Error processing Excel file: %s"
msgstr "خطأ في معالجة ملف Excel: %s"

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Could not find valid headers in Excel file. Expected headers: %s"
msgstr "لا يمكن العثور على عناوين صحيحة في ملف Excel. العناوين المتوقعة: %s"

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Missing required fields: %s"
msgstr "حقول مطلوبة مفقودة: %s"

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Invalid email format: %s"
msgstr "تنسيق بريد إلكتروني غير صحيح: %s"

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Visitor with ID %s not found and creation is disabled"
msgstr "الزائر برقم الهوية %s غير موجود وإنشاء الزوار معطل"

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Error processing visitor %s: %s"
msgstr "خطأ في معالجة الزائر %s: %s"

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Email %s already exists for visitor %s"
msgstr "البريد الإلكتروني %s موجود بالفعل للزائر %s"

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Error creating template file: %s"
msgstr "خطأ في إنشاء ملف النموذج: %s"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation_visitor_import_wizard__excel_file
msgid "Upload Excel file with visitor data"
msgstr "رفع ملف Excel يحتوي على بيانات الزوار"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation_visitor_import_wizard__update_existing
msgid "Update existing visitors if found by ID number"
msgstr "تحديث الزوار الموجودين إذا تم العثور عليهم برقم الهوية"

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation_visitor_import_wizard__create_missing
msgid "Create new visitors if not found"
msgstr "إنشاء زوار جدد إذا لم يتم العثور عليهم"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__status
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__status
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__status
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_list
msgid "Visit Status"
msgstr "حالة الزيارة"

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.ams_vm_combined_dashboard_menu
#: model:ir.ui.menu,name:ams_vm.ams_vm_dashboard_menu
msgid "Visit Management Dashboard"
msgstr "لوحة إدارة الزيارات"

#. module: ams_vm
#. odoo-javascript
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Invitations that have been created by the employee but have not yet been submitted or forwarded officially."
msgstr "هي الدعوات التي تم إنشاؤها بواسطة الموظف، ولكن لم يتم تقديمها أو رفعها بشكل رسمي بعد."

#. module: ams_vm
#. odoo-javascript
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Invitations that require approval from the direct manager, especially in cases such as outside working hours."
msgstr "هي الدعوات التي تتطلب موافقة المدير المباشر، خصوصًا في الحالات التي تكون خارج أوقات الدوام الرسمي."

#. module: ams_vm
#. odoo-javascript
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Invitations that have been approved by the direct manager, or automatically approved by the requester when scheduled during official working hours."
msgstr "هي الدعوات التي تمت الموافقة عليها من قبل المدير المباشر، أو تمت الموافقة عليها تلقائيًا من قبل الموظف في حال كانت ضمن أوقات العمل الرسمية."

#. module: ams_vm
#. odoo-javascript
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Invitations that have been confirmed by security or reception, triggering the activation of a QR code that registers the visitor on biometric devices."
msgstr "هي الدعوات التي تم تأكيدها من قبل موظف الأمن أو موظف الاستقبال، حيث يتم تفعيل رمز الاستجابة السريعة (QR Code) لتسجيل حضور الزائر على أجهزة البصمة."

#. module: ams_vm
#. odoo-javascript
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Invitations that have been declined by the direct manager when approval was required."
msgstr "هي الدعوات التي تم رفضها من قبل المدير المباشر في الحالات التي كانت تتطلب الموافقة."

#. module: ams_vm
#. odoo-javascript
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Invitations that were issued to the visitor, but the visitor did not arrive at the scheduled time, and are marked as a no-show."
msgstr "هي الدعوات التي تم إصدارها للزائر، ولكنه لم يحضر في الموعد المحدد، وبالتالي تُسجل كدعوة لزائر لم يحضر."

#. module: ams_vm
#. odoo-javascript
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Definitions"
msgstr "التعريفات"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_visitor_request__weekend
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__weekend
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Weekend"
msgstr "عطلة نهاية الاسبوع"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_visitor_request__outside_working_time
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__outside_working_time
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Outside Working Time"
msgstr "خارج ساعات العمل"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__vip
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__vip
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_visitor_request__vip
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_search
msgid "VIP"
msgstr "كبار الشخصيات"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_search
msgid "VIP Visitors"
msgstr "كبار الشخصيات"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_search
msgid "Draft"
msgstr "مسودة"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_search
msgid "Building"
msgstr "المبنى"

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_search
msgid "Invitation Date"
msgstr "تاريخ الدعوة"

#. module: ams_vm
#: model:ir.filters,name:ams_vm.tomorrow_filter
msgid "Tomorrow"
msgstr "غداً"

#. module: ams_vm
#: model:ir.filters,name:ams_vm.next_seven_days_filter
msgid "Next 7 Days"
msgstr "الأيام السبعة القادمة"

#. module: ams_vm
#: model:ir.filters,name:ams_vm.next_fourteen_days_filter
msgid "Next 14 Days"
msgstr "الأيام الأربعة عشر القادمة"

#. module: ams_vm
#: model:ir.filters,name:ams_vm.next_thirty_days_filter
msgid "Next 30 Days"
msgstr "الأيام الثلاثون القادمة"

#. module: ams_vm
#: model:ir.filters,name:ams_vm.yesterday_filter
msgid "Yesterday"
msgstr "أمس"

#. module: ams_vm
#: model:ir.filters,name:ams_vm.last_seven_days_filter
msgid "Last 7 Days"
msgstr "الأيام السبعة الماضية"

#. module: ams_vm
#: model:ir.filters,name:ams_vm.last_fourteen_days_filter
msgid "Last 14 Days"
msgstr "الأيام الأربعة عشر الماضية"

#. module: ams_vm
#: model:ir.filters,name:ams_vm.last_thirty_days_filter
msgid "Last 30 Days"
msgstr "الأيام الثلاثون الماضية"

#. module: ams_vm
#. odoo-python
#: code:addons/ams_vm/models/base_visitor_request.py:0
msgid "For single day schedules, you can only select one floor | access group."
msgstr "للجدولة ليوم واحد، يمكنك اختيار طابق واحد فقط | مجموعة وصول واحدة."

#. module: ams_vm
#. odoo-python
#: code:addons/ams_vm/models/visitor.py:0
msgid "First name can only contain English/Arabic letters, numbers, and spaces."
msgstr "الاسم الأول يمكن أن يحتوي على أحرف إنجليزية/عربية وأرقام ومسافات فقط."

#. module: ams_vm
#. odoo-python
#: code:addons/ams_vm/models/visitor.py:0
msgid "Middle name can only contain English/Arabic letters, numbers, and spaces."
msgstr "الاسم الأوسط يمكن أن يحتوي على أحرف إنجليزية/عربية وأرقام ومسافات فقط."

#. module: ams_vm
#. odoo-python
#: code:addons/ams_vm/models/visitor.py:0
msgid "Last name can only contain English/Arabic letters, numbers, and spaces."
msgstr "اسم العائلة يمكن أن يحتوي على أحرف إنجليزية/عربية وأرقام ومسافات فقط."

