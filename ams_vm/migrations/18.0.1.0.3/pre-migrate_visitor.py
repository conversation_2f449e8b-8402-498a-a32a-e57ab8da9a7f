# -*- coding: utf-8 -*-
from odoo import api, SUPERUSER_ID


def migrate(cr, version):
    # First get the field_id from ir_model_fields
    cr.execute("""
           DELETE FROM mail_tracking_value
           WHERE field_id IN (
               SELECT id FROM ir_model_fields 
               WHERE name = 'last_department_id' 
                AND model in ('ams_vm.visitor')
            )
       """)

    # Remove the column if it exists
    cr.execute("""
           DO $$
           BEGIN
                IF EXISTS (SELECT 1 FROM information_schema.columns 
                         WHERE table_name='ams_vm_visitor' 
                         AND column_name='last_department_id') THEN
                   ALTER TABLE ams_vm_visitor DROP COLUMN last_department_id CASCADE;
               END IF;
           END $$;
       """)

    # Clean up the field from ir_model_fields
    cr.execute("""
           DELETE FROM ir_model_fields 
           WHERE name = 'last_department_id' 
           AND model in ('ams_vm.visitor')
       """)


