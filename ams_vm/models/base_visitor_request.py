# -*- coding: utf-8 -*-

from odoo import fields, models, api, _
from odoo.exceptions import UserError
from dateutil.relativedelta import relativedelta
from datetime import datetime, timedelta

class BaseVisitorRequest(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_vm.base_request'
    _description = 'Base Visitor Request'
    _inherit = 'ams_base.request', 'mail.thread', 'mail.activity.mixin'
    _order = 'create_date desc'

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------

    def _need_approval(self):
        # Check if approval is required based on the user's level or if it's an event
        if self.env.user.level != 'user' or self.is_event:
            return False

        # Check if approval is required based on the company settings
        company = self.env.company
        if company.required_manager_approval:
            return True

        if self.end_date:
            # Check if the times are within the required approval time
            invitation_end_time = fields.Datetime.context_timestamp(self, self.end_date).hour
            approval_time = company.required_approval_after_time
            if invitation_end_time >= approval_time:
                return True

        # If the conditions are not met, return False
        return False

    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    related_company = fields.Char(string='Related Organization', related='visitor_id.organization', store=True)
    qr_code = fields.Char(string="QR Code", copy=False, readonly=True, search='_search_qrcode')
    visitor_id_number = fields.Char(string="Main Visitor ID")
    vip = fields.Boolean(string='VIP', related='visitor_id.vip', store=True, readonly=False)
    # endregion

    # region  Special
    # endregion

    # region  Relational
    visitor_id = fields.Many2one('ams_vm.visitor', string='Main Visitor', required=True, ondelete='cascade')
    building_id = fields.Many2one('ams.building', string='Building')
    access_groups_ids = fields.Many2many('ams.access_group', string="Floor",
                                         domain="[('is_visitor_group', '=', True), ('synced', '=', True), '|', ('building_id', '=', False), ('building_id', '=', building_id)]", required=True)
    invitation_visitor_ids = fields.One2many('ams_vm.invitation_visitor', 'invitation_id',
                                             string="Invitation Visitors")
    # endregion

    # region  Computed
    visitors_count = fields.Integer(string='Visitors Count', compute='_compute_visitors_count', default=1,
                                    readonly=False, store=True, copy=False)
    is_readonly = fields.Boolean(compute='_compute_is_readonly', store=False, copy=False)
    is_current_user_requester = fields.Boolean(compute='_compute_is_current_user_requester', default=True, copy=False)
    need_approval = fields.Boolean(string='Need Approval', default=_need_approval, compute='_compute_need_approval',
                                   copy=False)
    has_approval_access = fields.Boolean(compute='_compute_has_approval_access', copy=False)
    state = fields.Selection(
        [('pending', 'Draft'), ('need_approval', 'Need Approval'), ('approved', 'Approved'),
         ('confirmed', 'Confirmed'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')],
        string='State', default='pending', tracking=True)
    status = fields.Selection(
        [('pending', 'Scheduled'), ('running', 'Running'), ('finished', 'Finished')],
        string='Visit Status', default='pending', readonly=True, copy=False)
    email_state = fields.Selection([
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed')
    ], string="Email State", default='pending', readonly=True)
    weekend = fields.Boolean(string='Weekend', compute='_compute_weekend', store=True, copy=False)
    outside_working_time = fields.Boolean(string='Outside Working Time', compute='_compute_outside_working_time', store=True, copy=False)


    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('need_approval')
    def _compute_has_approval_access(self):
        for record in self:
            if self.env.user.level == 'user':
                if self.need_approval:
                    record.has_approval_access = False  # display request_approve button
                else:
                    record.has_approval_access = True  # display approval button
            else:
                record.has_approval_access = True  # self.env.user._is_admin() or self.env.user.level == 'manager' or self.env.user.level == 'supervisor'

    def _compute_is_current_user_requester(self):
        for record in self:
            record.is_current_user_requester = record.request_uid == self.env.user

    @api.depends('state')
    def _compute_is_readonly(self):
        for record in self:
            record.is_readonly = False
            if record.state not in ['pending', 'need_approval']:
                record.is_readonly = True
            elif record.request_uid != self.env.user:
                record.is_readonly = True

            # record.is_readonly = record.state not in ['pending', 'need_approval'] and record.request_uid == self.env.user

    @api.depends('invitation_visitor_ids', 'is_event')
    def _compute_visitors_count(self):
        for rec in self:
            if not rec.is_event:
                rec.visitors_count = len(rec.invitation_visitor_ids)

    @api.depends('start_date', 'end_date', 'duration', 'company_id.required_manager_approval')
    def _compute_need_approval(self):
        for rec in self:
            rec.need_approval = self._need_approval()

    def _search_qrcode(self, operator, value):
        return [('qr_code', 'ilike', value)]

    @api.depends('start_date', 'end_date')
    def _compute_weekend(self):
        for record in self:
            record.weekend = False

            if not record.start_date or not record.end_date:
                continue

            start_date = record.start_date.date()
            end_date = record.end_date.date()

            # Fast path: if duration is 7+ days, weekend is definitely included
            if (end_date - start_date).days >= 6:
                record.weekend = True
                continue

            # Efficiently check only days in range without looping unnecessarily
            for i in range((end_date - start_date).days + 1):
                day = start_date + timedelta(days=i)
                if day.weekday() in (4, 5):  # Friday=4, Saturday=5
                    record.weekend = True
                    break

    @api.depends('end_date')
    def _compute_outside_working_time(self):
        for record in self:
            record.outside_working_time = False
            
            if record.end_date:
                # Check if the times are within the required approval time
                invitation_end_time = fields.Datetime.context_timestamp(record, record.end_date).hour
                approval_time = record.company_id.required_approval_after_time
                if invitation_end_time >= approval_time:
                    record.outside_working_time = True

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.constrains('schedule_type', 'start_date', 'end_date', 'duration')
    def _constrains_schedule_duration_validate(self):
        # Skip validation if we're in confirmation process
        # (validation already passed during approval)
        if self.env.context.get('skip_duration_validation', False):
            return

        message = self._validate_schedule_duration()
        if message:
            raise UserError(_(message))

    @api.constrains('schedule_type', 'access_groups_ids')
    def _constrains_access_groups_single_day(self):
        """Ensure only one access group is selected for single day schedules."""
        for record in self:
            if record.schedule_type == 'single' and len(record.access_groups_ids) > 1:
                raise UserError(_('For single day schedules, you can only select one floor | access group.'))

    @api.onchange('schedule_type')
    def _onchange_schedule_type_access_groups(self):
        """Clear access groups when changing to single day schedule if more than one is selected."""
        if self.schedule_type == 'single' and len(self.access_groups_ids) > 1:
            # Keep only the first selected access group
            self.access_groups_ids = [(6, 0, [self.access_groups_ids[0].id])]

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    
    def write(self, vals):
        """Override write method to send email notifications when state changes."""
        # Track state changes for email notifications
        state_changes = {}
        if 'state' in vals:
            for record in self:
                old_state = record.state
                new_state = vals['state']
                if old_state != new_state and new_state in ['approved', 'confirmed', 'rejected']:
                    state_changes[record.id] = {'old_state': old_state, 'new_state': new_state}
        
        # Call parent write method
        result = super(BaseVisitorRequest, self).write(vals)
        
        # Send email notifications for state changes
        if state_changes:
            self._send_state_change_notifications(state_changes)
        
        return result
    
    def _send_state_change_notifications(self, state_changes):
        """Send email notifications to requesters when invitation state changes."""
        # Get the email template
        template = self.env.ref('ams_vm.mail_template_invitation_state_change_notification', raise_if_not_found=False)
        if not template:
            return
        
        for record_id, change_info in state_changes.items():
            record = self.browse(record_id)
            
            # Only send email if the requester has an email address
            if record.create_uid and record.create_uid.email:
                try:
                    # Send email using the template
                    template.sudo().send_mail(
                        record.id,
                        force_send=True,  # Use mail queue for better performance
                        raise_exception=False,
                        email_values={
                            'email_to': record.create_uid.email,
                            'email_from': record.company_id.email or self.env.user.email or '',
                        }
                    )
                    # Log the email sending in the chatter
                    record.message_post(
                        body=f"Email notification sent to {record.create_uid.name} ({record.create_uid.email}) for state change to '{dict(record._fields['state'].selection)[record.state]}'.",
                        message_type='notification'
                    )
                except Exception as e:
                    # Log error if email sending fails
                    record.message_post(
                        body=f"Failed to send email notification to {record.create_uid.name} ({record.create_uid.email}): {str(e)}",
                        message_type='notification'
                    )
    
    # endregion

    # region ----------------------- TODO[IMP]: Properties ---------------------------------------
    @property
    def api_type(self):
        return self.env.company.api_type

    @property
    def api_user_model(self):
        return self.env['ams.user'].get_api_model(self.api_type)

    @property
    def api_card_model(self):
        return self.env['ams.card'].get_api_model(self.api_type)

    @property
    def api_access_group_model(self):
        return self.env['ams.access_group'].get_api_model(self.api_type)

    @property
    def api_card_type_model(self):
        return self.env['ams.card_type'].get_api_model(self.api_type)

    @property
    def api_user_group_model(self):
        return self.env['ams.user_group'].get_api_model(self.api_type)

    @property
    def activity_can_be_done(self):
        # Only allow marking as done if the current user is the approval manager
        return self.env.user == self.approval_uid

    @property
    def _localized_start_date(self):
        return fields.Datetime.context_timestamp(self, self.start_date).strftime('%Y-%m-%d %H:%M')

    @property
    def _localized_end_date(self):
        return fields.Datetime.context_timestamp(self, self.end_date).strftime('%Y-%m-%d %H:%M')

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def _update_state(self, new_state):
        """Helper method to update request state and related fields."""
        return self.sudo().write({
            'state': new_state,
            'response_date': fields.Datetime.now(),
            'response_uid': self.env.uid,
            'request_date': fields.Datetime.now()
        })

    def action_request_approve(self):
        # Validate visitor
        if not self.visitor_id:
            raise UserError(_("Main Visitor Not Found , please check main visitor id"))

        # Check if approval manager is set
        if not self.approval_uid:
            raise UserError(_("Please select an approval manager."))

        # Send email notification to approval manager using custom template
        try:
            template = self.env.ref('ams_vm.mail_template_manager_approval_request')
            if template:
                template.sudo().send_mail(self.id, force_send=True)
        except Exception as e:
            # Fallback to activity if email fails
            activity = self.sudo().activity_schedule(
                'mail.mail_activity_data_todo',
                user_id=self.approval_uid.id,
                summary='Approval Required',
                note=f'Please approve the visitor request: {self.name}',
                date_deadline=fields.Date.today(),
            )
            # Set can_write based on the assigned user
            if activity:
                activity.sudo().write({'can_write': self.activity_can_be_done})

        # Request approval for all visitors
        for invitation_visitor_id in self.invitation_visitor_ids:
            invitation_visitor_id.sudo().action_request_approve()

        # Update invitation state
        return self.sudo()._update_state('need_approval')

    def action_approve(self):
        # Validate visitor
        if not self.visitor_id:
            raise UserError(_("Main Visitor Not Found , please check main visitor id"))

        # Check if visitor is already in the invitation-visitor relation table
        if not self.visitor_id in self.invitation_visitor_ids.mapped('visitor_id'):
            # Create a new invitation-visitor relation
            self.invitation_visitor_ids.sudo().create({'invitation_id': self.id, 'visitor_id': self.visitor_id.id,
                                                       'visitor_id_number': self.visitor_id_number})

        # Approve all visitors and send invitation emails
        for invitation_visitor_id in self.invitation_visitor_ids:
            invitation_visitor_id.sudo().action_approve()
            invitation_visitor_id.sudo().action_send_invitation_email()

        # Set Initial QR Code from Main Visitor
        self.qr_code = self.visitor_id.qr_code

        # Update invitation state
        return self._update_state('approved')

    def action_cancel(self):
        for invitation_visitor_id in self.invitation_visitor_ids:
            invitation_visitor_id.sudo().action_cancel()

        return self._update_state('cancelled')

    def action_reject(self):
        for invitation_visitor_id in self.invitation_visitor_ids:
            invitation_visitor_id.sudo().action_reject()

        return self._update_state('rejected')

    def action_reset(self):
        # Reset all visitors
        for invitation_visitor_id in self.invitation_visitor_ids:
            invitation_visitor_id.sudo().action_reset()

        # Update invitation state
        return self.sudo().write({
            'state': 'pending',
            'email_state': 'pending',
            'status': 'pending',
            'response_date': None,
            'response_uid': '',
        })

    def action_confirm(self):
        """Confirm visitor request and handle API synchronization for user and card."""
        self.logger.info(f"[BASE_CONFIRM] Starting confirmation for invitation {self.name} (schedule_type: {self.schedule_type})")

        # Confirm all visitors
        total_visitors = len(self.invitation_visitor_ids)
        self.logger.info(f"[BASE_CONFIRM] Confirming {total_visitors} visitor(s)")

        for idx, invitation_visitor_id in enumerate(self.invitation_visitor_ids, 1):
            self.logger.info(f"[BASE_CONFIRM] Confirming visitor {idx}/{total_visitors}: {invitation_visitor_id.visitor_id.name}")
            invitation_visitor_id.sudo()._confirm()
            self.logger.info(f"[BASE_CONFIRM] Visitor {idx}/{total_visitors} confirmed successfully")

        # Update invitation state only if all visitors are confirmed
        # Skip duration validation during confirmation (already validated during approval)
        self.logger.info(f"[BASE_CONFIRM] Updating invitation state to 'confirmed'")
        self.sudo().with_context(skip_duration_validation=True)._update_state('confirmed')

        # Update status based on start_date and end_date
        # Also skip duration validation here since write() will trigger constraints
        self.logger.info(f"[BASE_CONFIRM] Updating invitation status based on dates")
        self.sudo().with_context(skip_duration_validation=True)._update_status_based_on_dates()

        self.logger.info(f"[BASE_CONFIRM] Confirmation completed successfully for invitation {self.id}")

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _update_status_based_on_dates(self):
        """Update status based on start_date and end_date"""
        now = fields.Datetime.now()

        pending_records = self.filtered(lambda r: r.start_date > now)
        finished_records = self.filtered(lambda r: r.end_date < now)
        running_records = self - pending_records - finished_records

        if pending_records:
            pending_records.write({'status': 'pending'})
        if finished_records:
            finished_records.write({'status': 'finished'})
        if running_records:
            running_records.write({'status': 'running'})

    @api.model
    def _cron_update_request_status(self):
        """Cron job to update request status based on dates"""
        domain = [
            ('status', '!=', 'finished'),
        ]

        requests = self.env['ams_vm.invitation'].sudo().search(domain)

        if not requests:
            return

        # Skip duration validation during cron job (status update only)
        requests.sudo().with_context(skip_duration_validation=True)._update_status_based_on_dates()

    def _validate_schedule_duration(self):
        """ Validate schedule duration based on schedule type and maximum allowed days and hours. """

        # Get maximum allowed days and hours
        company = self.env.company
        max_allowed_days = company.request_max_days
        max_allowed_hours = company.visit_max_hours

        for record in self:
            # Early return if dates are not set
            if not (record.start_date and record.end_date):
                continue

            # logger request name and schedule type
            self.logger.info(f"Validating schedule duration for {record.name} (schedule_type: {record.schedule_type})")
            if record.schedule_type == "multiple":
                date_difference = (record.end_date - record.start_date).days + 1  # Include both start and end dates

                if date_difference > max_allowed_days and not record.is_event:
                    return (
                        _("Multiple Schedule Duration Exceeded: "
                          "The schedule spans {duration} days, which exceeds the maximum allowed {max_days} days.")
                        .format(duration=date_difference, max_days=max_allowed_days)
                    )
            else:
                # Convert to hours with more precision
                duration_hours = (record.end_date - record.start_date).total_seconds() / 3600

                if max_allowed_hours < duration_hours < 24 and not record.is_event:
                    return (
                        _("Single Schedule Duration Exceeded: "
                          "The schedule spans {duration:.2f} hours, which exceeds the maximum allowed {max_hours} hours.")
                        .format(duration=duration_hours, max_hours=max_allowed_hours)
                    )

    def calculate_future_date(self, number_to_add, is_month=False):
        future_date = fields.Datetime.now()
        if is_month:
            future_date += relativedelta(months=number_to_add)
        else:
            future_date += relativedelta(days=number_to_add)
        return future_date.date()

    def get_date_domain(self):
        current_date = fields.Date.context_today(self)

        up_coming_select_value = self.env.company.dashboard_up_coming_domain

        match up_coming_select_value:
            case 'today':
                return [('date', '=', current_date)]
            case 'tomorrow':
                return [('date', '=', self.calculate_future_date(1))]
            case 'nextWeek':
                return [('date', '>=', current_date), ('date', '<', self.calculate_future_date(7))]
            case 'nextMonth':
                return [('date', '>=', current_date), ('date', '<', self.calculate_future_date(1, is_month=True))]
            case 'all':
                return [('date', '>=', current_date)]
            case _:
                return []

    def get_dashboard_domain(self):
        date_domain = self.get_date_domain()
        follow_up_domain = self._domain_follow_up()
        return date_domain + follow_up_domain  # + [('is_event', '=', False)]

    # def get_dashboard_count_item_domain(self,state):
    #     date_domain = self.get_date_domain()
    #     follow_up_domain = self.follow_up_domain
    #     return [('state', '=', state)] + date_domain + follow_up_domain

    # endregion
