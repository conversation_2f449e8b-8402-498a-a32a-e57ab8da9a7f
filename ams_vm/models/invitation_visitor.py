# -*- coding: utf-8 -*-

from odoo import api, fields, models,_
from odoo.exceptions import UserError
import base64



class InvitationVisitor(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_vm.invitation_visitor'
    _description = 'Invitation Visitor Assignment'
    _inherit ="ams_base.abstract_model"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    def _generate_unique_numeric_qr_code(self):
        return self.env['ams_vm.visitor']._generate_unique_numeric_qr_code()
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(compute='_compute_name', store=True)
    state = fields.Selection([
        ('pending', 'Draft'),
        ('need_approval', 'Need Approval'),
        ('approved', 'Approved'),
        ('confirmed', 'Confirmed'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled')
    ],string="State", default='pending')
    email_state = fields.Selection([
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed')
    ],string="Email State", default='pending')
    qr_code = fields.Char(
        string="QR Code",
        copy=False,
        readonly=True,
        default=lambda self: self._generate_unique_numeric_qr_code()
    )
    visitor_id_number = fields.Char(string="Visitor ID" )
    vip = fields.Boolean(string='VIP', related='visitor_id.vip', store=True, readonly=False)
    # endregion

    # region  Special
    # endregion

    # region  Relational
    visitor_id = fields.Many2one('ams_vm.visitor', string="Visitor", required=True , ondelete='restrict')
    invitation_id = fields.Many2one('ams_vm.invitation', string="Invitation", required=True, ondelete='restrict')
    visit_id = fields.Many2one('ams_vm.visit', string="Visit")
    subject = fields.Char(related='invitation_id.subject', string="Subject", store=True)
    building_id = fields.Many2one('ams.building', related='invitation_id.building_id', string="Building", store=True)

    # endregion

    # region  Computed
    # endregion

    # endregion----------

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('visitor_id', 'invitation_id')
    def _compute_name(self):
        for record in self:
            if record.visitor_id and record.invitation_id:
                record.name = f"{record.visitor_id.name} - {record.invitation_id.name}"
            else:
                record.name = "New Visitor Invitation"
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Properties ---------------------------------------
    def duration_display(self, language='en'):
        """Display duration in a human-readable format in Arabic"""

        self.ensure_one()
        if not self.invitation_id.duration:
            return ""
        translations = {
            'en': {
                'hour': 'hour',
                '2hours': '2 hours',
                'hours': 'hours',
                'minute': 'minute',
                'minutes': 'minutes',
            },
            'ar': {
                'hour': 'ساعة',
                '2hours':'ساعتين',
                'hours': 'ساعات',
                'minute': 'دقيقة',
                'minutes': 'دقائق',
            },
        }
        # Convert duration (in hours) to hours and minutes
        hours = int(self.invitation_id.duration)
        remaining_minutes = int((self.invitation_id.duration - hours) * 60)


        # If duration is less than an hour, show only minutes
        if hours == 0:
            return f"{remaining_minutes} {translations[language]['minute']}"
        # Format hours according to Arabic grammar rules

        if hours == 1:
            hours_text = f"{translations[language]['hour']}"
        elif hours == 2:
            hours_text = f"{translations[language]['2hours']}"
        elif 3 <= hours <= 10:
            hours_text = f"{hours} {translations[language]['hours']}"
        else:  # 11 and above
            hours_text = f"{hours} {translations[language]['hour']}"

        # Add minutes if they exist
        if remaining_minutes > 0:
            return f"{hours_text} , {remaining_minutes} {translations[language]['minutes']}"
        else:
            return hours_text

    @property
    def badge_report_action(self):
        report_action =self.env.company.badge_report_id or self.env.ref('ams_vm.general_invitation_badge_report_action')
        return report_action

    @property
    def badge_report_template(self):
        template = self.env.company.email_template_id or self.env.ref('ams_vm.general_invitation_badge_template')
        return template

    @property
    def api_type(self):
        return self.env.company.api_type

    @property
    def api_user_model(self):
        return self.env['ams.user'].get_api_model(self.api_type)

    @property
    def api_card_model(self):
        return self.env['ams.card'].get_api_model(self.api_type)

    @property
    def api_access_group_model(self):
        return self.env['ams.access_group'].get_api_model(self.api_type)

    @property
    def api_card_type_model(self):
        return self.env['ams.card_type'].get_api_model(self.api_type)

    @property
    def api_user_group_model(self):
        return self.env['ams.user_group'].get_api_model(self.api_type)



    # endregion
    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_send_invitation_email(self):
        self.sudo()._send_invitation_email()

        # Check if all emails are sent
        all_sent = all(
            visitor.sudo().email_state == 'sent'
            for visitor in self.invitation_id.invitation_visitor_ids.sudo()
        )

        # Update invitation Email state only if all emails are sent
        if all_sent:
            self.invitation_id.email_state = 'sent'
        else:
            self.invitation_id.email_state = 'failed'

    def _send_invitation_email(self):
        """Send email using selected template or default template with PDF attachment"""
        self.ensure_one()
        try:
            # Use selected template or get default template
            template = self.badge_report_template

            if not template:
                raise UserError(_('Email template not found'))

            # Update template from database to get latest changes
            self.env.registry.clear_cache()

            # Add PDF Report
            report_action = self.badge_report_action
            if report_action:
                # Remove old attachments from template
                template.attachment_ids.unlink()

                report = self.env['ir.actions.report'].with_context(lang=self.env.user.lang)
                pdf_content = report._render_qweb_pdf(report_action, self.ids)[0]
                # html = report._render_qweb_html(self.ids)[0]
                # print(html[:500])
                attachment_vals = {
                    'name': f'Invitation Badge - {self.name}.pdf',
                    'type': 'binary',
                    'datas': base64.b64encode(pdf_content),
                    'res_model': self._name,
                    'res_id': self.id,
                    'mimetype': 'application/pdf',
                }
                attachment = self.env['ir.attachment'].create(attachment_vals)

                # self._save_pdf_and_upload_media(pdf_data= pdf_content , file_name= f'{self.qr_code}.pdf')
                self._generate_and_upload_qr_image_to_whatsapp()

                template.attachment_ids = [(4, attachment.id)]

                # Send email using template
                self.logger.info(f"Sending email to {template.email_to} , from {template.email_from} , subject {template.subject}")
                mail_id = template.with_context(lang=self.env.user.lang).send_mail(
                    self.id,
                    force_send=True,
                    raise_exception=True
                )

                # Clean old attachments from database
                # TODO remove : why we need?
                attachments_to_clean = self.env['ir.attachment'].search([
                    ('res_model', '=', self._name),  # Search only attachments related to this model
                    ('res_id', '=', self.id),  # Search only attachments related to this record
                    ('id', '!=', attachment.id),  # Exclude the newly created attachment
                    ('name', 'like', 'Invitation Badge%')  # Search only for invitation badge attachments
                ])
                if attachments_to_clean:
                    attachments_to_clean.unlink()

                # Check if email was sent successfully
                if mail_id:
                    self.email_state = 'sent'
                    # mail=self.env['mail.mail'].browse(mail_id)
                    # self.logger.info(f"Email created:{mail_id},{mail_id.state},{mail_id.email_to},{mail_id.email_from},{mail_id.subject}")
                    # if mail.state == 'sent':
                    #     self.email_state = 'sent'
                    # else:
                    #     self.email_state = 'failed'
                else:
                    self.email_state = 'failed'

        except Exception as e:
            self.email_state = 'failed'
            self.logger.error(f"Failed to send email: {str(e)}")
            # raise UserError(f"Failed to send email: {str(e)}")

    def action_confirm(self):
        self.sudo()._confirm()

        # Check if all visitors are confirmed
        all_confirmed = all(
            visitor.state == 'confirmed'
            for visitor in self.invitation_id.invitation_visitor_ids
        )

        # Update invitation state only if all visitors are confirmed
        if all_confirmed:
            self.invitation_id._update_state('confirmed')

    def _confirm(self):
        """Confirm visitor request and handle API synchronization for user and card."""
        if not self.visitor_id:
            raise UserError(_("Visitor Not Found"))

        # Step 1: Handle User Creation/Update
        api_user = self._handle_api_user_sync()

        # Step 2: Handle Card Creation/Update
        self._handle_api_card_sync(api_user.id)

        # Step 3: Create confirmed visit
        self.visit_id = self._create_confirmed_visit()

        self.state = 'confirmed'

    def action_reset(self):
        # Reset visitor request
        return self.sudo().write({
            'state': 'pending',
            'email_state': 'pending',
            'visit_id': '',
            'qr_code': self._generate_unique_numeric_qr_code()
        })

    def action_request_approve(self):
        self.state = 'need_approval'

    def action_approve(self):
        # check if auto generate qr code per request is enabled
        if self.env.company.auto_generate_qr_code:
                self.visitor_id._assign_qr_code(self.qr_code)
        else:
                self.qr_code = self.visitor_id.qr_code

        self.state = 'approved'

    def action_reject(self):
        self.state = 'rejected'

    def action_cancel(self):
        self.state = 'cancelled'

    def _create_confirmed_visit(self):
        """Create a confirmed visit from the invitation data."""
        # Get current datetime for request_date
        current_datetime = fields.Datetime.now()

        return self.sudo().env['ams_vm.visit'].create({
            # Basic fields from base_request
            'name': 'New',  # Will be generated by sequence
            'subject': self.invitation_id.subject,
            'start_date': self.invitation_id.start_date,
            'duration': self.invitation_id.duration,
            'request_date': current_datetime,
            'state': 'confirmed',
            'status': 'pending',
            'email_state': 'pending',
            'end_date': self.invitation_id.end_date,  # Copy end_date directly from invitation
            'qr_code': self.qr_code,  # Copy qr_code directly from invitation_visitor

            # Relational fields from base_request
            'request_uid': self.invitation_id.request_uid.id,
            'response_uid': self.env.uid,  # Current user as responder

            # Fields from invitation
            'invitation_id': self.invitation_id.id,
            'visitor_id': self.visitor_id.id,

            # 'room_name': self.room_name,
            # 'location_address': self.location_address,
            # 'host_work_mobile': self.host_work_mobile,
            # 'host_work_phone': self.host_work_phone,
        })

    def _handle_api_user_sync(self):
        """Handle API user creation/update and synchronization."""
        # Prepare user record values
        user_vals = self._prepare_api_user_record_vals()

        # Create or update user
        api_user =  self.api_user_model.create_or_update(user_vals,synced=False)

        # Push user to external system
        api_user.action_push_user()

        # Check for errors
        if api_user.error:
            raise UserError(_(api_user.error_msg))

        return api_user  # Return the created or updated user to use in step 2

    def _handle_api_card_sync(self, api_user_id):
        """Handle API card creation/update and synchronization."""
        # fix qr_code in case multiple request approved and need confirm , update visitor qr_code with
        if self.qr_code != self.visitor_id.qr_code:
            self.logger.warning(f"QR Code mismatch, updating visitor qr_code from {self.visitor_id.qr_code} to {self.qr_code}")
            self.visitor_id._assign_qr_code(qr_code=self.qr_code)

        if not self.visitor_id.card_id:
            # Prepare card record values
            card_vals = self._prepare_api_card_record_vals(api_user_id)

            # Create card
            api_card = self.api_card_model.create(card_vals)

            # Push card to external system
            ams_card = api_card.action_push_card()

            # Check for errors
            if api_card.error:
                raise UserError(_(api_card.error_msg))

            # link card to visitor
            self.visitor_id.card_id = ams_card.id

    def _prepare_api_user_record_vals(self):
        """Prepare api user record values using visitor and request data"""
        visitor = self.visitor_id
        user_record_vals = {
            'name': visitor.name,
            'enroll_number': visitor.enroll_number,
            'user_type': 'visitor',
            'start_datetime': self.invitation_id.start_date,
            'end_datetime': self.invitation_id.end_date,
            'user_group_id': self.api_user_group_model.get_res_id(self.env.company.visitors_group_id.record_id),
            'email': visitor.email,
            'card_number': visitor.qr_code,
            'record_id': visitor.enroll_number,
            'activate': True
        }
        # Update ac_group_ids field with Command.link or record IDs
        if self.invitation_id.access_groups_ids:
            self.api_access_group_model.get_res_ids(self.invitation_id.access_groups_ids.record_ids)
            user_record_vals['ac_group_ids'] = self.api_access_group_model.get_res_ids(self.invitation_id.access_groups_ids.record_ids)

        return user_record_vals

    def _prepare_api_card_record_vals(self, user_id):
        """Prepare api card record values using visitor and request data"""
        card_record_vals = {
            'name': f'{self.visitor_id.name}-{self.visitor_id.qr_code}', # TODO when sync if record exist not assign name
            'card_number': self.visitor_id.qr_code,
            'card_type_id': self.api_card_type_model.get_res_id('6'),
        }

        if user_id:
            card_record_vals['user_id'] = user_id

        return card_record_vals

    def action_print_badge(self):
        """Print the invitation badge for the visitor"""
        self.ensure_one()
        report_action = self.badge_report_action
        return report_action.report_action(self.ids, config= False)

    def _generate_and_upload_qr_image_to_whatsapp(self):
        pass

    def _save_pdf_and_upload_media(self, pdf_data, file_name):
        pass

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
