# -*- coding: utf-8 -*-

from odoo import models, api
from odoo.http import request


class IrHttp(models.AbstractModel):
    _inherit = 'ir.http'

    def session_info(self):
        """Override session_info to dynamically set home_action_id based on user groups and system parameters"""
        result = super(IrHttp, self).session_info()
        
        # Check if user is logged in and has a valid session
        if request and hasattr(request, 'session') and request.session.uid:
            user = request.env.user
            
            # Check if user doesn't have a direct action_id set
            if not user.action_id:
                # Check if user belongs to the employee request group
                if user.has_group('ams_vm.group_employee_request'):
                    # First, try to get dashboard action from system parameter
                    dashboard_action_xmlid = request.env['ir.config_parameter'].sudo().get_param(
                        'ams_vm.default_dashboard_action', 
                        'ams_vm.action_client_dashboard_home'  # fallback to current default
                    )
                    
                    # Get the AMS dashboard action
                    dashboard_action = request.env.ref(dashboard_action_xmlid, raise_if_not_found=False)
                    if dashboard_action:
                        # Dynamically set the home_action_id in session
                        result['home_action_id'] = dashboard_action.id
                        
        return result