from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ResCompany(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "res.company"
    _inherit = ["res.company"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    api_type = fields.Selection([('suprema', 'Suprema'), ('zk', 'ZKTeco')], string="API Type", readonly=True,
                                default='suprema')
    dashboard_up_coming_domain = fields.Selection([
        ('today', 'Today'),
        ('tomorrow', 'Tomorrow'),
        ('nextWeek', 'Next Week'),
        ('nextMonth', 'Next Month'),
        ('all', 'All')
    ], string="Dashboard Upcoming Domain", default='all', required=True)
    auto_generate_qr_code = fields.Boolean(
        string="Auto Generate QR Code",
        help="Auto generate QR code Per Request",
    )
    required_manager_approval = fields.Boolean(
        string="Required Manager Approval",
        help="Always needs manager approval",
    )
    required_approval_after_time = fields.Float(
        string="Required Approval After Time",
        help="Time after which manager approval is required",
        default=17
    )
    request_max_days = fields.Integer(
        string="Request Max Days",
        help="Maximum number of days for which request can be raised",
        default=1
    )
    visit_max_hours = fields.Integer(
        string="Visit Max Hours",
        help="Maximum number of hours for which visit can be raised",
        default=12
    )
    # endregion

    # region  Special
    # endregion

    # region  Relational
    email_template_id = fields.Many2one(
        'mail.template',
        string='Email Template',
        domain=[('model', '=', 'ams_vm.invitation_visitor')],
        help="Select email template to be used when sending email"
    )

    badge_report_id = fields.Many2one(
        'ir.actions.report',
        string='Badge Report Attachment',
        domain=[('model', '=', 'ams_vm.invitation_visitor')],
        help="Select default badge report to include in email attachment "
    )
    visitors_group_id = fields.Many2one(
        'ams.user_group',
        string='Visitors Group',
        help="Select group to be used for visitors"
    )

    # endregion
    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.onchange('request_max_days', 'visit_max_hours', 'required_approval_after_time')
    def _check_field_constraints(self):
        for record in self:
            if record.request_max_days <= 0:
                raise ValidationError(_("Request Max Days must be greater than 0."))
            if not (1 <= record.visit_max_hours <= 24):
                raise ValidationError(_("Visit Max Hours must be between 1 and 24."))
            if not (0 <= record.required_approval_after_time < 24):
                raise ValidationError(_("Required Approval After Time must be between 0 and 23.59"))  # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
