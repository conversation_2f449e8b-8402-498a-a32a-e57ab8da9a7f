# -*- coding: utf-8 -*-


from odoo import models, fields, api, Command, _
from odoo.exceptions import UserError


class ResUser(models.Model):
    """Predefined Odoo module addons/base/res_users"""

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "res.users"

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    @api.model_create_multi
    def create(self, vals):

        if isinstance(vals, dict):
            vals = [vals] # convert dict to list of dicts

        res = super(ResUser, self).create(vals)

        readonly_group = self.env.ref('ams_base.ams_group_readonly')
        employee_request = self.env.ref('ams_vm.group_employee_request')

        # res.groups_id = [Command.link(readonly_group.id), Command.link(employee_request.id)]
        # Iterate over each created record and assign groups
        for user in res:
            user.groups_id = [Command.link(readonly_group.id), Command.link(employee_request.id)]

        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
