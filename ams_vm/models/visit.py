# -*- coding: utf-8 -*-
from odoo import models, fields


class Visit(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_vm.visit'
    _description = 'Visit'
    _inherit = 'ams_vm.base_request'

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(string="Visit No", required=True, readonly=True, index=True, default='New')
    # endregion

    # region  Special
    # endregion

    # region  Relational
    invitation_id = fields.Many2one('ams_vm.invitation', 'Invitation')
    invitation_qr_code = fields.Char('Invitation QR Code', related='invitation_id.qr_code', store=True, index=True)

    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def create(self, vals):
        if isinstance(vals, dict):
            vals = [vals]  # convert dict to list of dicts
        for val in vals:
            if val.get('name', 'New') == 'New':
                val['name'] = self.env['ir.sequence'].next_by_code('ams.seq_visit') or 'New'
        res = super(Visit, self).create(vals)
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
