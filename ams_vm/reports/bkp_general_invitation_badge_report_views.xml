<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Paper Format for Laplace Invitation Badge -->
        <record id="general_invitation_badge" model="report.paperformat">
            <field name="name">General Invitation Badge Format A4</field>
            <field name="default" eval="False"/>
            <field name="format">A4</field>
            <field name="page_height">0</field>
            <field name="page_width">0</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">10</field>
            <field name="margin_bottom">32</field>
            <field name="margin_left">7</field>
            <field name="margin_right">7</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">0</field>
            <field name="dpi">90</field>
        </record>

        <!-- Report Action -->
        <record id="general_invitation_badge_report_action" model="ir.actions.report">
            <field name="name">General Invitation Badge </field>
            <field name="model">ams_vm.invitation</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ams_vm.general_invitation_badge_template</field>
            <field name="report_file">ams_vm.general_invitation_badge_template</field>
            <field name="binding_model_id" ref="model_ams_vm_invitation"/>
            <field name="binding_type">report</field>
            <field name="paperformat_id" ref="general_invitation_badge"/>
        </record>

        <!-- Report Template -->
        <template id="general_invitation_badge_template">
            <t t-call="web.basic_layout">
                <style>
                    .info-item {
                        display: flex;
                        justify-content: space-between;
                        margin: 16px 0;
                        padding: 8px 0;
                        border-bottom: 1px solid #e5e7eb;
                    }

                    .profile-section {
                        display: flex;
                        justify-content: center;
                        margin-bottom: 24px;
                    }

            .profile-photo {
                width: 140px;
                height: 140px;
                border-radius: 50%;
                padding: 4px;
                background: white;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                border: 3px solid #3b82f6;
                overflow: hidden;
            }

            .profile-photo img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: cover;
                background-color: #f3f4f6;
            }
                </style>
                <t t-foreach="docs" t-as="doc">
                    <div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; page-break-after: always;">
                        <!-- Badge Container -->
                        <div style="width: 400px; height: 600px; display: flex; flex-direction: column; align-items: center; background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%); border-radius: 12px; box-shadow: 0 8px 24px rgba(0,0,0,0.12); padding: 24px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; overflow: hidden;">
                            <!-- Profile Section -->
                            <div class="profile-section">
                                <!-- Profile Photo -->
                                <div class="profile-photo">
                                    <img t-att-src="image_data_uri(doc.visitor_id.image_1920)" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;"/>
                                </div>
                            </div>

                            <!-- Visitor Info -->
                            <h1 style="font-size: 28px; font-weight: 600; text-align: center; margin: 40px; color: #1f2937;"><t t-esc="doc.visitor_id.name"/></h1>
                            <table style="width: 100%; text-align: left; font-size: 14px; margin-bottom: 20px;">
                                <tr class="info-item">
                                    <td>Check-in Time:</td>
                                    <td><strong><t t-esc="doc.start_date"/></strong></td>
                                </tr>
                                <tr class="info-item">
                                    <td>Check-out Time:</td>
                                    <td> <strong> <t t-esc="doc.end_date"/></strong></td>
                                </tr>
                                <tr style="display: flex; margin: 16px 0 ; padding: 8px 0; justify-content: space-between;" >
                                    <td>Reference #:</td>
                                    <td> <strong> <t t-esc="doc.name"/></strong> </td>
                                </tr>
                            </table>

                            <!-- QR Code -->
                            <div style="display: flex; justify-content: center; align-items: center; padding: 16px; background: rgba(249, 250, 251, 0.8); border-radius: 8px; border: 1px solid #e5e7eb;">
                                <img t-attf-src="/report/barcode/QR/{{doc.qr_code }}?&amp;width=150&amp;height=150&amp;quiet=0" style="width: 150px; height: 150px; background: white; padding: 8px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);" />
                            </div>
                        </div>
                    </div>
                </t>
            </t>
        </template>
    </data>
</odoo>
