<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Paper Format for Badge -->
        <record id="paperformat_invitation_badge" model="report.paperformat">
            <field name="name">Invitation Badge Format A4</field>
            <field name="default" eval="True"/>
            <field name="format">A4</field>
            <field name="page_height">0</field>
            <field name="page_width">0</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">10</field>
            <field name="margin_bottom">32</field>
            <field name="margin_left">7</field>
            <field name="margin_right">7</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">0</field>
            <field name="dpi">90</field>
        </record>

        <!-- Report Action -->
        <record id="invitation_report_action" model="ir.actions.report">
            <field name="name">One Time Invitation Badge</field>
            <field name="model">ams_vm.invitation</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ams_vm.invitation_report_template</field>
            <field name="report_file">ams_vm.invitation_report_template</field>
            <field name="print_report_name">'Invitation_Badge- %s' % object.name</field>
            <field name="binding_model_id" ref="model_ams_vm_invitation"/>
            <field name="binding_type">report</field>
            <field name="paperformat_id" ref="paperformat_invitation_badge"/>
        </record>

        <!-- Report Template -->
        <template id="invitation_report_template">
            <t t-call="web.basic_layout">
                <t t-foreach="docs" t-as="doc">
                    <!-- Wrapper for centering the badge -->
                    <div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;  page-break-after: always;">

                        <!-- Badge Container -->
                        <div t-set="css"
                             t-value="'width: 400px; height: 600px; display: flex; flex-direction: column; align-items: center; background-image: url(\'/ams_vm/static/src/img/badge_bg.jpg\'); background-size: cover; background-position: center; direction: rtl;'"/>

                        <div t-att-style="css" class="mt-5">
                            <!-- QR Code with updated style -->
                            <img t-attf-src="/report/barcode/QR/{{ doc.qr_code }}?&amp;width=200&amp;height=200&amp;quiet=0"
                                 alt="QR Code"
                                 style="width: 200px; height: 200px; margin-bottom: 20px; margin-top: 100px;"/>

                            <!-- Table -->
                            <table style="width: 100%; text-align: right; font-size: 14px; border-collapse: collapse;margin-right: 200px; margin-top: 10px;">
                                <tr>
                                    <td><strong>الاسم كامل:</strong></td>
                                    <td t-esc="doc.visitor_id.name"></td>
                                </tr>
                                <tr>
                                    <td><strong>قاعة الاجتماع:</strong></td>
                                    <td t-esc="doc.room_name"></td>
                                </tr>
                                <tr>
                                    <td><strong>الدور:</strong></td>
                                    <td t-esc="doc.location_address"></td>
                                </tr>
                                <tr>
                                    <td><strong>وقت الزيارة:</strong></td>
                                    <td t-esc="doc.start_date"></td>
                                </tr>
                                <tr>
                                    <td><strong>وقت الخروج:</strong></td>
                                    <td t-esc="doc.end_date"></td>
                                </tr>
                                <tr>
                                    <td><strong>رقم المرجع:</strong></td>
                                    <td t-esc="doc.name"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </t>
            </t>
        </template>
    </data>
</odoo>