<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Paper Format for Laplace Invitation Badge -->
        <record id="general_invitation_badge" model="report.paperformat">
            <field name="name">General Invitation Badge Format A4</field>
            <field name="default" eval="False"/>
            <field name="format">A4</field>
            <field name="page_height">0</field>
            <field name="page_width">0</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">10</field>
            <field name="margin_bottom">32</field>
            <field name="margin_left">7</field>
            <field name="margin_right">7</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">0</field>
            <field name="dpi">90</field>
        </record>

        <!-- Report Action -->
        <record id="general_invitation_badge_report_action" model="ir.actions.report">
            <field name="name">General Invitation Badge</field>
            <field name="model">ams_vm.invitation_visitor</field>
            <field name="report_type">qweb-html</field>
            <field name="report_name">ams_vm.general_invitation_badge_template</field>
            <field name="report_file">ams_vm.general_invitation_badge_template</field>
            <field name="binding_model_id" ref="model_ams_vm_invitation_visitor"/>
            <field name="binding_type">report</field>
            <field name="paperformat_id" ref="general_invitation_badge"/>
        </record>
        <template id="general_invitation_badge_template">
            <t t-call="web.basic_layout">
                <style>
                    @page {
                        margin: 0;
                    }

                    body {
                        margin: 5px;
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        direction: rtl;
                    }

                    .badge-container {
                        width: 400px;
                        height: 610px;
                        background-color: #f9f9f9;
                        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
                        margin: 10px auto;
                        position: relative;
                        page-break-after: always;


                    }

                    .card-header {
                        width: 100%;
                        background-color: #00cba9;
                        color: #fff;
                        text-align: center;
                        font-size: 18px;
                        font-weight: bold;
                        padding: 5px 0;
                        border-radius: 15px 15px 0 0 !important;


                    }

                    .profile-section {
                        text-align: center;
                        margin-top: 10px;

                    }

                    .profile-photo {
                        width: 120px;
                        height: 120px;
                        margin: 0 5px;
                        border-radius: 50%;
                        background: #f3f4f6;
                        display: inline-block;
                        overflow: hidden;
                        box-shadow: 3px 5px rgba(36, 35, 35, 0.1);
                    }

                    .profile-photo img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    /* Visitor Info */
                    .visitor-info {
                        margin-right: 30px;
                        text-align: right;

                    }

                    .visitor-info table {
                        width: 100%;
                        border-collapse: collapse;
                        font-size: 14px;

                    }

                    .visitor-info td {
                        padding: 8px;
                        border-bottom: 1px solid #eff2f7;
                        text-align: right;

                    }

                    .visitor-info tr:last-child td {
                        border-bottom: none;
                    }

                    .visitor-info .label {
                        font-weight: bold;
                        text-align: center;
                        white-space: nowrap; /* Prevent text wrapping */

                    }


                    .qr-code {
                        text-align: center;
                        padding: 15px;
                        background-color: #f9f9f9;
                        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
                        margin: 0;
                    }


                    .qr-code img {
                        border-radius: 8px;
                        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
                    }

                    .card-footer {
                        position: relative;
                        width: 100%;
                        text-align: center;
                        font-size: 14px;
                        font-weight: bold;
                        overflow: hidden;
                        height: 50px;
                        background-color: #f9f9f9;
                        border-radius: 0 0 15px 15px !important;
                    }

                    .card-footer img {
                        position: absolute;
                        top: -20px;
                        left: 0;
                        width: 100%;
                    }
                </style>

                <t t-foreach="docs" t-as="doc">
                    <div class="badge-container">
                        <div class="card-header">
                            بطاقة دعوة
                        </div>

                        <div class="profile-section">
                            <div class="profile-photo">
                                <t t-if="doc.visitor_id.image_1920">
                                    <img t-att-src="image_data_uri(doc.visitor_id.image_1920)" alt="Profile Photo"/>
                                </t>
                                <t t-else="">
                                    <i class="fa fa-user-circle" style="font-size: 120px; color: #ccc;"></i>
                                </t>
                            </div>
                        </div>

                        <div class="visitor-info">
                            <table>
                                <tr>
                                    <td class="label">الاسم :</td>
                                    <td t-esc="doc.visitor_id.name"></td>
                                </tr>
                                <tr>
                                    <td class="label">قاعة الاجتماع:</td>
                                    <td t-esc="doc.invitation_id.room_name"></td>
                                </tr>
                                <tr>
                                    <td class="label">الدور:</td>
                                    <td t-esc="doc.invitation_id.location_address"></td>
                                </tr>
                                <tr>
                                    <td class="label">وقت الزيارة:</td>
                                    <td t-esc="context_timestamp(doc.invitation_id.start_date).strftime('%Y-%m-%d %H:%M')"></td>
                                </tr>
                                <tr>
                                    <td class="label">وقت الخروج:</td>
                                    <td t-esc="context_timestamp(doc.invitation_id.end_date).strftime('%Y-%m-%d %H:%M')"></td>
                                </tr>
                                <tr>
                                    <td class="label">رقم المرجع:</td>
                                    <td t-esc="doc.invitation_id.name"></td>
                                </tr>
                            </table>
                        </div>

                        <div class="qr-code">
                            <img t-attf-src="/report/barcode/QR/{{ doc.qr_code }}?&amp;width=200&amp;height=200&amp;quiet=0"
                                 alt="QR Code"/>

                        </div>
                        <div class="card-footer">
                            <img t-att-src="'/ams_vm/static/src/img/wave.png'" alt="Wave Footer"/>
                        </div>

                    </div>
                </t>
            </t>
        </template>

    </data>
</odoo>


