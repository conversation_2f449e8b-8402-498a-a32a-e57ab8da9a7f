<?xml version="1.0" encoding="utf-8"?>
<odoo>
     <!-- Create Category for the Groups -->
    <record model="ir.module.category" id="ams_vm_group_category">
        <field name="name">Visit Management GROUPS</field>
        <field name="description">Visit Management GROUPS</field>
        <field name="sequence">10</field>
    </record>

     <!-- Security Approval Group -->
    <record id="group_security_approval" model="res.groups">
        <field name="name">Security Approval</field>
        <field name="category_id" ref="ams_vm_group_category"/>
    </record>

    <!-- Employee Request Group -->
    <record id="group_employee_request" model="res.groups">
        <field name="name">Employee Request</field>
        <field name="category_id" ref="ams_vm_group_category"/>
    </record>

     <record id="group_event_request" model="res.groups">
        <field name="name">Event Request</field>
        <field name="category_id" ref="ams_vm_group_category"/>
    </record>

    <record id="group_event_manager" model="res.groups">
        <field name="name">Event Manager</field>
        <field name="category_id" ref="ams_vm_group_category"/>
    </record>

<!--    rule for employee group-->
<!--    <record id="employee_request_rule" model="ir.rule">-->
<!--    <field name="name">Employee Own Invitations</field>-->
<!--    <field name="model_id" ref="model_ams_vm_invitation"/>-->
<!--    <field name="domain_force">[('create_uid', '=', user.id)]</field>-->
<!--    <field name="groups" eval="[(4, ref('group_employee_request'))]"/>-->
<!--</record>-->

        <!-- Record Rule for HR Employee -->



</odoo>

