<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="0">
<!--    <record id="hr_employee_rule_own_record" model="ir.rule">-->
<!--        <field name="name">HR Employee VM: Edit Own Record In Visitor Request </field>-->
<!--        <field name="model_id" ref="hr.model_hr_employee"/>-->
<!--        <field name="domain_force">[('user_id', '=', user.id)]</field>-->
<!--        <field name="groups" eval="[(4, ref('group_employee_request'))]"/>-->
<!--        <field name="perm_read" eval="True"/>-->
<!--        <field name="perm_write" eval="True"/>-->
<!--        <field name="perm_create" eval="False"/>-->
<!--        <field name="perm_unlink" eval="False"/>-->
<!--    </record>-->
</odoo>
