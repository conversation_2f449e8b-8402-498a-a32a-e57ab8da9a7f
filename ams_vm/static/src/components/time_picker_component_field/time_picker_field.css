/* AMS VM Time Picker Component Styles */
.ams_vm_time_picker_field {
    position: relative;
}

.ams_vm_time_picker {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1050;
    padding: 1rem;
    min-width: 320px;
}

.ams_vm_time_picker .form-label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.ams_vm_time_picker .input-group-sm .form-control {
    font-size: 0.875rem;
    padding: 0.375rem 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.ams_vm_time_picker .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
    background-color: #f8f9fa;
    color: #495057;
    transition: all 0.15s ease-in-out;
}

.ams_vm_time_picker .btn-sm:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}

.ams_vm_time_picker .btn-sm:active {
    background-color: #dee2e6;
    border-color: #adb5bd;
    transform: translateY(1px);
}

.ams_vm_time_picker .btn-outline-primary {
    border-color: #0d6efd;
    color: #0d6efd;
    background-color: transparent;
}

.ams_vm_time_picker .btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.ams_vm_time_picker .btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
    background-color: transparent;
}

.ams_vm_time_picker .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.ams_vm_time_picker input[type="number"] {
    -moz-appearance: textfield;
    text-align: center;
    font-weight: 500;
}

.ams_vm_time_picker input[type="number"]::-webkit-outer-spin-button,
.ams_vm_time_picker input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.ams_vm_time_picker_field .input-group {
    border-radius: 0.25rem;
    display: flex;
}

.ams_vm_time_picker_field .input-group .btn {
    border: 1px solid #ced4da;
    z-index: 2;
    position: relative;
}

.ams_vm_time_picker_field .input-group .btn:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-right: none;
}

.ams_vm_time_picker_field .input-group .btn:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-left: none;
}

.ams_vm_time_picker_field .input-group .o_input {
    border: 1px solid #ced4da;
    border-radius: 0;
    z-index: 1;
    flex: 1;
}

.ams_vm_time_picker_field .input-group .o_input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    z-index: 3;
}

.ams_vm_time_picker .gap-1 {
    gap: 0.25rem;
}

.ams_vm_time_picker .gap-2 {
    gap: 0.5rem;
}

.ams_vm_time_picker .text-muted {
    color: #6c757d !important;
}

.ams_vm_time_picker .quick-time-buttons {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.ams_vm_time_picker .done-button {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
    text-align: center;
}