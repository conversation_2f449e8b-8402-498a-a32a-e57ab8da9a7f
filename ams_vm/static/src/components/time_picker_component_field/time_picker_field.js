import { _t } from "@web/core/l10n/translation";
import { registry } from "@web/core/registry";
import { formatFloatTime } from "@web/views/fields/formatters";
import { parseFloatTime } from "@web/views/fields/parsers";
import { useInputField } from "@web/views/fields/input_field_hook";
import { standardFieldProps } from "@web/views/fields/standard_field_props";
import { useNumpadDecimal } from "@web/views/fields/numpad_decimal_hook";
import { Component, useState, useRef, useEffect } from "@odoo/owl";

export class TimePickerField extends Component {
    static template = "ams_vm.TimePickerField";
    static props = {
        ...standardFieldProps,
        placeholder: { type: String, optional: true },
        displaySeconds: { type: Boolean, optional: true },
    };
    static defaultProps = {
        displaySeconds: false,
    };

    // Translation dictionary
    translations = {
        'Hours': _t('Hours'),
        'Minutes': _t('Minutes'),
        'Done': _t('Done'),
    };

    setup() {
        this.state = useState({
            showPicker: false,
            hours: 0,
            minutes: 0,
        });
        
        this.inputRef = useRef("timeInput");
        
        this.inputFloatTimeRef = useInputField({
            getValue: () => this.formattedValue,
            refName: "timeInput",
            parse: (v) => parseFloatTime(v),
        });
        useNumpadDecimal();
        
        // Handle document clicks to close picker
        useEffect(() => {
            const handleDocumentClick = (event) => {
                if (this.state.showPicker && 
                    !event.target.closest('.ams_vm_time_picker_field')) {
                    this.closePicker();
                }
            };
            
            document.addEventListener('click', handleDocumentClick);
            return () => {
                document.removeEventListener('click', handleDocumentClick);
            };
        }, () => [this.state.showPicker]);
        
        // Initialize time from current value
        this.updateTimeFromValue();
    }

    get formattedValue() {
        return formatFloatTime(this.props.record.data[this.props.name], {
            displaySeconds: this.props.displaySeconds,
        });
    }

    get currentValue() {
        return this.props.record.data[this.props.name] || 0;
    }

    updateTimeFromValue() {
        const totalHours = this.currentValue;
        this.state.hours = Math.floor(totalHours);
        this.state.minutes = Math.round((totalHours - this.state.hours) * 60);
    }

    togglePicker() {
        if (this.props.readonly) return;
        if (this.state.showPicker) {
            this.closePicker();
        } else {
            this.openPicker();
        }
    }

    closePicker() {
        this.state.showPicker = false;
    }

    onHourChange(event) {
        const hours = parseInt(event.target.value) || 0;
        this.state.hours = Math.max(0, Math.min(23, hours));
        this.updateValue();
    }

    onMinuteChange(event) {
        const minutes = parseInt(event.target.value) || 0;
        this.state.minutes = Math.max(0, Math.min(59, minutes));
        this.updateValue();
    }

    incrementHour() {
        this.state.hours = (this.state.hours + 1) % 24;
        this.updateValue();
    }

    decrementHour() {
        this.state.hours = this.state.hours > 0 ? this.state.hours - 1 : 23;
        this.updateValue();
    }

    incrementMinute() {
        if (this.state.minutes >= 59) {
            this.state.minutes = 0;
            this.incrementHour();
        } else {
            this.state.minutes += 1;
        }
        this.updateValue();
    }

    decrementMinute() {
        if (this.state.minutes <= 0) {
            this.state.minutes = 59;
            this.decrementHour();
        } else {
            this.state.minutes -= 1;
        }
        this.updateValue();
    }

    updateValue() {
        const floatTime = this.state.hours + (this.state.minutes / 60);
        this.props.record.update({ [this.props.name]: floatTime });
    }

    onInputClick() {
        if (!this.props.readonly && !this.state.showPicker) {
            this.openPicker();
        }
    }

    onInputFocus() {
        if (!this.props.readonly && !this.state.showPicker) {
            this.openPicker();
        }
    }

    onInputBlur(event) {
        // Document click handler will manage closing the picker
        // This prevents conflicts and flicker issues
    }

    openPicker() {
        this.state.showPicker = true;
        this.updateTimeFromValue();
    }

    onPickerClick(event) {
        // Prevent picker from closing when clicking inside it
        event.stopPropagation();
    }

    getTranslation(key) {
        return this.translations[key] || key;
    }
}

export const timePickerField = {
    component: TimePickerField,
    displayName: _t("Time Picker"),
    supportedOptions: [
        {
            label: _t("Display seconds"),
            name: "display_seconds",
            type: "boolean",
        },
    ],
    supportedTypes: ["float"],
    isEmpty: () => false,
    extractProps: ({ attrs, options }) => ({
        displaySeconds: options.displaySeconds,
        placeholder: attrs.placeholder,
    }),
};

registry.category("fields").add("time_picker", timePickerField);