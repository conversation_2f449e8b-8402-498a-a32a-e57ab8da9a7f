<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="ams_vm.TimePickerField">
        <div class="ams_vm_time_picker_field position-relative">
            <!-- Input field -->
            <div class="input-group">
                <input 
                    t-if="!props.readonly" 
                    t-ref="timeInput" 
                    t-att-id="props.id" 
                    type="text" 
                    class="o_input form-control" 
                    autocomplete="off"
                    t-att-placeholder="props.placeholder || 'HH:MM'"
                    t-on-click="onInputClick"
                    t-on-focus="onInputFocus"
                    t-on-blur="onInputBlur"
                />
                <span t-if="props.readonly" t-esc="formattedValue" class="o_field_char" />
                <button 
                    t-if="!props.readonly" 
                    type="button" 
                    class="btn btn-outline-secondary"
                    t-on-click.prevent="togglePicker"
                    title="Open time picker"
                >
                    <i class="fa fa-clock-o"/>
                </button>
            </div>

            <!-- Time picker dropdown -->
            <div t-if="state.showPicker and !props.readonly" class="ams_vm_time_picker dropdown-menu show position-absolute mt-1" t-on-click="onPickerClick">
                <div class="row g-2">
                    <!-- Hours -->
                    <div class="col-6">
                        <label class="form-label small text-muted" t-esc="this.getTranslation('Hours')"/>
                        <div class="input-group input-group-sm">
                            <button type="button" class="btn btn-sm" t-on-click="decrementHour">
                                <i class="fa fa-minus"/>
                            </button>
                            <input 
                                type="number" 
                                class="form-control text-center" 
                                t-att-value="state.hours"
                                min="0" 
                                max="23"
                                t-on-input="onHourChange"
                            />
                            <button type="button" class="btn btn-sm" t-on-click="incrementHour">
                                <i class="fa fa-plus"/>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Minutes -->
                    <div class="col-6">
                        <label class="form-label small text-muted" t-esc="this.getTranslation('Minutes')"/>
                        <div class="input-group input-group-sm">
                            <button type="button" class="btn btn-sm" t-on-click="decrementMinute">
                                <i class="fa fa-minus"/>
                            </button>
                            <input 
                                type="number" 
                                class="form-control text-center" 
                                t-att-value="state.minutes"
                                min="0" 
                                max="59"
                                t-on-input="onMinuteChange"
                            />
                            <button type="button" class="btn btn-sm" t-on-click="incrementMinute">
                                <i class="fa fa-plus"/>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Quick time buttons -->
                <div class="quick-time-buttons">
                    <div class="d-flex flex-wrap gap-2">
                        <button type="button" class="btn btn-sm btn-outline-primary" t-on-click="() => { this.state.hours = 0; this.state.minutes = 30; this.updateValue(); }">
                            0:30
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" t-on-click="() => { this.state.hours = 1; this.state.minutes = 0; this.updateValue(); }">
                            1:00
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" t-on-click="() => { this.state.hours = 1; this.state.minutes = 30; this.updateValue(); }">
                            1:30
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" t-on-click="() => { this.state.hours = 2; this.state.minutes = 0; this.updateValue(); }">
                            2:00
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" t-on-click="() => { this.state.hours = 4; this.state.minutes = 0; this.updateValue(); }">
                            4:00
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" t-on-click="() => { this.state.hours = 8; this.state.minutes = 0; this.updateValue(); }">
                            8:00
                        </button>
                    </div>
                </div>
                
                <!-- Close button -->
                <div class="done-button">
                    <button type="button" class="btn btn-sm btn-secondary" t-on-click="closePicker">
                        <i class="fa fa-check me-1"/><span t-esc="this.getTranslation('Done')"/>
                    </button>
                </div>
            </div>
        </div>
    </t>

</templates>