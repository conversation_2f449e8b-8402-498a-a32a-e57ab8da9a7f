# Dashboard Last Update Time Feature

## Overview

The dashboard includes a real-time "Last updated" indicator that shows when the dashboard data was last refreshed. This feature provides users with transparency about data freshness and helps them understand when information was last synchronized.

## Features

### 🕒 **Smart Time Formatting**
- **Just now**: For updates within the last 60 seconds
- **X minutes ago**: For updates between 1-59 minutes
- **X hours ago**: For updates between 1-23 hours  
- **Full timestamp**: For updates older than 24 hours

### 🔄 **Real-time Updates**
- Automatically updates when dashboard items refresh their data
- Updates every 30 seconds to refresh the time display
- Reactive to all dashboard data changes

### 🌐 **Multilingual Support**
- English: "Just now", "5 minutes ago", "2 hours ago"
- Arabic: "الآن", "منذ 5 دقيقة", "منذ 2 ساعة"

## Technical Implementation

### Architecture

```
Dashboard Items (fetchDomain/fetchCount) 
    ↓ (triggers auto_refresh event)
Bus Service (auto_refresh channel)
    ↓ (listens for events)
Main Dashboard (updates lastUpdated state)
    ↓ (reactive getter)
UI Display (formattedLastUpdated)
```

### Key Components

#### 1. Main Dashboard Component (`dashboard.js`)

**State Management:**
```javascript
this.state = useState({
    lastUpdated: new Date(),    // When data was last updated
    currentTime: new Date()     // Current time for calculations
});
```

**Bus Service Listener:**
```javascript
const dashboardRefreshListener = async (payload) => {
    this.state.lastUpdated = new Date();
    this.state.currentTime = new Date();
};

this.busService.subscribe('auto_refresh', dashboardRefreshListener);
```

**Time Formatting Logic:**
```javascript
get formattedLastUpdated() {
    const now = this.state.currentTime;
    const diff = now - this.state.lastUpdated;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (seconds < 60) return _t("Just now");
    else if (minutes < 60) return _t("%s minutes ago", minutes);
    else if (hours < 24) return _t("%s hours ago", hours);
    else return this.state.lastUpdated.toLocaleString();
}
```

#### 2. Dashboard Item Component (`dashboard_item_count.js`)

**Update Triggers:**
```javascript
// In fetchDomain()
this.busService.trigger('auto_refresh', {
    model: this.props.model,
    timestamp: new Date(),
    source: 'fetchDomain'
});

// In fetchCount()
this.busService.trigger('auto_refresh', {
    model: this.props.model,
    timestamp: new Date(),
    source: 'fetchCount'
});
```

#### 3. Template Integration (`dashboard.xml`)

```xml
<div class="d-flex align-items-center text-muted" style="font-size: 13px;">
    <i class="fa fa-clock-o me-2"></i>
    <span>Last updated: <span t-esc="formattedLastUpdated"/></span>
</div>
```

### Lifecycle Management

**Initialization:**
- Sets initial `lastUpdated` and `currentTime` on component mount
- Starts 30-second interval timer for time display updates
- Subscribes to `auto_refresh` bus service events

**Updates:**
- Triggered when any dashboard item calls `fetchDomain()` or `fetchCount()`
- Updates both `lastUpdated` and `currentTime` states
- UI automatically re-renders due to reactive state

**Cleanup:**
- Clears interval timer on component unmount
- Unsubscribes from bus service events
- Prevents memory leaks

## Translations

### English (ams_vm.pot)
```po
msgid "Just now"
msgstr ""

msgid "%s minutes ago"
msgstr ""

msgid "%s hours ago"
msgstr ""
```

### Arabic (ar_001.po)
```po
msgid "Just now"
msgstr "الآن"

msgid "%s minutes ago"
msgstr "منذ %s دقيقة"

msgid "%s hours ago"
msgstr "منذ %s ساعة"
```

## User Experience

### Visual Design
- **Icon**: Clock icon (`fa-clock-o`) for immediate recognition
- **Typography**: Muted text color for secondary information
- **Position**: Right-aligned in the action bar for non-intrusive display
- **Size**: Small font (13px) to maintain hierarchy

### Behavior
- **Initial Load**: Shows "Just now" when dashboard first loads
- **Data Refresh**: Updates immediately when dashboard items refresh
- **Time Progression**: Automatically progresses through time intervals
- **Responsive**: Works on all screen sizes

## Benefits

### For Users
- **Transparency**: Know when data was last updated
- **Trust**: Confidence in data freshness
- **Context**: Understand if they're viewing current or stale data

### For Developers
- **Debugging**: Easy to see when dashboard updates occur
- **Monitoring**: Track dashboard refresh frequency
- **User Feedback**: Visual confirmation of system activity

## Maintenance

### Adding New Update Triggers
To add new update triggers from other components:

```javascript
// In any component that updates dashboard data
this.busService.trigger('auto_refresh', {
    model: 'your.model',
    timestamp: new Date(),
    source: 'your_method'
});
```

### Customizing Time Intervals
To change the display update frequency:

```javascript
// Change 30000 (30 seconds) to desired interval
this.timeInterval = setInterval(() => {
    this.state.currentTime = new Date();
}, 30000);
```

### Adding New Time Formats
To add new time format ranges:

```javascript
// Add new conditions in formattedLastUpdated getter
const days = Math.floor(hours / 24);
if (days < 7) return _t("%s days ago", days);
```

## Testing

### Manual Testing
1. Load dashboard and verify "Just now" appears
2. Wait for dashboard items to refresh (automatic)
3. Verify time progresses: "Just now" → "X minutes ago" → "X hours ago"
4. Test in both English and Arabic interfaces

### Automated Testing
- Unit tests for time calculation logic
- Integration tests for bus service communication
- UI tests for proper display and updates

---

**Last Updated**: January 2025  
**Version**: 1.0  
**Module**: ams_vm  
**Component**: Dashboard
