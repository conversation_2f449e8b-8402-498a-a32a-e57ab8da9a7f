<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev/svgjs" width="1920" height="1080" preserveAspectRatio="none" viewBox="0 0 1920 1080"><g mask="url(&quot;#SvgjsMask1100&quot;)" fill="none"><rect width="1920" height="1080" x="0" y="0" fill="url(&quot;#SvgjsLinearGradient1101&quot;)"></rect><path d="M733 408L732 36" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M306 445L305 917" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M987 801L986 1311" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M549 46L548 502" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M1785 422L1784 -93" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M458 975L457 457" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M1072 811L1071 461" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M1306 158L1305 -179" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1104&quot;)" stroke-linecap="round" class="Up"></path><path d="M478 745L477 346" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M1113 817L1112 464" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path><path d="M1128 549L1127 -43" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M193 702L192 1416" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M1889 897L1888 1385" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M978 632L977 114" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1104&quot;)" stroke-linecap="round" class="Up"></path><path d="M1729 408L1728 1026" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path><path d="M1635 146L1634 519" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path><path d="M222 577L221 211" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M231 147L230 611" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M827 749L826 1087" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M1098 415L1097 838" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1104&quot;)" stroke-linecap="round" class="Up"></path><path d="M1779 17L1778 -594" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M526 710L525 -72" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path><path d="M1231 44L1230 -453" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path><path d="M1138 157L1137 775" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path><path d="M1715 369L1714 734" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1104&quot;)" stroke-linecap="round" class="Up"></path><path d="M503 867L502 1674" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path><path d="M427 160L426 -331" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M1784 920L1783 1463" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1104&quot;)" stroke-linecap="round" class="Up"></path><path d="M1405 424L1404 -308" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path><path d="M1594 743L1593 406" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path><path d="M214 719L213 220" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M1216 979L1215 264" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M965 125L964 -654" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M1254 803L1253 1462" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M1264 745L1263 255" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1104&quot;)" stroke-linecap="round" class="Up"></path><path d="M1846 322L1845 -474" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M1382 254L1381 -340" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M261 968L260 1654" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M50 229L49 -563" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M782 471L781 -79" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M1691 137L1690 -638" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M1690 234L1689 -323" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path><path d="M1061 1L1060 708" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M1512 298L1511 -251" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1104&quot;)" stroke-linecap="round" class="Up"></path><path d="M555 848L554 1498" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M316 195L315 936" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1103&quot;)" stroke-linecap="round" class="Down"></path><path d="M1819 261L1818 -331" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1102&quot;)" stroke-linecap="round" class="Up"></path><path d="M165 936L164 455" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1105&quot;)" stroke-linecap="round" class="Down"></path></g><defs><mask id="SvgjsMask1100"><rect width="1920" height="1080" fill="#ffffff"></rect></mask><linearGradient x1="10.94%" y1="-19.44%" x2="89.06%" y2="119.44%" gradientUnits="userSpaceOnUse" id="SvgjsLinearGradient1101"><stop stop-color="#0e2a47" offset="0"></stop><stop stop-color="rgba(35, 65, 72, 1)" offset="1"></stop></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="SvgjsLinearGradient1102"><stop stop-color="rgba(87, 164, 157, 0)" offset="0"></stop><stop stop-color="rgba(87, 164, 157, 1)" offset="1"></stop></linearGradient><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="SvgjsLinearGradient1103"><stop stop-color="rgba(28, 83, 142, 0)" offset="0"></stop><stop stop-color="#1c538e" offset="1"></stop></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="SvgjsLinearGradient1104"><stop stop-color="rgba(28, 83, 142, 0)" offset="0"></stop><stop stop-color="#1c538e" offset="1"></stop></linearGradient><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="SvgjsLinearGradient1105"><stop stop-color="rgba(87, 164, 157, 0)" offset="0"></stop><stop stop-color="rgba(87, 164, 157, 1)" offset="1"></stop></linearGradient></defs></svg>