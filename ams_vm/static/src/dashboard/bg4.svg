<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev/svgjs" width="1920" height="1080" preserveAspectRatio="none" viewBox="0 0 1920 1080"><g mask="url(&quot;#SvgjsMask1026&quot;)" fill="none"><rect width="1920" height="1080" x="0" y="0" fill="url(&quot;#SvgjsLinearGradient1027&quot;)"></rect><path d="M473.61071245720103 414.8397742094747L417.5122250523732 218.4950682925774 291.2906283915107 344.71666495343993z" fill="rgba(28, 83, 142, 0.4)" class="triangle-float3"></path><path d="M1899.005,950.64C1985.509,951.255,2073.914,923.469,2119.096,849.7C2166.09,772.974,2162.889,674.099,2115.06,597.891C2069.935,525.992,1983.741,493.961,1899.005,499.02C1822.711,503.575,1763.618,556.25,1723.448,621.272C1680.501,690.789,1645.833,774.499,1684.268,846.608C1724.365,921.836,1813.76,950.034,1899.005,950.64" fill="rgba(28, 83, 142, 0.4)" class="triangle-float1"></path><path d="M624.1633174402238 612.9562173567268L807.0443901408582 560.5159136171987 636.943398181249 312.41445643601094z" fill="rgba(28, 83, 142, 0.4)" class="triangle-float2"></path><path d="M1673.894,903.42C1723.646,905.038,1774.294,888.922,1801.352,847.14C1830.734,801.77,1834.938,742.988,1808.221,695.999C1781.222,648.515,1728.505,624.114,1673.894,622.975C1617.004,621.789,1556.003,639.834,1529.499,690.186C1504.171,738.303,1529.54,794.268,1560.029,839.292C1586.207,877.95,1627.231,901.903,1673.894,903.42" fill="rgba(28, 83, 142, 0.4)" class="triangle-float2"></path><path d="M140.14372201976656 91.62511401680268L58.0901642175663-62.69518371762021-96.2301335168566 19.35837408458005-14.176575714656337 173.67867181900294z" fill="rgba(28, 83, 142, 0.4)" class="triangle-float1"></path><path d="M570.9489130221879 478.2222794723255L721.7801334153255 629.0534998654631 872.611353808463 478.22227947232545 721.7801334153254 327.39105907918787z" fill="rgba(28, 83, 142, 0.4)" class="triangle-float3"></path></g><defs><mask id="SvgjsMask1026"><rect width="1920" height="1080" fill="#ffffff"></rect></mask><linearGradient x1="10.94%" y1="-19.44%" x2="89.06%" y2="119.44%" gradientUnits="userSpaceOnUse" id="SvgjsLinearGradient1027"><stop stop-color="#0e2a47" offset="0"></stop><stop stop-color="rgba(23, 54, 60, 1)" offset="1"></stop></linearGradient><style>
                @keyframes float1 {
                    0%{transform: translate(0, 0)}
                    50%{transform: translate(-10px, 0)}
                    100%{transform: translate(0, 0)}
                }

                .triangle-float1 {
                    animation: float1 5s infinite;
                }

                @keyframes float2 {
                    0%{transform: translate(0, 0)}
                    50%{transform: translate(-5px, -5px)}
                    100%{transform: translate(0, 0)}
                }

                .triangle-float2 {
                    animation: float2 4s infinite;
                }

                @keyframes float3 {
                    0%{transform: translate(0, 0)}
                    50%{transform: translate(0, -10px)}
                    100%{transform: translate(0, 0)}
                }

                .triangle-float3 {
                    animation: float3 6s infinite;
                }
            </style></defs></svg>