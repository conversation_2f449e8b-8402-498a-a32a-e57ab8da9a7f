
/* Global body background to prevent white flicker on page load */
/* body {
    background-color: #0e2a47 !important;
    background: linear-gradient(135deg, #0e2a47 0%, rgba(23, 54, 60, 1) 100%) !important;
}

html {
    background-color: #0e2a47 !important;
} */

.lp-dashboard-ams_vm-item-count {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white;
    border-radius: 12px;
    margin-top: 1rem !important;
    margin-bottom: 1.5rem !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    transition: all 0.3s ease;
}

.lp-dashboard-ams_vm-item-count:hover {
    background-color: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4) !important;
}

.lp-dashboard-item-count__badge-container {
    margin-top: 1rem;
}

.lp-dashboard-item-count__badge {
    font-size: 1.5rem;
    padding: 0.5rem;
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    color: white;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.lp-dashboard-item-count__icon {
    font-size: 1.5rem;
}

.lp-dashboard-item-count__label-container {
    margin-top: 2rem;
}

.lp-dashboard-item-count__label {
    color: white;
    font-size: 1.3rem;
}

.lp-dashboard-item-count__count-container {
    font-size: 2rem;
}

.lp-dashboard-item-count__chart-icon {
    font-size: 1.3rem;
}

.lp-dashboard-item-count__button-container {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.lp-dashboard-item-count__button {
    width: 100%;
    background-color: rgba(255, 255, 255, 0.05);
    border: none;
    text-align: center;
    padding: 1rem;
    color: white;
    border-radius: 0 0 12px 12px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.lp-dashboard-item-count__button:hover {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.lp-dashboard_ams_vm_scrollable_container {
    max-height: 100vh;
    overflow-y: auto;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    /* Fallback background color to prevent white flicker during SVG load */
    background-color: #0e2a47;
    background: linear-gradient(135deg, #0e2a47 0%, rgba(23, 54, 60, 1) 100%);
    background-image: url('./bg4.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
}

/* White text styling for dark background */
.lp-dashboard_ams_vm_scrollable_container h1,
.lp-dashboard_ams_vm_scrollable_container h2,
.lp-dashboard_ams_vm_scrollable_container h3,
.lp-dashboard_ams_vm_scrollable_container h4,
.lp-dashboard_ams_vm_scrollable_container h5,
.lp-dashboard_ams_vm_scrollable_container h6,
.lp-dashboard_ams_vm_scrollable_container p,
.lp-dashboard_ams_vm_scrollable_container span,
.lp-dashboard_ams_vm_scrollable_container div {
    color: white !important;
}

/* Override specific dark text colors */
.lp-dashboard_ams_vm_scrollable_container .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Make containers semi-transparent for better readability */
.lp-dashboard_ams_vm_scrollable_container .container-fluid {
    background-color: rgba(0, 0, 0, 0.1) !important;
}

/* Card backgrounds with transparency */
.lp-dashboard_ams_vm_scrollable_container .d-flex.align-items-start.p-3 {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}




