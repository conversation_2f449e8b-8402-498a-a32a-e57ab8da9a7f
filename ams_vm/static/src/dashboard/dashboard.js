/** @odoo-module **/

import {Component, useRef, useState, onMounted, onWillUnmount, onWillStart} from "@odoo/owl";
import {registry} from "@web/core/registry";
import {DashboardItemCount} from "./dashboard_item_count";
import {_t} from "@web/core/l10n/translation";
import {useService} from "@web/core/utils/hooks";
import {user} from "@web/core/user";


class AMSDashboard extends Component {
    static template = "ams.AwesomeDashboard";
    static components = {DashboardItemCount};
    static props = {
        action: { type: Object, optional: true },
        actionId: { type: Number, optional: true },
        updateActionState: { type: Function, optional: true },
        className: { type: String, optional: true },
        globalState: { type: Object, optional: true }, 
    };

    setup() {
        this.upComingSelect = useRef("upComingSelect");
        this.action = useService("action");
        this.busService = useService("bus_service");
        
        // State for last updated time
        this.state = useState({
            lastUpdated: new Date(),
            currentTime: new Date(),
            userGroups: {}
        });

        // Check user groups on component start
        onWillStart(async () => {
            try {
                if (user && user.hasGroup) {
                    this.state.userGroups.hasSecurityApproval = await user.hasGroup("ams_vm.group_security_approval");
                } else {
                    // Fallback if user is not available
                    this.state.userGroups.hasSecurityApproval = false;
                }
            } catch (error) {
                console.warn('Failed to check user groups:', error);
                this.state.userGroups.hasSecurityApproval = false;
            }
        });

        // Listen for dashboard updates using the existing auto_refresh pattern
        const dashboardRefreshListener = async (payload) => {
            // Update last updated time when any dashboard item refreshes
            this.state.lastUpdated = new Date();
            this.state.currentTime = new Date();
        };

        // Use the same auto_refresh channel that dashboard items already use
        this.busService.subscribe('auto_refresh', dashboardRefreshListener);
        this.busService.addChannel('auto_refresh');

        this._dashboardRefreshStopBus = () => {
            this.busService.unsubscribe('auto_refresh', dashboardRefreshListener);
            this.busService.deleteChannel('auto_refresh');
        };

        // Update current time every 30 seconds to refresh the "time ago" display
        this.timeInterval = null;

        onMounted(() => {
            this.state.lastUpdated = new Date();
            this.state.currentTime = new Date();

            // Update current time every 30 seconds
            this.timeInterval = setInterval(() => {
                this.state.currentTime = new Date();
            }, 30000);
        });

        onWillUnmount(() => {
            if (this.timeInterval) {
                clearInterval(this.timeInterval);
            }
            // Clean up bus service listener
            if (this._dashboardRefreshStopBus) {
                this._dashboardRefreshStopBus();
            }
        });
    }


    // Translation dictionary
    get translations() {
        return {
            'show_more': _t("Show More"),
            'main_dashboard_title': _t("Visitor Management System"),
            'request_dashboard_title': _t("Upcoming Invitations:"),
            'add_new_visit_button': _t("Add New Invitation"),
            'view_all_button': _t("View All"),
            'last_updated_label': _t("Last updated"),
            'just_now': _t("Just now"),
            'minutes_ago': _t("%s minutes ago"),
            'hours_ago': _t("%s hours ago"),
            'pending_invitations': _t("Draft Invitations"),
            'need_approval_invitations': _t("Need Approval Invitations"),
            'approved_invitations': _t("Approved Invitations"),
            'confirmed_invitations': _t("Confirmed Invitations"),
            'rejected_invitations': _t("Rejected Invitations"),
            'no_show_invitations': _t("No Show Invitations"),
            'definitions': _t("Definitions"),
            
            'pending_invitations_desc': _t("Invitations that have been created by the employee but have not yet been submitted or forwarded officially."),
            'need_approval_invitations_desc': _t("Invitations that require approval from the direct manager, especially in cases such as outside working hours."),
            'approved_invitations_desc': _t("Invitations that have been approved by the direct manager, or automatically approved by the requester when scheduled during official working hours."),
            'confirmed_invitations_desc': _t("Invitations that have been confirmed by security or reception, triggering the activation of a QR code that registers the visitor on biometric devices."),
            'rejected_invitations_desc': _t("Invitations that have been declined by the direct manager when approval was required."),
            'no_show_invitations_desc': _t("Invitations that were issued to the visitor, but the visitor did not arrive at the scheduled time, and are marked as a no-show.")
        };
    }

    // Centralized translation method
    getTranslateText(key, ...args) {
        const translation = this.translations[key];
        if (!translation) {
            console.warn(`Translation key '${key}' not found`);
            return key;
        }
        
        // Handle string formatting for translations with placeholders
        if (args.length > 0) {
            return translation.replace(/%s/g, () => args.shift());
        }
        
        return translation;
    }

    // Get personalized subtitle with user name
    getPersonalizedSubtitle() {
        try {
            if (user?.name) {
                return _t("Hi %(user_name)s, welcome to your dashboard to manage your invitations!", {user_name: user.name});
            } else {
                return _t("Hi User, welcome to your dashboard to manage your invitations!", {user_name: 'User'});
            }
        } catch (error) {
            console.warn('Failed to get user name:', error);
            return _t("Hi User, welcome to your dashboard to manage your invitations!", {user_name: 'User'});
        }
    }

    get formattedLastUpdated() {
        // Use currentTime from state to make this reactive
        const now = this.state.currentTime;
        const diff = now - this.state.lastUpdated;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (seconds < 60) {
            return this.getTranslateText('just_now');
        } else if (minutes < 60) {
            return this.getTranslateText('minutes_ago', minutes);
        } else if (hours < 24) {
            return this.getTranslateText('hours_ago', hours);
        } else {
            return this.state.lastUpdated.toLocaleString();
        }
    }


    getRequestDashboardItems() {
        // const dateDomain = this.getDateDomain();
        // console.log("dashboard >> dateDomain:", dateDomain);
        return [
            {
                label: this.getTranslateText('pending_invitations'),
                description: this.getTranslateText('pending_invitations_desc'),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'pending']],
                icon: "fa fa-hourglass-half",
                chartIcon: "fa fa-pie-chart",
                color: "bg-primary",
                views: [[false, "list"], [false, "form"]],
                visible: !this.state.userGroups.hasSecurityApproval, // Hide for security approval users
            },
            {
                label: this.getTranslateText('need_approval_invitations'),
                description: this.getTranslateText('need_approval_invitations_desc'),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'need_approval']],
                icon: "fa fa-clock-o",
                chartIcon: "fa fa-area-chart",
                color: "bg-warning",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: this.getTranslateText('approved_invitations'),
                description: this.getTranslateText('approved_invitations_desc'),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'approved']],
                icon: "fa fa-thumbs-up",
                chartIcon: "fa fa-line-chart",
                color: "bg-success",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: this.getTranslateText('confirmed_invitations'),
                description: this.getTranslateText('confirmed_invitations_desc'),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'confirmed']],
                icon: "fa fa-check-circle",
                chartIcon: "fa fa-pie-chart",
                color: "bg-info",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: this.getTranslateText('rejected_invitations'),
                description: this.getTranslateText('rejected_invitations_desc'),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'rejected']],
                icon: "fa fa-thumbs-down",
                chartIcon: "fa fa-bar-chart",
                color: "bg-danger",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: this.getTranslateText('no_show_invitations'),
                description: this.getTranslateText('no_show_invitations_desc'),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'approved'], ['end_date', '<', (new Date()).toISOString().split('T')[0] + ' 00:00:00']],
                icon: "fa fa-user-times",
                chartIcon: "fa fa-bar-chart",
                color: "bg-primary",
                views: [[false, "list"], [false, "form"]],
            }
        ];
    }

    openAddNewInvitationForm() {
        this.action.doAction("ams_vm.invitation_form_action");
        // this.action.doAction({
        //     type: "ir.actions.act_window",
        //     name: "Add New Invitation",
        //     res_model: "ams_vm.invitation",
        //     view_mode: "form",
        //     views: [[false, "form"]],
        //     target: "current",
        //     context: {'search_default_today': 1, 'apply_followup_domain': 1}
        // });
    }

    openViewAllInvitations() {
        this.action.doAction("ams_vm.invitation_action");
    }



}


registry.category("actions").add("ams.dashboard", AMSDashboard);
