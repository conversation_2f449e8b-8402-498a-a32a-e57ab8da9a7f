<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
    <t t-name="ams.AwesomeDashboard">
        <!-- Scrollable container for the entire dashboard -->
        <div class="lp-dashboard_ams_vm_scrollable_container">
            <!-- Modern Header Section -->
            <div class="container-fluid" style="border-bottom: 1px solid rgba(255, 255, 255, 0.2); padding: 24px 0;">
                <div class="container-xxl px-4">
                    <div class="row align-items-center">
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <div class="me-3 bg-primary" style="width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fa fa-users" style="color: white; font-size: 20px;"></i>
                                </div>
                                <div>
                                    <h1 style="font-size: 28px; font-weight: 700; color: white; margin: 0; line-height: 1.2;">
                                        <t t-esc="getTranslateText('main_dashboard_title')"/>
                                    </h1>
                                    <p style="color: rgba(255, 255, 255, 0.8); font-size: 14px; margin: 4px 0 0 0;">
                                        <t t-esc="getPersonalizedSubtitle()"/>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Bar Section -->
            <div class="container-fluid" style="border-bottom: 1px solid rgba(255, 255, 255, 0.2); padding: 16px 0;">
                <div class="container-xxl px-4">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto">
                            <div class="d-flex gap-3">
                                <button class="btn bg-primary text-white"
                                        style="border: none; padding: 12px 24px; font-size: 14px; font-weight: 600; border-radius: 8px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); transition: all 0.2s ease;"
                                        t-on-click="openAddNewInvitationForm"
                                        onmouseover="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.15)'"
                                        onmouseout="this.style.backgroundColor='#3b82f6'; this.style.transform='translateY(0)'; this.style.boxShadow='0 1px 3px rgba(0, 0, 0, 0.1)'">
                                    <i class="fa fa-plus me-2"></i>
                                    <t t-esc="getTranslateText('add_new_visit_button')"/>
                                </button>
                                <button class="btn"
                                        style="background-color: #6b7280; color: white; border: none; padding: 12px 24px; font-size: 14px; font-weight: 600; border-radius: 8px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); transition: all 0.2s ease;"
                                        t-on-click="openViewAllInvitations"
                                        onmouseover="this.style.backgroundColor='#4b5563'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.15)'"
                                        onmouseout="this.style.backgroundColor='#6b7280'; this.style.transform='translateY(0)'; this.style.boxShadow='0 1px 3px rgba(0, 0, 0, 0.1)'">
                                    <i class="fa fa-list me-2"></i>
                                    <t t-esc="getTranslateText('view_all_button')"/>
                                </button>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex align-items-center text-muted" style="font-size: 13px;">
                                <i class="fa fa-clock-o me-2"></i>
                                <span><t t-esc="getTranslateText('last_updated_label')"/>: <span t-esc="formattedLastUpdated"/></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="container-xxl px-4 py-4">
                <!-- Section for Requests -->
                <div class="mb-4">
                    <h2 style="font-size: 20px; font-weight: 600; color: white; margin: 0 0 16px 0; display: flex; align-items: center;">
                        <div class="me-3" style="width: 32px; height: 32px; background-color: rgba(255, 255, 255, 0.2); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <i class="fa fa-calendar-check-o" style="color: white; font-size: 14px;"></i>
                        </div>
                        <t t-esc="getTranslateText('request_dashboard_title')"/>
                    </h2>
                </div>

                <!-- Dashboard Cards Grid -->
                <div class="row ">
                    <t t-foreach="getRequestDashboardItems()" t-as="requestItem" t-key="requestItem.label">
                        <div class="col-12 col-sm-6 col-lg-4 col-xl-3" t-if="requestItem.visible !== false">
                            <DashboardItemCount
                                    size="16"
                                    label="requestItem.label"
                                    icon="requestItem.icon"
                                    chartIcon="requestItem.chartIcon"
                                    buttonLabel="getTranslateText('show_more')"
                                    color="requestItem.color"
                                    model="requestItem.model"
                                    domain="requestItem.domain"
                                    views="requestItem.views"
                            />
                        </div>
                    </t>
                </div>
            </div>

            <!-- Footer Section with Status Descriptions -->
            <div class="container-fluid" style="border-top: 1px solid rgba(255, 255, 255, 0.2); padding: 32px 0; margin-top: 40px;">
                <div class="container-xxl px-4">
                    <div class="mb-4">
                        <h3 style="font-size: 18px; font-weight: 600; color: white; margin: 0 0 20px 0; display: flex; align-items: center;">
                            <div class="me-3" style="width: 28px; height: 28px; background-color: rgba(255, 255, 255, 0.2); border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                                <i class="fa fa-info-circle" style="color: white; font-size: 12px;"></i>
                            </div>
                            <t t-esc="getTranslateText('definitions')"/>
                        </h3>
                    </div>
                    
                    <div class="row g-3 mb-4">
                        <t t-foreach="getRequestDashboardItems()" t-as="requestItem" t-key="requestItem.label">
                            <div class="col-12 col-md-6 col-lg-4 d-flex" t-if="requestItem.visible !== false">
                                <div class="d-flex align-items-start p-3 w-100" style="background-color: rgba(255, 255, 255, 0.1); border-radius: 8px; border: 1px solid rgba(255, 255, 255, 0.2); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); min-height: 120px;">
                                    <!-- Status Circle with Icon -->
                                    <div class="me-3 flex-shrink-0" t-attf-class="{{requestItem.color}}" style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                                        <i t-attf-class="{{requestItem.icon}}" style="color: white; font-size: 16px;"></i>
                                    </div>
                                    
                                    <!-- Status Content -->
                                    <div class="flex-grow-1 d-flex flex-column justify-content-between">
                                        <div>
                                            <h5 style="font-size: 14px; font-weight: 600; color: white; margin: 0 0 8px 0; line-height: 1.3;">
                                                <t t-esc="requestItem.label"/>
                                            </h5>
                                            <p style="font-size: 12px; color: rgba(255, 255, 255, 0.8); margin: 0; line-height: 1.4; overflow-wrap: break-word;">
                                                <t t-esc="requestItem.description"/>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
