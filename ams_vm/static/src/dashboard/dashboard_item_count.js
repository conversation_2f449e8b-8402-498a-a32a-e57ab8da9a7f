/** @odoo-module **/

import {Component, useState, onWillStart, onWillDestroy} from "@odoo/owl";
import {useService} from "@web/core/utils/hooks";


export class DashboardItemCount extends Component {
    static template = "ams.DashboardItemCount";

    static props = {
        size: {type: Number, default: 18, optional: true},
        label: {type: String, default: "Item Label"},
        icon: {type: String, optional: true},
        chartIcon: {type: String, optional: true},
        buttonLabel: {type: String, default: "Show More", optional: true},
        color: {type: String, default: "#ffffff", optional: true},
        model: {type: String, required: true},
        domain: {type: Array, default: [], required: true},
        views: {type: Array, required: true},
    };

    setup() {
        this.orm = useService("orm");
        this.action = useService("action");
        this.busService = this.env.services.bus_service;

        this.state = useState({
            count: 0,
        });

        onWillStart(async () => {
            await this.fetchDomain();
            await this.fetchCount();
        });

        const refreshBusListener = async (payload) => {
            if (payload.model === this.props.model) {
                await this.fetchDomain();
                await this.fetchCount();
            }
        }
        this.busService.subscribe('auto_refresh', refreshBusListener);
        this.busService.addChannel('auto_refresh');
        this._refreshStopBus = () => {
            this.busService.unsubscribe('auto_refresh', refreshBusListener);
            this.busService.deleteChannel('auto_refresh');
        }

        onWillDestroy(() => {
            this._refreshStopBus();
        });


    }

    async fetchDomain() {
        // console.log('dashboard >> fetchDomain >> domain:', this.props.domain)
        const domain = await this.orm.call(this.props.model, 'get_dashboard_domain', [this.props.model], {});
        this.props.domain = [...this.props.domain, ...domain];
        console.log('dashboard >> fetchDomain >> domain_after:', this.props.domain)

        // Trigger auto_refresh event to update dashboard timestamp
        this.busService.trigger('auto_refresh', {
            model: this.props.model,
            timestamp: new Date(),
            source: 'fetchDomain'
        });

        // this.orm.call(this.props.model, 'get_dashboard_domain')
        //     .then((domain) => {
        //         console.log("dashboard >> fetchDomain >>domain:", domain);
        //         if (Array.isArray(domain)) {
        //             this.props.domain = [...this.props.domain, ...domain];
        //         } else {
        //             console.warn("Invalid domain returned:", domain);
        //         }
        //     })
        //     .catch((error) => {
        //         console.error("Error fetching domain:", error);
        //     });
    }


    fetchCount() {
        // console.log("dashboard >> fetchCount >> domain&model:", this.props.model, this.props.domain)
        this.orm.searchCount(this.props.model, this.props.domain)
            .then((count) => {
                this.state.count = count;
                // Trigger auto_refresh event to update dashboard timestamp
                this.busService.trigger('auto_refresh', {
                    model: this.props.model,
                    timestamp: new Date(),
                    source: 'fetchCount'
                });
            });
        // console.log(this.props.state)
        // this.orm.call(this.props.model, 'get_dashboard_count_item_value', [this.props.state], {state: this.props.state})
        //     .then((count) => {
        //         this.state.count = count;
        //     })
        //     .catch((error) => {
        //         console.error("Error fetching count:", error);
        //     });
    }


    openEntryView() {
        this.action.doAction({
            type: "ir.actions.act_window",
            name: `${this.props.label}`,
            res_model: this.props.model,
            views: this.props.views,
            domain: this.props.domain,
             context: {
                'apply_followup_domain': 1
             }
        });
    }

}

