<templates xml:space="preserve">
    <t t-name="ams.DashboardItemCount">
        <!-- Card Container -->
        <div class="lp-dashboard-ams_vm-item-count card m-2 text-center shadow-lg"
             t-att-style="'width: ' + props.size + 'rem;'" t-att-class="props.color">

            <!--Icon Badge-->
            <div class="row justify-content-center lp-dashboard-item-count__badge-container">
                <span class="lp-dashboard-item-count__badge position-absolute top-0 start-50 translate-middle badge" t-att-class="props.color">
                    <i t-att-class="props.icon" class="lp-dashboard-item-count__icon"></i>
                </span>
            </div>

            <!--Label Section -->
            <div class="row lp-dashboard-item-count__label-container">
                <h5 class="lp-dashboard-item-count__label card-title p-1">
                    <t t-esc="props.label"/>
                </h5>
            </div>

            <!--Count and Chart Icon-->
            <div class="lp-dashboard-item-count__count-container">
                <span class="lp-dashboard-item-count__count">
                    <t t-esc="state.count"/>
                </span>
                <i t-att-class="props.chartIcon" class="lp-dashboard-item-count__chart-icon p-2"></i>
            </div>

            <!--Button Section -->
            <div class="lp-dashboard-item-count__button-container">
                <button class="lp-dashboard-item-count__button" t-on-click="openEntryView">
                    <t t-esc="props.buttonLabel"/>
                </button>
            </div>
        </div>
    </t>
</templates>