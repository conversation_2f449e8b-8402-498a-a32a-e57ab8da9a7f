<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <template id="ams_vm_email_template">
            <t t-name="ams_vm.ams_vm_email_template">
                <html>
                    <head>
                        <meta charset="UTF-8"/>
                        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
                        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&amp;display=swap"
                              rel="stylesheet"/>
                        <style>
                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                                direction: rtl;
                            }

                            body {
                                font-family: 'Cairo', sans-serif;
                                background-color: #f8f9fa;
                                color: #1a1a1a;
                                line-height: 1.6;
                            }

                            .email-wrapper {
                                max-width: 600px;
                                margin: 0 auto;
                                background-color: #ffffff;
                                border-radius: 12px;
                                overflow: hidden;
                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                            }

                            .email-header {
                                background: linear-gradient(135deg, #006C35 0%, #005229 100%);
                                padding: 30px 20px;
                                text-align: center;
                            }

                            .header-logo {
                                width: 120px;
                                height: auto;
                                margin-bottom: 15px;
                            }

                            .header-title {
                                color: #ffffff;
                                font-size: 28px;
                                font-weight: 700;
                                margin: 0;
                                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                            }

                            .email-content {
                                padding: 40px 30px;
                            }

                            .greeting {
                                font-size: 22px;
                                font-weight: 600;
                                color: #006C35;
                                margin-bottom: 20px;
                            }

                            .visit-details {
                                background-color: #f8f9fa;
                                border-radius: 8px;
                                padding: 25px;
                                margin: 25px 0;
                                border: 1px solid #e9ecef;
                            }

                            .detail-item {
                                display: flex;
                                align-items: center;
                                margin-bottom: 15px;
                                padding-bottom: 15px;
                                border-bottom: 1px solid #e9ecef;
                            }

                            .detail-item:last-child {
                                margin-bottom: 0;
                                padding-bottom: 0;
                                border-bottom: none;
                            }

                            .detail-label {
                                font-weight: 600;
                                color: #A17C43;
                                min-width: 140px;
                            }

                            .detail-value {
                                color: #1a1a1a;
                            }

                            .message {
                                background-color: #e8f5ee;
                                border-right: 4px solid #006C35;
                                padding: 15px;
                                margin: 25px 0;
                                border-radius: 4px;
                            }

                            .cta-button {
                                display: inline-block;
                                background: linear-gradient(135deg, #A17C43 0%, #8B6B3A 100%);
                                color: #ffffff;
                                padding: 12px 30px;
                                border-radius: 6px;
                                text-decoration: none;
                                font-weight: 600;
                                margin: 20px 0;
                                transition: transform 0.2s ease;
                            }

                            .cta-button:hover {
                                transform: translateY(-2px);
                                background: linear-gradient(135deg, #8B6B3A 0%, #A17C43 100%);
                            }

                            .email-footer {
                                background-color: #f8f9fa;
                                padding: 30px;
                                text-align: center;
                                border-top: 1px solid #e9ecef;
                            }

                            .social-links {
                                margin: 20px 0;
                            }

                            .social-link {
                                display: inline-block;
                                margin: 0 10px;
                                color: #006C35;
                                text-decoration: none;
                                font-weight: 600;
                            }

                            .social-link:hover {
                                color: #A17C43;
                            }

                            .footer-text {
                                color: #6c757d;
                                font-size: 14px;
                            }

                            @media (max-width: 600px) {
                                .email-wrapper {
                                    border-radius: 0;
                                }

                                .email-content {
                                    padding: 30px 20px;
                                }

                                .detail-item {
                                    flex-direction: column;
                                    align-items: flex-start;
                                }

                                .detail-label {
                                    margin-bottom: 5px;
                                }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="email-wrapper">
                            <div class="email-header">
                                <h1 class="header-title">
                                    <i class="fa fa-id-card-o" style="margin-left: 10px;"></i>
                                    تصريح زيارة
                                </h1>
                            </div>

                            <div class="email-content">
                                <h2 class="greeting">مرحباً <t t-esc="visitor_name"/>،</h2>
                                <p>نرحب بكم في وزارة الاقتصاد والتخطيط. فيما يلي تفاصيل تصريح زيارتكم:</p>

                                <div class="visit-details">
                                    <div class="detail-item">
                                        <span class="detail-label">رقم التصريح:</span>
                                        <span class="detail-value"><t t-esc="visit_number"/></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">غرفة الاجتماعات:</span>
                                        <span class="detail-value"><t t-esc="meeting_room"/></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">الدور:</span>
                                        <span class="detail-value"><t t-esc="floor_building"/></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">وقت الزيارة:</span>
                                        <span class="detail-value"><t t-esc="check_in_time"/></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">مدة الزيارة:</span>
                                        <span class="detail-value"><t t-esc="duration_display"/></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">وقت الخروج:</span>
                                        <span class="detail-value"><t t-esc="check_out_time"/></span>
                                    </div>
                                </div>

                                <div class="message">
                                    <p><strong>ملاحظات هامة:</strong></p>
                                    <ul style="margin-top: 10px; padding-right: 20px;">
                                        <li>تم إرفاق تصريح الزيارة مع هذا البريد الإلكتروني</li>
                                        <li>يرجى إبراز هذا التصريح عند نقطة الأمن</li>
                                        <li>يرجى الالتزام بتعليمات الأمن والسلامة</li>
                                    </ul>
                                </div>

                                <p>نتطلع لاستقبالكم ونتمنى لكم زيارة موفقة.</p>

                                <p>مع أطيب التحيات،<br/>
                                    <strong>إدارة الأمن والسلامة</strong><br/>
                                    وزارة الاقتصاد والتخطيط</p>
                            </div>

                            <div class="email-footer">
                                <div class="social-links">
                                    <a href="https://mep.gov.sa/ar" class="social-link">موقعنا الإلكتروني</a>
                                    <a href="https://mep.gov.sa/ar/contact-us" class="social-link">تواصل معنا</a>
                                </div>
                                <p class="footer-text">جميع الحقوق محفوظة لوزارة الاقتصاد والتخطيط ©2024</p>
                            </div>
                        </div>
                    </body>
                </html>
            </t>
        </template>
    </data>
</odoo>
