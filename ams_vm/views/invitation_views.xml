<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Invitation Form View Inheritance -->
    <record id="invitations_view_form" model="ir.ui.view">
        <field name="name">ams_vm.invitation_form_inherit</field>
        <field name="model">ams_vm.invitation</field>
        <field name="inherit_id" ref="base_visitor_request_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
<!--                <button-->
<!--                        class="oe_stat_button"-->
<!--                        name="action_open_visit_view"-->
<!--                        type="object"-->
<!--                        icon="fa-calendar"-->
<!--                        string="Visits Count"-->
<!--                >-->
<!--                    <field name="total_visits" widget="statinfo"/>-->
<!--                </button>-->
            </xpath>
            <xpath expr="//div[@name='button_box']" position="after">
                <div class="alert alert-warning" role="alert"
                     invisible="state not in ['pending' ,'need_approval'] or not need_approval">

                    <p><strong><span><i class="fa fa-exclamation-triangle" title="Info"/></span></strong> This request
                        need to be approved from manager or supervisor <br/> because request time outside allowed time
                        or approval forced required from manager !!
                        <span invisible="not is_current_user_requester or approval_uid"><br/> Please select
                            your <strong>Approval Manager</strong> below and click on button Request
                            Approval.</span></p>
                </div>

            </xpath>
            <xpath expr="//widget" position="replace">
                <widget name="web_ribbon" title="Single Day"
                        bg_color="bg-success"
                        invisible="schedule_type == 'multiple'"/>
                <widget name="web_ribbon" title=" Multiple Days"
                        bg_color="bg-info"
                        invisible="schedule_type == 'single'"/>
            </xpath>
            <xpath expr="header" position="inside">

                <button name="action_send_invitation_email" type="object" string="Send Email"
                        icon="fa-envelope"
                        class="btn-primary"
                        invisible="state != 'approved'"/>
                />
                <!-- Smart button for Visits Count -->

            </xpath>
            <xpath expr="//field[@name='commit']" position="before">
<!--                <field name="location_address" readonly="is_readonly"/>-->
                <field name="building_id" readonly="is_readonly" options="{'no_create': True}" required="1"/>
                <field name="access_groups_ids" widget="many2many_tags" options="{'no_create': True}"
                       readonly="is_readonly"
                       help="For single day schedules, only one access group can be selected"/>
                <field name="room_name" readonly="is_readonly" required="1"/>
            </xpath>
            <xpath expr="//field[@name='date']" position="before">
                <field name="host_work_mobile" required="1"/>
                <field name="host_work_phone" required="1"/>
            </xpath>
            <xpath expr="//field[@name='duration']" position="attributes">
                <attribute name="required">schedule_type == 'single'</attribute>
            </xpath>
            <xpath expr="//page[@name='request_info']" position="after">
                <page string="Visitors" name="visitors">
                    <div class="d-flex justify-content-between align-items-center mb-3" invisible="is_readonly">
                        <h5 class="mb-0">Visitors List</h5>
                        <button name="%(ams_vm.action_invitation_visitor_import_wizard)d" type="action"
                                string="Import from Excel" icon="fa-upload" class="btn btn-primary btn-sm"
                                help="Import visitors from Excel file"/>
                    </div>
                    <field name="invitation_visitor_ids" readonly="is_readonly">
                        <list editable="bottom">
                            <field name="visitor_id"/>
                            <field name="visit_id" optional="hide" readonly="1"/> 
                            <field name="vip" readonly="1" optional="hide"/>
                            <field name="invitation_id" invisible="1"/>
                            <field name="state" readonly="1" optional="show"
                                   decoration-bf="state == 'pending'"
                                   decoration-warning="state == 'need_approval'"
                                   decoration-success="state == 'approved'"
                                   decoration-danger="state == 'rejected'"
                                   decoration-info="state == 'confirmed'"
                                   decoration-muted="state == 'cancelled'"/>
                            <field name="email_state" readonly="1" optional="show"
                                   decoration-bf="email_state == 'pending'"
                                   decoration-success="email_state == 'sent'"
                                   decoration-danger="email_state == 'failed'"/>
                            <field name="qr_code" readonly="1" optional="hide"/>
                            <button name="action_confirm" type="object" string="" icon="fa-check"
                                    title="Confirm Visit" invisible="state != 'approved'"
                                    groups="ams_vm.group_security_approval"/>
                            <button name="action_send_invitation_email" type="object" string="" icon="fa-envelope"
                                    title="Send Invitation Email" invisible="state != 'approved'"/>
                            <button name="action_print_badge" type="object" string="" icon="fa-print"
                                    title="Print Invitation" invisible="state  != 'approved'"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>


    <!-- List View -->
    <record id="invitation_view_list" model="ir.ui.view">
        <field name="name">ams_vm.invitation.list</field>
        <field name="model">ams_vm.invitation</field>
        <field name="inherit_id" ref="base_visitor_request_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="attributes">
                <attribute name="string">Invitations</attribute>
            </xpath>
            <xpath expr="//field[@name='start_date']" position="after">
<!--                <field name="duration" optional="hide"/>-->
                <field name="room_name" optional="hide"/>
                <field name="location_address" optional="hide"/>
                <field name="host_work_mobile" optional="hide"/>
                <field name="host_work_phone" optional="hide"/>
                <field name="single_visitor" optional="hide"/>
                <field name="weekend" optional="hide"/>
                <field name="outside_working_time" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='qr_code']" position="after">
                <button name="action_confirm" type="object" icon="fa-check" title="Confirm Invitation" 
                        invisible="not single_visitor or state != 'approved' or status == 'finished'" groups="ams_vm.group_security_approval"/>
            </xpath>

        </field>
    </record>

    <!-- Kanban View  -->
    <record id="view_invitation_kanban" model="ir.ui.view">
        <field name="name">ams_vm.invitation.kanban</field>
        <field name="model">ams_vm.invitation</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column" quick_create="0" records_draggable="0"
                    sample="1">
                <field name="state"/>
                <field name="name"/>
                <field name="subject"/>
                <field name="start_date"/>
                <field name="visitor_id"/>

                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_global_click mb-2">
                            <div>
                                <div style="margin-bottom: 10px;">
                                    <strong class="o_kanban_record_title">
                                        <i class="fa fa-bookmark mx-1" title="Subject"></i>
                                        <field name="rec_name"/>
                                    </strong>
                                </div>

                                <hr/>
                                <div class="o_kanban_middle_row"
                                     style="display: flex; justify-content: space-between; margin-bottom: 10px;">

                                    <div>
                                        <i class="fa fa-user mx-1" title="Visitor"></i>
                                        <field name="visitor_id" options="{'no_open': True}"/>
                                    </div>
                                    <div class="badge" t-att-class="{
                                        'bg-warning-light': record.state.raw_value === 'pending',
                                        'bg-info': record.state.raw_value === 'need_approval',
                                        'bg-success': record.state.raw_value === 'approved',
                                        'bg-primary': record.state.raw_value === 'confirmed',
                                        'bg-danger': record.state.raw_value === 'rejected',
                                        'bg-warning': record.state.raw_value === 'cancelled',
                                    }">
                                        <field name="state"/>
                                    </div>

                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <div style="width: 70%; padding-right: 5px;">
                                        <i class="fa fa-calendar mx-1" title="Date"></i>
                                        <field name="start_date"/>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>


    <!--TODO[IMP]:invitation _view_calendar-->
    <record id="invitation_view_calendar" model="ir.ui.view">
        <field name="name">ams_vm.invitation.calendar</field>
        <field name="model">ams_vm.invitation</field>
        <field name="arch" type="xml">

            <calendar string="Invitations Calendar" date_start="start_date" date_stop="end_date" color="state"
                      event_open_popup="True" quick_create="False">

                <field name="subject"/>
                <field name="state" filters="1"/>
                <field name="visitor_id" filters="1" avatar_field="image_128"/>
            </calendar>

        </field>
    </record>

    <!-- Search View -->
    <record id="invitation_view_search" model="ir.ui.view">
        <field name="name">ams_vm.invitation.search</field>
        <field name="model">ams_vm.invitation</field>
        <field name="inherit_id" ref="base_visitor_request_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search/field[@name='name']" position="before">
<!--                    <field name="qr_code"/>-->
                <field name="qr_codes"/>
            </xpath>
            <xpath expr="//search" position="inside">
                <field name="room_name"/>
                <field name="location_address"/>
                <field name="host_work_phone"/>
                <field name="visitor_id"/>
                <field name="visitor_mobile"/>
                <field name="visitor_phone"/>
                <field name="vip"/>
            </xpath>
        </field>
    </record>

    <!-- Action -->
    <record id="invitation_action" model="ir.actions.act_window">
        <field name="name">Invitations</field>
        <field name="res_model">ams_vm.invitation</field>
        <field name="view_mode">list,form,kanban,calendar</field>
        <field name="context">{'search_default_today': 1,'apply_followup_domain':1}</field>
        <field name="domain">[('is_event', '=', False)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first invitation!
            </p>
        </field>
    </record>

     <record id="invitation_form_action" model="ir.actions.act_window">
        <field name="name">Invitation Form</field>
        <field name="res_model">ams_vm.invitation</field>
        <field name="view_mode">form</field>
        <field name="context">{'search_default_today': 1,'apply_followup_domain':1}</field>
        <field name="domain">[('is_event', '=', False)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first invitation!
            </p>
        </field>
    </record>

    <record id="invitation_server_action" model="ir.actions.server">
        <field name="name">Invitations</field>
        <field name="model_id" ref="model_ams_vm_invitation"/>
        <field name="binding_model_id" ref="model_ams_vm_invitation"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_vm.invitation'].sudo().action_open_views(
                {
                    'action_ref': 'ams_vm.invitation_action',
                     'use_domain_follow_up_visibility': False
                }
            )

        </field>
        <field name="type">ir.actions.server</field>
        <field name="groups_id" eval="[(4, ref('base.group_no_one'))]"/>
    </record>

    <record id="event_invitation_action" model="ir.actions.act_window">
        <field name="name">Event Invitation</field>
        <field name="res_model">ams_vm.invitation</field>
        <field name="view_mode">list,form,kanban,calendar</field>
        <field name="context">{'default_is_event': True, 'search_default_today': 1}</field>
        <field name="domain">[('is_event', '=', True)]</field>
        <field name="target">current</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an event invitation
            </p>
        </field>
    </record>

    <record id="event_invitation_server_action" model="ir.actions.server">
        <field name="name">My Event Invitations</field>
        <field name="model_id" ref="model_ams_vm_invitation"/>
        <field name="binding_model_id" ref="model_ams_vm_invitation"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_vm.invitation'].sudo().action_open_views(
                {
                    'action_ref': 'ams_vm.event_invitation_action',
                    'use_domain_follow_up_visibility': False
                }
            )

        </field>
        <field name="type">ir.actions.server</field>
        <field name="groups_id" eval="[(4, ref('base.group_no_one'))]"/>
    </record>

    <record id="action_server_print_all_visitor_badges" model="ir.actions.server">
        <field name="name">Print All Visitor Badges</field>
        <field name="model_id" ref="model_ams_vm_invitation"/>
        <field name="binding_model_id" ref="model_ams_vm_invitation"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_vm.invitation'].print_all_visitor_badges(records.ids)
        </field>
        <field name="type">ir.actions.server</field>
    </record>


    <!--        <record id="single_invitation_action" model="ir.actions.act_window">-->
    <!--        <field name="name">Single Invitation</field>-->
    <!--        <field name="res_model">ams_vm.invitation</field>-->
    <!--        <field name="view_mode">list,form,kanban,calendar</field>-->
    <!--        <field name="context">{'default_schedule_type': 'single', 'search_default_today': 1}</field>-->
    <!--        <field name="target">current</field>-->

    <!--        <field name="help" type="html">-->
    <!--            <p class="o_view_nocontent_smiling_face">-->
    <!--                Create a single invitation-->
    <!--            </p>-->
    <!--        </field>-->
    <!--    </record>-->

    <!--    <record id="action_server_open_my_single_invitation" model="ir.actions.server">-->
    <!--        <field name="name">My Single Invitations</field>-->
    <!--        <field name="model_id" ref="model_ams_vm_invitation"/>-->
    <!--        <field name="binding_model_id" ref="model_ams_vm_invitation"/>-->
    <!--        <field name="state">code</field>-->
    <!--        <field name="code">-->
    <!--            action = env['ams_vm.invitation'].sudo().action_open_views(-->
    <!--            {-->
    <!--            'action_ref': 'ams_vm.single_invitation_action'-->
    <!--            }-->
    <!--            )-->

    <!--        </field>-->
    <!--        <field name="type">ir.actions.server</field>-->
    <!--        <field name="groups_id" eval="[(4, ref('base.group_no_one'))]"/>-->
    <!--    </record>-->


    <!--    <record id="multiple_invitation_action" model="ir.actions.act_window">-->
    <!--        <field name="name">Multiple Invitation</field>-->
    <!--        <field name="res_model">ams_vm.invitation</field>-->
    <!--        <field name="view_mode">list,form,kanban,calendar</field>-->
    <!--        <field name="context">{'default_schedule_type': 'multiple', 'search_default_today': 1}</field>-->

    <!--        <field name="target">current</field>-->
    <!--        <field name="help" type="html">-->
    <!--            <p class="o_view_nocontent_smiling_face">-->
    <!--                Create a multiple invitation-->
    <!--            </p>-->
    <!--        </field>-->
    <!--    </record>-->

    <!--    <record id="action_server_open_my_multiple_invitation" model="ir.actions.server">-->
    <!--        <field name="name">My Multiple Invitations</field>-->
    <!--        <field name="model_id" ref="model_ams_vm_invitation"/>-->
    <!--        <field name="binding_model_id" ref="model_ams_vm_invitation"/>-->
    <!--        <field name="state">code</field>-->
    <!--        <field name="code">-->
    <!--            action = env['ams_vm.invitation'].sudo().action_open_views(-->
    <!--            {-->
    <!--            'action_ref': 'ams_vm.multiple_invitation_action'-->
    <!--            }-->
    <!--            )-->

    <!--        </field>-->
    <!--        <field name="type">ir.actions.server</field>-->
    <!--        <field name="groups_id" eval="[(4, ref('base.group_no_one'))]"/>-->
    <!--    </record>-->
</odoo>
