<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Form view -->
        <record id="view_res_company_form" model="ir.ui.view">
            <field name="name">res_company_form</field>
            <field name="model">res.company</field>
            <field name="type">form</field>
            <field name="priority" eval="8"/>
            <field name="inherit_id" ref="base.view_company_form"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <page string="Visitors Management Settings" name="visitor_settings">
                        <group>
                            <group>
                                <field name="email_template_id"/>
                                <field name="badge_report_id"/>
                                <field name="required_manager_approval"/>
                                <field name="required_approval_after_time" invisible="required_manager_approval == True"
                                       widget="float_time"/>
                                <field name="dashboard_up_coming_domain"/>
                                <field name="follow_up_option"/>
                            </group>
                            <group>
                                <field name="api_type"/>
                                <field name="visitors_group_id"/>
                                <field name="request_max_days"/>
                                <field name="visit_max_hours"/>
                                <field name="auto_generate_qr_code"/>
                                <field name="visitor_visibility"/>
                            </group>

                        </group>
                    </page>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
