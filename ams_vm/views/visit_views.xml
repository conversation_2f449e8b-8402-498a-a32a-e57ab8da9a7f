<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Visit Form View Inheritance -->
    <record id="visits_view_form" model="ir.ui.view">
        <field name="name">ams_vm.visit_form_inherit</field>
        <field name="model">ams_vm.visit</field>
        <field name="inherit_id" ref="base_visitor_request_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <!-- Display fields in the specified order -->
            <xpath expr="//field[@name='visitor_id']" position="after">
                                <field name="invitation_id"/>

            </xpath>
            <xpath expr="header" position="inside">

<!--                <button name="action_send_email" type="object" string="Send Email" icon="fa-envelope"-->
<!--                        class="btn-primary"/>-->
            </xpath>
        </field>
    </record>

    <!-- List View -->
    <record id="visit_view_list" model="ir.ui.view">
        <field name="name">ams_vm_base_request_list_inherit</field>
        <field name="model">ams_vm.visit</field>
        <field name="inherit_id" ref="base_visitor_request_view_list"/> <!-- Correct parent view ID -->
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qr_code']" position="after">
                <field name="invitation_id" optional="show"/>
                <field name="invitation_qr_code" optional="show"/>
            </xpath>
        </field>
    </record>

    <!-- Search View -->
    <record id="visit_view_search" model="ir.ui.view">
        <field name="name">ams_vm_base_request_search_inherit</field>
        <field name="model">ams_vm.visit</field>
        <field name="inherit_id" ref="base_visitor_request_view_search"/> <!-- Correct parent view ID -->
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='visitor_id']" position="attributes">
                <field name="visitor_id"/>
            </xpath>
        </field>
    </record>

    <!-- Action -->
    <record id="visit_action" model="ir.actions.act_window">
        <field name="name">Visits</field>
        <field name="res_model">ams_vm.visit</field>
        <field name="view_mode">list,form,search</field>
        <field name="context">{'search_default_today':1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">Create a visit record</p>
            <p>Create visit records</p>
        </field>
    </record>
</odoo>
