<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Import Wizard Form View -->
    <record id="invitation_visitor_import_wizard_view_form" model="ir.ui.view">
        <field name="name">ams_vm.invitation_visitor_import_wizard.form</field>
        <field name="model">ams_vm.invitation_visitor_import_wizard</field>
        <field name="arch" type="xml">
            <form string="Import Visitors from Excel">
                <header>
                    <button name="action_import_visitors" type="object" string="Import Visitors"
                            class="btn-primary" invisible="state == 'imported'"/>
                    <button name="action_download_template" type="object" string="Download Template"
                            class="btn-secondary" invisible="state == 'imported'"/>
                    <button name="action_close" type="object" string="Close"
                            class="btn-secondary" invisible="state != 'imported'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,imported"/>
                </header>
                
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="invitation_id" readonly="1"/>
                        </h1>
                        <h2>Import Visitors from Excel File</h2>
                    </div>
                    
                    <group invisible="state == 'imported'">
                        <group string="File Upload">
                            <field name="excel_file" widget="binary" filename="filename"/>
                            <field name="filename" invisible="1"/>
                        </group>
                        
                        <group string="Import Options">
                            <field name="update_existing"/>
                            <field name="create_missing"/>
                        </group>
                    </group>
                    
                    <group invisible="state != 'imported'">
                        <group string="Import Results">
                            <field name="total_rows" readonly="1"/>
                            <field name="created_count" readonly="1"/>
                            <field name="updated_count" readonly="1"/>
                            <field name="error_count" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook invisible="state != 'imported'">
                        <page string="Import Summary">
                            <field name="import_summary" widget="text" readonly="1"/>
                        </page>
                    </notebook>
                    
                    <div class="alert alert-info" role="alert" invisible="state == 'imported'">
                        <strong>Excel File Format:</strong><br/>
                        The Excel file should contain the following columns (case insensitive):<br/>
                        <ul>
                            <li><strong>first_name</strong> (required) - Visitor's first name</li>
                            <li><strong>middle_name</strong> (required) - Visitor's middle name</li>
                            <li><strong>last_name</strong> (required) - Visitor's last name</li>
                            <li><strong>id_type</strong> (required) - ID Type (e.g., National ID or or Resident ID or Passport)</li>
                            <li><strong>id_number</strong> (required) - National ID or other identification number value</li>
                            <li><strong>email</strong> (required) - Email address</li>
                            <li><strong>organization</strong> (required) - visitor company or organization</li>
                            <li><strong>nationality</strong> (required) - Country name with standard format</li>
                            <li><strong>mobile</strong> (required) - Mobile phone number</li>
                            <li><strong>gender</strong> (required) - Gender</li>
                        </ul>
                        <br/>
                        <strong>Notes:</strong><br/>
                        • The first row should contain column headers<br/>
                        • Visitors will be matched by email<br/>
                        • Empty rows will be skipped<br/>
                        • Existing visitors can be updated if the option is enabled
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Window Action -->
    <record id="action_invitation_visitor_import_wizard" model="ir.actions.act_window">
        <field name="name">Import Visitors from Excel</field>
        <field name="res_model">ams_vm.invitation_visitor_import_wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'default_invitation_id': active_id}</field>
    </record>
</odoo>
