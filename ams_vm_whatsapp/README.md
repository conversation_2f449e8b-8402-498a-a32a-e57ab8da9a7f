# AMS VM WhatsApp Integration Module

This module extends the AMS Visitor Management system to integrate WhatsApp messaging capabilities via Meta's WhatsApp Business API.

## Features

- **WhatsApp Configuration Management**: Configure multiple WhatsApp Business API connections
- **Message Templates**: Pre-defined templates for visitor notifications
- **Automated Notifications**: Send WhatsApp messages when visitor requests are approved/rejected
- **Message Tracking**: Track delivery status and message history
- **Webhook Integration**: Handle delivery status updates from Meta API
- **Service Health Monitoring**: Monitor API connection health and performance
- **Bulk Operations**: Send messages to multiple visitors at once
- **Retry Mechanism**: Automatic retry for failed messages
- **Analytics & Reporting**: Message analytics and template usage reports

## Prerequisites

1. **Meta WhatsApp Business API Access**:
   - WhatsApp Business Account
   - Meta Developer Account
   - Phone Number ID
   - Access Token
   - Webhook Verify Token

2. **Odoo Requirements**:
   - Odoo 18.0+
   - `ams_vm` module installed
   - `requests` Python library

## Installation

1. **Install the Module**:
   ```bash
   # Copy the module to your addons directory
   cp -r ams_vm_whatsapp /path/to/odoo/addons/
   
   # Update the module list
   # Go to Apps > Update Apps List
   
   # Install the module
   # Go to Apps > Search "AMS VM WhatsApp" > Install
   ```

2. **Configure WhatsApp API**:
   - Go to `WhatsApp > Configuration > WhatsApp Configuration`
   - Create a new configuration with your Meta API credentials
   - Test the connection using the "Test Connection" button

3. **Set Up Webhook** (Optional but recommended):
   - Configure your webhook URL in Meta Developer Console
   - Point it to: `https://yourdomain.com/whatsapp/webhook`
   - Use the webhook verify token from your configuration

## Configuration

### WhatsApp API Setup

1. **Get Meta API Credentials**:
   - Visit [Meta for Developers](https://developers.facebook.com/)
   - Create a WhatsApp Business App
   - Get your Phone Number ID and Access Token
   - Generate a Webhook Verify Token

2. **Configure in Odoo**:
   ```
   WhatsApp > Configuration > WhatsApp Configuration
   
   - Name: Your configuration name
   - Phone Number ID: From Meta Console
   - Access Token: From Meta Console
   - Webhook Verify Token: Your generated token
   - API Version: v18.0 (or latest)
   - Active: ✓
   ```

3. **Test Connection**:
   - Use the "Test Connection" button
   - Check the logs for any errors
   - Verify webhook setup if configured

### Message Templates

The module comes with pre-configured templates:

- **Visitor Approval**: Sent when a visitor request is approved
- **Visitor Rejection**: Sent when a visitor request is rejected
- **Visit Reminder**: Reminder notifications
- **Check-in/Check-out**: Visit status updates
- **Emergency Alerts**: Urgent notifications
- **Welcome Message**: New user onboarding

You can customize these templates or create new ones:
```
WhatsApp > Configuration > Message Templates
```

### Visitor Configuration

For each visitor request, you can:

1. **Enable WhatsApp Notifications**:
   - Edit visitor record
   - Check "Send WhatsApp Notification"
   - Enter WhatsApp phone number

2. **Automatic Notifications**:
   - Messages are sent automatically when requests are approved/rejected
   - Manual sending is also available via buttons

## Usage

### Sending Messages

1. **Automatic (Recommended)**:
   - Enable "Send WhatsApp Notification" on visitor records
   - Messages are sent automatically on approval/rejection

2. **Manual**:
   - Go to visitor record
   - Click "Send WhatsApp Message" button
   - Select template and send

3. **Bulk Operations**:
   - Select multiple visitor records
   - Use "Send WhatsApp Messages" action
   - Choose template and send to all

### Monitoring

1. **Message Status**:
   ```
   WhatsApp > Messages > All Messages
   ```
   - View all sent messages
   - Check delivery status
   - Retry failed messages

2. **Analytics**:
   ```
   WhatsApp > Reports > Message Analytics
   ```
   - Delivery success rates
   - Template usage statistics
   - Performance metrics

3. **Service Health**:
   ```
   WhatsApp > Reports > Service Health
   ```
   - API connection status
   - Rate limiting information
   - Error logs

### Troubleshooting

1. **Connection Issues**:
   - Verify API credentials
   - Check network connectivity
   - Review Meta API quotas
   - Test connection from configuration

2. **Message Delivery Failures**:
   - Check phone number format (+**********)
   - Verify template approval status
   - Review rate limiting
   - Check webhook configuration

3. **Webhook Problems**:
   - Verify webhook URL accessibility
   - Check verify token match
   - Review server logs
   - Test webhook endpoint manually

## API Endpoints

The module provides several webhook endpoints:

- `GET/POST /whatsapp/webhook`: Main webhook for Meta API
- `GET /whatsapp/webhook/test`: Test webhook connectivity
- `GET /whatsapp/webhook/status`: Service health check
- `GET /whatsapp/webhook/logs`: Recent webhook logs
- `POST /whatsapp/webhook/send_test`: Send test message

## Security

- All API credentials are encrypted in the database
- Webhook signatures are verified for authenticity
- Access controls based on user groups:
  - `Visitor Manager`: Full access to all features
  - `Visitor User`: Limited access to messages and basic operations
  - `System Admin`: Full system configuration access

## Maintenance

### Automated Tasks

The module includes several cron jobs:

1. **Retry Failed Messages** (Every 30 minutes):
   - Automatically retries failed messages
   - Limited to 3 attempts per message

2. **Cleanup Old Messages** (Daily):
   - Removes old message records
   - Configurable retention period (default: 90 days)

3. **Health Check** (Hourly):
   - Tests API connectivity
   - Logs any issues found

### Manual Maintenance

1. **Quick Actions**:
   ```
   WhatsApp > Quick Actions
   ```
   - Test Connection
   - Cleanup Old Messages
   - Sync Templates

2. **Database Cleanup**:
   - Regularly review message logs
   - Archive old configurations
   - Monitor storage usage

## Support

For technical support:

1. **Check Logs**:
   - Odoo server logs
   - WhatsApp webhook logs
   - Meta API error responses

2. **Common Issues**:
   - Rate limiting: Reduce message frequency
   - Template rejection: Review Meta guidelines
   - Webhook failures: Check server configuration

3. **Documentation**:
   - [Meta WhatsApp Business API Docs](https://developers.facebook.com/docs/whatsapp)
   - [Odoo Development Documentation](https://www.odoo.com/documentation/)

## License

This module is licensed under LGPL-3.

## Changelog

### Version 1.0.0
- Initial release
- Basic WhatsApp integration