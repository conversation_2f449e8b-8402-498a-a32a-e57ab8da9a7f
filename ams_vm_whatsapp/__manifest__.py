# -*- coding: utf-8 -*-
{
    'name': 'AMS VM WhatsApp Integration',
    'version': '********.0',
    'category': 'AMS',
    'summary': 'WhatsApp integration for AMS Visitor Management System',
    'description': """
        WhatsApp Integration for AMS Visitor Management
        ===============================================
        
        This module extends the AMS Visitor Management system to send WhatsApp
        messages to visitors using Meta's WhatsApp Business API.
        
        Features:
        ---------
        * Send WhatsApp messages when visitor requests are approved
        * Integration with Meta WhatsApp Business API
        * Message template management
        * Delivery status tracking
        * Webhook support for message status updates
        * QR code sharing via WhatsApp
        * Multi-language support (Arabic/English)
        
        Requirements:
        ------------
        * Meta WhatsApp Business API access
        * Valid WhatsApp Business Account
        * Approved message templates
    """,
    'author': 'Laplacesoftware',
    'website': 'https://www.laplacesoftware.com/',
    'depends': [
        'base',
        'ams_vm',
        'lp_whatsapp',
    ],
    # No external dependencies needed - using lp_whatsapp for API calls
    'data': [
        'security/ir.model.access.csv',
        # 'data/whatsapp_templates.xml',
        'views/invitation_views.xml',
        'views/invitation_visitor_views.xml',
        'views/whatsapp_message_views.xml',
        'views/whatsapp_menu.xml',
    ],
    'demo': [],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
    'sequence': 100,
}