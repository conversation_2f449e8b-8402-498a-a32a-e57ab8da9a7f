# WhatsApp Integration Workflow Documentation

## Overview

This document outlines the comprehensive end-to-end workflow for WhatsApp message integration within the Ministry of Economy and Planning's (MEP) visitor management system. The workflow covers all stages from visitor request submission to message delivery confirmation and status tracking.

## Workflow Architecture

### 1. System Components Interaction

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   AMS Visitor   │    │   lp_whatsapp    │    │  Meta WhatsApp  │
│   Management    │◄──►│   Integration    │◄──►│  Business API   │
│     System      │    │     Module       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Visitor       │    │   WhatsApp       │    │   Webhook       │
│   Database      │    │   Message        │    │   Status        │
│                 │    │   Tracking       │    │   Updates       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## End-to-End Workflow Process

### 2. Phase 1: Visitor Request Processing

#### 2.1 Visitor Registration
**Trigger**: New visitor request submitted
**Process**:
1. Visitor information captured in AMS system
2. WhatsApp phone number field populated (optional)
3. WhatsApp notification preference set (default: enabled)
4. Visitor record created with pending approval status

**Data Flow**:
```
Visitor Request → AMS Database → WhatsApp Phone Validation → Notification Preference Setting
```

**Key Fields**:
- `whatsapp_phone`: Visitor's WhatsApp number with country code
- `send_whatsapp_notification`: Boolean flag for notification preference
- `whatsapp_template_id`: Selected message template
- `whatsapp_service_id`: WhatsApp service configuration

#### 2.2 Approval Workflow Integration
**Trigger**: Visitor request approved by authorized personnel
**Process**:
1. Approval action triggered in AMS system
2. System checks WhatsApp notification settings
3. WhatsApp service validation performed
4. Message preparation initiated

**Validation Checks**:
- WhatsApp notification enabled for visitor
- Valid WhatsApp phone number present
- Active WhatsApp service configuration available
- Approved message template exists

### 3. Phase 2: Message Preparation and Sending

#### 3.1 Template Selection and Parameter Preparation
**Process**:
1. System retrieves appropriate message template
2. Template parameters extracted from visitor data
3. Message content formatted with dynamic values
4. Language preference applied (Arabic/English)

**Template Parameters**:
```python
{
    'visitor_name': 'John Doe',
    'visit_date': '2024-01-15',
    'visit_time': '10:00 AM',
    'company_name': 'Ministry of Economy and Planning',
    'location': 'Main Building - Floor 3',
    'requester_name': 'Ahmed Al-Rashid'
}
```

#### 3.2 WhatsApp Service Invocation
**Process**:
1. WhatsApp service configuration retrieved
2. Meta API credentials validated
3. Message payload constructed
4. API call initiated to Meta WhatsApp Business API

**API Call Structure**:
```json
{
    "messaging_product": "whatsapp",
    "to": "************",
    "type": "template",
    "template": {
        "name": "visitor_approval_ar",
        "language": {
            "code": "ar"
        },
        "components": [
            {
                "type": "body",
                "parameters": [
                    {"type": "text", "text": "John Doe"},
                    {"type": "text", "text": "2024-01-15"},
                    {"type": "text", "text": "Ministry of Economy and Planning"}
                ]
            }
        ]
    }
}
```

#### 3.3 Message Tracking Creation
**Process**:
1. WhatsApp message record created in database
2. Initial status set to 'pending'
3. Message linked to visitor record
4. Tracking information stored for monitoring

**Message Record Fields**:
- `recipient_phone`: Target WhatsApp number
- `message_type`: 'visitor_notification'
- `template_id`: Used template reference
- `status`: Current delivery status
- `whatsapp_message_id`: Meta API message ID
- `visitor_id`: Link to visitor record

### 4. Phase 3: Message Delivery and Status Tracking

#### 4.1 Meta API Response Processing
**Success Response**:
```json
{
    "messaging_product": "whatsapp",
    "contacts": [
        {
            "input": "************",
            "wa_id": "************"
        }
    ],
    "messages": [
        {
            "id": "wamid.************************************************************************"
        }
    ]
}
```

**Process**:
1. API response received and validated
2. Message status updated to 'sent'
3. WhatsApp message ID stored
4. Delivery timestamp recorded
5. Visitor record status updated

#### 4.2 Webhook Status Updates
**Webhook Endpoint**: `POST /whatsapp/webhook`
**Process**:
1. Meta sends delivery status updates to webhook
2. Webhook signature verified for security
3. Status update processed and validated
4. Message record updated with new status
5. Timestamp information recorded

**Status Update Flow**:
```
sent → delivered → read (optional)
  ↓
failed (if delivery unsuccessful)
```

**Webhook Payload Example**:
```json
{
    "object": "whatsapp_business_account",
    "entry": [
        {
            "id": "WHATSAPP_BUSINESS_ACCOUNT_ID",
            "changes": [
                {
                    "value": {
                        "messaging_product": "whatsapp",
                        "metadata": {
                            "display_phone_number": "************",
                            "phone_number_id": "PHONE_NUMBER_ID"
                        },
                        "statuses": [
                            {
                                "id": "wamid.************************************************************************",
                                "status": "delivered",
                                "timestamp": "**********",
                                "recipient_id": "************"
                            }
                        ]
                    },
                    "field": "messages"
                }
            ]
        }
    ]
}
```

### 5. Phase 4: Error Handling and Retry Mechanisms

#### 5.1 Error Detection and Classification
**API Errors**:
- Authentication failures (invalid tokens)
- Rate limiting exceeded
- Invalid phone numbers
- Template not approved
- Network connectivity issues

**Processing Errors**:
- Database connection failures
- Template parameter validation errors
- Webhook processing failures
- Configuration issues

#### 5.2 Retry Logic Implementation
**Automatic Retry Process**:
1. Failed message identified
2. Error type classified
3. Retry eligibility determined
4. Exponential backoff applied
5. Maximum retry attempts enforced (3 attempts)

**Retry Schedule**:
- First retry: 1 minute after failure
- Second retry: 5 minutes after first retry
- Third retry: 15 minutes after second retry
- Final failure: Manual intervention required

#### 5.3 Manual Intervention Workflow
**Process**:
1. Failed messages flagged for review
2. Administrative notification sent
3. Manual retry option available
4. Error resolution tracking
5. Success confirmation required

### 6. Monitoring and Analytics Workflow

#### 6.1 Real-time Monitoring
**Metrics Tracked**:
- Message delivery success rate
- Average response time
- API call volume and rate limiting
- Template usage statistics
- Error frequency and types

**Monitoring Dashboard**:
- Live message status updates
- Service health indicators
- Performance metrics visualization
- Alert notifications for issues

#### 6.2 Reporting and Analytics
**Daily Reports**:
- Message volume and delivery rates
- Template performance analysis
- Error summary and resolution status
- Service availability metrics

**Monthly Analytics**:
- Trend analysis and patterns
- Template effectiveness evaluation
- System performance optimization recommendations
- Capacity planning insights

### 7. Bulk Operations Workflow

#### 7.1 Bulk Message Sending
**Trigger**: Multiple visitor approvals processed simultaneously
**Process**:
1. Batch of approved visitors identified
2. WhatsApp eligibility validation performed
3. Messages queued for processing
4. Rate limiting compliance ensured
5. Batch processing initiated with progress tracking

**Queue Management**:
- Priority-based message ordering
- Rate limit compliance (60 messages/minute)
- Progress tracking and status updates
- Error handling for individual failures

#### 7.2 Bulk Status Updates
**Process**:
1. Webhook receives multiple status updates
2. Batch processing of status changes
3. Database updates performed efficiently
4. Notification aggregation for administrators
5. Summary reporting generated

### 8. Security and Compliance Workflow

#### 8.1 Data Protection Measures
**Process**:
1. Phone number encryption at rest
2. API credential secure storage
3. Webhook signature verification
4. Access control validation
5. Audit trail maintenance

#### 8.2 Compliance Monitoring
**Process**:
1. Message content compliance validation
2. Template approval status verification
3. Data retention policy enforcement
4. Privacy regulation compliance
5. Audit log generation and storage

## Workflow Performance Metrics

### 9. Key Performance Indicators

**Delivery Metrics**:
- Message delivery success rate: Target >95%
- Average delivery time: Target <30 seconds
- Template approval rate: Target >90%
- System availability: Target >99.5%

**Operational Metrics**:
- Processing throughput: 60 messages/minute
- Error resolution time: Target <2 hours
- User satisfaction: Target >90%
- System response time: Target <3 seconds

### 10. Workflow Optimization Opportunities

**Immediate Improvements**:
- Template pre-approval automation
- Enhanced error classification
- Improved retry logic efficiency
- Real-time monitoring enhancements

**Future Enhancements**:
- AI-powered message optimization
- Predictive analytics for delivery success
- Advanced personalization capabilities
- Integration with additional communication channels

## Conclusion

The WhatsApp integration workflow provides a comprehensive, secure, and efficient process for delivering visitor notifications through the Meta WhatsApp Business API. The workflow ensures reliable message delivery, comprehensive tracking, and robust error handling while maintaining compliance with security and privacy requirements.

The modular design allows for future enhancements and scalability while providing immediate value through automated visitor notification capabilities.