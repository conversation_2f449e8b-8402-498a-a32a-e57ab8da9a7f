# -*- coding: utf-8 -*-
import json
import logging
import requests
from odoo import http
from odoo.http import request
from odoo.addons.lp_whatsapp.controllers.whatsapp_webhook import WhatsAppWebhook  # <-- adjust to your module path

_logger = logging.getLogger(__name__)

class WhatsAppWebhookExtended(WhatsAppWebhook):
    """
    Inherits WhatsAppWebhook to add media upload endpoint
    """


    @http.route('/whatsapp/media/upload', type='http', auth='public', methods=['POST'], csrf=False)
    def whatsapp_media_upload(self, **kwargs):
        try:
            upload = request.httprequest.files.get('file')
            file_type = request.params.get('type', upload.content_type)

            if not upload:
                return request.make_json_response({'status': 'error', 'message': 'File is required'})

            # Read the file
            file_data = upload.read()

            config = request.env['whatsapp.config'].sudo().search([('is_active', '=', True)], limit=1)
            if not config:
                return request.make_json_response({'status': 'error', 'message': 'No active WhatsApp config found'})

            graph_url = f"https://graph.facebook.com/{config.api_version}/{config.phone_number_id}/media"
            headers = {'Authorization': f'Bearer {config.access_token}'}

            files = {'file': (upload.filename, file_data, file_type)}
            data = {'messaging_product': 'whatsapp'}


            response = requests.post(graph_url, headers=headers, files=files, data=data)
            response_json = response.json()

            if response.status_code == 200:
                return request.make_json_response({'status': 'success', 'data': response_json})
            else:
                return request.make_json_response({
                    'status': 'error',
                    'message': response_json.get('error', {}).get('message', 'Upload failed'),
                    'details': response_json
                })

        except Exception as e:
            _logger.exception("Media upload error")
            return request.make_json_response({'status': 'error', 'message': str(e)})

    @http.route('/whatsapp/send/document', type='http', auth='public', methods=['POST'], csrf=False)
    def whatsapp_send_document(self, **kwargs):
        """
        Sends a WhatsApp document message using an already uploaded media ID.
        Accepts JSON body sent from Odoo backend or Postman.
        """
        try:
            # 1️⃣ Parse JSON body manually
            try:
                body = request.httprequest.data.decode('utf-8')
                data = json.loads(body)
            except Exception:
                return request.make_json_response({
                    'status': 'error',
                    'message': 'Invalid or missing JSON body'
                })

            _logger.info("Received WhatsApp send request body: %s", data)

            # 2️⃣ Get active WhatsApp configuration
            config = request.env['whatsapp.config'].sudo().search([('is_active', '=', True)], limit=1)
            if not config:
                return request.make_json_response({'status': 'error', 'message': 'No active WhatsApp config found'})

            # 3️⃣ Prepare Graph API request
            graph_url = f"https://graph.facebook.com/{config.api_version}/{config.phone_number_id}/messages"
            headers = {
                'Authorization': f'Bearer {config.access_token}',
                'Content-Type': 'application/json'
            }

            # 4️⃣ Send request to WhatsApp Graph API
            response = requests.post(graph_url, headers=headers, json=data)
            response_json = response.json()

            _logger.info("Graph API response: %s", response_json)

            if response.status_code == 200:
                return request.make_json_response({'status': 'success', 'data': response_json})
            else:
                return request.make_json_response({
                    'status': 'error',
                    'message': response_json.get('error', {}).get('message', 'Message send failed'),
                    'details': response_json
                })

        except Exception as e:
            _logger.exception("WhatsApp send document error")
            return request.make_json_response({'status': 'error', 'message': str(e)})
