<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Visitor-Specific WhatsApp Templates -->
        
        <!-- Visitor Approval Template -->
        <record id="template_visitor_approval" model="whatsapp.template">
            <field name="name">Visitor Approval Notification</field>
            <field name="template_id">visitor_approval_001</field>
            <field name="language">en</field>
            <field name="category">UTILITY</field>
            <field name="status">APPROVED</field>
            <field name="header_type">TEXT</field>
            <field name="header_text">🎉 Visitor Request Approved</field>
            <field name="body_text">Hello {{1}},

Great news! Your visitor request has been approved.

📋 *Request Details:*
• Visitor: {{2}}
• Visit Date: {{3}}
• Purpose: {{4}}
• Location: {{5}}

✅ Your visitor can now proceed with the scheduled visit. Please ensure they bring a valid ID for verification.

If you have any questions, feel free to contact us.

Thank you!</field>
            <field name="footer_text">Automated message from Visitor Management System</field>
            <field name="parameter_count">5</field>
            <field name="active">True</field>
        </record>
        
        <!-- Visitor Rejection Template -->
        <record id="template_visitor_rejection" model="whatsapp.template">
            <field name="name">Visitor Rejection Notification</field>
            <field name="template_id">visitor_rejection_001</field>
            <field name="language">en</field>
            <field name="category">UTILITY</field>
            <field name="status">APPROVED</field>
            <field name="header_type">TEXT</field>
            <field name="header_text">❌ Visitor Request Update</field>
            <field name="body_text">Hello {{1}},

We regret to inform you that your visitor request has been declined.

📋 *Request Details:*
• Visitor: {{2}}
• Requested Date: {{3}}
• Purpose: {{4}}

❌ *Reason:* {{5}}

If you believe this is an error or would like to discuss this decision, please contact our support team.

Thank you for your understanding.</field>
            <field name="footer_text">Automated message from Visitor Management System</field>
            <field name="parameter_count">5</field>
            <field name="active">True</field>
        </record>
        
        <!-- Visitor Reminder Template -->
        <record id="template_visitor_reminder" model="whatsapp.template">
            <field name="name">Visit Reminder</field>
            <field name="template_id">visit_reminder_001</field>
            <field name="language">en</field>
            <field name="category">UTILITY</field>
            <field name="status">APPROVED</field>
            <field name="header_type">TEXT</field>
            <field name="header_text">⏰ Visit Reminder</field>
            <field name="body_text">Hello {{1}},

This is a friendly reminder about your upcoming visit:

📅 *Visit Details:*
• Date: {{2}}
• Time: {{3}}
• Visitor: {{4}}
• Location: {{5}}

📝 *Important Reminders:*
• Please arrive 15 minutes early
• Bring a valid government-issued ID
• Follow all safety protocols

We look forward to your visit!</field>
            <field name="footer_text">Automated message from Visitor Management System</field>
            <field name="parameter_count">5</field>
            <field name="active">True</field>
        </record>
        
        <!-- Visit Check-in Template -->
        <record id="template_visit_checkin" model="whatsapp.template">
            <field name="name">Visit Check-in Confirmation</field>
            <field name="template_id">visit_checkin_001</field>
            <field name="language">en</field>
            <field name="category">UTILITY</field>
            <field name="status">APPROVED</field>
            <field name="header_type">TEXT</field>
            <field name="header_text">✅ Check-in Successful</field>
            <field name="body_text">Hello {{1}},

Your visitor has successfully checked in:

👤 *Check-in Details:*
• Visitor: {{2}}
• Check-in Time: {{3}}
• Location: {{4}}

✅ The visit is now in progress. You will receive another notification when the visitor checks out.

Thank you!</field>
            <field name="footer_text">Automated message from Visitor Management System</field>
            <field name="parameter_count">4</field>
            <field name="active">True</field>
        </record>
        
        <!-- Visit Check-out Template -->
        <record id="template_visit_checkout" model="whatsapp.template">
            <field name="name">Visit Check-out Confirmation</field>
            <field name="template_id">visit_checkout_001</field>
            <field name="language">en</field>
            <field name="category">UTILITY</field>
            <field name="status">APPROVED</field>
            <field name="header_type">TEXT</field>
            <field name="header_text">🚪 Check-out Complete</field>
            <field name="body_text">Hello {{1}},

Your visitor has completed their visit:

👤 *Visit Summary:*
• Visitor: {{2}}
• Check-out Time: {{3}}
• Total Duration: {{4}}
• Location: {{5}}

✅ The visit has been completed successfully. Thank you for using our visitor management system.

Have a great day!</field>
            <field name="footer_text">Automated message from Visitor Management System</field>
            <field name="parameter_count">5</field>
            <field name="active">True</field>
        </record>
        
        <!-- Welcome Template for Visitors -->
        <record id="template_visitor_welcome" model="whatsapp.template">
            <field name="name">Visitor Welcome Message</field>
            <field name="template_id">visitor_welcome_001</field>
            <field name="language">en</field>
            <field name="category">UTILITY</field>
            <field name="status">APPROVED</field>
            <field name="header_type">TEXT</field>
            <field name="header_text">👋 Welcome to Visitor Management!</field>
            <field name="body_text">Hello {{1}},

Welcome to our Visitor Management System! 🎉

You can now:
✅ Submit visitor requests
✅ Track visit status
✅ Receive real-time notifications
✅ Manage your visitor history

📱 You will receive WhatsApp notifications for all important updates regarding your visitor requests.

If you need assistance, feel free to contact our support team.

Thank you for choosing our services!</field>
            <field name="footer_text">Welcome to Visitor Management System</field>
            <field name="parameter_count">1</field>
            <field name="active">True</field>
        </record>
    </data>
</odoo>