<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Inherit Invitation Form View to Add WhatsApp Button -->
        <record id="invitation_whatsapp_view__form" model="ir.ui.view">
            <field name="name">ams_vm.invitation.whatsapp.form</field>
            <field name="model">ams_vm.invitation</field>
            <field name="inherit_id" ref="ams_vm.invitations_view_form"/>
            <field name="arch" type="xml">
                
                <!-- Add WhatsApp button in header after Send Email button -->
                <xpath expr="//button[@name='action_send_invitation_email']" position="after">
                    <button name="action_send_whatsapp_message"
                            type="object" 
                            string="Send WhatsApp"
                            icon="fa-whatsapp"
                            class="btn-success"
                            invisible="state != 'approved'"
                            help="Send WhatsApp notification to all visitors"/>
                </xpath>
                
                <!-- Add WhatsApp state field in the form -->
                <xpath expr="//field[@name='email_state']" position="after">
<!--                    <field name="whatsapp_state" readonly="1"/>-->
                    <i class="fa fa-whatsapp text-primary mx-2" title="Email Status"></i>
                                <span class="text-info">
                                    <field name="whatsapp_state"
                                           decoration-info="whatsapp_state == 'pending'"
                                           decoration-success="whatsapp_state == 'sent'"
                                           decoration-danger="whatsapp_state == 'failed'"/>
                                </span>
                </xpath>
                
            </field>
        </record>

        <!-- Inherit Invitation List View to Add WhatsApp State Column -->
        <record id="invitation_whatsapp_view_list" model="ir.ui.view">
            <field name="name">ams_vm.invitation.whatsapp.list</field>
            <field name="model">ams_vm.invitation</field>
            <field name="inherit_id" ref="ams_vm.invitation_view_list"/>
            <field name="arch" type="xml">
                
                <!-- Add WhatsApp state column after email_state -->
                <xpath expr="//field[@name='email_state']" position="after">
                    <field name="whatsapp_state" 
                           optional="show"
                           decoration-bf="whatsapp_state == 'pending'"
                           decoration-success="whatsapp_state == 'sent'" 
                           decoration-danger="whatsapp_state == 'failed'"/>
                </xpath>
                
            </field>
        </record>

        <!-- Inherit Base Visitor Request List View to Add WhatsApp State -->
        <record id="base_visitor_request_whatsapp_view_list" model="ir.ui.view">
            <field name="name">ams_vm.base_request.whatsapp.list</field>
            <field name="model">ams_vm.invitation</field>
            <field name="inherit_id" ref="ams_vm.base_visitor_request_view_list"/>
            <field name="arch" type="xml">
                
                <!-- Add WhatsApp state column after email_state -->
                <xpath expr="//field[@name='email_state']" position="after">
                    <field name="whatsapp_state" 
                           optional="show"
                           decoration-bf="whatsapp_state == 'pending'"
                           decoration-success="whatsapp_state == 'sent'" 
                           decoration-danger="whatsapp_state == 'failed'"/>
                </xpath>
                
            </field>
        </record>

        <!-- Add WhatsApp button and state to Invitation Visitor List inside Invitation Form -->
        <record id="invitation_visitor_whatsapp_inline_view_list" model="ir.ui.view">
            <field name="name">ams_vm.invitation_visitor.whatsapp.inline.list</field>
            <field name="model">ams_vm.invitation</field>
            <field name="inherit_id" ref="ams_vm.invitations_view_form"/>
            <field name="arch" type="xml">
                
                <!-- Add WhatsApp state column after email_state in visitors list -->
                <xpath expr="//field[@name='invitation_visitor_ids']//field[@name='email_state']" position="after">
                    <field name="whatsapp_state" 
                           readonly="1" 
                           optional="show"
                           decoration-bf="whatsapp_state == 'pending'"
                           decoration-success="whatsapp_state == 'sent'"
                           decoration-danger="whatsapp_state == 'failed'"/>
                </xpath>
                
                <!-- Add WhatsApp button after email button in visitors list -->
                <xpath expr="//field[@name='invitation_visitor_ids']//button[@name='action_send_invitation_email']" position="after">
                    <button name="action_send_whatsapp_message"
                            type="object"
                            string=""
                            icon="fa-whatsapp"
                            title="Send WhatsApp Notification"
                            invisible="state != 'approved'"
                            />
                </xpath>
                
            </field>
        </record>

        <!-- Server Action for Bulk WhatsApp Send from Invitation Level -->
<!--        <record id="action_invitation_bulk_send_whatsapp" model="ir.actions.server">-->
<!--            <field name="name">Send WhatsApp to All Visitors</field>-->
<!--            <field name="model_id" ref="ams_vm.model_ams_vm_invitation"/>-->
<!--            <field name="binding_model_id" ref="ams_vm.model_ams_vm_invitation"/>-->
<!--            <field name="binding_view_types">list,form</field>-->
<!--            <field name="state">code</field>-->
<!--            <field name="code">-->
<!--                for record in records:-->
<!--                    if record.state == 'approved':-->
<!--                        record.action_send_whatsapp_notification()-->
<!--            </field>-->
<!--        </record>-->

    </data>
</odoo>

