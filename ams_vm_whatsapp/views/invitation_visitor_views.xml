<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Inherit Invitation Visitor Form View -->
<!--        <record id="view_invitation_visitor_form_whatsapp" model="ir.ui.view">-->
<!--            <field name="name">invitation.visitor.form.whatsapp</field>-->
<!--            <field name="model">ams_vm.invitation_visitor</field>-->
<!--            <field name="inherit_id" ref="ams_vm.invitations_view_form"/>-->
<!--            <field name="arch" type="xml">-->
<!--                -->
<!--                &lt;!&ndash; Add WhatsApp tab &ndash;&gt;-->
<!--                <notebook position="inside">-->
<!--                    <page string="WhatsApp" name="whatsapp" invisible="whatsapp_phone == False" >-->
<!--                        <group>-->
<!--                            <group name="whatsapp_config">-->
<!--                                <field name="whatsapp_phone"/>-->
<!--                                <field name="send_whatsapp_notification" widget="boolean_toggle"/>-->
<!--                                <field name="whatsapp_template_id" domain="[('status', '=', 'APPROVED')]"/>-->
<!--                                <field name="whatsapp_service_id"/>-->
<!--                            </group>-->
<!--                            <group name="whatsapp_status">-->
<!--                                <field name="whatsapp_state" readonly="1"/>-->
<!--                                 <field name="whatsapp_status" readonly="1" invisible="whatsapp_state=='pending'"/>-->
<!--                                 <field name="whatsapp_last_sent" readonly="1" invisible="whatsapp_state == 'pending'" />-->
<!--                            </group>-->
<!--                        </group>-->
<!--                        -->
<!--                        <group string="WhatsApp Messages" invisible="whatsapp_message_ids == []" >-->
<!--                            <field name="whatsapp_message_ids" readonly="1" nolabel="1">-->
<!--                                <tree string="WhatsApp Messages" -->
<!--                                      decoration-success="status == 'delivered'"-->
<!--                                      decoration-info="status == 'sent'"-->
<!--                                      decoration-warning="status == 'pending'"-->
<!--                                      decoration-danger="status == 'failed'">-->
<!--                                    <field name="create_date"/>-->
<!--&lt;!&ndash;                                    <field name="message_type"/>&ndash;&gt;-->
<!--&lt;!&ndash;                                    <field name="status" widget="badge"/>&ndash;&gt;-->
<!--                                    <field name="template_id"/>-->
<!--                                    <field name="retry_count"/>-->
<!--                                    <field name="error_message" invisible="status != 'failed'"/>-->
<!--                                </tree>-->
<!--                            </field>-->
<!--                        </group>-->
<!--                        -->
<!--                        <div class="mt-3">-->
<!--                            <button name="action_test_whatsapp_template" type="object" string="Send Test Message" -->
<!--                                    class="btn-secondary" icon="fa-paper-plane"-->
<!--                                    invisible="not whatsapp_phone"/>-->
<!--                            <button name="action_resend_whatsapp_message" type="object" string="Resend WhatsApp" -->
<!--                                    class="btn-primary" icon="fa-refresh"-->
<!--                                    invisible="not whatsapp_phone or state != 'approved'"/>-->
<!--                            <button name="action_view_whatsapp_messages" type="object" string="View All Messages" -->
<!--                                    class="btn-info" icon="fa-comments"-->
<!--                                    invisible="not whatsapp_message_ids"/>-->
<!--                        </div>-->
<!--                        -->
<!--                        <div class="alert alert-info mt-3" role="alert" invisible="whatsapp_phone">-->
<!--                            <h6><i class="fa fa-info-circle"/> WhatsApp Integration</h6>-->
<!--                            <p class="mb-0">-->
<!--                                Add a WhatsApp phone number to enable automatic notifications when this visitor is approved.-->
<!--                                The system will send a personalized message using the configured template.-->
<!--                            </p>-->
<!--                        </div>-->
<!--                        -->
<!--                        <div class="alert alert-warning mt-3" role="alert" invisible="not whatsapp_phone or send_whatsapp_notification">-->
<!--                            <h6><i class="fa fa-exclamation-triangle"/> WhatsApp Notifications Disabled</h6>-->
<!--                            <p class="mb-0">-->
<!--                                WhatsApp notifications are currently disabled for this visitor. -->
<!--                                Enable them to automatically send approval notifications via WhatsApp.-->
<!--                            </p>-->
<!--                        </div>-->
<!--                    </page>-->
<!--                </notebook>-->
<!--                -->
<!--                &lt;!&ndash; Add WhatsApp button in header &ndash;&gt;-->
<!--                <header position="inside">-->
<!--                    <button name="action_send_whatsapp_notification" type="object" string="Send WhatsApp" -->
<!--                            class="btn-success" icon="fa-whatsapp"-->
<!--                            invisible="not whatsapp_phone or state != 'approved' or whatsapp_state == 'sent'"/>-->
<!--                </header>-->
<!--            </field>-->
<!--        </record>-->
<!--        -->
<!--        &lt;!&ndash; Inherit Invitation Visitor Tree View &ndash;&gt;-->
<!--        <record id="view_invitation_visitor_tree_whatsapp" model="ir.ui.view">-->
<!--            <field name="name">invitation.visitor.tree.whatsapp</field>-->
<!--            <field name="model">ams_vm.invitation_visitor</field>-->
<!--            <field name="arch" type="xml">-->
<!--                &lt;!&ndash; Add WhatsApp columns &ndash;&gt;-->
<!--                <field name="qr_code" position="after">-->
<!--                    <field name="whatsapp_phone" optional="hide"/>-->
<!--                    <field name="send_whatsapp_notification" widget="boolean_toggle" optional="hide"/>-->
<!--                    <field name="whatsapp_state" optional="hide"/>-->
<!--                    <field name="whatsapp_status" optional="hide" -->
<!--                           invisible="whatsapp_state == 'pending'"/>-->
<!--                </field>-->
<!--            </field>-->
<!--        </record>-->
        

        <!-- WhatsApp Message Action for Visitors -->
        <record id="action_visitor_whatsapp_messages" model="ir.actions.act_window">
            <field name="name">Visitor WhatsApp Messages</field>
            <field name="res_model">whatsapp.message</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('visitor_id', '!=', False)]</field>
            <field name="context">{'default_message_type': 'visitor_notification'}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No WhatsApp messages sent to visitors yet!
                </p>
                <p>
                    WhatsApp messages are automatically sent to visitors when they are approved,
                    if they have a WhatsApp phone number configured and notifications are enabled.
                </p>
            </field>
        </record>
        
        <!-- Server Action for Bulk WhatsApp Send -->
        <record id="action_bulk_send_whatsapp" model="ir.actions.server">
            <field name="name">Send WhatsApp to Selected Visitors</field>
            <field name="model_id" ref="ams_vm.model_ams_vm_invitation_visitor"/>
            <field name="binding_model_id" ref="ams_vm.model_ams_vm_invitation_visitor"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">records.action_bulk_send_whatsapp()</field>
        </record>
        
        <!-- Server Action for Bulk WhatsApp Retry -->
        <record id="action_bulk_retry_whatsapp" model="ir.actions.server">
            <field name="name">Retry Failed WhatsApp Messages</field>
            <field name="model_id" ref="ams_vm.model_ams_vm_invitation_visitor"/>
            <field name="binding_model_id" ref="ams_vm.model_ams_vm_invitation_visitor"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">records.action_bulk_retry_whatsapp()</field>
        </record>
    </data>
</odoo>