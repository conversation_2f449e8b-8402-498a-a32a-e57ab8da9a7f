<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- WhatsApp Main Menu for Visitor Management -->
        <menuitem id="menu_whatsapp_main"
                  name="WhatsApp"
                  parent="ams_vm.top_menu"
                  sequence="50"
                  groups="base.group_system"/>
        
        <!-- Visitor WhatsApp Messages Menu Item -->
        <menuitem id="menu_visitor_whatsapp_messages"
                  name="Visitor Messages"
                  parent="menu_whatsapp_main"
                  action="lp_whatsapp.action_whatsapp_message"
                  sequence="10"
                   groups="base.group_system"/>
        
        <!-- Visitor WhatsApp Messages Action with visitor filter -->
        <record id="action_visitor_whatsapp_messages" model="ir.actions.act_window">
            <field name="name">Visitor WhatsApp Messages</field>
            <field name="res_model">whatsapp.message</field>
            <field name="view_mode">list,form,graph,pivot</field>
            <field name="domain">[('visitor_id', '!=', False)]</field>
            <field name="context">{
                'search_default_group_status': 1,
                'search_default_group_date': 1,
                'default_message_type': 'visitor_notification',
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No visitor WhatsApp messages yet!
                </p>
                <p>
                    Messages sent to visitors through WhatsApp will appear here.
                    You can track delivery status, view message content, and
                    manage visitor communication history.
                </p>
            </field>
        </record>
        
        <!-- Visitor Messages with specific action -->
        <menuitem id="menu_visitor_whatsapp_messages_filtered"
                  name="Visitor Messages"
                  parent="menu_whatsapp_main"
                  action="action_visitor_whatsapp_messages"
                  sequence="20"
                  groups="base.group_system"/>
    </data>
</odoo>