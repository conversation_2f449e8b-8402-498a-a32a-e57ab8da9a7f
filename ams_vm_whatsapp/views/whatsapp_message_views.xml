<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Visitor WhatsApp Messages Action -->
        <record id="action_visitor_whatsapp_messages" model="ir.actions.act_window">
            <field name="name">Visitor WhatsApp Messages</field>
            <field name="res_model">whatsapp.message</field>
            <field name="view_mode">list,form,graph,pivot</field>
            <field name="domain">[('visitor_id', '!=', False)]</field>
            <field name="context">{
                'search_default_group_status': 1,
                'search_default_group_date': 1,
                'default_message_type': 'visitor_notification',
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No visitor WhatsApp messages yet!
                </p>
                <p>
                    Messages sent to visitors through WhatsApp will appear here.
                    You can track delivery status, view message content, and
                    manage visitor communication history.
                </p>
            </field>
        </record>
        
        <!-- Extend WhatsApp Message Form View for Visitor Context -->
        <record id="view_whatsapp_message_form_visitor" model="ir.ui.view">
            <field name="name">whatsapp.message.form.visitor</field>
            <field name="model">whatsapp.message</field>
            <field name="inherit_id" ref="lp_whatsapp.view_whatsapp_message_form"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_view_visitor']" position="attributes">
                    <attribute name="invisible">0</attribute>
                </xpath>
                <xpath expr="//field[@name='visitor_id']" position="attributes">
                    <attribute name="invisible">0</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>