Here's a well-organized and polished version of your content about **Odoo configuration parameters**, categorized for clarity and readability:

---

# **Odoo Configuration Parameters Guide**

**Odoo**, as a powerful and modular ERP system, provides a wide range of configuration parameters that allow you to customize and optimize its behavior. Whether you’re deploying a new instance or tuning an existing one, understanding these options is crucial.

---

## 🔧 **1. General Settings**

Basic functionality and core system behavior:

* `addons_path`: Directories where Odoo searches for modules.
* `admin_passwd`: Master password for database management.
* `db_name`: Default database name on startup.
* `data_dir`: Path for storing data such as filestore.
* `logfile`: Path to the log file.
* `log_level`: Log verbosity (`info`, `debug`, `error`, etc.).

---

## 🗃️ **2. Database Settings**

Control how Odoo connects to PostgreSQL:

* `db_host`: Database server hostname.
* `db_port`: Port for PostgreSQL (default: `5432`).
* `db_user`: Username for database access.
* `db_password`: Password for the database user.
* `db_maxconn`: Maximum allowed database connections.
* `dbfilter`: Regex filter to match allowed databases.
* `pg_path`: Path to PostgreSQL binaries (for db operations).

---

## ⚙️ **3. Performance and Resource Limits**

Helps manage resource consumption and stability:

* `limit_memory_hard`: Max memory (bytes) before killing a worker.
* `limit_memory_soft`: Memory threshold to raise warnings.
* `limit_time_cpu`: Max CPU time per request (seconds).
* `limit_time_real`: Max real-time per request (seconds).
* `limit_time_real_cron`: Time limit for cron jobs.
* `limit_file_upload`: Max upload file size (bytes).

---

## 🌐 **4. Server Settings**

Defines ports and workers for server operations:

* `http_port`: Port for HTTP server (default: `8069`).
* `xmlrpc_port`: XML-RPC port (often same as HTTP).
* `longpolling_port`: Port for chat/notifications.
* `proxy_mode`: Enable if behind a reverse proxy.
* `workers`: Number of worker processes (`0` for multithreading).

---

## 🔐 **5. Security**

Secure your deployment with these parameters:

* `ssl_certificate`: Path to the SSL cert file.
* `ssl_private_key`: Path to the SSL private key.
* `proxy_mode`: Ensures correct headers from proxy.
* `db_sslmode`: SSL mode for DB (`disable`, `prefer`, `require`, etc.).

---

## 📧 **6. Mail and Notifications**

Configure Odoo’s outgoing email server:

* `smtp_server`: SMTP server address.
* `smtp_port`: SMTP port (`25`, `587`, etc.).
* `smtp_user`: SMTP username.
* `smtp_password`: SMTP password.
* `mail_queue_limit`: Number of emails to send per batch.

---

## 🌍 **7. Localization**

Customize language and timezone:

* `lang`: Default language code (e.g., `en_US`).
* `timezone`: Default timezone (e.g., `UTC`, `Asia/Riyadh`).

---

## 🧪 **8. Developer and Debugging**

For development and testing environments:

* `dev_mode`: Enables developer mode.
* `test_enable`: Activates test framework.
* `debug`: Enables detailed request logging.

---

## 🧠 **9. Advanced Parameters**

Fine-tune protocol behavior and interface settings:

* `jsonrpc`: Enable JSON-RPC protocol.
* `xmlrpc`: Enable XML-RPC protocol.
* `xmlrpc_interface`: Specify interface for XML-RPC.
* `netrpc_interface`: Specify interface for NET-RPC.
* `reportgz`: Enable gzip compression for reports.

---

## 🛠️ **How to Use These Parameters**

You can configure these either in the `odoo.conf` file or via command-line when launching Odoo.

### 📄 *odoo.conf example*:

```ini
[options]
addons_path = /mnt/extra-addons
db_host = localhost
admin_passwd = my_master_password
workers = 4
```

### 💻 *Command-line example*:

```bash
./odoo-bin --addons-path=/mnt/extra-addons --db_host=localhost --workers=4
```

---

Let me know if you'd like a downloadable PDF version or a quick reference cheat sheet.
