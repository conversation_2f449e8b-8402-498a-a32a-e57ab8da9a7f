To **update `DOMPurify.js` in Odoo 18 (Community)** running in **Docker using Docker Compose**, follow these steps carefully.

---

## ✅ Overview

Odoo uses `DOMPurify.js` in its frontend codebase (particularly in `web` module) to sanitize HTML input for XSS protection. If the bundled version is outdated and flagged (e.g., by security scans), you can safely replace it **manually** with the latest version.

> 🔒 Ensure you keep a backup and test thoroughly after changes.

---

## 📍 Step-by-Step Instructions

### Step 1: **Find where Odoo stores `DOMPurify.js`**

In Odoo 18 source code, the library is located inside:

```
/odoo/addons/web/static/lib/dompurify/purify.js
```

This path is inside the container filesystem. You’ll need to replace that file with the latest one from:

🔗 [https://github.com/cure53/DOMPurify/blob/main/dist/purify.js](https://github.com/cure53/DOMPurify/blob/main/dist/purify.js)

---

### Step 2: **Download the Latest DOMPurify.js**

Download the file to your host machine:

```bash
curl -o purify.js https://raw.githubusercontent.com/cure53/DOMPurify/main/dist/purify.js
```

---

### Step 3: **Access the Odoo container**

Run this command to open a shell in the Odoo container:

```bash
docker exec -it your_odoo_container_name bash
```

Replace `your_odoo_container_name` with the actual name (e.g. `alfakhera18-web-1`).

---

### Step 4: **Navigate to the DOMPurify location inside the container**

```bash
cd /opt/odoo/odoo/addons/web/static/lib/dompurify/
```

📌 In some cases, path could also be `/usr/lib/python3/dist-packages/odoo/...` depending on your base image.

---

### Step 5: **Backup the old file (just in case)**

```bash
mv purify.js purify.js.bak
```

---

### Step 6: **Copy the new file into the container**

From your host, run:

```bash
docker cp purify.js your_odoo_container_name:/opt/odoo/odoo/addons/web/static/lib/dompurify/purify.js
```

✅ This will overwrite the old version with the new one.

---

### Step 7: **Rebuild frontend assets**

You need to **regenerate Odoo's minified assets**, otherwise the old version may still be used from cache.

Inside the container:

```bash
cd /opt/odoo
python3 odoo-bin -d your_db_name -u web --dev=assets --stop-after-init
```

Or if you want to recompile assets using the `--load=web` method:

```bash
python3 odoo-bin -c /etc/odoo/odoo.conf -d your_db_name --update=web --dev=assets --stop-after-init
```

---

### Step 8: **Restart Odoo**

Exit the container and restart from your host:

```bash
docker-compose restart
```

---

## 🧪 Final Checks

1. **Inspect the DOMPurify version** in browser dev tools > Sources.
2. **Search for `DOMPurify.version`** in the console to confirm it matches the expected version.
3. **Test any forms or WYSIWYG editors** that use HTML input (e.g., portal comments, chatter) to ensure no regressions.

---

## 🔁 Optional: Automate with Dockerfile

To make this replacement permanent across rebuilds:

### `Dockerfile` (example)

```dockerfile
FROM odoo:18

# Replace DOMPurify.js
ADD purify.js /opt/odoo/odoo/addons/web/static/lib/dompurify/purify.js
```

Then build the image:

```bash
docker build -t my-odoo18 .
```

Update your `docker-compose.yml` to use `my-odoo18` as the image.

---

## 📌 Notes

* Odoo might also load a minified version. If so, replace `purify.min.js` as well.
* Always test the sanitization functionality (especially XSS filtering) after replacing.

---

If you want me to prepare a minimal patch or Dockerfile for this replacement, just let me know.
