## Summary of Technical Findings

**4.1 Insecure Direct Object Reference (IDOR)**
- **Severity:** Medium
- **Observation:** Application allows users to access data belonging to others by manipulating object references (e.g., changing an ID in a request). This was demonstrated using test user privileges to access other users' data.
- **Impact:** Unauthorized access, data modification or deletion, privilege escalation, and compliance violations (e.g., GDPR, HIPAA).
- **Recommendation:** Implement strict access control checks for each object, and use complex identifiers as an additional layer of protection[1].

**4.2 Improper Error Handling**
- **Severity:** Medium
- **Observation:** The application displays detailed error messages (e.g., stack traces, database dumps) to users, which can reveal sensitive system information.
- **Impact:** Information leakage, aiding attackers in understanding the application’s structure.
- **Recommendation:** Display generic, user-friendly error messages and log detailed errors securely for authorized personnel only[1].

**4.3 Debugging Information Disclosure**
- **Severity:** Medium
- **Observation:** Debug messages or error logs containing sensitive information are exposed to users.
- **Impact:** Sensitive data exposure, facilitation of further attacks, and potential Denial of Service (DoS) due to excessive logging.
- **Recommendation:** Disable public access to debug endpoints, implement authentication, and restrict access to trusted internal users[1].

**4.4 Information Disclosure**
- **Severity:** Medium
- **Observation:** Unauthorized users (including external registrants) can access sensitive system and user information.
- **Impact:** Reconnaissance, credential exposure, sensitive data breach, and facilitation of other attacks.
- **Recommendation:** Harden server configuration (disable directory listing, remove unnecessary headers), and restrict external user registration[1].

**4.5 Unsecure Communication and Session Handling**
- **Severity:** Medium
- **Observation:** The application lacks TLS/SSL encryption, transmitting sensitive data over unencrypted connections. Session cookies have a long timeout (7 days).
- **Impact:** Risk of Man-in-the-Middle (MitM) attacks, unauthorized access to sensitive information, and non-compliance with data protection regulations.
- **Recommendation:** Implement TLS/SSL certificates, enforce HTTPS for all traffic, and use short session timeouts[1].

**4.6 Outdated and Vulnerable Component**
- **Severity:** Low
- **Observation:** The application uses an outdated version of the DOMPurify library, which is vulnerable to mutation cross-site scripting (mXSS).
- **Impact:** Potential for mXSS attacks if the vulnerability is exploited.
- **Recommendation:** Upgrade DOMPurify to the latest stable version (3.2.4 or newer)[1].

## Key Takeaways

- **No critical or high-severity vulnerabilities were found.**
- **Medium-severity issues include IDOR, improper error handling, debugging information disclosure, information disclosure, and unsecure communication/session handling.**
- **One low-severity issue is related to an outdated third-party library.**
- **Recommendations focus on access controls, secure error handling, disabling debug endpoints, server hardening, implementing TLS/SSL, and updating vulnerable components[1].**

[1] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/attachments/75912228/941046c3-f4a6-46dc-ab7b-56137354565c/MEP_VMS_WebApp_PT_Report_v1.pdf