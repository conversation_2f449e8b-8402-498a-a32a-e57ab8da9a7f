;Test Environment /etc/odoo/odoo.conf ; /mnt/odoo17e/odoo/addons
[options]
addons_path = /usr/lib/python3/dist-packages/odoo/addons,/mnt/extra-addons,/mnt/odoo18/addons_thp/muk_web_theme,/mnt/odoo18/addons_thp/lp,/mnt/odoo18/addons_thp/others,/mnt/odoo18/addons_thp/oca
logfile = /var/log/odoo/odoo.log
db_user = odoo
db_password = LP_P@ssw0rd
db_name = MEP_18_2025
;dbfilter = False
db_filter = MEP_18_2025

without_demo = True
list_db = odoo.conf
dev_mode = False
debug_mode = False

; HTTP Service Configuration
proxy_mode = True
forwarded = True
xmlrpc_interface = 0.0.0.0
netrpc_interface = 0.0.0.0
xmlrpc_port = 8069
longpolling_port = 8072
gevent_port = 7072

;admin_pass=admin
admin_passwd =Admin@123456


limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 2048
limit_time_cpu = 600
limit_time_real = 1200

;in some cases websocket not work if set max_cron_threads = 1

;max_cron_threads = 1
;workers = 4