# Use Odoo 17 base image
FROM odoo:18.0
# Switch to root user
USER root
RUN pip install --target=/usr/lib/python3/dist-packages pysaml2==7.5.2

# Copy .deb files into image
COPY xmlsec-debs/ /tmp/debs/
# Install .deb packages manually (skip apt-get)
#ENV PYTHONPATH=/usr/lib/python3/dist-packages:$PYTHONPATH

RUN dpkg -i /tmp/debs/*.deb &&  apt-get install -f -y

# Replace DOMPurify.js
COPY js_lib/DOMpurify.js /usr/lib/python3/dist-packages/odoo/addons/web/static/lib/dompurify/DOMpurify.js

USER odoo
