# docker network create lp-apps
networks:
  lp-apps:
    external: true

services:
  web:
    build:
        context: .  # This tells Docker Compose to use the current directory (where Dockerfile is located)

    ports:
      - "8069:8069"
      - "7072:7072"
    volumes:
      - ../../../addons_ams:/mnt/extra-addons
      # mirror third party custom modules
      - ../../../addons_thp:/mnt/odoo18/addons_thp:ro #
      - ./etc:/etc/odoo # mirror odoo config

      # /var/log/odoo/mep18 #shoulde be exist before compose with full permission any user
      # sudo mkdir -p /var/log/odoo/mep18 , sudo chmod 777 /var/log/odoo/mep18
      - /var/log/odoo/mep18:/var/log/odoo
      - odoo-web-data:/var/lib/odoo
    environment:
      - HOST=pg-db # write Postgres VM IP address or hostname or (image service name)
      - HOST=**************
      - USER=odoo
      - PASSWORD=LP_P@ssw0rd
    restart: always             # run as a service

    networks:
      - lp-apps

volumes:
  odoo-web-data:
