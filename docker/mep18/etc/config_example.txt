[options]
; ===================
; | Common options) |
; ===================
; ------
; -c / --config | specify alternate config file
; ------
; config = 

; ------
; -s / --save | save configuration to ~/.odoorc (or to ~/.openerp_serverrc if it exists)
; ------
; save = 

; ------
; -i / --init | install one or more modules (comma-separated list, use "all" for all modules), requires -d
; ------
; init = 

; ------
; -u / --update | update one or more modules (comma-separated list, use "all" for all modules). Requires -d.
; ------
; update = 

; ------
; --without-demo | disable loading demo data for modules to be installed (comma-separated, use "all" for all modules). Requires -d and -i. Default is %default
; ------
; without_demo = 

; ------
; -P / --import-partial | Use this for big data importation, if it crashes you will be able to continue at the current state. Provide a filename to store intermediate importation states.
; ------
; import_partial = 

; ------
; --pidfile | file where the server pid will be stored
; ------
; pidfile = 

; ------
; --addons-path | type = string | specify additional addons paths (separated by commas).
; ------
addons_path = /mnt/extra-addons/enterprise/odoo/addons,/mnt/extra-addons/addons_emr/odoo/addons


; ------
; --upgrade-path | type = string | specify an additional upgrade path.
; ------
; upgrade_path = 

; ------
; --load | Comma-separated list of server-wide modules.
; ------
; server_wide_modules = base,web

; ------
; -D / --data-dir | Directory where to store Odoo data
; ------
data_dir = /etc/odoo

admin_passwd = admin

; ==============================
; | HTTP Service Configuration |
; ==============================
; ------
; --http-interface | Listen interface address for HTTP services. Keep empty to listen on all interfaces (0.0.0.0)
; ------
; http_interface = 

; ------
; -p / --http-port | type = int | Listen port for the main HTTP service
; ------
;http_port = 8069

; ------
; --longpolling-port | type = int | Listen port for the longpolling HTTP service
; ------
; longpolling_port = 8072

; ------
; --no-http | Disable the HTTP and Longpolling services entirely
; ------
; http_enable = True

; ------
; --proxy-mode | Activate reverse proxy WSGI wrappers (headers rewriting) Only enable this when running behind a trusted web proxy!
; ------
; proxy_mode = 

; ------
; --xmlrpc-interface | SUPPRESSHELP
; ------
; http_interface = 

; ------
; --xmlrpc-port | type = int | SUPPRESSHELP
; ------
; http_port = 

; ------
; --no-xmlrpc | SUPPRESSHELP
; ------
; http_enable = 

; ===============================
; | Web interface Configuration |
; ===============================
; ------
; --db-filter | Regular expressions for filtering available databases for Web UI. The expression can use %d (domain) and %h (host) placeholders.
; ------
; dbfilter = 

; =========================
; | Testing Configuration |
; =========================
; ------
; --test-file | Launch a python test file.
; ------
; test_file = 

; ------
; --test-enable | Enable unit tests.
; ------
; test_enable = 

; ------
; --test-tags | Comma-separated list of spec to filter which tests to execute. Enable unit tests if set. A filter spec has the format: [-][tag][/module][:class][.method] The '-' specifies if we want to include or exclude tests matching this spec. The tag will match tags added on a class with a @tagged decorator. By default tag value is 'standard' when not given on include mode. '*' will match all tags. Tag will also match module name (deprecated, use /module) The module, class, and method will respectively match the module name, test class name and test method name. examples: :TestClass.test_func,/test_module,external
; ------
; test_tags = 

; ------
; --screencasts | Screencasts will go in DIR/{db_name}/screencasts.
; ------
; screencasts = 

; ------
; --screenshots | Screenshots will go in DIR/{db_name}/screenshots. Defaults to /etc/odoo/odoo_tests.
; ------
; screenshots = /etc/odoo/odoo_tests

; =========================
; | Logging Configuration |
; =========================
; ------
; --logfile | file where the server log will be stored
; ------
logfile = /etc/odoo/odoo-server.log

; ------
; --syslog | Send the log to the syslog server
; ------
; syslog = 

; ------
; --log-handler | setup a handler at LEVEL for a given PREFIX. An empty PREFIX indicates the root logger. This option can be repeated. Example: "odoo.orm:DEBUG" or "werkzeug:CRITICAL" (default: ":INFO")
; ------
; None = :INFO

; ------
; --log-request | shortcut for --log-handler=odoo.http.rpc.request:DEBUG
; ------
; log_handler = 

; ------
; --log-response | shortcut for --log-handler=odoo.http.rpc.response:DEBUG
; ------
; log_handler = 

; ------
; --log-web | shortcut for --log-handler=odoo.http:DEBUG
; ------
; log_handler = 

; ------
; --log-sql | shortcut for --log-handler=odoo.sql_db:DEBUG
; ------
; log_handler = 

; ------
; --log-db | Logging database
; ------
; log_db = 

; ------
; --log-db-level | Logging database level
; ------
; log_db_level = warning

; ------
; --log-level | type = choice | choices = ['info', 'debug_rpc', 'warn', 'test', 'critical', 'runbot', 'debug_sql', 'error', 'debug', 'debug_rpc_answer', 'notset'] | specify the level of the logging. Accepted values: ['info', 'debug_rpc', 'warn', 'test', 'critical', 'runbot', 'debug_sql', 'error', 'debug', 'debug_rpc_answer', 'notset'].
; ------
; log_level = info

; ======================
; | SMTP Configuration |
; ======================
; ------
; --email-from | specify the SMTP email address for sending email
; ------
; email_from = 

; ------
; --smtp | specify the SMTP server for sending email
; ------
; smtp_server = localhost

; ------
; --smtp-port | type = int | specify the SMTP port
; ------
; smtp_port = 25

; ------
; --smtp-ssl | if passed, SMTP connections will be encrypted with SSL (STARTTLS)
; ------
; smtp_ssl = 

; ------
; --smtp-user | specify the SMTP username for sending email
; ------
; smtp_user = 

; ------
; --smtp-password | specify the SMTP password for sending email
; ------
; smtp_password = 

; ============================
; | Database related options |
; ============================
; ------
; -d / --database | specify the database name
; ------
; db_name = 

; ------
; -r / --db_user | specify the database user name
; ------
; db_user = 

; ------
; -w / --db_password | specify the database password
; ------
; db_password = 

; ------
; --pg_path | specify the pg executable path
; ------
; pg_path = 

; ------
; --db_host | specify the database host
; ------
; db_host = 

; ------
; --db_port | type = int | specify the database port
; ------
; db_port = 

; ------
; --db_sslmode | type = choice | choices = ['disable', 'allow', 'prefer', 'require', 'verify-ca', 'verify-full'] | specify the database ssl connection mode (see PostgreSQL documentation)
; ------
; db_sslmode = prefer

; ------
; --db_maxconn | type = int | specify the maximum number of physical connections to PostgreSQL
; ------
; db_maxconn = 64

; ------
; --db-template | specify a custom database template to create a new database
; ------
; db_template = template0

; ========================
; | Internationalisation |
; ========================
; ------
; --load-language | specifies the languages for the translations you want to be loaded
; ------
; load_language = 

; ------
; -l / --language | specify the language of the translation file. Use it with --i18n-export or --i18n-import
; ------
; language = 

; ------
; --i18n-export | export all sentences to be translated to a CSV file, a PO file or a TGZ archive and exit
; ------
; translate_out = 

; ------
; --i18n-import | import a CSV or a PO file with translations and exit. The '-l' option is required.
; ------
; translate_in = 

; ------
; --i18n-overwrite | overwrites existing translation terms on updating a module or importing a CSV or a PO file.
; ------
; overwrite_existing_translations = 

; ------
; --modules | specify modules to export. Use in combination with --i18n-export
; ------
; translate_modules = 

; ============================
; | Security-related options |
; ============================
; ------
; --no-database-list | Disable the ability to obtain or view the list of databases. Also disable access to the database manager and selector, so be sure to set a proper --database parameter first
; ------
; list_db = True

; ====================
; | Advanced options |
; ====================
; ------
; --dev | type = string | Enable developer mode. Param: List of options separated by comma. Options : all, [pudb|wdb|ipdb|pdb], reload, qweb, werkzeug, xml
; ------
dev_mode = reload

; ------
; --shell-interface | type = string | Specify a preferred REPL to use in shell mode. Supported REPLs are: [ipython|ptpython|bpython|python]
; ------
; shell_interface = 

; ------
; --stop-after-init | stop the server after its initialization
; ------
; stop_after_init = 

; ------
; --osv-memory-count-limit | type = int | Force a limit on the maximum number of records kept in the virtual osv_memory tables. The default is False, which means no count-based limit.
; ------
; osv_memory_count_limit = 

; ------
; --transient-age-limit | type = float | Time limit (decimal value in hours) records created with a TransientModel (mosly wizard) are kept in the database. Default to 1 hour.
; ------
; transient_age_limit = 1.0

; ------
; --osv-memory-age-limit | type = float | Deprecated alias to the transient-age-limit option
; ------
; osv_memory_age_limit = 

; ------
; --max-cron-threads | type = int | Maximum number of threads processing concurrently cron jobs (default 2).
; ------
; max_cron_threads = 2

; ------
; --unaccent | Try to enable the unaccent extension when creating new databases.
; ------
; unaccent = 

; ------
; --geoip-db | Absolute path to the GeoIP database file.
; ------
; geoip_database = /usr/share/GeoIP/GeoLite2-City.mmdb

; ===========================
; | Multiprocessing options |
; ===========================
; ------
; --workers | type = int | Specify the number of workers, 0 disable prefork mode.
; ------
; workers = 

; ------
; --limit-memory-soft | type = int | Maximum allowed virtual memory per worker (in bytes), when reached the worker be reset after the current request (default 2048MiB).
; ------
; limit_memory_soft = 2147483648

; ------
; --limit-memory-hard | type = int | Maximum allowed virtual memory per worker (in bytes), when reached, any memory allocation will fail (default 2560MiB).
; ------
; limit_memory_hard = 2684354560

; ------
; --limit-time-cpu | type = int | Maximum allowed CPU time per request (default 60).
; ------
; limit_time_cpu = 60

; ------
; --limit-time-real | type = int | Maximum allowed Real time per request (default 120).
; ------
; limit_time_real = 120

; ------
; --limit-time-real-cron | type = int | Maximum allowed Real time per cron job. (default: --limit-time-real). Set to 0 for no limit.
; ------
; limit_time_real_cron = -1

; ------
; --limit-request | type = int | Maximum number of request to be processed per worker (default 8192).
; ------
; limit_request = 8192