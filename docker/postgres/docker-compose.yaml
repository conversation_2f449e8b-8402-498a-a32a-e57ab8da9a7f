version: '3'
# (Optional) when using custom network
networks:
  lp-apps:
    external: true

services:
  pg-db:
    # (Recommended) replace "latest" with specific version
    image: postgres:15
    environment:
      - POSTGRES_USER=odoo
      - POSTGRES_PASSWORD=LP_P@ssw0rd
      - POSTGRES_DB=postgres
    ports:
      - 5433:5432
    # (Optional) when using custom network
    networks:
      - lp-apps
    volumes:
      - db-data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  db-data: