# Fire Alarm System and Security Dashboard

## Overview

The Fire Alarm module provides a comprehensive solution for monitoring, managing, and responding to fire alarm events across multiple buildings and facilities. It includes a real-time dashboard for security personnel to monitor alarm status and receive alerts.

## Features

- Real-time monitoring of fire alarm devices
- Centralized management of buildings, zones, and devices
- Event logging and tracking
- Interactive security dashboard
- Notification system for alerts
- Historical reporting and analysis

## Installation

1. Install the module through the Odoo Apps menu
2. Configure buildings, zones, and devices
3. Set up integration with fire alarm hardware (if applicable)
4. Configure user access and notification preferences

## Configuration

### Buildings

Define the buildings that will be monitored by the system:

1. Go to Fire Alarm > Configuration > Buildings
2. Create a new building with name, code, and address
3. Specify the number of floors

### Zones

Define alarm zones within buildings:

1. Go to Fire Alarm > Configuration > Zones
2. Create a new zone with name, code, and building
3. Specify the floor number and description

### Devices

Configure alarm devices:

1. Go to Fire Alarm > Configuration > Devices
2. Create a new device with name, serial number, and type
3. Assign to a building and zone
4. Set maintenance schedule

## Usage

### Dashboard

Access the security dashboard:

1. Go to Fire Alarm > Dashboard
2. View active alarms, recent events, and system status
3. Use filters to focus on specific buildings or zones

### Alarm Management

Manage alarm events:

1. Go to Fire Alarm > Events
2. View, acknowledge, and resolve alarm events
3. Add notes and track response actions

## Demo Data

The module includes demo data to help you get started and test the functionality. The demo data includes:

### Sample Data

- **Fire Alarm Systems**: Siemens FireFinder XLS, Honeywell Notifier

- **Buildings**: Headquarters Building (12 floors), Research Center (5 floors), Data Center (3 floors)

- **Zones**: HQ Ground Floor/First Floor/Second Floor, RC Ground Floor/First Floor, DC Server Room/Network Operations

- **Devices**: Various smoke detectors, heat detectors, manual call points, and gas suppression systems with different statuses (normal, alarm, fault)

- **Events**: Active alarms and faults, acknowledged events, resolved events, test and restore events

### Using the Demo Data

To use the demo data:

1. Install the module with demo data enabled
2. Navigate to the Fire Alarm Dashboard to see an overview of the system
3. Explore the Buildings, Zones, Devices, and Events sections
4. Try acknowledging and resolving active events

## Support

For support, please contact:

- Email: [<EMAIL>](mailto:<EMAIL>)
- Phone: +966 59 277 2339
