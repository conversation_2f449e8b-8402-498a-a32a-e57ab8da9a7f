# Fire Alarm Management System - Technical Architecture

## 1. Overview

The Fire Alarm Management System is an Odoo 18 module designed to monitor, track, and manage fire alarm systems across multiple buildings and facilities. The system provides real-time monitoring of fire alarm events, device status tracking, and comprehensive reporting capabilities.

## 2. System Architecture

### 2.1 Component Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                     Fire Alarm Management System                 │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        Core Components                           │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│  Buildings  │    Zones    │   Devices   │   Events    │ Reports │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Integration Layer                           │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│  REST API   │ Bus System  │ Webhooks    │ Automation  │ Alerts  │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Presentation Layer                          │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│  Dashboard  │ List Views  │ Form Views  │ Map View    │ Charts  │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
```

### 2.2 Module Structure

The Fire Alarm Management System is organized into the following key components:

1. **Core Data Models**:
   - Fire Alarm Systems
   - Buildings
   - Zones
   - Devices
   - Events

2. **User Interface Components**:
   - Modern Dashboard
   - List and Form Views
   - Charts and Statistics
   - Real-time Event Monitor

3. **Integration Components**:
   - REST API Controllers
   - Bus Notification System
   - External System Connectors

4. **Automation Components**:
   - Scheduled Actions
   - Event Handlers
   - Notification System

## 3. Data Model

### 3.1 Entity Relationship Diagram

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│  Fire Alarm   │     │   Building    │     │     Zone      │
│   System      │1   *│               │1   *│               │
├───────────────┤◄────┼───────────────┼◄────┼───────────────┤
│ ID            │     │ ID            │     │ ID            │
│ Name          │     │ Name          │     │ Name          │
│ Active        │     │ Code          │     │ Description   │
└───────────────┘     │ Address       │     │ Building ID   │
                      │ Floors        │     └───────┬───────┘
                      │ Fire Alarm ID │             │
                      └───────────────┘             │
                                                    │
┌───────────────┐                                   │
│    Event      │                                   │
├───────────────┤                          ┌────────▼──────┐
│ ID            │                          │    Device     │
│ Name          │◄─────────────────────────┤               │
│ Device ID     │                        1 ├───────────────┤
│ Event Type    │                          │ ID            │
│ Severity      │                          │ Name          │
│ Event Time    │                          │ Serial Number │
│ Description   │                          │ Device Type   │
│ Acknowledged  │                          │ Status        │
│ Resolved      │                          │ Building ID   │
└───────────────┘                          │ Zone ID       │
                                           │ Location      │
                                           │ IP Address    │
                                           └───────────────┘
```

## 4. Key Components

### 4.1 Fire Alarm Systems

The Fire Alarm System represents the physical fire alarm control panel or system installed in a building or facility.

**Attributes**:
- Name
- Active status
- Associated buildings

### 4.2 Buildings

Buildings represent the physical structures monitored by the fire alarm system.

**Attributes**:
- Name
- Code
- Address
- Number of floors
- Associated fire alarm system
- Geographical coordinates (latitude/longitude)

### 4.3 Zones

Zones are specific areas within a building that are monitored by the fire alarm system.

**Attributes**:
- Name
- Description
- Associated building
- Floor number

### 4.4 Devices

Devices represent the physical fire alarm components installed throughout the buildings.

**Attributes**:
- Name
- Serial number
- Device type (smoke detector, heat detector, manual call point, etc.)
- Status (normal, alarm, fault, disabled, testing)
- Associated building and zone
- Specific location
- IP address (for networked devices)
- Maintenance information

**Device Types**:
- Smoke Detector
- Heat Detector
- Manual Call Point
- Multi-Sensor
- Control Panel
- Gas Suppression System

**Device Status Flow**:
```
┌─────────┐     ┌─────────┐     ┌─────────┐
│ Normal  │────►│  Alarm  │────►│ Testing │
└────┬────┘     └─────────┘     └─────────┘
     │                ▲               ▲
     │                │               │
     ▼                │               │
┌─────────┐           │               │
│  Fault  │───────────┘───────────────┘
└─────────┘
     ▲
     │
     ▼
┌─────────┐
│Disabled │
└─────────┘
```

### 4.5 Events

Events represent incidents detected by the fire alarm system.

**Attributes**:
- Event ID
- Associated device
- Event type
- Severity
- Event time
- Description
- Geographical coordinates
- Acknowledgment status
- Resolution status

**Event Types**:
- Alarm Activated
- Fault Detected
- System Restored
- Test Activation
- Maintenance

**Event Severity Levels**:
- Low
- Medium
- High
- Critical

**Event Status Flow**:
```
┌─────────┐     ┌──────────────┐     ┌──────────┐
│   New   │────►│ Acknowledged │────►│ Resolved │
└─────────┘     └──────────────┘     └──────────┘
```

## 5. Real-time Monitoring System

### 5.1 Dashboard

The system provides a modern, responsive dashboard that displays:

- Active alarms count
- System faults count
- Critical alarms count
- Total devices count
- Online devices count
- Fault devices count
- Total buildings count
- Total zones count
- Events by type (doughnut chart)
- Alarms by building (bar chart)
- Recent events list

### 5.2 Real-time Updates

The system uses Odoo's bus notification system to provide real-time updates to the dashboard and other views:

```
┌────────────────┐     ┌────────────────┐     ┌────────────────┐
│  Event Created │────►│  Bus System    │────►│  Dashboard     │
│  or Updated    │     │                │     │  Update        │
└────────────────┘     └────────────────┘     └────────────────┘
```

**Bus Notification Flow**:
1. An event is created or updated in the system
2. The system sends a notification through the bus system
3. The dashboard and other UI components listen for these notifications
4. When a notification is received, the UI components update their data
5. Charts and statistics are re-rendered with the new data

## 6. API Integration

### 6.1 REST API Endpoints

The system provides REST API endpoints for integration with external systems:

1. **Event Creation Endpoint**:
   - URL: `/fire_alarm/trigger_event`
   - Method: GET/POST
   - Parameters:
     - device_serial: Serial number of the device
     - event_type: Type of event (alarm, fault, restore, test)
     - severity: Severity level (low, medium, high, critical)
     - description: Description of the event

2. **Test Bus Notification Endpoint**:
   - URL: `/fire_alarm/test_bus`
   - Method: GET
   - Purpose: Manually trigger a bus notification for testing

### 6.2 External System Integration

The system can integrate with external fire alarm systems through:

1. **Webhook Receivers**: Endpoints that receive notifications from external systems
2. **API Clients**: Components that query external system APIs
3. **Direct Database Integration**: For systems with accessible databases

## 7. Event Handling Workflow

### 7.1 Event Creation

```
┌────────────────┐     ┌────────────────┐     ┌────────────────┐
│  Event Source  │────►│  Event Created │────►│  Notifications │
│  (Device/API)  │     │  in Database   │     │  Sent          │
└────────────────┘     └────────────────┘     └────────────────┘
                                │
                                ▼
                       ┌────────────────┐
                       │  Dashboard     │
                       │  Updated       │
                       └────────────────┘
```

### 7.2 Event Acknowledgment

```
┌────────────────┐     ┌────────────────┐     ┌────────────────┐
│  User Clicks   │────►│  Event Status  │────►│  Notification  │
│  Acknowledge   │     │  Updated       │     │  Sent          │
└────────────────┘     └────────────────┘     └────────────────┘
                                │
                                ▼
                       ┌────────────────┐
                       │  Dashboard     │
                       │  Updated       │
                       └────────────────┘
```

### 7.3 Event Resolution

```
┌────────────────┐     ┌────────────────┐     ┌────────────────┐
│  User Clicks   │────►│  Event Status  │────►│  Notification  │
│  Resolve       │     │  Updated       │     │  Sent          │
└────────────────┘     └────────────────┘     └────────────────┘
                                │
                                ▼
                       ┌────────────────┐
                       │  Dashboard     │
                       │  Updated       │
                       └────────────────┘
```

## 8. Security Model

### 8.1 Access Rights

The system implements a role-based access control model with the following roles:

1. **Administrator**: Full access to all features
2. **Manager**: Can manage buildings, zones, devices, and view/acknowledge/resolve events
3. **Technician**: Can manage devices and view/acknowledge/resolve events
4. **Operator**: Can view and acknowledge events
5. **Viewer**: Can only view events and statistics

### 8.2 Record Rules

Record rules ensure that users can only access data relevant to their responsibilities:

1. **Company-based Access**: Users can only access records for their own company
2. **Building-based Access**: Users can be restricted to specific buildings
3. **Event Type Access**: Users can be restricted to specific event types

## 9. Reporting and Analytics

### 9.1 Built-in Reports

The system provides several built-in reports:

1. **Alarm History Report**: Historical record of all alarm events
2. **Device Status Report**: Current status of all devices
3. **Building Alarm Frequency Report**: Frequency of alarms by building
4. **Response Time Report**: Time taken to acknowledge and resolve alarms

### 9.2 Analytics Dashboard

The analytics dashboard provides insights into:

1. **Alarm Trends**: Trends in alarm frequency over time
2. **Device Reliability**: Identification of devices with frequent faults
3. **Response Efficiency**: Analysis of response times
4. **Building Risk Assessment**: Risk assessment based on alarm history

## 10. Technical Dependencies

The Fire Alarm Management System depends on:

1. **Odoo Base**: Core Odoo functionality
2. **AMS Base**: Base module for Access Management System
3. **Bus System**: For real-time notifications
4. **Web Framework**: For UI components
5. **Chart.js**: For dashboard visualizations

## 11. Deployment Considerations

### 11.1 System Requirements

- Odoo 18
- PostgreSQL 14+
- Modern web browser with JavaScript enabled
- Network connectivity to fire alarm systems (if integrated)

### 11.2 Performance Considerations

- **Database Indexing**: Critical fields are indexed for performance
- **Query Optimization**: Complex queries are optimized
- **Caching**: Dashboard data is cached where appropriate
- **Real-time Updates**: Bus notifications are optimized to minimize overhead

## 12. Maintenance and Troubleshooting

### 12.1 Common Issues

1. **Bus Notification Issues**: If real-time updates are not working, check:
   - Bus service configuration
   - JavaScript console for errors
   - Network connectivity

2. **Device Status Issues**: If device status is not updating, check:
   - Integration with physical devices
   - Network connectivity
   - Device configuration

### 12.2 Logging

The system implements comprehensive logging:

1. **Event Logs**: All alarm events are logged
2. **System Logs**: System operations are logged
3. **API Logs**: API calls are logged
4. **Error Logs**: Errors are logged with detailed information

## 13. Future Enhancements

Planned future enhancements include:

1. **Mobile Application**: Native mobile app for notifications and management
2. **AI-based Anomaly Detection**: Machine learning for identifying unusual patterns
3. **Expanded Integration**: Additional integrations with third-party systems
4. **Advanced Visualization**: 3D building maps with device locations
5. **Predictive Maintenance**: Predictive analytics for device maintenance

---

*This technical documentation was prepared for the Fire Alarm Management System module. For further information, please contact the technical support team.*
