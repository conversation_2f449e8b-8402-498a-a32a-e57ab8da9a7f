# Fire Alarm Event Logging System

## 1. Overview

The Event Logging System is a critical component of the Fire Alarm Management System, responsible for capturing, storing, and managing all events related to fire alarm systems. This document provides a detailed technical description of the event logging architecture, workflows, and states.

## 2. Event Data Model

### 2.1 Event Structure

Each event in the system is represented by the following attributes:

| Attribute      | Description                                      | Type      | Example Values                      |
|----------------|--------------------------------------------------|-----------|-------------------------------------|
| ID             | Unique identifier                                | Integer   | 1, 2, 3                             |
| Name           | Event name                                       | String    | "Smoke Detector Alarm - Floor 3"    |
| Device ID      | Associated device                                | Many2one  | Reference to fire_alarm.device      |
| Event Type     | Type of event                                    | Selection | alarm, fault, restore, test         |
| Severity       | Event severity                                   | Selection | low, medium, high, critical         |
| Event Time     | When the event occurred                          | Datetime  | 2025-04-16 09:04:59                 |
| Description    | Detailed description                             | Text      | "Smoke detected in server room"     |
| Location       | Specific location                                | String    | "Server Room, 3rd Floor"            |
| Latitude       | Geographical latitude                            | Float     | 24.7136                             |
| Longitude      | Geographical longitude                           | Float     | 46.6753                             |
| Acknowledged   | Whether the event has been acknowledged          | Boolean   | True/False                          |
| Ack Time       | When the event was acknowledged                  | Datetime  | 2025-04-16 09:10:23                 |
| Ack User       | User who acknowledged the event                  | Many2one  | Reference to res.users              |
| Resolved       | Whether the event has been resolved              | Boolean   | True/False                          |
| Resolution Time| When the event was resolved                      | Datetime  | 2025-04-16 09:45:12                 |
| Resolution User| User who resolved the event                      | Many2one  | Reference to res.users              |
| Resolution Note| Notes on how the event was resolved              | Text      | "Reset detector after investigation"|

### 2.2 Event Types

The system supports the following event types:

```
┌─────────────────────────────────────────────────────────────────┐
│                          Event Types                             │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│    Alarm    │    Fault    │   Restore   │    Test     │  Other  │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
```

1. **Alarm**: Indicates a fire alarm condition detected by a device
   - Subtypes: Smoke, Heat, Manual, Gas, Multi-sensor

2. **Fault**: Indicates a fault or error condition in a device or system
   - Subtypes: Device Fault, Communication Fault, Power Fault, System Fault

3. **Restore**: Indicates that a device or system has returned to normal operation
   - Subtypes: Alarm Restore, Fault Restore, System Restore

4. **Test**: Indicates a test activation of a device or system
   - Subtypes: Device Test, System Test, Scheduled Test

5. **Other**: Miscellaneous events that don't fit into the above categories
   - Subtypes: Maintenance, Configuration Change, System Startup/Shutdown

### 2.3 Severity Levels

Events are categorized by severity to help prioritize response:

```
┌─────────────────────────────────────────────────────────────────┐
│                        Severity Levels                           │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│     Low     │   Medium    │    High     │  Critical   │ Unknown │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
```

1. **Critical**: Highest priority events requiring immediate attention
   - Example: Fire alarm in occupied area, multiple detector activations

2. **High**: Important events requiring prompt attention
   - Example: Single detector activation, critical system fault

3. **Medium**: Events requiring attention but not immediately critical
   - Example: Non-critical system fault, device communication issue

4. **Low**: Minor events that should be noted but are not urgent
   - Example: Test activation, routine maintenance notification

5. **Unknown**: Events where severity could not be determined
   - Example: Events from unknown sources or with incomplete data

## 3. Event Lifecycle

### 3.1 Event States

Events in the system progress through the following states:

```
┌─────────┐     ┌──────────────┐     ┌──────────┐
│   New   │────►│ Acknowledged │────►│ Resolved │
└─────────┘     └──────────────┘     └──────────┘
```

1. **New**: The event has been created but not yet acknowledged
   - Created when: A device reports an alarm/fault, or via API
   - Visible indicators: Highlighted in red in the UI, notification sent
   - Actions available: Acknowledge, Resolve

2. **Acknowledged**: The event has been acknowledged by a user
   - Created when: A user acknowledges the event
   - Visible indicators: Highlighted in yellow in the UI
   - Actions available: Resolve, Add notes

3. **Resolved**: The event has been resolved
   - Created when: A user resolves the event, or a restore event is received
   - Visible indicators: Displayed in green or removed from active views
   - Actions available: View details, Reopen (for administrators)

### 3.2 State Transition Rules

The following rules govern state transitions:

1. **New to Acknowledged**:
   - Requires: User with acknowledge permission
   - Records: Timestamp and user ID
   - Triggers: Notification to relevant users, dashboard update

2. **Acknowledged to Resolved**:
   - Requires: User with resolve permission
   - Records: Timestamp, user ID, and optional resolution notes
   - Triggers: Notification to relevant users, dashboard update

3. **Automatic Resolution**:
   - Condition: Restore event received for the same device
   - Records: Timestamp and system user ID
   - Triggers: Dashboard update

4. **Reopening Events** (Admin only):
   - Condition: Need to revisit a resolved event
   - Records: Reopening timestamp and user ID
   - State: Returns to Acknowledged state

## 4. Event Creation Workflow

### 4.1 Event Sources

Events can be created from multiple sources:

```
┌─────────────────────────────────────────────────────────────────┐
│                         Event Sources                            │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│  Physical   │    API      │   Manual    │ Scheduled   │ System  │
│  Devices    │  Endpoints  │   Entry     │   Tests     │ Internal│
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
```

1. **Physical Devices**: Events received directly from fire alarm devices
   - Integration methods: Direct network connection, gateway devices

2. **API Endpoints**: Events received via the REST API
   - Primary endpoint: `/fire_alarm/trigger_event`
   - Authentication: API key or OAuth token

3. **Manual Entry**: Events created manually by users
   - Use cases: Recording events from non-connected systems, historical data entry

4. **Scheduled Tests**: Events generated by scheduled system tests
   - Configuration: Test schedule, devices to test, notification settings

5. **System Internal**: Events generated by the system itself
   - Examples: System startup/shutdown, configuration changes

### 4.2 Event Processing Flow

When an event is created, it goes through the following processing flow:

```
┌────────────┐     ┌────────────┐     ┌────────────┐     ┌────────────┐
│   Event    │────►│  Validate  │────►│  Enrich    │────►│   Store    │
│   Source   │     │   Event    │     │   Event    │     │   Event    │
└────────────┘     └────────────┘     └────────────┘     └────────────┘
                                                               │
                                                               ▼
┌────────────┐     ┌────────────┐     ┌────────────┐     ┌────────────┐
│  Update    │◄────┤   Trigger  │◄────┤   Send     │◄────┤  Process   │
│ Dashboard  │     │ Automation │     │Notifications│     │ Duplicates │
└────────────┘     └────────────┘     └────────────┘     └────────────┘
```

1. **Validation**: Ensure the event data is valid and complete
   - Checks: Required fields, data types, valid references
   - Handling: Invalid events are logged but not processed further

2. **Enrichment**: Add additional information to the event
   - Added data: Device details, location information, building/zone info
   - Sources: Device database, building database, geolocation services

3. **Storage**: Save the event to the database
   - Primary table: fire_alarm.event
   - Indexing: Event time, device ID, event type, severity

4. **Duplicate Processing**: Handle duplicate or related events
   - Detection: Events from same device within time window
   - Handling: Group related events, update existing events

5. **Notification**: Send notifications to relevant users
   - Channels: Email, SMS, in-app notifications, external systems
   - Recipients: Based on event type, severity, location, user roles

6. **Automation**: Trigger automated actions based on event
   - Actions: Update device status, create maintenance requests
   - Conditions: Based on event type, severity, device history

7. **Dashboard Update**: Update real-time dashboards
   - Mechanism: Bus notifications
   - Updates: Event counters, charts, recent events list

## 5. Real-time Notification System

### 5.1 Bus Notification Architecture

The system uses Odoo's bus notification system to provide real-time updates:

```
┌────────────┐     ┌────────────┐     ┌────────────┐     ┌────────────┐
│   Event    │────►│    Bus     │────►│ JavaScript │────►│ Dashboard  │
│  Created   │     │   System   │     │  Listener  │     │   Update   │
└────────────┘     └────────────┘     └────────────┘     └────────────┘
```

1. **Event Trigger**: When an event is created or updated
   - Method: `_send_dashboard_update` in the event model

2. **Bus Message**: A message is sent to the bus channel
   - Channel: 'auto_refresh'
   - Message type: 'auto_refresh'
   - Payload: Contains model name and record ID

3. **Client Listeners**: JavaScript components listen for bus messages
   - Setup: During component initialization
   - Channel subscription: 'auto_refresh'

4. **UI Updates**: When a message is received, the UI is updated
   - Data refresh: Fetch latest data from the server
   - Chart updates: Re-render charts with new data
   - Counter updates: Update statistic counters

### 5.2 Notification Payload Structure

The notification payload has the following structure:

```json
{
  "model": "fire_alarm.event",
  "id": 123,
  "timestamp": "2025-04-16T09:04:59"
}
```

## 6. Event Querying and Filtering

### 6.1 Common Filters

The system provides several pre-defined filters for event queries:

1. **Active Alarms**: All unresolved alarm events
   - Domain: `[('event_type', '=', 'alarm'), ('resolved', '=', False)]`

2. **Active Faults**: All unresolved fault events
   - Domain: `[('event_type', '=', 'fault'), ('resolved', '=', False)]`

3. **Critical Events**: All unresolved critical events
   - Domain: `[('severity', '=', 'critical'), ('resolved', '=', False)]`

4. **Recent Events**: Events from the last 24 hours
   - Domain: `[('event_time', '>=', last_24_hours)]`

5. **By Building**: Events for a specific building
   - Domain: `[('device_id.building_id', '=', building_id)]`

6. **By Device**: Events for a specific device
   - Domain: `[('device_id', '=', device_id)]`

### 6.2 Advanced Search

The system supports advanced search capabilities:

1. **Full-text Search**: Search across event descriptions and names
2. **Date Range Selection**: Filter events by date/time range
3. **Multi-criteria Filtering**: Combine multiple filters
4. **Saved Searches**: Save and reuse common search criteria

## 7. Event Reporting

### 7.1 Standard Reports

The system includes several standard reports for event analysis:

1. **Event History Report**: Complete history of all events
   - Grouping: By date, event type, device, building
   - Filters: Date range, event type, severity, resolution status

2. **Alarm Frequency Report**: Analysis of alarm frequency
   - Grouping: By building, zone, device type
   - Metrics: Count, average response time, resolution time

3. **Response Time Report**: Analysis of response times
   - Metrics: Time to acknowledge, time to resolve
   - Grouping: By event type, severity, time of day

4. **Device Reliability Report**: Analysis of device reliability
   - Metrics: Fault frequency, false alarm rate
   - Grouping: By device type, manufacturer, installation date

### 7.2 Custom Report Builder

The system includes a custom report builder that allows users to:

1. Create custom reports with selected fields
2. Define custom grouping and aggregation
3. Set up scheduled report generation
4. Export reports in various formats (PDF, Excel, CSV)

## 8. Integration with External Systems

### 8.1 API for Event Creation

External systems can create events via the REST API:

**Endpoint**: `/fire_alarm/trigger_event`

**Methods**: GET/POST

**Parameters**:
- `device_serial`: Serial number of the device (required)
- `event_type`: Type of event (alarm, fault, restore, test)
- `severity`: Severity level (low, medium, high, critical)
- `description`: Description of the event
- `latitude`: Geographical latitude (optional)
- `longitude`: Geographical longitude (optional)

**Example Request**:
```
GET /fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=high&description=Smoke%20detected
```

**Example Response**:
```json
{
  "success": true,
  "event_id": 123,
  "message": "Event created successfully"
}
```

### 8.2 Webhook Notifications

The system can send webhook notifications to external systems when events occur:

1. **Configuration**: URL, authentication, event types to notify
2. **Payload**: JSON representation of the event
3. **Retry Logic**: Automatic retry for failed notifications
4. **Verification**: Optional verification of webhook delivery

## 9. Performance Considerations

### 9.1 Database Optimization

The event logging system is optimized for performance:

1. **Indexing**: Key fields are indexed for fast querying
   - Indexed fields: event_time, device_id, event_type, severity, resolved

2. **Partitioning**: For large installations, event data can be partitioned
   - Partition strategy: By date (monthly/quarterly)
   - Benefits: Improved query performance, easier archiving

3. **Query Optimization**: Complex queries are optimized
   - Techniques: Limit field selection, use indexed fields in filters

### 9.2 Archiving Strategy

For long-term storage of event data:

1. **Retention Policy**: Define how long to keep events in the active database
   - Default: 90 days for resolved events, indefinite for unresolved

2. **Archiving Process**: Move older events to archive storage
   - Frequency: Monthly/quarterly
   - Storage: Separate database table or external storage

3. **Data Access**: Provide access to archived data when needed
   - Interface: Special archive search view
   - Performance: Clearly indicate when searching archived data

## 10. Troubleshooting

### 10.1 Common Issues

1. **Missing Events**:
   - Check device connectivity
   - Verify API credentials
   - Check event validation rules

2. **Delayed Notifications**:
   - Check bus service configuration
   - Verify browser WebSocket support
   - Check network latency

3. **Incorrect Event Data**:
   - Verify device configuration
   - Check data mapping in integrations
   - Review data enrichment rules

### 10.2 Diagnostic Tools

The system includes several diagnostic tools:

1. **Event Log Viewer**: Detailed view of all system events
2. **Bus Monitor**: Tool to monitor bus notifications
3. **API Test Tool**: Test API endpoints directly
4. **System Health Dashboard**: Overview of system performance

---

*This technical documentation was prepared for the Fire Alarm Event Logging System. For further information, please contact the technical support team.*
