# Fire Alarm Dashboard and Visualization System

## 1. Overview

The Fire Alarm Dashboard and Visualization System provides real-time monitoring and visualization of fire alarm events, device status, and system health. This document details the technical architecture, components, and functionality of the dashboard system.

## 2. Dashboard Architecture

### 2.1 Component Architecture

The dashboard is built using a modern component-based architecture:

```
┌─────────────────────────────────────────────────────────────────┐
│                     Modern Dashboard Component                   │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   Header    │  │  Statistics │  │   Charts    │  │ Recent  │ │
│  │   Section   │  │   Section   │  │   Section   │  │ Events  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
│                                                                 │
│  ┌─────────────────────┐  ┌─────────────────────────────────┐  │
│  │  AlarmCountItem     │  │        ChartComponent           │  │
│  │  Components         │  │        Components               │  │
│  └─────────────────────┘  └─────────────────────────────────┘  │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 Technology Stack

The dashboard is built using the following technologies:

1. **Frontend Framework**: Odoo Owl Framework
2. **Visualization Library**: Chart.js
3. **Real-time Updates**: Odoo Bus System
4. **Styling**: SCSS with responsive design
5. **Data Access**: Odoo ORM

### 2.3 Component Hierarchy

The dashboard consists of the following components:

1. **ModernDashboard**: Main container component
   - Manages overall state and data fetching
   - Handles real-time updates
   - Controls layout and responsiveness

2. **AlarmCountItem**: Displays count statistics
   - Shows count with icon and label
   - Supports animation and color coding
   - Handles click events for navigation

3. **AlarmChartComponent**: Renders charts
   - Wraps Chart.js functionality
   - Supports different chart types
   - Handles data updates and re-rendering

4. **EventList**: Displays recent events
   - Shows scrollable list of recent events
   - Highlights events by severity
   - Provides action buttons for each event

## 3. Dashboard Data Flow

### 3.1 Data Fetching

The dashboard fetches data from the server using the following process:

```
┌────────────┐     ┌────────────┐     ┌────────────┐     ┌────────────┐
│ Dashboard  │────►│  ORM Call  │────►│  Backend   │────►│ Format &   │
│ Component  │     │            │     │  Method    │     │ Return Data │
└────────────┘     └────────────┘     └────────────┘     └────────────┘
       ▲                                                       │
       └───────────────────────────────────────────────────────┘
```

1. **Initial Load**: When the dashboard is first loaded
   - Method: `fetchData()` in ModernDashboard component
   - Backend: `get_alarm_statistics()` in fire_alarm.event model

2. **Periodic Refresh**: Optional timed refresh
   - Frequency: Configurable (default: disabled)
   - Method: Same as initial load

3. **Manual Refresh**: User-triggered refresh
   - Trigger: Refresh button click
   - Method: Same as initial load

### 3.2 Real-time Updates

The dashboard implements a real-time update mechanism using Odoo's bus notification system. This architecture enables immediate dashboard updates whenever fire alarm events occur, without requiring manual page refreshes.

```
┌────────────┐     ┌────────────┐     ┌────────────┐     ┌────────────┐
│   Event    │────►│    Bus     │────►│ JavaScript │────►│ Dashboard  │
│  Created   │     │   System   │     │  Listener  │     │   Update   │
└────────────┘     └────────────┘     └────────────┘     └────────────┘
```

#### 3.2.1 Bus Notification Architecture

The real-time update system consists of three main components:

1. **Backend Event Publisher**
   - When a fire alarm event is created or updated in the database, the backend automatically publishes a notification to the bus system
   - The notification includes the model name ('fire_alarm.event') and record ID
   - The notification is sent on the 'auto_refresh' channel, which is a standard channel used across the AMS modules

2. **Bus Service**
   - Odoo's bus service acts as the communication layer between backend and frontend
   - It maintains a persistent connection using long-polling technology
   - Messages are queued and delivered reliably to all connected clients
   - The bus service handles connection management, reconnection, and message delivery

3. **Dashboard Subscriber**
   - The dashboard component subscribes to the 'auto_refresh' channel during initialization
   - It registers a listener function that processes incoming notifications
   - When a notification arrives, the listener checks if it's relevant (from the fire_alarm.event model)
   - If relevant, it triggers a data refresh to update the dashboard with the latest information

#### 3.2.2 Data Flow Process

The complete data flow for real-time updates follows this sequence:

1. **Event Trigger**: A fire alarm event is created or updated in the system
   - This can happen through the UI, API, or automated processes

2. **Notification Publishing**: The backend publishes a notification to the bus
   - The notification contains minimal information to identify what changed

3. **Message Delivery**: The bus system delivers the notification to all connected clients
   - This happens nearly instantaneously for active browser sessions

4. **Notification Processing**: The dashboard receives and processes the notification
   - It filters notifications to only respond to relevant ones

5. **Data Refresh**: The dashboard fetches fresh data from the server
   - A complete set of updated statistics is retrieved

6. **State Update**: The dashboard updates its internal state with the new data
   - This includes all statistics, chart data, and recent events

7. **UI Re-rendering**: The dashboard components re-render to reflect the new data
   - Charts are completely redrawn with the updated information
   - Statistics counters are updated with new values
   - Recent events list is refreshed

#### 3.2.3 Optimization Considerations

The real-time update system incorporates several optimizations:

1. **Selective Updates**: Only notifications from the fire_alarm.event model trigger updates
   - This prevents unnecessary refreshes from unrelated changes

2. **Complete Refresh**: When an update is needed, all dashboard data is refreshed
   - This ensures consistency across all dashboard components
   - It simplifies the implementation compared to partial updates

3. **Efficient Chart Rendering**: Charts are updated using a dependency-based approach
   - Charts only re-render when their data actually changes
   - This prevents unnecessary redraws that could impact performance

4. **Visual Feedback**: The dashboard shows the last update time
   - This provides users with confidence that the data is current
   - It also helps with troubleshooting if updates aren't occurring

## 4. Dashboard Components

### 4.1 Statistics Section

The statistics section displays key metrics as count items:

```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│   Active    │  │  Critical   │  │   System    │  │   Total     │
│   Alarms    │  │   Alarms    │  │   Faults    │  │  Devices    │
└─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘

┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│   Online    │  │    Fault    │  │    Total    │  │    Total    │
│   Devices   │  │   Devices   │  │  Buildings  │  │    Zones    │
└─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘
```

Each statistic is displayed with:
- Icon representing the metric
- Count value
- Label describing the metric
- Color coding based on status
- Click action to navigate to related view

### 4.2 Chart Section

The chart section displays visualizations of alarm data:

```
┌─────────────────────────────┐  ┌─────────────────────────────┐
│                             │  │                             │
│                             │  │                             │
│     Events by Type          │  │     Alarms by Building      │
│     (Doughnut Chart)        │  │     (Bar Chart)            │
│                             │  │                             │
│                             │  │                             │
└─────────────────────────────┘  └─────────────────────────────┘
```

1. **Events by Type Chart**:
   - Chart Type: Doughnut
   - Data: Count of events by type (alarm, fault, test, restore)
   - Colors: Color-coded by event type
   - Interaction: Click to filter events by type

2. **Alarms by Building Chart**:
   - Chart Type: Bar
   - Data: Count of alarms by building
   - Sorting: Descending by alarm count
   - Interaction: Click to view alarms for specific building

### 4.3 Recent Events Section

The recent events section displays the latest alarm events:

```
┌─────────────────────────────────────────────────────────────────┐
│ Recent Events                                           View All │
├─────────────────────────────────────────────────────────────────┤
│ [CRITICAL] Smoke Detector Alarm - Server Room                   │
│ Building: HQ, Zone: IT Department, Time: 2 minutes ago          │
├─────────────────────────────────────────────────────────────────┤
│ [HIGH] Manual Call Point - Main Entrance                        │
│ Building: HQ, Zone: Reception, Time: 15 minutes ago             │
├─────────────────────────────────────────────────────────────────┤
│ [MEDIUM] System Fault - Fire Panel Communication                │
│ Building: Branch Office, Zone: System, Time: 1 hour ago         │
├─────────────────────────────────────────────────────────────────┤
│ [LOW] Detector Test - Conference Room                           │
│ Building: HQ, Zone: Meeting Rooms, Time: 3 hours ago            │
└─────────────────────────────────────────────────────────────────┘
```

Features:
- Color-coded by severity
- Shows key information (location, time)
- Scrollable for more events
- Click to view event details
- "View All" button to navigate to full event list

## 5. Responsive Design

### 5.1 Breakpoints

The dashboard is designed to be responsive across different screen sizes:

```
┌─────────────────────────────────────────────────────────────────┐
│                        Responsive Design                         │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│  Desktop    │   Laptop    │   Tablet    │  Mobile     │ Compact │
│ (1920px+)   │ (1366px+)   │ (1024px+)   │ (768px+)    │ (<768px)│
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
```

1. **Desktop View** (1920px and above):
   - Full layout with all components
   - 4 statistics per row
   - Side-by-side charts
   - Extended recent events list

2. **Laptop View** (1366px to 1919px):
   - Full layout with all components
   - 4 statistics per row
   - Side-by-side charts
   - Standard recent events list

3. **Tablet View** (1024px to 1365px):
   - Adjusted layout
   - 2-3 statistics per row
   - Stacked charts
   - Compact recent events list

4. **Mobile View** (768px to 1023px):
   - Vertical layout
   - 2 statistics per row
   - Stacked charts
   - Minimal recent events list

5. **Compact View** (below 768px):
   - Vertical layout
   - 1 statistic per row
   - Stacked charts
   - Very compact recent events list

### 5.2 Responsive Implementation

The responsive design is implemented using:

1. **CSS Grid/Flexbox**: For layout adjustments
2. **Media Queries**: For breakpoint-specific styling
3. **JavaScript**: For dynamic adjustments
4. **Chart Responsiveness**: Charts resize to container

## 6. Chart Implementation

### 6.1 Chart Component

The `AlarmChartComponent` is a reusable component that wraps Chart.js functionality:

```javascript
// Simplified component structure
class AlarmChartComponent {
  // Properties
  props: {
    type: String,      // Chart type (bar, doughnut, etc.)
    data: Object,      // Chart data
    options: Object    // Chart options
  }

  // References
  canvasRef: Reference to canvas element
  chartRef: Reference to Chart.js instance

  // Lifecycle methods
  setup() { ... }      // Initialize component
  onMounted() { ... }  // Create chart when mounted
  onWillUnmount() { ... } // Clean up chart

  // Chart methods
  initChart() { ... }  // Initialize the chart
  willUpdateProps() { ... } // Handle data updates
}
```

### 6.2 Chart Types

The dashboard uses the following chart types:

1. **Doughnut Chart** for Events by Type:
   - Shows distribution of event types
   - Color-coded segments
   - Legend on the right side
   - Hover effects for details

2. **Bar Chart** for Alarms by Building:
   - Horizontal or vertical bars
   - Building names on one axis
   - Alarm counts on the other axis
   - Color-coded by count threshold

### 6.3 Chart Data Structure

Chart data follows the Chart.js data structure:

```javascript
// Doughnut chart data example
{
  labels: ['Alarm', 'Fault', 'Test', 'Restore'],
  datasets: [{
    data: [12, 8, 3, 5],
    backgroundColor: ['#dc3545', '#ffc107', '#0dcaf0', '#198754'],
    borderWidth: 0
  }]
}

// Bar chart data example
{
  labels: ['HQ Building', 'Branch Office', 'Warehouse', 'Data Center'],
  datasets: [{
    label: 'Active Alarms',
    data: [5, 2, 1, 3],
    backgroundColor: '#dc3545',
    borderWidth: 0
  }]
}
```

### 6.4 Chart Update Mechanism

Charts are updated when data changes using the following approach:

1. **Data Change Detection**: Using Owl's reactivity system
2. **Chart Update**: Using Chart.js update method
3. **Animation**: Smooth transitions between data states

## 7. Real-time Updates Implementation

### 7.1 Bus Service Integration

The dashboard integrates with Odoo's bus service for real-time updates:

```javascript
// Simplified bus service integration
setup() {
  // Get bus service
  this.busService = this.env.services.bus_service;

  // Set up listener
  const refreshBusListener = async (payload) => {
    if (payload.model === "fire_alarm.event") {
      // Fetch new data
      this.fetchData();
    }
  };

  // Subscribe to channel
  this.busService.subscribe('auto_refresh', refreshBusListener);
  this.busService.addChannel('auto_refresh');

  // Clean up function
  this._refreshStopBus = () => {
    this.busService.unsubscribe('auto_refresh', refreshBusListener);
    this.busService.deleteChannel('auto_refresh');
  };
}
```

### 7.2 Update Workflow

When an event occurs, the following update workflow is triggered:

1. **Event Creation/Update**: An event is created or updated in the backend
2. **Bus Notification**: The backend sends a notification through the bus
3. **Client Reception**: The dashboard receives the notification
4. **Data Refresh**: The dashboard fetches fresh data
5. **UI Update**: The UI components update to reflect the new data

### 7.3 Optimizations

Several optimizations are implemented to ensure efficient updates:

1. **Debouncing**: Multiple rapid updates are debounced
2. **Selective Updates**: Only affected components are updated
3. **Chart Update**: Charts are updated without full re-rendering
4. **Caching**: Temporary caching of data to reduce server load

## 8. User Interactions

### 8.1 Navigation Actions

The dashboard provides several navigation actions:

1. **View All Alarms**: Navigate to the alarm list view
2. **View Active Alarms**: Navigate to active alarms list
3. **View Devices**: Navigate to the device list
4. **View Buildings**: Navigate to the building list
5. **View Event Details**: Navigate to specific event form

### 8.2 Dashboard Controls

The dashboard includes the following controls:

1. **Refresh Button**: Manually refresh dashboard data
2. **View Mode Toggle**: Switch between grid and list views
3. **Time Range Selector**: Filter data by time range
4. **Filter Controls**: Apply various filters to the data

### 8.3 Chart Interactions

Charts support the following interactions:

1. **Hover**: Show detailed information for data points
2. **Click**: Navigate to filtered view of data
3. **Legend Click**: Toggle visibility of data series
4. **Zoom**: On some charts, zoom in/out of data

## 9. Performance Considerations

### 9.1 Rendering Optimization

The dashboard implements several rendering optimizations:

1. **Lazy Loading**: Components are loaded only when needed
2. **Virtualization**: For long lists of events
3. **Efficient DOM Updates**: Minimize DOM manipulations
4. **CSS Optimization**: Efficient CSS selectors and properties

### 9.2 Data Optimization

Data handling is optimized for performance:

1. **Minimal Data Transfer**: Only necessary data is fetched
2. **Data Transformation**: Data is transformed on the server when possible
3. **Incremental Updates**: Only changed data is processed
4. **Pagination**: Large datasets are paginated

### 9.3 Real-time Update Optimization

Real-time updates are optimized to minimize overhead:

1. **Payload Size**: Minimal notification payload
2. **Update Frequency**: Controlled update frequency
3. **Selective Updates**: Only update what has changed
4. **Connection Management**: Efficient connection handling

## 10. Customization Options

### 10.1 User Preferences

Users can customize the dashboard through preferences:

1. **Layout**: Grid or list view preference
2. **Default Filters**: Preferred default filters
3. **Chart Types**: Preferred chart visualizations
4. **Color Scheme**: Light or dark mode

### 10.2 Administrator Configuration

Administrators can configure system-wide dashboard settings:

1. **Available Metrics**: Which metrics to display
2. **Default Views**: Default dashboard configuration
3. **Refresh Rate**: Auto-refresh frequency
4. **Chart Configuration**: Default chart settings

## 11. Testing and Debugging

### 11.1 Testing Tools

The dashboard includes several tools for testing:

1. **Test Bus Notification**: Endpoint to send test notifications
2. **Console Logging**: Detailed logging for debugging
3. **Performance Monitoring**: Tools to monitor performance

### 11.2 Common Issues

Common dashboard issues and their solutions:

1. **Charts Not Rendering**:
   - Check Chart.js loading
   - Verify data structure
   - Check canvas element

2. **Real-time Updates Not Working**:
   - Verify bus service configuration
   - Check channel subscription
   - Inspect network for longpolling requests

3. **Performance Issues**:
   - Check data volume
   - Monitor browser memory usage
   - Review chart rendering options

---

*This technical documentation was prepared for the Fire Alarm Dashboard and Visualization System. For further information, please contact the technical support team.*
