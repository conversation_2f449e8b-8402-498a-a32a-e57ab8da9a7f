# Fire Alarm System Integration Guide

## 1. Overview

The Fire Alarm Management System is designed to integrate with various external fire alarm systems, building management systems, and other enterprise applications. This document provides a comprehensive guide to the integration capabilities, APIs, and implementation strategies.

## 2. Integration Architecture

### 2.1 Integration Layers

The system implements a multi-layered integration architecture:

```
┌─────────────────────────────────────────────────────────────────┐
│                     External Systems                             │
│  (Fire Alarm Panels, BMS, Notification Systems, ERP, etc.)      │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Integration Layer                            │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│  REST API   │ Webhooks    │ Database    │ File-based  │ Custom  │
│  Endpoints  │ (Inbound)   │ Integration │ Integration │ Adapters│
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Processing Layer                             │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│  Validation │ Transformation│ Enrichment │ Routing     │ Logging │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Core System                                  │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│  Data Models│ Business    │ Notification│ Dashboard   │ Reporting│
│             │ Logic       │ System      │ System      │ System   │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
```

### 2.2 Integration Methods

The system supports the following integration methods:

1. **REST API**: HTTP-based API for real-time integration
   - Primary use: Event creation, device management, data queries
   - Authentication: API keys, OAuth tokens
   - Format: JSON

2. **Webhooks**: Inbound HTTP callbacks for event notifications
   - Primary use: Receiving events from external systems
   - Authentication: Shared secrets, IP whitelisting
   - Format: JSON

3. **Database Integration**: Direct database connections
   - Primary use: Batch processing, reporting
   - Methods: Database links, ETL processes
   - Security: Read-only access, dedicated users

4. **File-based Integration**: File exchange for batch processing
   - Primary use: Bulk data import/export
   - Formats: CSV, XML, JSON
   - Transport: SFTP, shared storage

5. **Custom Adapters**: Purpose-built integration components
   - Primary use: Complex or proprietary integrations
   - Implementation: Python modules, external services
   - Deployment: Server-side components

## 3. REST API

### 3.1 API Overview

The REST API provides programmatic access to fire alarm system data and functionality:

```
┌─────────────────────────────────────────────────────────────────┐
│                         REST API                                 │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│   Events    │  Devices    │  Buildings  │   Zones     │ System  │
│   Endpoints │  Endpoints  │  Endpoints  │  Endpoints  │ Endpoints│
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
```

### 3.2 Authentication

The API supports the following authentication methods:

1. **API Key**: Simple key-based authentication
   - Implementation: HTTP header `X-API-Key`
   - Key management: Generated in admin interface

2. **OAuth 2.0**: Token-based authentication
   - Flows supported: Client credentials, authorization code
   - Token management: Standard OAuth flows

3. **Session Authentication**: For browser-based access
   - Implementation: Cookie-based session
   - Usage: Testing, admin interfaces

### 3.3 Event API Endpoints

#### 3.3.1 Create Event

**Endpoint**: `/fire_alarm/trigger_event`

**Methods**: GET/POST

**Parameters**:
- `device_serial`: Serial number of the device (required)
- `event_type`: Type of event (alarm, fault, restore, test)
- `severity`: Severity level (low, medium, high, critical)
- `description`: Description of the event
- `latitude`: Geographical latitude (optional)
- `longitude`: Geographical longitude (optional)

**Example Request**:
```
GET /fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=high&description=Smoke%20detected
```

**Example Response**:
```json
{
  "success": true,
  "event_id": 123,
  "message": "Event created successfully"
}
```

#### 3.3.2 Get Events

**Endpoint**: `/api/fire_alarm.event`

**Method**: GET

**Parameters**:
- `filters`: JSON-encoded filters
- `limit`: Maximum number of records to return
- `offset`: Offset for pagination
- `fields`: Comma-separated list of fields to include

**Example Request**:
```
GET /api/fire_alarm.event?filters=[["event_type","=","alarm"],["resolved","=",false]]&limit=10
```

**Example Response**:
```json
{
  "count": 5,
  "results": [
    {
      "id": 123,
      "name": "Smoke Detector Alarm - Server Room",
      "event_type": "alarm",
      "severity": "high",
      "event_time": "2025-04-16T09:04:59",
      "description": "Smoke detected in server room",
      "resolved": false
    },
    // Additional events...
  ]
}
```

#### 3.3.3 Update Event

**Endpoint**: `/api/fire_alarm.event/{id}`

**Method**: PUT

**Parameters**:
- Event data in request body

**Example Request**:
```
PUT /api/fire_alarm.event/123
Content-Type: application/json

{
  "acknowledged": true,
  "ack_notes": "Investigating the alarm"
}
```

**Example Response**:
```json
{
  "success": true,
  "id": 123,
  "message": "Event updated successfully"
}
```

### 3.4 Device API Endpoints

#### 3.4.1 Get Devices

**Endpoint**: `/api/fire_alarm.device`

**Method**: GET

**Parameters**:
- `filters`: JSON-encoded filters
- `limit`: Maximum number of records to return
- `offset`: Offset for pagination
- `fields`: Comma-separated list of fields to include

**Example Request**:
```
GET /api/fire_alarm.device?filters=[["status","=","fault"]]&limit=10
```

**Example Response**:
```json
{
  "count": 3,
  "results": [
    {
      "id": 45,
      "name": "Smoke Detector - Server Room",
      "serial_number": "SD-HQ-GF-001",
      "device_type": "smoke_detector",
      "status": "fault",
      "building_id": 2,
      "zone_id": 5
    },
    // Additional devices...
  ]
}
```

#### 3.4.2 Update Device Status

**Endpoint**: `/api/fire_alarm.device/{id}`

**Method**: PUT

**Parameters**:
- Device data in request body

**Example Request**:
```
PUT /api/fire_alarm.device/45
Content-Type: application/json

{
  "status": "normal",
  "last_maintenance_date": "2025-04-15"
}
```

**Example Response**:
```json
{
  "success": true,
  "id": 45,
  "message": "Device updated successfully"
}
```

### 3.5 Building and Zone API Endpoints

Similar endpoints are available for buildings and zones, following the same patterns as the device endpoints.

### 3.6 System API Endpoints

#### 3.6.1 System Status

**Endpoint**: `/api/fire_alarm/system_status`

**Method**: GET

**Example Response**:
```json
{
  "status": "operational",
  "active_alarms": 5,
  "active_faults": 3,
  "device_count": 120,
  "online_devices": 118,
  "last_event_time": "2025-04-16T09:04:59"
}
```

#### 3.6.2 Test Bus Notification

**Endpoint**: `/fire_alarm/test_bus`

**Method**: GET

**Example Response**:
```json
{
  "success": true,
  "message": "Test bus notification sent successfully!"
}
```

## 4. Webhook Integration

### 4.1 Inbound Webhooks

External systems can send data to the fire alarm system via webhooks:

#### 4.1.1 Event Webhook

**Endpoint**: `/fire_alarm/webhook/event`

**Method**: POST

**Headers**:
- `X-Webhook-Token`: Authentication token
- `Content-Type`: application/json

**Payload**:
```json
{
  "device_serial": "SD-HQ-GF-001",
  "event_type": "alarm",
  "severity": "high",
  "description": "Smoke detected in server room",
  "event_time": "2025-04-16T09:04:59",
  "additional_data": {
    "temperature": 45,
    "smoke_level": 75,
    "battery_level": 90
  }
}
```

**Response**:
```json
{
  "success": true,
  "event_id": 123,
  "message": "Webhook processed successfully"
}
```

#### 4.1.2 Device Status Webhook

**Endpoint**: `/fire_alarm/webhook/device_status`

**Method**: POST

**Headers**:
- `X-Webhook-Token`: Authentication token
- `Content-Type`: application/json

**Payload**:
```json
{
  "device_serial": "SD-HQ-GF-001",
  "status": "normal",
  "battery_level": 90,
  "signal_strength": 85,
  "last_test_time": "2025-04-15T14:30:00"
}
```

**Response**:
```json
{
  "success": true,
  "device_id": 45,
  "message": "Device status updated successfully"
}
```

### 4.2 Outbound Webhooks

The system can send webhooks to external systems when events occur:

#### 4.2.1 Configuration

Outbound webhooks are configured in the system with:

1. **Target URL**: Where to send the webhook
2. **Event Types**: Which events trigger the webhook
3. **Payload Format**: What data to include
4. **Authentication**: How to authenticate with the target system
5. **Retry Policy**: How to handle failed deliveries

#### 4.2.2 Sample Payload

```json
{
  "event_id": 123,
  "event_type": "alarm",
  "severity": "high",
  "device": {
    "id": 45,
    "name": "Smoke Detector - Server Room",
    "serial_number": "SD-HQ-GF-001",
    "type": "smoke_detector"
  },
  "location": {
    "building": "Headquarters",
    "zone": "Server Room",
    "floor": "Ground Floor"
  },
  "event_time": "2025-04-16T09:04:59",
  "description": "Smoke detected in server room",
  "system_url": "https://example.com/fire_alarm/event/123"
}
```

## 5. Database Integration

### 5.1 Database Schema

For direct database integration, the following key tables are relevant:

1. **fire_alarm_event**: Stores event data
2. **fire_alarm_device**: Stores device data
3. **fire_alarm_building**: Stores building data
4. **fire_alarm_zone**: Stores zone data

### 5.2 Read-Only Views

For reporting and integration purposes, the following database views are provided:

1. **fire_alarm_active_alarms_view**: View of all active alarms
2. **fire_alarm_device_status_view**: View of current device status
3. **fire_alarm_event_summary_view**: Summary of events by type and period

### 5.3 Integration Considerations

When implementing database-level integration:

1. **Use read-only access** where possible
2. **Create dedicated database users** with limited permissions
3. **Consider performance impact** of queries on production system
4. **Implement change data capture** for real-time integration

## 6. File-based Integration

### 6.1 Import Formats

The system supports importing data from files in the following formats:

#### 6.1.1 Device Import (CSV)

```csv
serial_number,name,device_type,building_code,zone_name,location,ip_address
SD-HQ-GF-001,Smoke Detector - Server Room,smoke_detector,HQ,Server Room,Rack 3,*************
SD-HQ-GF-002,Heat Detector - Kitchen,heat_detector,HQ,Kitchen,Ceiling,*************
```

#### 6.1.2 Building Import (CSV)

```csv
code,name,address,floors,latitude,longitude
HQ,Headquarters,123 Main St,5,24.7136,46.6753
BR1,Branch Office 1,456 Oak Ave,2,24.7234,46.6891
```

#### 6.1.3 Event Import (JSON)

```json
[
  {
    "device_serial": "SD-HQ-GF-001",
    "event_type": "alarm",
    "severity": "high",
    "event_time": "2025-04-16T09:04:59",
    "description": "Smoke detected in server room"
  },
  {
    "device_serial": "SD-HQ-GF-002",
    "event_type": "fault",
    "severity": "medium",
    "event_time": "2025-04-16T08:30:12",
    "description": "Device communication failure"
  }
]
```

### 6.2 Export Formats

The system can export data in various formats:

1. **CSV**: For spreadsheet applications
2. **JSON**: For programmatic processing
3. **XML**: For legacy system integration
4. **PDF**: For reporting and documentation

### 6.3 File Transfer Methods

Files can be transferred using:

1. **SFTP**: Secure file transfer protocol
2. **Shared Network Storage**: Mounted file systems
3. **HTTP Upload/Download**: Via web interface
4. **Email Attachments**: For smaller files

## 7. Custom Integration Adapters

### 7.1 Adapter Framework

The system provides a framework for building custom integration adapters:

```
┌─────────────────────────────────────────────────────────────────┐
│                     Adapter Framework                            │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│  Connection │ Data        │ Transformation│ Error     │ Logging │
│  Management │ Extraction  │ & Loading   │ Handling   │         │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
```

### 7.2 Adapter Types

The following adapter types can be implemented:

1. **Protocol Adapters**: For specific communication protocols
   - Examples: Modbus, BACnet, MQTT, proprietary protocols

2. **System Adapters**: For specific external systems
   - Examples: Specific fire alarm panel brands, BMS systems

3. **Format Adapters**: For specific data formats
   - Examples: Proprietary file formats, legacy data structures

### 7.3 Implementation Approach

Custom adapters can be implemented using:

1. **Python Modules**: Extending the core system
2. **External Services**: Standalone services that communicate via API
3. **ETL Processes**: For batch data processing
4. **Message Queues**: For asynchronous processing

## 8. Integration Patterns

### 8.1 Real-time Event Integration

For real-time event integration, the recommended pattern is:

```
┌────────────┐     ┌────────────┐     ┌────────────┐     ┌────────────┐
│  External  │────►│  REST API  │────►│  Event     │────►│ Notification│
│  System    │     │  Endpoint  │     │ Processing │     │   System   │
└────────────┘     └────────────┘     └────────────┘     └────────────┘
```

### 8.2 Batch Data Synchronization

For batch data synchronization, the recommended pattern is:

```
┌────────────┐     ┌────────────┐     ┌────────────┐     ┌────────────┐
│  External  │────►│  File      │────►│  Import    │────►│  Data      │
│  System    │     │  Export    │     │  Process   │     │  Validation │
└────────────┘     └────────────┘     └────────────┘     └────────────┘
```

### 8.3 Bidirectional Integration

For bidirectional integration, the recommended pattern is:

```
┌────────────┐     ┌────────────┐     ┌────────────┐
│  External  │◄───►│ Integration │◄───►│  Fire Alarm│
│  System    │     │  Service   │     │   System   │
└────────────┘     └────────────┘     └────────────┘
```

## 9. Security Considerations

### 9.1 Authentication and Authorization

All integrations should implement proper authentication and authorization:

1. **API Authentication**: API keys or OAuth tokens
2. **Webhook Authentication**: Shared secrets or tokens
3. **Database Authentication**: Dedicated users with limited permissions
4. **File Transfer Authentication**: SFTP credentials or secure links

### 9.2 Data Protection

Sensitive data should be protected:

1. **Encryption in Transit**: HTTPS, SFTP, VPN
2. **Encryption at Rest**: Database encryption, file encryption
3. **Data Minimization**: Only transfer necessary data
4. **Data Retention**: Implement appropriate retention policies

### 9.3 Audit Logging

All integration activities should be logged:

1. **Access Logs**: Who accessed what and when
2. **Change Logs**: What changes were made
3. **Error Logs**: What errors occurred
4. **Security Logs**: Security-related events

## 10. Testing and Validation

### 10.1 Integration Testing

Integration points should be thoroughly tested:

1. **Functional Testing**: Verify correct behavior
2. **Performance Testing**: Verify acceptable performance
3. **Security Testing**: Verify security controls
4. **Error Handling**: Verify proper error handling

### 10.2 Test Endpoints

The system provides test endpoints for integration testing:

1. **Event Test Endpoint**: `/fire_alarm/trigger_event`
2. **Bus Test Endpoint**: `/fire_alarm/test_bus`
3. **Webhook Test Endpoint**: `/fire_alarm/test_webhook`

### 10.3 Validation Tools

The following tools are available for validation:

1. **API Documentation**: Interactive API documentation
2. **Request Validator**: Tool to validate API requests
3. **Response Validator**: Tool to validate API responses
4. **Integration Monitor**: Tool to monitor integration health

## 11. Troubleshooting

### 11.1 Common Integration Issues

1. **Authentication Failures**:
   - Check credentials
   - Verify token expiration
   - Check IP restrictions

2. **Data Format Issues**:
   - Validate against schema
   - Check character encoding
   - Verify date/time formats

3. **Connection Issues**:
   - Check network connectivity
   - Verify firewall rules
   - Check SSL/TLS configuration

4. **Performance Issues**:
   - Monitor response times
   - Check resource utilization
   - Optimize queries and payloads

### 11.2 Logging and Monitoring

For effective troubleshooting:

1. **Enable Detailed Logging**: For integration components
2. **Monitor Integration Points**: Track performance and errors
3. **Set Up Alerts**: For integration failures
4. **Implement Health Checks**: For integration services

## 12. Implementation Examples

### 12.1 Python Client Example

```python
import requests
import json

class FireAlarmClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
    
    def trigger_event(self, device_serial, event_type, severity, description):
        url = f"{self.base_url}/fire_alarm/trigger_event"
        params = {
            'device_serial': device_serial,
            'event_type': event_type,
            'severity': severity,
            'description': description
        }
        response = requests.get(url, params=params, headers=self.headers)
        return response.json()
    
    def get_active_alarms(self):
        url = f"{self.base_url}/api/fire_alarm.event"
        filters = [["event_type", "=", "alarm"], ["resolved", "=", False]]
        params = {
            'filters': json.dumps(filters),
            'limit': 100
        }
        response = requests.get(url, params=params, headers=self.headers)
        return response.json()
```

### 12.2 Webhook Receiver Example

```python
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/webhook/fire_alarm', methods=['POST'])
def receive_fire_alarm_webhook():
    # Verify webhook token
    token = request.headers.get('X-Webhook-Token')
    if token != 'your_secret_token':
        return jsonify({'error': 'Unauthorized'}), 401
    
    # Process webhook data
    data = request.json
    event_id = data.get('event_id')
    event_type = data.get('event_type')
    severity = data.get('severity')
    
    # Process the event (example)
    print(f"Received fire alarm event: {event_id}, Type: {event_type}, Severity: {severity}")
    
    # Respond to webhook
    return jsonify({'success': True, 'message': 'Webhook received successfully'})

if __name__ == '__main__':
    app.run(port=5000)
```

---

*This technical documentation was prepared for the Fire Alarm System Integration Guide. For further information, please contact the technical support team.*
