# Fire Alarm API Documentation

This document provides information on how to integrate external systems with the Fire Alarm module using its REST API.

## Authentication

All API endpoints require authentication using API keys. To use the API, you need to:

1. Create an API key in Odoo:
   - Go to Settings > Technical > API Keys
   - Create a new API key with appropriate access rights

2. Include the API key in your requests:
   - For JSON endpoints: Include the `api_key` parameter in the request body
   - For HTTP endpoints: Include the `api_key` parameter in the query string

## Endpoints

### 1. Quick Trigger Event (for Testing)

**Endpoint:** `/fire_alarm/trigger_event`

**Method:** `GET`

**Description:** A simple endpoint to quickly create fire alarm events for testing purposes. Can be called directly from a browser or with a simple curl command.

**Query Parameters:**
- `device_serial`: The serial number of the device (required)
- `event_type`: The type of event (alarm, fault, test, restore) (required)
- `severity`: The severity of the event (low, medium, high, critical) (required)
- `description`: A description of the event (required)

**Example URL:**
```
/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=high&description=Test%20alarm
```

**Response:**
```json
{
    "success": true,
    "message": "Event created successfully!",
    "event_id": 123,
    "name": "ALM-2023-001",
    "event_time": "2023-04-15 12:34:56",
    "device": {
        "id": 456,
        "name": "Smoke Detector HQ-GF-01",
        "serial_number": "SD-HQ-GF-001"
    },
    "building": {
        "id": 1,
        "name": "Headquarters Building"
    },
    "zone": {
        "id": 1,
        "name": "HQ Ground Floor"
    }
}
```

### 2. Create Alarm Event

**Endpoint:** `/api/fire_alarm/event`

**Method:** `POST`

**Content Type:** `application/json`

**Description:** Creates a new alarm event from an external system.

**Request Body:**
```json
{
    "device_id": "SD-HQ-GF-001",  // Device serial number
    "event_type": "alarm",        // One of: alarm, fault, test, restore
    "severity": "high",           // One of: low, medium, high, critical
    "description": "Smoke detected in reception area",
    "latitude": 24.7136,          // Optional
    "longitude": 46.6753,         // Optional
    "additional_data": {}         // Optional JSON object with additional data
}
```

**Response:**
```json
{
    "success": true,
    "message": "Event created successfully",
    "data": {
        "event_id": 123,
        "name": "ALM-2023-001",
        "event_time": "2023-04-15 12:34:56"
    }
}
```

### 2. Get Device Status

**Endpoint:** `/api/fire_alarm/device/{serial_number}/status`

**Method:** `GET`

**Description:** Retrieves the status and details of a device by its serial number.

**Parameters:**
- `serial_number`: The serial number of the device (in the URL path)

**Response:**
```json
{
    "success": true,
    "message": "Device status retrieved successfully",
    "data": {
        "device_id": 123,
        "serial_number": "SD-HQ-GF-001",
        "name": "Smoke Detector HQ-GF-01",
        "status": "normal",
        "device_type": "smoke_detector",
        "building": {
            "id": 1,
            "name": "Headquarters Building",
            "code": "HQ-01"
        },
        "zone": {
            "id": 1,
            "name": "HQ Ground Floor",
            "code": "HQ-GF"
        },
        "latest_event": {
            "id": 456,
            "name": "ALM-2023-001",
            "event_type": "alarm",
            "severity": "high",
            "event_time": "2023-04-15 12:34:56",
            "acknowledged": false,
            "resolved": false
        }
    }
}
```

### 3. Get Events

**Endpoint:** `/api/fire_alarm/events`

**Method:** `GET`

**Description:** Retrieves a list of events with filtering options.

**Query Parameters:**
- `event_type`: Filter by event type (alarm, fault, test, restore)
- `building_id`: Filter by building ID
- `zone_id`: Filter by zone ID
- `device_id`: Filter by device ID
- `resolved`: Filter by resolved status (true/false)
- `acknowledged`: Filter by acknowledged status (true/false)
- `limit`: Maximum number of events to return (default: 50)
- `offset`: Number of events to skip (default: 0)

**Response:**
```json
{
    "success": true,
    "message": "Events retrieved successfully",
    "data": {
        "events": [
            {
                "id": 123,
                "name": "ALM-2023-001",
                "event_type": "alarm",
                "severity": "high",
                "description": "Smoke detected in reception area",
                "event_time": "2023-04-15 12:34:56",
                "acknowledged": false,
                "resolved": false,
                "device": {
                    "id": 456,
                    "name": "Smoke Detector HQ-GF-01",
                    "serial_number": "SD-HQ-GF-001"
                },
                "building": {
                    "id": 1,
                    "name": "Headquarters Building",
                    "code": "HQ-01"
                },
                "zone": {
                    "id": 1,
                    "name": "HQ Ground Floor",
                    "code": "HQ-GF"
                }
            }
        ],
        "total_count": 100,
        "limit": 50,
        "offset": 0
    }
}
```

## Error Handling

All endpoints return a consistent error format:

```json
{
    "success": false,
    "message": "Error message describing what went wrong"
}
```

Common error codes:
- `400`: Bad Request - Missing or invalid parameters
- `401`: Unauthorized - Invalid or missing API key
- `404`: Not Found - Resource not found
- `500`: Server Error - Unexpected error on the server

## Integration Examples

### Python Example

```python
import requests
import json

# Configuration
base_url = "https://your-odoo-instance.com"
api_key = "your_api_key"

# Create an alarm event
def create_alarm_event(device_serial, event_type, severity, description):
    url = f"{base_url}/api/fire_alarm/event"
    headers = {
        "Content-Type": "application/json",
    }
    payload = {
        "jsonrpc": "2.0",
        "params": {
            "device_id": device_serial,
            "event_type": event_type,
            "severity": severity,
            "description": description,
            "api_key": api_key
        }
    }

    response = requests.post(url, headers=headers, data=json.dumps(payload))
    return response.json()

# Get device status
def get_device_status(device_serial):
    url = f"{base_url}/api/fire_alarm/device/{device_serial}/status?api_key={api_key}"
    response = requests.get(url)
    return response.json()

# Get active alarms
def get_active_alarms():
    url = f"{base_url}/api/fire_alarm/events?event_type=alarm&resolved=false&api_key={api_key}"
    response = requests.get(url)
    return response.json()

# Quick trigger event (for testing)
def trigger_test_event(device_serial, event_type, severity, description):
    url = f"{base_url}/fire_alarm/trigger_event"
    params = {
        "device_serial": device_serial,
        "event_type": event_type,
        "severity": severity,
        "description": description
    }
    response = requests.get(url, params=params)
    return response.json()

# Example usage
result1 = trigger_test_event(
    "SD-HQ-GF-001",
    "alarm",
    "high",
    "Test alarm for testing purposes"
)
print(result1)

result2 = create_alarm_event(
    "SD-HQ-GF-001",
    "alarm",
    "high",
    "Smoke detected in reception area"
)
print(result2)
```

### cURL Example

```bash
# Quick trigger event (for testing)
curl -X GET \
  'https://your-odoo-instance.com/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=high&description=Test%20alarm'

# Create an alarm event
curl -X POST \
  https://your-odoo-instance.com/api/fire_alarm/event \
  -H 'Content-Type: application/json' \
  -d '{
    "jsonrpc": "2.0",
    "params": {
        "device_id": "SD-HQ-GF-001",
        "event_type": "alarm",
        "severity": "high",
        "description": "Smoke detected in reception area",
        "api_key": "your_api_key"
    }
}'

# Get device status
curl -X GET \
  'https://your-odoo-instance.com/api/fire_alarm/device/SD-HQ-GF-001/status?api_key=your_api_key'

# Get active alarms
curl -X GET \
  'https://your-odoo-instance.com/api/fire_alarm/events?event_type=alarm&resolved=false&api_key=your_api_key'
```

## Notes

- All timestamps are in UTC and follow the ISO 8601 format.
- The API uses standard HTTP status codes to indicate success or failure.
- For large deployments, consider implementing rate limiting and monitoring.
