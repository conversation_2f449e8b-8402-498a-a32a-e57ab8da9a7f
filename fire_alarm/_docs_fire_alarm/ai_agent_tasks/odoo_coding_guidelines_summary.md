# Odoo Coding Guidelines Summary

## Module Structure

- **Standard Directory Structure**:
  ```
  module_name/
  ├── __init__.py
  ├── __manifest__.py
  ├── controllers/
  ├── data/
  ├── demo/
  ├── models/
  ├── security/
  ├── static/
  ├── views/
  └── wizards/
  ```

- **Manifest File**: Must include name, description, version, dependencies, and data files

## Python Guidelines

### Imports

- Group imports in the following order:
  1. Standard library imports
  2. External dependencies
  3. Odoo imports (`odoo`)
  4. Odoo addons imports (`odoo.addons`)
  5. Local imports (relative)
- Sort imports alphabetically within each group
- Use explicit relative imports for local modules

```python
# Standard library
import base64
import datetime
import logging

# External dependencies
import requests

# Odoo imports
from odoo import api, fields, models
from odoo.exceptions import UserError

# Local imports
from . import utils
```

### Python Code Style

- Follow PEP 8 with some exceptions
- Line length: 79 characters (soft limit), 119 characters (hard limit)
- Use 4 spaces for indentation (no tabs)
- Use CamelCase for model names, snake_case for fields and methods
- Use double quotes for strings in Python code

### Model Definition

- Use meaningful model names with proper namespacing
- Include `_description` for all models
- Order model attributes consistently:
  1. Private attributes (`_name`, `_description`, etc.)
  2. Default method overrides
  3. Field declarations
  4. Compute and constraint methods
  5. CRUD methods
  6. Action methods
  7. Business methods

```python
class SaleOrder(models.Model):
    _name = 'sale.order'
    _description = 'Sales Order'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    name = fields.Char(string='Order Reference', required=True)
    partner_id = fields.Many2one('res.partner', string='Customer')
    
    @api.depends('order_line.price_total')
    def _compute_amount(self):
        # Compute implementation
```

### Field Declarations

- Always include a `string` parameter
- Group fields logically
- Use proper field types and attributes
- Define selection fields with a list of tuples

```python
state = fields.Selection([
    ('draft', 'Draft'),
    ('confirmed', 'Confirmed'),
    ('done', 'Done'),
    ('cancel', 'Cancelled'),
], string='Status', default='draft')
```

### Method Definitions

- Use descriptive method names
- Add docstrings for complex methods
- Use decorators appropriately (`@api.depends`, `@api.onchange`, etc.)
- Return `self` for methods that are meant to be chainable

## XML Guidelines

### General XML

- Use 2 spaces for indentation in XML files
- Use double quotes for attribute values
- Keep IDs descriptive and namespaced
- Order attributes consistently

### View Definitions

- Group related view elements
- Use proper view inheritance
- Use meaningful field labels
- Organize fields logically in forms

```xml
<record id="view_sale_order_form" model="ir.ui.view">
  <field name="name">sale.order.form</field>
  <field name="model">sale.order</field>
  <field name="arch" type="xml">
    <form>
      <header>
        <button name="action_confirm" string="Confirm" type="object"/>
        <field name="state" widget="statusbar"/>
      </header>
      <sheet>
        <group>
          <field name="partner_id"/>
          <field name="date_order"/>
        </group>
      </sheet>
    </form>
  </field>
</record>
```

### Security

- Define proper access rights
- Use record rules for row-level security
- Define security groups appropriately

```xml
<record id="sale_order_rule" model="ir.rule">
  <field name="name">Sale Order: see only own and assigned</field>
  <field name="model_id" ref="model_sale_order"/>
  <field name="domain_force">[
    '|', ('user_id', '=', user.id), ('user_id', '=', False)
  ]</field>
</record>
```

## JavaScript Guidelines

- Follow JavaScript Standard Style
- Use ES6 features appropriately
- Organize code in modules
- Use proper class inheritance
- Document complex functions

```javascript
odoo.define('module_name.ClassName', function (require) {
    "use strict";
    
    const core = require('web.core');
    const Widget = require('web.Widget');
    
    const ClassName = Widget.extend({
        init: function (parent, options) {
            this._super.apply(this, arguments);
            // Initialization
        },
        
        start: function () {
            return this._super.apply(this, arguments);
        },
    });
    
    return ClassName;
});
```

## CSS Guidelines

- Use descriptive class names
- Avoid inline styles
- Use SCSS when possible
- Follow BEM (Block Element Modifier) methodology
- Keep selectors as simple as possible

```scss
.o_sale_order {
    &__header {
        background-color: #f8f9fa;
        
        &--highlighted {
            background-color: #e2e6ea;
        }
    }
}
```

## Testing

- Write tests for all significant functionality
- Use appropriate test types:
  - Unit tests for isolated functionality
  - Integration tests for workflows
  - Tour tests for UI testing
- Follow test naming conventions
- Keep tests independent and isolated

## Documentation

- Add docstrings to complex methods
- Include module documentation
- Document non-obvious behavior
- Use proper formatting in docstrings

```python
def calculate_tax(self, amount):
    """Calculate tax amount based on current tax rules.
    
    Args:
        amount (float): The base amount to calculate tax on
        
    Returns:
        float: The calculated tax amount
    """
    # Implementation
```

## Performance Considerations

- Avoid N+1 query problems
- Use prefetching when appropriate
- Batch operations when possible
- Use indexed fields for searches
- Consider the impact of computed fields

## Upgrade Considerations

- Use proper version numbers
- Include migration scripts when needed
- Document breaking changes
- Follow deprecation policies

These guidelines help maintain consistency, readability, and quality across Odoo modules, making the codebase easier to maintain and extend.
