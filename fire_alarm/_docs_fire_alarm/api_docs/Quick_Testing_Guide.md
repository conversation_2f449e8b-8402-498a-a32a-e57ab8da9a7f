# Fire Alarm Quick Testing Guide

This guide provides instructions on how to quickly test the Fire Alarm system using the simplified testing endpoint.

## Quick Testing Endpoint

The Fire Alarm module provides a simplified HTTP GET endpoint that allows you to quickly create test events without needing to set up complex authentication or use JSON payloads. This is particularly useful during development and testing.

### Endpoint Details

- **URL**: `/fire_alarm/trigger_event`
- **Method**: GET
- **Authentication**: Public (no authentication required)
- **Content Type**: application/x-www-form-urlencoded (standard URL parameters)

### Required Parameters

| Parameter | Description | Example Values |
|-----------|-------------|----------------|
| `device_serial` | Serial number of the device | SD-HQ-GF-001, SD-B2-F3-002 |
| `event_type` | Type of event | alarm, fault, test, restore |
| `severity` | Severity level | low, medium, high, critical |
| `description` | Description of the event | "Smoke detected in reception area" |

### Response Format

The endpoint returns a JSON response with the following structure:

```json
{
    "success": true,
    "message": "Event created successfully!",
    "event_id": 123,
    "name": "ALM-20250415-155242",
    "event_time": "2025-04-15 15:52:42",
    "device": {
        "id": 456,
        "name": "Smoke Detector HQ-GF-01",
        "serial_number": "SD-HQ-GF-001"
    },
    "building": {
        "id": 1,
        "name": "Headquarters Building"
    },
    "zone": {
        "id": 1,
        "name": "HQ Ground Floor"
    }
}
```

## Usage Examples

### Browser

You can test the endpoint directly in your browser by entering a URL like this:

```
http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=high&description=Test%20alarm
```

### cURL

```bash
# Create a test alarm
curl "http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=high&description=Test%20alarm"

# Create a test fault
curl "http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=fault&severity=medium&description=Power%20supply%20issue"

# Create a test restore event
curl "http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=restore&severity=low&description=System%20restored%20to%20normal%20operation"
```

### Python

```python
import requests

def trigger_test_event(base_url, device_serial, event_type, severity, description):
    """
    Send a test event to the fire alarm system.
    
    Args:
        base_url (str): Base URL of the Odoo server (e.g., "http://localhost:8020")
        device_serial (str): Serial number of the device
        event_type (str): Type of event (alarm, fault, test, restore)
        severity (str): Severity level (low, medium, high, critical)
        description (str): Description of the event
    
    Returns:
        dict: JSON response from the server
    """
    url = f"{base_url}/fire_alarm/trigger_event"
    params = {
        "device_serial": device_serial,
        "event_type": event_type,
        "severity": severity,
        "description": description
    }
    
    response = requests.get(url, params=params)
    return response.json()

# Example usage
base_url = "http://localhost:8020"

# Create a test alarm
result = trigger_test_event(
    base_url,
    "SD-HQ-GF-001",
    "alarm",
    "high",
    "Test alarm for testing purposes"
)
print(result)

# Create multiple test events
test_events = [
    {
        "device_serial": "SD-HQ-GF-001",
        "event_type": "alarm",
        "severity": "high",
        "description": "Smoke detected in reception"
    },
    {
        "device_serial": "SD-HQ-GF-002",
        "event_type": "fault",
        "severity": "medium",
        "description": "Battery low"
    },
    {
        "device_serial": "SD-HQ-GF-003",
        "event_type": "test",
        "severity": "low",
        "description": "Routine test"
    }
]

for event in test_events:
    result = trigger_test_event(
        base_url,
        event["device_serial"],
        event["event_type"],
        event["severity"],
        event["description"]
    )
    print(f"Created event: {result['name']} - {result['message']}")
```

### JavaScript/AJAX

```javascript
/**
 * Send a test event to the fire alarm system
 * 
 * @param {string} baseUrl - Base URL of the Odoo server
 * @param {string} deviceSerial - Serial number of the device
 * @param {string} eventType - Type of event (alarm, fault, test, restore)
 * @param {string} severity - Severity level (low, medium, high, critical)
 * @param {string} description - Description of the event
 * @returns {Promise} - Promise that resolves to the JSON response
 */
function triggerTestEvent(baseUrl, deviceSerial, eventType, severity, description) {
    const url = new URL(`${baseUrl}/fire_alarm/trigger_event`);
    
    // Add query parameters
    url.searchParams.append('device_serial', deviceSerial);
    url.searchParams.append('event_type', eventType);
    url.searchParams.append('severity', severity);
    url.searchParams.append('description', description);
    
    return fetch(url.toString())
        .then(response => response.json())
        .catch(error => console.error('Error:', error));
}

// Example usage
const baseUrl = 'http://localhost:8020';

// Create a test alarm
triggerTestEvent(
    baseUrl,
    'SD-HQ-GF-001',
    'alarm',
    'high',
    'Test alarm for testing purposes'
).then(result => {
    console.log('Event created:', result);
});
```

## Common Test Scenarios

Here are some common test scenarios you might want to try:

### 1. Create Alarms for Different Devices

```
http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=high&description=Smoke%20detected%20in%20reception
```

```
http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-F1-002&event_type=alarm&severity=critical&description=Fire%20detected%20in%20server%20room
```

### 2. Create Different Types of Events for the Same Device

```
# Create an alarm
http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=high&description=Smoke%20detected

# Create a fault for the same device
http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=fault&severity=medium&description=Device%20communication%20error

# Create a restore event for the same device
http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=restore&severity=low&description=System%20restored%20to%20normal
```

### 3. Test Error Handling

```
# Missing required parameter
http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=high

# Invalid event type
http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=invalid&severity=high&description=Test

# Invalid severity
http://localhost:8020/fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=invalid&description=Test

# Non-existent device
http://localhost:8020/fire_alarm/trigger_event?device_serial=NONEXISTENT&event_type=alarm&severity=high&description=Test
```

## Notes

- This endpoint is intended for testing purposes only and should not be exposed in production environments.
- The endpoint does not require authentication, so anyone with access to the URL can create events.
- Events created through this endpoint are identical to those created through the regular API and will appear in the dashboard and reports.
- You can use this endpoint to quickly test the dashboard, notifications, and other features of the Fire Alarm module.
