<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Fire Alarm Systems -->
        <record id="fire_alarm_system_1" model="fire_alarm.fire_alarm">
            <field name="name">Siemens FireFinder XLS</field>
            <field name="active" eval="True"/>
        </record>

        <record id="fire_alarm_system_2" model="fire_alarm.fire_alarm">
            <field name="name">Honeywell Notifier</field>
            <field name="active" eval="True"/>
        </record>

        <!-- Buildings -->
        <record id="building_1" model="fire_alarm.building">
            <field name="name">Headquarters Building</field>
            <field name="code">HQ-01</field>
            <field name="fire_alarm_id" ref="fire_alarm_system_1"/>
            <field name="floors">12</field>
            <field name="address">Riyadh Gallery Mall, King Fahd Road, Riyadh, Saudi Arabia</field>
            <field name="active" eval="True"/>
        </record>

        <record id="building_2" model="fire_alarm.building">
            <field name="name">Research Center</field>
            <field name="code">RC-01</field>
            <field name="fire_alarm_id" ref="fire_alarm_system_1"/>
            <field name="floors">5</field>
            <field name="address">Near Riyadh Gallery Mall, King Fahd Road, Riyadh, Saudi Arabia</field>
            <field name="active" eval="True"/>
        </record>

        <record id="building_3" model="fire_alarm.building">
            <field name="name">Data Center</field>
            <field name="code">DC-01</field>
            <field name="fire_alarm_id" ref="fire_alarm_system_2"/>
            <field name="floors">3</field>
            <field name="address">500m from Riyadh Gallery Mall, King Fahd Road, Riyadh, Saudi Arabia</field>
            <field name="active" eval="True"/>
        </record>

        <!-- Zones -->
        <!-- Headquarters Building Zones -->
        <record id="zone_hq_1" model="fire_alarm.zone">
            <field name="name">HQ Ground Floor</field>
            <field name="code">HQ-GF</field>
            <field name="building_id" ref="building_1"/>
            <field name="floor">1</field>
            <field name="description">Reception area, security desk, and visitor waiting area</field>
            <field name="active" eval="True"/>
        </record>

        <record id="zone_hq_2" model="fire_alarm.zone">
            <field name="name">HQ First Floor</field>
            <field name="code">HQ-F1</field>
            <field name="building_id" ref="building_1"/>
            <field name="floor">2</field>
            <field name="description">Administrative offices and meeting rooms</field>
            <field name="active" eval="True"/>
        </record>

        <record id="zone_hq_3" model="fire_alarm.zone">
            <field name="name">HQ Second Floor</field>
            <field name="code">HQ-F2</field>
            <field name="building_id" ref="building_1"/>
            <field name="floor">3</field>
            <field name="description">Executive offices and conference rooms</field>
            <field name="active" eval="True"/>
        </record>

        <!-- Research Center Zones -->
        <record id="zone_rc_1" model="fire_alarm.zone">
            <field name="name">RC Ground Floor</field>
            <field name="code">RC-GF</field>
            <field name="building_id" ref="building_2"/>
            <field name="floor">1</field>
            <field name="description">Laboratories and testing facilities</field>
            <field name="active" eval="True"/>
        </record>

        <record id="zone_rc_2" model="fire_alarm.zone">
            <field name="name">RC First Floor</field>
            <field name="code">RC-F1</field>
            <field name="building_id" ref="building_2"/>
            <field name="floor">2</field>
            <field name="description">Research offices and equipment storage</field>
            <field name="active" eval="True"/>
        </record>

        <!-- Data Center Zones -->
        <record id="zone_dc_1" model="fire_alarm.zone">
            <field name="name">DC Server Room</field>
            <field name="code">DC-SR</field>
            <field name="building_id" ref="building_3"/>
            <field name="floor">1</field>
            <field name="description">Main server room with critical infrastructure</field>
            <field name="active" eval="True"/>
        </record>

        <record id="zone_dc_2" model="fire_alarm.zone">
            <field name="name">DC Network Operations</field>
            <field name="code">DC-NOC</field>
            <field name="building_id" ref="building_3"/>
            <field name="floor">2</field>
            <field name="description">Network operations center and monitoring stations</field>
            <field name="active" eval="True"/>
        </record>

        <!-- Devices -->
        <!-- HQ Ground Floor Devices -->
        <record id="device_hq_gf_1" model="fire_alarm.device">
            <field name="name">Smoke Detector HQ-GF-01</field>
            <field name="serial_number">SD-HQ-GF-001</field>
            <field name="device_type">smoke_detector</field>
            <field name="zone_id" ref="zone_hq_1"/>
            <field name="building_id" ref="building_1"/>
            <field name="status">normal</field>
            <field name="location">Reception Area</field>
            <field name="latitude">24.7741</field>
            <field name="longitude">46.7250</field>
            <field name="ip_address">*************</field>
            <field name="last_maintenance" eval="(DateTime.today() - relativedelta(months=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="next_maintenance" eval="(DateTime.today() + relativedelta(months=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="active" eval="True"/>
        </record>

        <record id="device_hq_gf_2" model="fire_alarm.device">
            <field name="name">Heat Detector HQ-GF-01</field>
            <field name="serial_number">HD-HQ-GF-001</field>
            <field name="device_type">heat_detector</field>
            <field name="zone_id" ref="zone_hq_1"/>
            <field name="building_id" ref="building_1"/>
            <field name="status">normal</field>
            <field name="location">Kitchen Area</field>
            <field name="latitude">24.7743</field>
            <field name="longitude">46.7252</field>
            <field name="ip_address">*************</field>
            <field name="last_maintenance" eval="(DateTime.today() - relativedelta(months=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="next_maintenance" eval="(DateTime.today() + relativedelta(months=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="active" eval="True"/>
        </record>

        <record id="device_hq_gf_3" model="fire_alarm.device">
            <field name="name">Manual Call Point HQ-GF-01</field>
            <field name="serial_number">MCP-HQ-GF-001</field>
            <field name="device_type">manual_call_point</field>
            <field name="zone_id" ref="zone_hq_1"/>
            <field name="building_id" ref="building_1"/>
            <field name="status">normal</field>
            <field name="location">Main Entrance</field>
            <field name="latitude">24.7745</field>
            <field name="longitude">46.7254</field>
            <field name="ip_address">*************</field>
            <field name="last_maintenance" eval="(DateTime.today() - relativedelta(months=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="next_maintenance" eval="(DateTime.today() + relativedelta(months=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="active" eval="True"/>
        </record>

        <!-- HQ First Floor Devices -->
        <record id="device_hq_f1_1" model="fire_alarm.device">
            <field name="name">Smoke Detector HQ-F1-01</field>
            <field name="serial_number">SD-HQ-F1-001</field>
            <field name="device_type">smoke_detector</field>
            <field name="zone_id" ref="zone_hq_2"/>
            <field name="building_id" ref="building_1"/>
            <field name="status">normal</field>
            <field name="location">Main Office Area</field>
            <field name="latitude">24.7747</field>
            <field name="longitude">46.7256</field>
            <field name="ip_address">*************</field>
            <field name="last_maintenance" eval="(DateTime.today() - relativedelta(months=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="next_maintenance" eval="(DateTime.today() + relativedelta(months=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="active" eval="True"/>
        </record>

        <record id="device_hq_f1_2" model="fire_alarm.device">
            <field name="name">Manual Call Point HQ-F1-01</field>
            <field name="serial_number">MCP-HQ-F1-001</field>
            <field name="device_type">manual_call_point</field>
            <field name="zone_id" ref="zone_hq_2"/>
            <field name="building_id" ref="building_1"/>
            <field name="status">fault</field>
            <field name="location">Emergency Exit</field>
            <field name="latitude">24.7749</field>
            <field name="longitude">46.7258</field>
            <field name="ip_address">*************</field>
            <field name="last_maintenance" eval="(DateTime.today() - relativedelta(months=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="next_maintenance" eval="(DateTime.today() + relativedelta(months=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="active" eval="True"/>
        </record>

        <!-- Research Center Devices -->
        <record id="device_rc_gf_1" model="fire_alarm.device">
            <field name="name">Smoke Detector RC-GF-01</field>
            <field name="serial_number">SD-RC-GF-001</field>
            <field name="device_type">smoke_detector</field>
            <field name="zone_id" ref="zone_rc_1"/>
            <field name="building_id" ref="building_2"/>
            <field name="status">normal</field>
            <field name="location">Main Laboratory</field>
            <field name="latitude">24.7751</field>
            <field name="longitude">46.7260</field>
            <field name="ip_address">*************</field>
            <field name="last_maintenance" eval="(DateTime.today() - relativedelta(months=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="next_maintenance" eval="(DateTime.today() + relativedelta(months=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="active" eval="True"/>
        </record>

        <record id="device_rc_gf_2" model="fire_alarm.device">
            <field name="name">Heat Detector RC-GF-01</field>
            <field name="serial_number">HD-RC-GF-001</field>
            <field name="device_type">heat_detector</field>
            <field name="zone_id" ref="zone_rc_1"/>
            <field name="building_id" ref="building_2"/>
            <field name="status">alarm</field>
            <field name="location">Chemical Storage</field>
            <field name="latitude">24.7753</field>
            <field name="longitude">46.7262</field>
            <field name="ip_address">*************</field>
            <field name="last_maintenance" eval="(DateTime.today() - relativedelta(months=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="next_maintenance" eval="(DateTime.today() + relativedelta(months=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="active" eval="True"/>
        </record>

        <!-- Data Center Devices -->
        <record id="device_dc_sr_1" model="fire_alarm.device">
            <field name="name">Smoke Detector DC-SR-01</field>
            <field name="serial_number">SD-DC-SR-001</field>
            <field name="device_type">smoke_detector</field>
            <field name="zone_id" ref="zone_dc_1"/>
            <field name="building_id" ref="building_3"/>
            <field name="status">normal</field>
            <field name="location">Server Rack A1</field>
            <field name="latitude">24.7755</field>
            <field name="longitude">46.7264</field>
            <field name="ip_address">*************</field>
            <field name="last_maintenance" eval="(DateTime.today() - relativedelta(months=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="next_maintenance" eval="(DateTime.today() + relativedelta(months=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="active" eval="True"/>
        </record>

        <record id="device_dc_sr_2" model="fire_alarm.device">
            <field name="name">Gas Suppression System DC-SR-01</field>
            <field name="serial_number">GSS-DC-SR-001</field>
            <field name="device_type">gas_suppression</field>
            <field name="zone_id" ref="zone_dc_1"/>
            <field name="building_id" ref="building_3"/>
            <field name="status">normal</field>
            <field name="location">Main Server Room</field>
            <field name="latitude">24.7757</field>
            <field name="longitude">46.7266</field>
            <field name="ip_address">*************</field>
            <field name="last_maintenance" eval="(DateTime.today() - relativedelta(months=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="next_maintenance" eval="(DateTime.today() + relativedelta(months=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="active" eval="True"/>
        </record>

        <!-- Events -->
        <!-- Active Alarm Events -->
        <record id="event_1" model="fire_alarm.event">
            <field name="device_id" ref="device_rc_gf_2"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(minutes=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">high</field>
            <field name="description">High temperature detected in chemical storage area</field>
            <field name="acknowledged" eval="False"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7753</field>
            <field name="longitude">46.7262</field>
        </record>

        <record id="event_2" model="fire_alarm.event">
            <field name="name">ALM-2023-002</field>
            <field name="device_id" ref="device_hq_gf_1"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(hours=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">medium</field>
            <field name="description">Smoke detected in reception area</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(hours=1, minutes=45)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7741</field>
            <field name="longitude">46.7250</field>
        </record>

        <!-- Active Fault Events -->
        <record id="event_3" model="fire_alarm.event">
            <field name="name">FLT-2023-001</field>
            <field name="device_id" ref="device_hq_f1_2"/>
            <field name="event_type">fault</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">low</field>
            <field name="description">Communication error with manual call point</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(hours=23)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7749</field>
            <field name="longitude">46.7258</field>
        </record>

        <!-- Resolved Events -->
        <record id="event_4" model="fire_alarm.event">
            <field name="name">ALM-2023-003</field>
            <field name="device_id" ref="device_dc_sr_1"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">critical</field>
            <field name="description">Smoke detected near server rack A1</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=3, minutes=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=3, hours=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">False alarm caused by dust during maintenance</field>
            <field name="latitude">24.7755</field>
            <field name="longitude">46.7264</field>
        </record>

        <record id="event_5" model="fire_alarm.event">
            <field name="name">FLT-2023-002</field>
            <field name="device_id" ref="device_hq_gf_3"/>
            <field name="event_type">fault</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">medium</field>
            <field name="description">Low battery warning</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=5, hours=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">Battery replaced</field>
            <field name="latitude">24.7745</field>
            <field name="longitude">46.7254</field>
        </record>

        <!-- Test Events -->
        <record id="event_6" model="fire_alarm.event">
            <field name="name">TST-2023-001</field>
            <field name="device_id" ref="device_dc_sr_2"/>
            <field name="event_type">test</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(weeks=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">low</field>
            <field name="description">Scheduled monthly test of gas suppression system</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(weeks=2, minutes=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(weeks=2, hours=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">System functioning correctly</field>
            <field name="latitude">24.7757</field>
            <field name="longitude">46.7266</field>
        </record>

        <!-- Restore Events -->
        <record id="event_7" model="fire_alarm.event">
            <field name="name">RST-2023-001</field>
            <field name="device_id" ref="device_rc_gf_1"/>
            <field name="event_type">restore</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(weeks=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">low</field>
            <field name="description">System restored after maintenance</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(weeks=1, minutes=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(weeks=1, minutes=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="latitude">24.7751</field>
            <field name="longitude">46.7260</field>
        </record>

        <!-- Additional Events for Testing -->
        <record id="event_8" model="fire_alarm.event">
            <field name="name">ALM-2023-004</field>
            <field name="device_id" ref="device_hq_gf_1"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(hours=6)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">high</field>
            <field name="description">Smoke detected in reception area</field>
            <field name="acknowledged" eval="False"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7741</field>
            <field name="longitude">46.7250</field>
        </record>

        <record id="event_9" model="fire_alarm.event">
            <field name="name">FLT-2023-003</field>
            <field name="device_id" ref="device_rc_gf_1"/>
            <field name="event_type">fault</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(hours=12)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">medium</field>
            <field name="description">Sensor calibration error</field>
            <field name="acknowledged" eval="False"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7751</field>
            <field name="longitude">46.7260</field>
        </record>

        <record id="event_10" model="fire_alarm.event">
            <field name="name">ALM-2023-005</field>
            <field name="device_id" ref="device_dc_sr_1"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">critical</field>
            <field name="description">Smoke detected in server room</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=2, minutes=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=2, hours=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">False alarm due to dust</field>
            <field name="latitude">24.7755</field>
            <field name="longitude">46.7264</field>
        </record>

        <!-- Additional Events for Dashboard Display -->
        <!-- More Alarm Events -->
        <record id="event_11" model="fire_alarm.event">
            <field name="name">ALM-2023-006</field>
            <field name="device_id" ref="device_hq_f1_1"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=1, hours=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">high</field>
            <field name="description">Smoke detected in office area</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=1, hours=3, minutes=50)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7747</field>
            <field name="longitude">46.7256</field>
        </record>

        <record id="event_12" model="fire_alarm.event">
            <field name="name">ALM-2023-007</field>
            <field name="device_id" ref="device_rc_gf_2"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">critical</field>
            <field name="description">High temperature in chemical storage area</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(hours=7, minutes=45)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7753</field>
            <field name="longitude">46.7262</field>
        </record>

        <record id="event_13" model="fire_alarm.event">
            <field name="name">ALM-2023-008</field>
            <field name="device_id" ref="device_dc_sr_1"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(hours=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">critical</field>
            <field name="description">Smoke detected near server rack B2</field>
            <field name="acknowledged" eval="False"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7755</field>
            <field name="longitude">46.7264</field>
        </record>

        <!-- More Fault Events -->
        <record id="event_14" model="fire_alarm.event">
            <field name="name">FLT-2023-004</field>
            <field name="device_id" ref="device_hq_gf_2"/>
            <field name="event_type">fault</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=1, hours=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">low</field>
            <field name="description">Battery low warning</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=1, hours=9)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7743</field>
            <field name="longitude">46.7252</field>
        </record>

        <record id="event_15" model="fire_alarm.event">
            <field name="name">FLT-2023-005</field>
            <field name="device_id" ref="device_dc_sr_2"/>
            <field name="event_type">fault</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(hours=18)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">medium</field>
            <field name="description">Gas pressure below threshold</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(hours=17)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7757</field>
            <field name="longitude">46.7266</field>
        </record>

        <record id="event_16" model="fire_alarm.event">
            <field name="name">FLT-2023-006</field>
            <field name="device_id" ref="device_rc_gf_1"/>
            <field name="event_type">fault</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(hours=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">high</field>
            <field name="description">Device communication failure</field>
            <field name="acknowledged" eval="False"/>
            <field name="resolved" eval="False"/>
            <field name="latitude">24.7751</field>
            <field name="longitude">46.7260</field>
        </record>

        <!-- More Test Events -->
        <record id="event_17" model="fire_alarm.event">
            <field name="name">TST-2023-002</field>
            <field name="device_id" ref="device_hq_gf_1"/>
            <field name="event_type">test</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">low</field>
            <field name="description">Scheduled test of smoke detector</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=3, minutes=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=3, minutes=15)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">Test successful</field>
            <field name="latitude">24.7741</field>
            <field name="longitude">46.7250</field>
        </record>

        <record id="event_18" model="fire_alarm.event">
            <field name="name">TST-2023-003</field>
            <field name="device_id" ref="device_hq_f1_2"/>
            <field name="event_type">test</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=2, hours=6)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">low</field>
            <field name="description">Manual call point test</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=2, hours=6, minutes=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=2, hours=6, minutes=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">Test successful</field>
            <field name="latitude">24.7749</field>
            <field name="longitude">46.7258</field>
        </record>

        <!-- More Restore Events -->
        <record id="event_19" model="fire_alarm.event">
            <field name="name">RST-2023-002</field>
            <field name="device_id" ref="device_hq_gf_3"/>
            <field name="event_type">restore</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">low</field>
            <field name="description">System restored after power outage</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=4, minutes=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=4, minutes=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="latitude">24.7745</field>
            <field name="longitude">46.7254</field>
        </record>

        <record id="event_20" model="fire_alarm.event">
            <field name="name">RST-2023-003</field>
            <field name="device_id" ref="device_dc_sr_2"/>
            <field name="event_type">restore</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=1, hours=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">low</field>
            <field name="description">System restored after maintenance</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=1, hours=2, minutes=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=1, hours=2, minutes=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="latitude">24.7757</field>
            <field name="longitude">46.7266</field>
        </record>

        <!-- Historical Events for Trends -->
        <record id="event_21" model="fire_alarm.event">
            <field name="name">ALM-2023-009</field>
            <field name="device_id" ref="device_hq_gf_1"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">medium</field>
            <field name="description">Smoke detected in reception area</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=10, minutes=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=10, hours=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">False alarm due to cooking</field>
            <field name="latitude">24.7741</field>
            <field name="longitude">46.7250</field>
        </record>

        <record id="event_22" model="fire_alarm.event">
            <field name="name">ALM-2023-010</field>
            <field name="device_id" ref="device_rc_gf_2"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=15)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">high</field>
            <field name="description">High temperature in chemical storage</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=15, minutes=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=15, hours=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">HVAC malfunction caused temperature rise</field>
            <field name="latitude">24.7753</field>
            <field name="longitude">46.7262</field>
        </record>

        <record id="event_23" model="fire_alarm.event">
            <field name="name">FLT-2023-007</field>
            <field name="device_id" ref="device_dc_sr_1"/>
            <field name="event_type">fault</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=20)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">medium</field>
            <field name="description">Sensor malfunction</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=20, hours=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=19)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">Sensor replaced</field>
            <field name="latitude">24.7755</field>
            <field name="longitude">46.7264</field>
        </record>

        <record id="event_24" model="fire_alarm.event">
            <field name="name">TST-2023-004</field>
            <field name="device_id" ref="device_hq_gf_3"/>
            <field name="event_type">test</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=25)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">low</field>
            <field name="description">Quarterly system test</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=25, minutes=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=25, hours=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">All systems functioning correctly</field>
            <field name="latitude">24.7745</field>
            <field name="longitude">46.7254</field>
        </record>

        <record id="event_25" model="fire_alarm.event">
            <field name="name">ALM-2023-011</field>
            <field name="device_id" ref="device_dc_sr_1"/>
            <field name="event_type">alarm</field>
            <field name="event_time" eval="(DateTime.now() - relativedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="severity">critical</field>
            <field name="description">Smoke detected in server room</field>
            <field name="acknowledged" eval="True"/>
            <field name="acknowledged_by" ref="base.user_admin"/>
            <field name="acknowledged_time" eval="(DateTime.now() - relativedelta(days=30, minutes=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="resolved" eval="True"/>
            <field name="resolved_by" ref="base.user_admin"/>
            <field name="resolved_time" eval="(DateTime.now() - relativedelta(days=30, minutes=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="notes">UPS battery overheated</field>
            <field name="latitude">24.7755</field>
            <field name="longitude">46.7264</field>
        </record>
    </data>
</odoo>
