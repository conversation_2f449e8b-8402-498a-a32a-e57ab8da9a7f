<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Fire Alarm Security Groups -->
        <record id="group_fire_alarm_user" model="res.groups">
            <field name="name">Fire Alarm User</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="group_fire_alarm_manager" model="res.groups">
            <field name="name">Fire Alarm Manager</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('group_fire_alarm_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_admin'))]"/>
        </record>

        <!-- Record Rules -->
        <record id="fire_alarm_comp_rule" model="ir.rule">
            <field name="name">Fire Alarm: multi-company rule</field>
            <field name="model_id" ref="model_fire_alarm_fire_alarm"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <record id="building_comp_rule" model="ir.rule">
            <field name="name">Building: multi-company rule</field>
            <field name="model_id" ref="model_fire_alarm_building"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <record id="zone_comp_rule" model="ir.rule">
            <field name="name">Zone: multi-company rule</field>
            <field name="model_id" ref="model_fire_alarm_zone"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <record id="device_comp_rule" model="ir.rule">
            <field name="name">Device: multi-company rule</field>
            <field name="model_id" ref="model_fire_alarm_device"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <record id="event_comp_rule" model="ir.rule">
            <field name="name">Event: multi-company rule</field>
            <field name="model_id" ref="model_fire_alarm_event"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>
