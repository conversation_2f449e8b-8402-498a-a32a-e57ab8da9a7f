/* @odoo-module */
import {Component, useState, onWillStart} from "@odoo/owl";
import {useService} from "@web/core/utils/hooks";

export class AlarmCountItem extends Component {
    static template = "fire_alarm.AlarmCountItem";
    static props = {
        color: {
            type: String,
            optional: true,
        },
        main_icon: {
            type: String,
            optional: true,
        },
        chart_icon: {
            type: String,
            optional: true,
        },
        label: {
            type: String,
            optional: true,
        },
        count: {
            type: Number,
            optional: true,
        },
        model: {
            type: String,
            optional: true,
        },
        domain: {
            type: Array,
            optional: true,
        },
    };
    static defaultProps = {
        color: "bg-primary",
        main_icon: "fa-bell",
        chart_icon: "fa-chart-bar",
        label: "Items",
        count: 0,
        model: "fire_alarm.event",
        domain: [],
    };

    setup() {
        this.action = useService("action");
        this.orm = useService("orm");
        
        // If count is not provided, fetch it
        if (this.props.count === undefined) {
            this.state = useState({count: 0});
            onWillStart(async () => {
                await this.fetchCount();
            });
        }
    }

    async fetchCount() {
        try {
            const count = await this.orm.searchCount(this.props.model, this.props.domain);
            this.state.count = count;
        } catch (error) {
            console.error("Error fetching count:", error);
        }
    }

    get displayCount() {
        return this.props.count !== undefined ? this.props.count : this.state.count;
    }

    showMore() {
        // Determine the action to open based on the model
        let actionName;
        switch (this.props.model) {
            case 'fire_alarm.event':
                actionName = 'fire_alarm.action_fire_alarm_event';
                break;
            case 'fire_alarm.device':
                actionName = 'fire_alarm.action_fire_alarm_device';
                break;
            case 'fire_alarm.building':
                actionName = 'fire_alarm.action_fire_alarm_building';
                break;
            case 'fire_alarm.zone':
                actionName = 'fire_alarm.action_fire_alarm_zone';
                break;
            default:
                actionName = `fire_alarm.action_${this.props.model.replace('.', '_')}`;
        }
        
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: this.props.label,
            res_model: this.props.model,
            domain: this.props.domain,
            views: [[false, 'list'], [false, 'form']],
            target: 'current',
        });
    }
}
