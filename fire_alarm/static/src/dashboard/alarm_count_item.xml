<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="fire_alarm.AlarmCountItem">
        <div class="position-relative mb-4">
            <!-- Icon at the top -->
            <div t-attf-class="icon-circle position-absolute {{props.color}}" style="top: 0; left: 50%; transform: translate(-50%, -50%); z-index: 100;">
                <i t-attf-class="fa {{props.main_icon}} fa-2x text-white"></i>
            </div>

            <div t-attf-class="card shadow-sm text-center overflow-hidden {{props.color}}">
                <div t-attf-class="card-body pt-4 text-white {{props.color}}">
                <!-- Title -->
                <h5 t-esc="props.label" class="mt-2 text-white fs-5 fw-bold"></h5>

                <!-- Value -->
                <h2 t-esc="displayCount" class="text-white fs-1 fw-bolder mb-2"></h2>

                <!-- Chart Icon -->
                <div class="mb-3">
                    <i t-attf-class="fa {{props.chart_icon}} fa-2x text-white"></i>
                </div>

                <!-- Show More Button -->
                <button class="btn btn-outline-light w-100 mt-3 rounded-3" t-on-click="showMore">
                    Show More
                </button>
                </div>
            </div>
        </div>
    </t>
</templates>
