/* @odoo-module */
import {Component, onWillStart, onMounted, onWillUnmount, useEffect} from "@odoo/owl";
import {loadJS} from "@web/core/assets";
import {useRef} from "@odoo/owl";

export class Alarm<PERSON>hartComponent extends Component {
    static template = "fire_alarm.ChartComponent";
    static props = {
        type: String,
        data: Object,
        options: Object,
    };

    setup() {
        this.canvasRef = useRef("canvas");
        this.chart = null;

        // Load Chart.js library
        onWillStart(() => loadJS("/web/static/lib/Chart/Chart.js"));

        // Initial render on mount
        onMounted(() => {
            // Add a small delay to ensure the DOM is fully rendered
            setTimeout(() => {
                this.renderChart();
            }, 100);
        });

        // Simple solution: Re-render chart whenever props.data changes
        // This uses the useEffect hook which runs after each render
        // and when the dependencies (props.data) change
        useEffect(
            () => {
                if (this.canvasRef.el) {
                    this.renderChart();
                }
            },
            () => [JSON.stringify(this.props.data)] // Dependency array - re-run when data changes
        );

        // Clean up on unmount
        onWillUnmount(() => {
            if (this.chart) {
                this.chart.destroy();
            }
        });
    }

    renderChart() {
        if (!this.props.data || !this.canvasRef.el) {
            console.warn('Cannot render chart: missing data or canvas element');
            return;
        }

        // Destroy existing chart if it exists
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }

        try {
            // Clean up the type string (remove quotes if present)
            const chartType = this.props.type.replace(/["']/g, "");

            // Create a new chart
            this.chart = new Chart(this.canvasRef.el, {
                type: chartType,
                data: this.props.data,
                options: this.props.options,
            });
        } catch (error) {
            console.error('Error rendering chart:', error);
        }
    }
}
