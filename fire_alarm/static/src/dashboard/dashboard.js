/* @odoo-module */
import {Component, onWillStart, onWill<PERSON><PERSON>roy} from "@odoo/owl";
import {registry} from "@web/core/registry";
import {Layout} from "@web/search/layout";
import {useService} from "@web/core/utils/hooks";
import {AlarmCountItem} from "./alarm_count_item";
import {AlarmChartComponent} from "./chart/chart";

class FireAlarmDashboard extends Component {
    static template = "fire_alarm.dashboard";
    static components = {Layout, AlarmCountItem, AlarmChartComponent};

    setup() {
        this.action = useService("action");
        this.orm = useService("orm");
        this.currentDate = new Date().toISOString().split('T')[0];

        this.display = {
            controlPanel: {},
        };
        
        onWillStart(async () => {
            this.statistics = await this.fetchDashboardData();
        });
    }
    
    async fetchDashboardData() {
        // Fetch summary data for the dashboard
        const alarmStats = await this.orm.call(
            'fire_alarm.event', 
            'get_alarm_statistics', 
            []
        );
        
        return alarmStats;
    }
}

registry.category("actions").add("fire_alarm.dashboard", FireAlarmDashboard);
