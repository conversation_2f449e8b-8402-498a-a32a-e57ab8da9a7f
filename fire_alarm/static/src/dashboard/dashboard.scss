.o_fire_alarm_dashboard {
    .icon-circle {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 100; /* Ensure the circle appears above the card */
        border: 2px solid white; /* Add a white border for better visibility */
    }

    /* Style for the card */
    .card {
        margin-top: 35px; /* Add margin to the top of the card to make room for the icon */
        border-radius: 10px;
        overflow: visible; /* Changed from hidden to visible to ensure the icon shows */
        transition: transform 0.3s ease;
        border: none; /* Remove default border */

        &:hover {
            transform: translateY(-5px);
        }

        /* Style for card body to match parent card border radius */
        .card-body {
            border-radius: 10px; /* Match the card's border radius */
            padding: 1.25rem 1.25rem 1.5rem; /* Slightly more padding at bottom */
            height: 100%; /* Ensure full height */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
    }

    .table-responsive {
        max-height: 300px;
        overflow-y: auto;
    }
}
