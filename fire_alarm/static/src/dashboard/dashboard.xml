<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
    <t t-name="fire_alarm.dashboard">
        <Layout display="display" className="'o_fire_alarm_dashboard h-100 bg-light'">
            <div class="container-fluid text-center mb-4 mt-4">
                <div class="d-flex flex-column align-items-center">
                    <h2 class="text-primary mx-2">Fire Alarm Dashboard</h2>
                    <h4 class="text-secondary mx-2" t-esc="currentDate"/>
                </div>
            </div>

            <!-- Alarm Statistics Cards -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <AlarmCountItem
                            color="'bg-danger'"
                            main_icon="'fa-bell'"
                            chart_icon="'fa-fire'"
                            label="'Active Alarms'"
                            count="statistics.active_alarms"
                            model="'fire_alarm.event'"
                            domain="[['event_type', '=', 'alarm'], ['resolved', '=', false]]"
                        />
                    </div>
                    <div class="col-md-3">
                        <AlarmCountItem
                            color="'bg-warning'"
                            main_icon="'fa-exclamation-triangle'"
                            chart_icon="'fa-wrench'"
                            label="'Active Faults'"
                            count="statistics.active_faults"
                            model="'fire_alarm.event'"
                            domain="[['event_type', '=', 'fault'], ['resolved', '=', false]]"
                        />
                    </div>
                    <div class="col-md-3">
                        <AlarmCountItem
                            color="'bg-info'"
                            main_icon="'fa-building'"
                            chart_icon="'fa-home'"
                            label="'Buildings'"
                            count="statistics.building_count"
                            model="'fire_alarm.building'"
                            domain="[]"
                        />
                    </div>
                    <div class="col-md-3">
                        <AlarmCountItem
                            color="'bg-success'"
                            main_icon="'fa-hdd-o'"
                            chart_icon="'fa-microchip'"
                            label="'Devices'"
                            count="statistics.device_count"
                            model="'fire_alarm.device'"
                            domain="[]"
                        />
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card shadow-sm h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Alarm Events by Type</h5>
                            </div>
                            <div class="card-body" style="height: 300px;">
                                <AlarmChartComponent
                                    type="'pie'"
                                    data="statistics.event_type_data"
                                    options="{
                                        responsive: true,
                                        maintainAspectRatio: false,
                                        plugins: {
                                            legend: { position: 'right' },
                                            title: { display: true, text: 'Event Types' }
                                        }
                                    }"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card shadow-sm h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Alarms by Building</h5>
                            </div>
                            <div class="card-body" style="height: 300px;">
                                <AlarmChartComponent
                                    type="'bar'"
                                    data="statistics.building_data"
                                    options="{
                                        responsive: true,
                                        maintainAspectRatio: false,
                                        scales: {
                                            y: { beginAtZero: true }
                                        },
                                        plugins: {
                                            legend: { display: false },
                                            title: { display: true, text: 'Alarms by Building' }
                                        }
                                    }"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Spacer -->
            <div class="container-fluid py-3"></div>

            <!-- Recent Alarms Section -->
            <div class="container-fluid mt-5 mb-4"> <!-- Increased top margin -->
                <h4 class="text-primary mb-3">Recent Alarm Events</h4> <!-- Added section title outside card -->
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Event History</h5>
                    </div>
                    <div class="card-body py-3"> <!-- Added vertical padding -->
                        <div class="table-responsive" style="height: 350px;"> <!-- Set fixed height for scrolling -->

                            <table class="table table-hover table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th class="py-3">Event</th>
                                        <th class="py-3">Device</th>
                                        <th class="py-3">Building</th>
                                        <th class="py-3">Zone</th>
                                        <th class="py-3">Type</th>
                                        <th class="py-3">Severity</th>
                                        <th class="py-3">Time</th>
                                        <th class="py-3">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="statistics.recent_events" t-as="event" t-key="event.id">
                                        <tr t-attf-class="{{event.event_type === 'alarm' ? 'table-danger' : event.event_type === 'fault' ? 'table-warning' : ''}}">
                                            <td class="py-3" t-esc="event.name"/>
                                            <td class="py-3" t-esc="event.device_id[1]"/>
                                            <td class="py-3" t-esc="event.building_id[1]"/>
                                            <td class="py-3" t-esc="event.zone_id[1]"/>
                                            <td class="py-3" t-esc="event.event_type"/>
                                            <td class="py-3" t-esc="event.severity"/>
                                            <td class="py-3" t-esc="event.event_time"/>
                                            <td class="py-3">
                                                <span t-if="event.resolved" class="badge bg-success p-2">Resolved</span>
                                                <span t-elif="event.acknowledged" class="badge bg-info p-2">Acknowledged</span>
                                                <span t-else="" class="badge bg-danger p-2">New</span>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    </t>
</templates>
