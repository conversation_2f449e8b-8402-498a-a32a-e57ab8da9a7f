/** @odoo-module **/
// This module defines the ModernDashboard component for the fire_alarm module

import { Component, useState, onWillStart, onMounted, onWillUnmount } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { registry } from "@web/core/registry";
import { Layout } from "@web/search/layout";
//import { AlarmCountItem } from "./alarm_count_item";
import { AlarmChartComponent } from "./chart/chart";

class ModernDashboard extends Component {
    static template = "fire_alarm.ModernDashboard";
    static components = { Layout, AlarmChartComponent };


    setup() {
        this.orm = useService("orm");
        this.actionService = useService("action");
        this.busService = this.env.services.bus_service;
        this.auto_refresh_channel = 'fire_alarm_auto_refresh';

        // For Layout component
        this.display = {
            controlPanel: {},
            layout: { cssProps: { minHeight: '100%', height: '100%' } },
        };

        this.state = useState({
            statistics: null,
            loading: true,
            error: null,
            lastUpdated: null,
            countUp: true,
            viewMode: localStorage.getItem('fireAlarmDashboardViewMode') || 'grid', // 'grid' or 'list'
        });

        onWillStart(async () => {
            await this.fetchData();
            // Bus notifications are handled in the setup method
        });

        onMounted(() => {
            // Add responsive behavior
            this.setupResponsiveLayout();
            window.addEventListener('resize', this.handleResize.bind(this));
        });

        onWillUnmount(() => {
            // Clean up
            window.removeEventListener('resize', this.handleResize.bind(this));
        });

        // Set up bus notifications for real-time updates
        const refreshBusListener = async (payload) => {
            //console.log("auto refresh >> Dashboard >> refreshBusListener >> payload:", payload);
            if (payload.model === "fire_alarm.event") {
                // Fetch new data - the chart component will handle re-rendering
                this.fetchData();
            }
        };

        //console.log('subscribe channel: ', this.auto_refresh_channel);
        this.busService.subscribe(this.auto_refresh_channel, refreshBusListener);
        this.busService.addChannel(this.auto_refresh_channel);


        this._refreshStopBus = () => {
            //console.log("_refreshStopBus >> Dashboard >> _refreshStopBus >> , channel:", this.auto_refresh_channel);
            this.busService.unsubscribe(this.auto_refresh_channel, refreshBusListener);
            this.busService.deleteChannel(this.auto_refresh_channel);
        };

        onWillUnmount(() => {
            if (this._refreshStopBus) {
                this._refreshStopBus();
            }
        });
    }

    async fetchData() {
        try {
            this.state.loading = true;

            // Use the event model's statistics method for consistency
            const result = await this.orm.call(
                "fire_alarm.event",
                "get_alarm_statistics",
                []
            );
            //console.log(result)
            // Log the data to help with debugging
            console.log("Dashboard data received:", result);

            // The data should now include all required fields directly from the backend
            // No need for complex mapping, but we'll ensure all fields exist with defaults
            const mappedResult = {
                ...result,
                // Ensure all required fields exist with defaults
                active_alarms: result.active_alarms || 0,
                active_faults: result.active_faults || 0,
                critical_alarms: result.critical_alarms || 0,
                total_devices: result.total_devices || result.device_count || 0,
                total_buildings: result.total_buildings || result.building_count || 0,
                online_devices: result.online_devices || 0,
                fault_devices: result.fault_devices || 0,
                total_zones: result.total_zones || 0
            };

            this.state.statistics = mappedResult;
            this.state.lastUpdated = new Date().toLocaleTimeString();
            this.state.error = null;
        } catch (error) {
            this.state.error = "Failed to load dashboard data. Please try again.";
            console.error("Dashboard data fetch error:", error);
        } finally {
            this.state.loading = false;
        }
    }

    setupResponsiveLayout() {
        this.handleResize();
    }

    handleResize() {
        // Adjust layout based on screen size
        const width = window.innerWidth;
        const dashboardEl = document.querySelector('.o_modern_fire_alarm_dashboard');

        if (!dashboardEl) return;

        if (width < 768) {
            dashboardEl.classList.add('compact-view');
            dashboardEl.classList.remove('tablet-view', 'desktop-view');
        } else if (width < 1024) {
            dashboardEl.classList.add('tablet-view');
            dashboardEl.classList.remove('compact-view', 'desktop-view');
        } else {
            dashboardEl.classList.add('desktop-view');
            dashboardEl.classList.remove('compact-view', 'tablet-view');
        }
    }

    toggleViewMode() {
        this.state.viewMode = this.state.viewMode === 'grid' ? 'list' : 'grid';
        localStorage.setItem('fireAlarmDashboardViewMode', this.state.viewMode);
    }

    refreshDashboard() {
        this.fetchData();
    }

    viewAllAlarms() {
        this.actionService.doAction({
            type: "ir.actions.act_window",
            name: "All Alarm Events",
            res_model: "fire_alarm.event",
            views: [
                [false, "list"],
                [false, "form"],
            ],
            target: "current",
        });
    }

    viewActiveAlarms() {
        this.actionService.doAction({
            type: "ir.actions.act_window",
            name: "Active Alarms",
            res_model: "fire_alarm.event",
            domain: [["resolved", "=", false]],
            views: [
                [false, "list"],
                [false, "form"],
            ],
            target: "current",
        });
    }

    viewDevices() {
        this.actionService.doAction({
            type: "ir.actions.act_window",
            name: "Devices",
            res_model: "fire_alarm.device",
            views: [
                [false, "list"],
                [false, "form"],
            ],
            target: "current",
        });
    }

    viewBuildings() {
        this.actionService.doAction({
            type: "ir.actions.act_window",
            name: "Buildings",
            res_model: "fire_alarm.building",
            views: [
                [false, "list"],
                [false, "form"],
            ],
            target: "current",
        });
    }
}

// Register the ModernDashboard component in the actions registry
registry.category("actions").add("fire_alarm.modern_dashboard", ModernDashboard);
