.o_modern_fire_alarm_dashboard {
    background-color: #f8f9fa;
    height: 100%;
    display: flex;
    flex-direction: column;

    .dashboard-content {
        padding-bottom: 2rem;
        overflow-y: auto;
        flex: 1 1 auto;
    }

    .dashboard-header {
        transition: all 0.3s ease;

        h2 {
            font-size: 1.5rem;

            @media (max-width: 768px) {
                font-size: 1.25rem;
            }
        }
    }

    .status-card {
        transition: all 0.3s ease;
        height: 100%;

        &:hover {
            transform: translateY(-5px);
        }

        .display-5 {
            font-size: 2.5rem;

            @media (max-width: 1200px) {
                font-size: 2rem;
            }
        }
    }

    .card {
        border: none;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        overflow: hidden;

        &:hover {
            transform: translateY(-3px);
        }

        .card-header {
            padding: 1rem;
            background-color: white;
        }

        .card-body {
            padding: 1rem;
        }
    }

    .table {
        margin-bottom: 0;

        th, td {
            padding: 0.75rem 0.5rem;
            vertical-align: middle;
        }

        th {
            font-weight: 600;
            border-top: none;
            background-color: #f8f9fa;
        }

        tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        td .badge {
            font-size: 0.75rem;
            padding: 0.35em 0.65em;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
    }

    .badge {
        padding: 0.5em 0.75em;
        font-weight: 500;
    }

    .btn-group-sm > .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    // Responsive adjustments
    &.compact-view {
        .dashboard-header {
            padding: 0.75rem !important;

            h2 {
                font-size: 1.1rem;
            }
        }

        .status-card {
            padding: 0.75rem !important;

            h6 {
                font-size: 0.8rem;
            }

            .display-5 {
                font-size: 1.5rem;
            }

            .small {
                font-size: 0.7rem;
            }

            .btn-sm {
                padding: 0.2rem 0.4rem;
                font-size: 0.7rem;
            }
        }

        .table {
            th, td {
                padding: 0.5rem 0.25rem;
                font-size: 0.8rem;
            }
        }

        .badge {
            font-size: 0.7rem;
        }
    }

    &.tablet-view {
        .status-card {
            .display-5 {
                font-size: 1.8rem;
            }
        }
    }
}
