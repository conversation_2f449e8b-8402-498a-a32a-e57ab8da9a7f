<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="fire_alarm.ModernDashboard">
        <Layout display="display">
            <div class="o_modern_fire_alarm_dashboard d-flex flex-column">
                <!-- Header Section -->
                <div class="dashboard-header bg-white shadow-sm mb-4 p-3 rounded-3">
                    <div class="container-fluid">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fa fa-fire-extinguisher text-danger fa-2x me-3"></i>
                                    <h2 class="mb-0 fw-bold">Fire Alarm Control Panel</h2>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-end align-items-center">
                                    <div class="me-3 text-muted small" t-if="state.lastUpdated">
                                        <i class="fa fa-clock-o me-1"></i> Last updated: <span t-esc="state.lastUpdated"/>
                                    </div>
                                    <div class="btn-group me-2">
                                        <button class="btn btn-sm btn-outline-secondary" t-on-click="toggleViewMode">
                                            <i t-attf-class="fa {{state.viewMode === 'grid' ? 'fa-list' : 'fa-th-large'}}"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" t-on-click="refreshDashboard">
                                            <i class="fa fa-refresh"></i>
                                        </button>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-primary dropdown-toggle" type="button" id="dashboardActions" data-bs-toggle="dropdown" aria-expanded="false">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dashboardActions">
                                            <li><a class="dropdown-item" href="#" t-on-click.prevent="viewAllAlarms"><i class="fa fa-bell me-2"></i>View All Alarms</a></li>
                                            <li><a class="dropdown-item" href="#" t-on-click.prevent="viewActiveAlarms"><i class="fa fa-exclamation-triangle me-2"></i>View Active Alarms</a></li>
                                            <li><a class="dropdown-item" href="#" t-on-click.prevent="viewDevices"><i class="fa fa-microchip me-2"></i>View Devices</a></li>
                                            <li><a class="dropdown-item" href="#" t-on-click.prevent="viewBuildings"><i class="fa fa-building me-2"></i>View Buildings</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div t-if="state.loading and !state.statistics" class="flex-grow-1 d-flex align-items-center justify-content-center">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading dashboard data...</p>
                    </div>
                </div>

                <!-- Error State -->
                <div t-elif="state.error" class="flex-grow-1 d-flex align-items-center justify-content-center">
                    <div class="alert alert-danger" role="alert">
                        <i class="fa fa-exclamation-circle me-2"></i>
                        <span t-esc="state.error"></span>
                        <button class="btn btn-sm btn-outline-danger ms-3" t-on-click="refreshDashboard">
                            <i class="fa fa-refresh me-1"></i> Retry
                        </button>
                    </div>
                </div>

                <!-- Dashboard Content -->
                <div t-elif="state.statistics" class="dashboard-content flex-grow-1 overflow-auto" style="height: calc(100vh - 120px);">
                    <!-- Status Cards Section -->
                    <div class="container-fluid mb-4">
                        <div t-attf-class="row {{state.viewMode === 'grid' ? 'g-3' : 'g-2'}}">
                            <!-- Active Alarms Card -->
                            <div t-attf-class="{{state.viewMode === 'grid' ? 'col-xl-3 col-md-6' : 'col-12'}}">
                                <div t-attf-class="status-card {{state.viewMode === 'list' ? 'd-flex align-items-center' : ''}} bg-danger bg-gradient text-white rounded-3 shadow-sm p-3">
                                    <div t-attf-class="{{state.viewMode === 'list' ? 'me-auto' : ''}}">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fa fa-bell fa-lg me-2"></i>
                                            <h6 class="mb-0">Active Alarms</h6>
                                        </div>
                                        <h2 t-attf-class="mb-0 {{state.viewMode === 'list' ? 'fs-5' : 'display-5'}} fw-bold" t-esc="state.statistics.active_alarms"></h2>
                                    </div>
                                    <div t-if="state.viewMode === 'grid'" class="mt-3 d-flex justify-content-between align-items-center">
                                        <span class="small">Critical: <span t-esc="state.statistics.critical_alarms"></span></span>
                                        <button class="btn btn-sm btn-outline-light" t-on-click="viewActiveAlarms">
                                            View <i class="fa fa-arrow-right ms-1"></i>
                                        </button>
                                    </div>
                                    <button t-if="state.viewMode === 'list'" class="btn btn-sm btn-outline-light" t-on-click="viewActiveAlarms">
                                        View <i class="fa fa-arrow-right ms-1"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Faults Card -->
                            <div t-attf-class="{{state.viewMode === 'grid' ? 'col-xl-3 col-md-6' : 'col-12'}}">
                                <div t-attf-class="status-card {{state.viewMode === 'list' ? 'd-flex align-items-center' : ''}} bg-warning bg-gradient text-white rounded-3 shadow-sm p-3">
                                    <div t-attf-class="{{state.viewMode === 'list' ? 'me-auto' : ''}}">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fa fa-exclamation-triangle fa-lg me-2"></i>
                                            <h6 class="mb-0">System Faults</h6>
                                        </div>
                                        <h2 t-attf-class="mb-0 {{state.viewMode === 'list' ? 'fs-5' : 'display-5'}} fw-bold" t-esc="state.statistics.active_faults"></h2>
                                    </div>
                                    <div t-if="state.viewMode === 'grid'" class="mt-3 d-flex justify-content-between align-items-center">
                                        <span class="small">Devices: <span t-esc="state.statistics.fault_devices"></span></span>
                                        <button class="btn btn-sm btn-outline-light" t-on-click="viewActiveAlarms">
                                            View <i class="fa fa-arrow-right ms-1"></i>
                                        </button>
                                    </div>
                                    <button t-if="state.viewMode === 'list'" class="btn btn-sm btn-outline-light" t-on-click="viewActiveAlarms">
                                        View <i class="fa fa-arrow-right ms-1"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Total Devices Card -->
                            <div t-attf-class="{{state.viewMode === 'grid' ? 'col-xl-3 col-md-6' : 'col-12'}}">
                                <div t-attf-class="status-card {{state.viewMode === 'list' ? 'd-flex align-items-center' : ''}} bg-primary bg-gradient text-white rounded-3 shadow-sm p-3">
                                    <div t-attf-class="{{state.viewMode === 'list' ? 'me-auto' : ''}}">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fa fa-microchip fa-lg me-2"></i>
                                            <h6 class="mb-0">Total Devices</h6>
                                        </div>
                                        <h2 t-attf-class="mb-0 {{state.viewMode === 'list' ? 'fs-5' : 'display-5'}} fw-bold" t-esc="state.statistics.total_devices"></h2>
                                    </div>
                                    <div t-if="state.viewMode === 'grid'" class="mt-3 d-flex justify-content-between align-items-center">
                                        <span class="small">Online: <span t-esc="state.statistics.online_devices"></span></span>
                                        <button class="btn btn-sm btn-outline-light" t-on-click="viewDevices">
                                            View <i class="fa fa-arrow-right ms-1"></i>
                                        </button>
                                    </div>
                                    <button t-if="state.viewMode === 'list'" class="btn btn-sm btn-outline-light" t-on-click="viewDevices">
                                        View <i class="fa fa-arrow-right ms-1"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Buildings Card -->
                            <div t-attf-class="{{state.viewMode === 'grid' ? 'col-xl-3 col-md-6' : 'col-12'}}">
                                <div t-attf-class="status-card {{state.viewMode === 'list' ? 'd-flex align-items-center' : ''}} bg-success bg-gradient text-white rounded-3 shadow-sm p-3">
                                    <div t-attf-class="{{state.viewMode === 'list' ? 'me-auto' : ''}}">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fa fa-building fa-lg me-2"></i>
                                            <h6 class="mb-0">Buildings</h6>
                                        </div>
                                        <h2 t-attf-class="mb-0 {{state.viewMode === 'list' ? 'fs-5' : 'display-5'}} fw-bold" t-esc="state.statistics.total_buildings"></h2>
                                    </div>
                                    <div t-if="state.viewMode === 'grid'" class="mt-3 d-flex justify-content-between align-items-center">
                                        <span class="small">Zones: <span t-esc="state.statistics.total_zones"></span></span>
                                        <button class="btn btn-sm btn-outline-light" t-on-click="viewBuildings">
                                            View <i class="fa fa-arrow-right ms-1"></i>
                                        </button>
                                    </div>
                                    <button t-if="state.viewMode === 'list'" class="btn btn-sm btn-outline-light" t-on-click="viewBuildings">
                                        View <i class="fa fa-arrow-right ms-1"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Section -->
                    <div class="container-fluid mb-4">
                        <div class="row g-3">
                            <!-- Events by Type Chart -->
                            <div class="col-lg-6">
                                <div class="card shadow-sm h-100">
                                    <div class="card-header bg-white border-0">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">Events by Type</h5>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-secondary active">Day</button>
                                                <button class="btn btn-outline-secondary">Week</button>
                                                <button class="btn btn-outline-secondary">Month</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height: 300px;">
                                            <AlarmChartComponent
                                                type="'doughnut'"
                                                data="state.statistics.event_type_data"
                                                options="{
                                                    responsive: true,
                                                    maintainAspectRatio: false,
                                                    plugins: {
                                                        legend: {
                                                            position: 'right'
                                                        }
                                                    }
                                                }"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Alarms by Building Chart -->
                            <div class="col-lg-6">
                                <div class="card shadow-sm h-100">
                                    <div class="card-header bg-white border-0">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">Alarms by Building</h5>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-secondary active">Day</button>
                                                <button class="btn btn-outline-secondary">Week</button>
                                                <button class="btn btn-outline-secondary">Month</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height: 300px;">
                                            <AlarmChartComponent
                                                type="'bar'"
                                                data="state.statistics.building_data"
                                                options="{
                                                    responsive: true,
                                                    maintainAspectRatio: false,
                                                    scales: {
                                                        y: { beginAtZero: true }
                                                    },
                                                    plugins: {
                                                        legend: {
                                                            display: false
                                                        }
                                                    }
                                                }"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Events Section -->
                    <div class="container-fluid mb-5">
                        <div class="card shadow-sm">
                            <div class="card-header bg-white border-0">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Recent Events</h5>
                                    <button class="btn btn-sm btn-primary" t-on-click="viewAllAlarms">
                                        View All <i class="fa fa-arrow-right ms-1"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <!-- Loading indicator -->
                                <div t-if="state.loading" class="d-flex justify-content-center align-items-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span class="ms-2">Loading events...</span>
                                </div>
                                <div t-else="" class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                    <table class="table table-hover table-striped-columns mb-0" style="border-collapse: separate; border-spacing: 0;">
                                        <thead>
                                            <tr>
                                                <th class="ps-3">Event</th>
                                                <th>Device</th>
                                                <th>Building</th>
                                                <th>Zone</th>
                                                <th>Type</th>
                                                <th>Severity</th>
                                                <th>Time</th>
                                                <th class="pe-3">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-if="state.statistics.recent_events.length === 0">
                                                <tr>
                                                    <td colspan="8" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="fa fa-info-circle me-1"></i> No recent events found
                                                        </div>
                                                    </td>
                                                </tr>
                                            </t>
                                            <t t-foreach="state.statistics.recent_events" t-as="event" t-key="event.id">
                                                <tr>
                                                    <td class="ps-3" t-esc="event.name"/>
                                                    <td t-esc="event.device_id[1]"/>
                                                    <td t-esc="event.building_id[1]"/>
                                                    <td t-esc="event.zone_id[1]"/>
                                                    <td>
                                                        <span t-if="event.event_type === 'alarm'" class="badge bg-danger">Alarm</span>
                                                        <span t-elif="event.event_type === 'fault'" class="badge bg-warning text-dark">Fault</span>
                                                        <span t-elif="event.event_type === 'restore'" class="badge bg-success">Restore</span>
                                                        <span t-else="" class="badge bg-secondary" t-esc="event.event_type"/>
                                                    </td>
                                                    <td>
                                                        <span t-if="event.severity === 'critical'" class="badge bg-danger">Critical</span>
                                                        <span t-elif="event.severity === 'high'" class="badge bg-warning text-dark">High</span>
                                                        <span t-elif="event.severity === 'medium'" class="badge bg-info">Medium</span>
                                                        <span t-elif="event.severity === 'low'" class="badge bg-success">Low</span>
                                                    </td>
                                                    <td t-esc="event.event_time"/>
                                                    <td class="pe-3">
                                                        <span t-if="event.resolved" class="badge bg-success">Resolved</span>
                                                        <span t-elif="event.acknowledged" class="badge bg-info">Acknowledged</span>
                                                        <span t-else="" class="badge bg-danger">New</span>
                                                    </td>
                                                </tr>
                                            </t>
                                        </tbody>
                                    </table>
                                </div> <!-- End of table-responsive -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    </t>
</templates>
