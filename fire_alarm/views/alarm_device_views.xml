<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Alarm Device Form View -->
    <record id="alarm_device_view_form" model="ir.ui.view">
        <field name="name">fire_alarm.device.view.form</field>
        <field name="model">fire_alarm.device</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_events" type="object" class="oe_stat_button" icon="fa-bell">
                            <field name="event_count" string="Events" widget="statinfo"/>
                        </button>
                        <button name="action_test_device" type="object" string="Test Device" class="oe_highlight"/>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Device Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="serial_number"/>
                            <field name="device_type"/>
                            <field name="building_id"/>
                            <field name="zone_id" domain="[('building_id', '=', building_id)]"/>
                        </group>
                        <group>
                            <field name="status" widget="badge" decoration-success="status == 'normal'" decoration-danger="status == 'alarm'" decoration-warning="status == 'fault'" decoration-info="status == 'testing'" decoration-muted="status == 'disabled'"/>
                            <field name="location"/>
                            <field name="latitude"/>
                            <field name="longitude"/>
                            <field name="ip_address"/>
                            <field name="last_maintenance"/>
                            <field name="next_maintenance"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Events" name="events">
                            <field name="event_ids" nolabel="1">
                                <list>
                                    <field name="name"/>
                                    <field name="event_type"/>
                                    <field name="event_time"/>
                                    <field name="severity"/>
                                    <field name="acknowledged"/>
                                    <field name="resolved"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Alarm Device List View -->
    <record id="alarm_device_view_list" model="ir.ui.view">
        <field name="name">fire_alarm.device.view.list</field>
        <field name="model">fire_alarm.device</field>
        <field name="arch" type="xml">
            <list decoration-danger="status == 'alarm'" decoration-warning="status == 'fault'" decoration-info="status == 'testing'" decoration-muted="status == 'disabled'">
                <field name="name"/>
                <field name="serial_number"/>
                <field name="device_type"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <field name="status"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <!-- Alarm Device Search View -->
    <record id="alarm_device_view_search" model="ir.ui.view">
        <field name="name">fire_alarm.device.view.search</field>
        <field name="model">fire_alarm.device</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="serial_number"/>
                <field name="device_type"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <field name="status"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <filter string="Alarm" name="alarm" domain="[('status', '=', 'alarm')]"/>
                <filter string="Fault" name="fault" domain="[('status', '=', 'fault')]"/>
                <filter string="Normal" name="normal" domain="[('status', '=', 'normal')]"/>
                <filter string="Testing" name="testing" domain="[('status', '=', 'testing')]"/>
                <filter string="Disabled" name="disabled" domain="[('status', '=', 'disabled')]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Building" name="building" domain="[]" context="{'group_by': 'building_id'}"/>
                    <filter string="Zone" name="zone" domain="[]" context="{'group_by': 'zone_id'}"/>
                    <filter string="Device Type" name="device_type" domain="[]" context="{'group_by': 'device_type'}"/>
                    <filter string="Status" name="status" domain="[]" context="{'group_by': 'status'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Alarm Device Map View -->
    <record id="alarm_device_view_map" model="ir.ui.view">
        <field name="name">fire_alarm.device.view.map</field>
        <field name="model">fire_alarm.device</field>
        <field name="type">lp_map</field>
        <field name="arch" type="xml">
            <lp_map string="Device Locations"
                   field_latitude="latitude"
                   field_longitude="longitude"
                   field_title="name"
                   limit="200"
                   panel_title="Devices"
                   hide_address="False"
                   hide_name="False"
                   hide_title="False">
                <field name="name" string="Device Name"/>
                <field name="longitude" string="Longitude"/>
                <field name="latitude" string="Latitude"/>
                <field name="serial_number" string="Serial Number"/>
                <field name="device_type" string="Type"/>
                <field name="building_id" string="Building"/>
                <field name="zone_id" string="Zone"/>
                <field name="status" string="Status"/>
                <field name="location" string="Location"/>
                <field name="last_maintenance" string="Last Maintenance"/>
                <field name="next_maintenance" string="Next Maintenance"/>
            </lp_map>
        </field>
    </record>

    <!-- Alarm Device Action -->
    <record id="action_fire_alarm_device" model="ir.actions.act_window">
        <field name="name">Alarm Devices</field>
        <field name="res_model">fire_alarm.device</field>
        <field name="view_mode">list,form,lp_map</field>
        <field name="search_view_id" ref="alarm_device_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first alarm device
            </p>
            <p>
                Create alarm devices to monitor your buildings.
            </p>
        </field>
    </record>
</odoo>
