<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Alarm Event Map View -->
    <record id="alarm_event_view_map" model="ir.ui.view">
        <field name="name">fire_alarm.event.view.map</field>
        <field name="model">fire_alarm.event</field>
        <field name="type">lp_map</field>
        <field name="arch" type="xml">
            <lp_map string="Alarm Events Map"
                   field_latitude="latitude"
                   field_longitude="longitude"
                   field_title="name"
                   limit="200"
                   panel_title="Alarm Events"
                   hide_address="False"
                   hide_name="False"
                   hide_title="False">
                
                <field name="longitude" string="Longitude" />
                <field name="latitude" string="Latitude" />
                
                <field name="name" string="Event ID"/>
                <field name="device_id" string="Device"/>
                <field name="building_id" string="Building"/>
                <field name="zone_id" string="Zone"/>
                <field name="event_type" string="Event Type"/>
                <field name="severity" string="Severity"/>
                <field name="event_time" string="Time"/>
                <field name="acknowledged" string="Acknowledged"/>
                <field name="resolved" string="Resolved"/>
                <field name="description" string="Description"/>
            </lp_map>
        </field>
    </record>

    <!-- Update the existing action to include the map view -->
    <record id="action_fire_alarm_event" model="ir.actions.act_window">
        <field name="name">Alarm Events</field>
        <field name="res_model">fire_alarm.event</field>
        <field name="view_mode">lp_map,list,form</field>
        <field name="search_view_id" ref="alarm_event_view_search"/>
        <field name="context">{'search_default_unresolved': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No alarm events yet
            </p>
            <p>
                Alarm events will be displayed here when they occur.
            </p>
        </field>
    </record>


</odoo>
