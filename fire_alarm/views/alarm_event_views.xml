<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Alarm Event Form View -->
    <record id="alarm_event_view_form" model="ir.ui.view">
        <field name="name">fire_alarm.event.view.form</field>
        <field name="model">fire_alarm.event</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_acknowledge" string="Acknowledge" type="object" class="oe_highlight" invisible="acknowledged == True"/>
                    <button name="action_resolve" string="Resolve" type="object" class="oe_highlight" invisible="resolved == True"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Event ID"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="device_id"/>
                            <field name="zone_id"/>
                            <field name="building_id"/>
                            <field name="event_type"/>
                            <field name="severity"/>
                            <field name="severity" widget="badge" decoration-info="severity == 'low'" decoration-warning="severity == 'medium'" decoration-danger="severity == 'high' or severity == 'critical'"/>
                        </group>
                        <group>
                            <field name="event_time"/>
                            <field name="acknowledged"/>
                            <field name="acknowledged_by" invisible="not acknowledged"/>
                            <field name="acknowledged_time" invisible="not acknowledged"/>
                            <field name="resolved"/>
                            <field name="resolved_by" invisible="not resolved"/>
                            <field name="resolved_time" invisible="not resolved"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" nolabel="1" placeholder="Description..."/>
                        </page>
                        <page string="Location" name="location">
                            <group>
                                <group string="Coordinates (From Device)">
                                    <field name="latitude" readonly="1"/>
                                    <field name="longitude" readonly="1"/>
                                </group>
                                <group string="Device Location">
                                    <field name="device_location" readonly="1"/>
                                    <field name="building_id" readonly="1"/>
                                    <field name="zone_id" readonly="1"/>
                                </group>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <i class="fa fa-info-circle"/> Location coordinates are automatically synchronized with the device location.
                            </div>
                        </page>
                        <page string="Notes" name="notes">
                            <field name="notes" nolabel="1" placeholder="Notes..."/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Alarm Event List View -->
    <record id="alarm_event_view_list" model="ir.ui.view">
        <field name="name">fire_alarm.event.view.list</field>
        <field name="model">fire_alarm.event</field>
        <field name="arch" type="xml">
            <list decoration-danger="event_type == 'alarm' and not resolved" decoration-warning="event_type == 'fault' and not resolved" decoration-info="event_type == 'test'" decoration-success="event_type == 'restore' or resolved">
                <field name="name"/>
                <field name="device_id"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <field name="event_type"/>
                <field name="severity"/>
                <field name="event_time"/>
                <field name="acknowledged"/>
                <field name="resolved"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <!-- Alarm Event Search View -->
    <record id="alarm_event_view_search" model="ir.ui.view">
        <field name="name">fire_alarm.event.view.search</field>
        <field name="model">fire_alarm.event</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="device_id"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <field name="event_type"/>
                <field name="severity"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <filter string="Alarms" name="alarms" domain="[('event_type', '=', 'alarm')]"/>
                <filter string="Faults" name="faults" domain="[('event_type', '=', 'fault')]"/>
                <filter string="Tests" name="tests" domain="[('event_type', '=', 'test')]"/>
                <filter string="Restores" name="restores" domain="[('event_type', '=', 'restore')]"/>
                <filter string="Maintenance" name="maintenance" domain="[('event_type', '=', 'maintenance')]"/>
                <filter string="Critical" name="critical" domain="[('severity', '=', 'critical')]"/>
                <filter string="High" name="high" domain="[('severity', '=', 'high')]"/>
                <filter string="Medium" name="medium" domain="[('severity', '=', 'medium')]"/>
                <filter string="Low" name="low" domain="[('severity', '=', 'low')]"/>
                <filter string="Unacknowledged" name="unacknowledged" domain="[('acknowledged', '=', False)]"/>
                <filter string="Acknowledged" name="acknowledged" domain="[('acknowledged', '=', True)]"/>
                <filter string="Unresolved" name="unresolved" domain="[('resolved', '=', False)]"/>
                <filter string="Resolved" name="resolved" domain="[('resolved', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Building" name="building" domain="[]" context="{'group_by': 'building_id'}"/>
                    <filter string="Zone" name="zone" domain="[]" context="{'group_by': 'zone_id'}"/>
                    <filter string="Device" name="device" domain="[]" context="{'group_by': 'device_id'}"/>
                    <filter string="Event Type" name="event_type" domain="[]" context="{'group_by': 'event_type'}"/>
                    <filter string="Severity" name="severity" domain="[]" context="{'group_by': 'severity'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Alarm Event Action -->
    <record id="action_fire_alarm_event" model="ir.actions.act_window">
        <field name="name">Alarm Events</field>
        <field name="res_model">fire_alarm.event</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="alarm_event_view_search"/>
        <field name="context">{'search_default_unresolved': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No alarm events yet
            </p>
            <p>
                Alarm events will be displayed here when they occur.
            </p>
        </field>
    </record>
</odoo>
