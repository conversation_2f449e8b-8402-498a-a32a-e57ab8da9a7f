<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Alarm Zone Form View -->
    <record id="alarm_zone_view_form" model="ir.ui.view">
        <field name="name">fire_alarm.zone.view.form</field>
        <field name="model">fire_alarm.zone</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-hdd-o">
                            <field name="device_count" string="Devices" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Zone Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="building_id"/>
                            <field name="floor"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" nolabel="1" placeholder="Description..."/>
                        </page>
                        <page string="Devices" name="devices">
                            <field name="device_ids" nolabel="1">
                                <list>
                                    <field name="name"/>
                                    <field name="serial_number"/>
                                    <field name="device_type"/>
                                    <field name="status"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Alarm Zone List View -->
    <record id="alarm_zone_view_list" model="ir.ui.view">
        <field name="name">fire_alarm.zone.view.list</field>
        <field name="model">fire_alarm.zone</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="code"/>
                <field name="building_id"/>
                <field name="floor"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <!-- Alarm Zone Search View -->
    <record id="alarm_zone_view_search" model="ir.ui.view">
        <field name="name">fire_alarm.zone.view.search</field>
        <field name="model">fire_alarm.zone</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
                <field name="building_id"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Building" name="building" domain="[]" context="{'group_by': 'building_id'}"/>
                    <filter string="Floor" name="floor" domain="[]" context="{'group_by': 'floor'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Alarm Zone Action -->
    <record id="action_fire_alarm_zone" model="ir.actions.act_window">
        <field name="name">Alarm Zones</field>
        <field name="res_model">fire_alarm.zone</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="alarm_zone_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first alarm zone
            </p>
            <p>
                Create alarm zones to organize your fire alarm devices.
            </p>
        </field>
    </record>
</odoo>
