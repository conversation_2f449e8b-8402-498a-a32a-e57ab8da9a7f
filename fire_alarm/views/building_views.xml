<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Building Form View -->
    <record id="building_view_form" model="ir.ui.view">
        <field name="name">fire_alarm.building.view.form</field>
        <field name="model">fire_alarm.building</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_zones" type="object" class="oe_stat_button" icon="fa-th-large">
                            <field name="zone_count" string="Zones" widget="statinfo"/>
                        </button>
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-hdd-o">
                            <field name="device_count" string="Devices" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Building Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="fire_alarm_id"/>
                            <field name="floors"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Address" name="address">
                            <field name="address" nolabel="1" placeholder="Address..."/>
                        </page>
                        <page string="Zones" name="zones">
                            <field name="zone_ids" nolabel="1">
                                <list>
                                    <field name="name"/>
                                    <field name="code"/>
                                    <field name="floor"/>
                                    <field name="device_count"/>
                                </list>
                            </field>
                        </page>
                        <page string="Devices" name="devices">
                            <field name="device_ids" nolabel="1">
                                <list>
                                    <field name="name"/>
                                    <field name="serial_number"/>
                                    <field name="device_type"/>
                                    <field name="zone_id"/>
                                    <field name="status"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Building List View -->
    <record id="building_view_list" model="ir.ui.view">
        <field name="name">fire_alarm.building.view.list</field>
        <field name="model">fire_alarm.building</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="code"/>
                <field name="fire_alarm_id"/>
                <field name="floors"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <!-- Building Search View -->
    <record id="building_view_search" model="ir.ui.view">
        <field name="name">fire_alarm.building.view.search</field>
        <field name="model">fire_alarm.building</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
                <field name="fire_alarm_id"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Fire Alarm System" name="fire_alarm" domain="[]" context="{'group_by': 'fire_alarm_id'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Building Action -->
    <record id="action_fire_alarm_building" model="ir.actions.act_window">
        <field name="name">Buildings</field>
        <field name="res_model">fire_alarm.building</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="building_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first building
            </p>
            <p>
                Create buildings to organize your fire alarm zones and devices.
            </p>
        </field>
    </record>
</odoo>
