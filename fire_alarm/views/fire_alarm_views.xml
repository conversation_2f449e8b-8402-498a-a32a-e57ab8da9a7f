<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Fire Alarm Form View -->
    <record id="fire_alarm_view_form" model="ir.ui.view">
        <field name="name">fire.alarm.view.form</field>
        <field name="model">fire_alarm.fire_alarm</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_buildings" type="object" class="oe_stat_button" icon="fa-building">
                            <field name="building_count" string="Buildings" widget="statinfo"/>
                        </button>
                        <button name="action_view_zones" type="object" class="oe_stat_button" icon="fa-th-large">
                            <field name="zone_count" string="Zones" widget="statinfo"/>
                        </button>
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-hdd-o">
                            <field name="device_count" string="Devices" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Fire Alarm System Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Buildings" name="buildings">
                            <field name="building_ids" nolabel="1">
                                <list>
                                    <field name="name"/>
                                    <field name="code"/>
                                    <field name="floors"/>
                                    <field name="address"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Fire Alarm List View -->
    <record id="fire_alarm_view_list" model="ir.ui.view">
        <field name="name">fire.alarm.view.list</field>
        <field name="model">fire_alarm.fire_alarm</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="device_count"/>
                <field name="zone_count"/>
            </list>
        </field>
    </record>

    <!-- Fire Alarm Search View -->
    <record id="fire_alarm_view_search" model="ir.ui.view">
        <field name="name">fire.alarm.view.search</field>
        <field name="model">fire_alarm.fire_alarm</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Fire Alarm Action -->
    <record id="action_fire_alarm" model="ir.actions.act_window">
        <field name="name">Fire Alarm Systems</field>
        <field name="res_model">fire_alarm.fire_alarm</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="fire_alarm_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first fire alarm system
            </p>
            <p>
                Create fire alarm systems to monitor your buildings.
            </p>
        </field>
    </record>
</odoo>
