

```
import requests
import json

url = "https://graph.facebook.com/v23.0/796556116870860/messages"

payload = json.dumps({
  "messaging_product": "whatsapp",
  "to": "+966592772339",
  "type": "template",
  "template": {
    "name": "hello_world",
    "language": {
      "code": "en_US"
    }
  }
})
headers = {
  'Content-Type': 'application/json',
  'Authorization': '••••••'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)
```

Token:
EAASbZBcMQpv8BPvtNAhnvdcZBe4O4FqA6YESZB6MZCKILWGriISUzODIQ2fYA3rJpIImpSjcmLvVq41IwEsEvcbaDLQSvpMh6zShcGvVprjVNcGKyECtZAzGmkUyibdYjsu1GZChaAtZADlZBbDOk9ZB8bAKnCzgiAZCCDZC5ExcQ2r54rlLwqgASTluR1VuKKLTpZAdglFgkJH0HUrjzuGryZCNqEZAdgznAz2NDp5tf8hshZA1Wl4Fg1WoZCv062CiRi8ZD
