# Meta WhatsApp Business API Integration Guide

## Overview

This documentation provides comprehensive instructions for integrating Odoo with Meta's WhatsApp Business API using the `lp_whatsapp` and `ams_vm_whatsapp` modules. The integration allows sending WhatsApp messages to visitors through the AMS Visitor Management system.

## Architecture Overview

### Module Structure

1. **lp_whatsapp** - Core WhatsApp integration module
   - Provides base WhatsApp functionality
   - Handles API communication with Meta
   - Manages templates, configurations, and message tracking
   - Webhook handling for message status updates

2. **ams_vm_whatsapp** - Visitor Management WhatsApp extension
   - Extends AMS Visitor Management with WhatsApp capabilities
   - Sends notifications when visitor requests are approved
   - Integrates with the base lp_whatsapp module

### Key Components

- **WhatsApp Configuration** (`whatsapp.config`): Stores API credentials and settings
- **WhatsApp Service** (`whatsapp.service`): Service layer for API communication
- **WhatsApp Template** (`whatsapp.template`): Message template management
- **WhatsApp Message** (`whatsapp.message`): Message tracking and status
- **Webhook Controller**: Handles incoming webhook notifications from Meta

## Prerequisites

### Meta Business Account Setup

1. **Meta Business Account**
   - Create a Meta Business account at [business.facebook.com](https://business.facebook.com)
   - Verify your business information

2. **WhatsApp Business Account**
   - Add WhatsApp Business to your Meta Business account
   - Verify your phone number
   - Complete business verification process

3. **App Creation**
   - Create a new app in Meta for Developers
   - Add WhatsApp Business API product to your app
   - Note down your App ID and App Secret

### Required Information

Before starting the integration, collect the following:

- **Phone Number ID**: From WhatsApp Business Manager
- **Access Token**: System User Access Token with WhatsApp permissions
- **App Secret**: For webhook signature verification
- **Webhook Verify Token**: Custom token for webhook verification

## Step-by-Step Setup Guide

### Step 1: Install Required Modules

1. Install the base modules:
   ```bash
   # Install lp_whatsapp (core WhatsApp module)
   # Install ams_vm_whatsapp (visitor management extension)
   ```

2. Update module list and install:
   - Go to Apps menu in Odoo
   - Update Apps List
   - Search for "Laplace WhatsApp Integration"
   - Install the module

### Step 2: Configure WhatsApp Settings

1. **Navigate to WhatsApp Configuration**
   - Go to WhatsApp → Configuration → WhatsApp Configurations
   - Click "Create" to add a new configuration

2. **Fill in Configuration Details**:
   ```
   Configuration Name: Production WhatsApp Config
   Phone Number ID: [Your Phone Number ID from Meta]
   Access Token: [Your System User Access Token]
   App Secret: [Your App Secret from Meta]
   Webhook Verify Token: [Custom token - create a secure random string]
   API Version: v19.0 (default)
   Base URL: https://graph.facebook.com (default)
   Test Phone Number: [Your test phone number with country code]
   ```

3. **Test the Configuration**
   - Click "Test Connection" button
   - Verify the connection is successful
   - Check the connection status shows "Connected"

### Step 3: Setup Webhook

1. **Configure Webhook URL in Meta**
   - Go to your app in Meta for Developers
   - Navigate to WhatsApp → Configuration
   - Set Webhook URL to: `https://yourdomain.com/whatsapp/webhook`
   - Set Verify Token to match your configuration
   - Subscribe to webhook fields: `messages`, `message_deliveries`

2. **Test Webhook**
   - Use the webhook test endpoint: `/whatsapp/webhook/test`
   - Verify webhook verification is working
   - Check webhook logs for any issues

### Step 4: Create WhatsApp Service

1. **Navigate to WhatsApp Services**
   - Go to WhatsApp → Configuration → WhatsApp Services
   - Click "Create" to add a new service

2. **Configure Service**:
   ```
   Service Name: Main WhatsApp Service
   WhatsApp Configuration: [Select your configuration]
   Active: ✓
   Max Retry Attempts: 3
   Retry Delay (Minutes): 5
   Rate Limit Per Minute: 60
   ```

3. **Test Service**
   - Click "Test Connection"
   - Verify service status shows "Active"

### Step 5: Create Message Templates

#### Important: Template Approval Process

**All WhatsApp message templates must be approved by Meta before use. This process typically takes 24-48 hours.**

1. **Create Templates in Meta Business Manager**
   - Go to WhatsApp Manager → Message Templates
   - Create templates for different use cases:
     - Visitor approval notification
     - Visitor rejection notification
     - General notifications

2. **Add Templates to Odoo**
   - Go to WhatsApp → Templates → WhatsApp Templates
   - Click "Create" for each approved template

3. **Example Template Configuration**:
   ```
   Template Name: Visitor Approval Notification
   WhatsApp Template ID: visitor_approval_ar (from Meta)
   Language: Arabic
   Category: UTILITY
   Body Text: مرحباً {{1}}، تم الموافقة على طلب زيارتك لـ {{2}} في {{3}}. رمز الدخول: {{4}}
   Parameter Count: 4 (auto-calculated)
   Parameter Names: visitor_name, company_name, visit_date, access_code
   Approval Status: APPROVED
   ```

### Step 6: Configure Visitor Management Integration

1. **Enable WhatsApp in Visitor Settings**
   - Go to AMS → Configuration → Settings
   - Enable WhatsApp notifications
   - Set default WhatsApp service and templates

2. **Configure Visitor Forms**
   - Ensure visitor forms include WhatsApp phone field
   - Set default WhatsApp notification preferences

## Testing Procedures (Free Messages Only)

### Meta's Free Message Allowance

Meta provides **1,000 free conversations per month** for testing and development. A conversation is a 24-hour window of messaging between your business and a user.

### Test Message Types

1. **Template Messages** (Recommended for testing)
   - Use approved templates only
   - Count as business-initiated conversations
   - Best for testing the complete flow

2. **Free-form Messages** (Limited)
   - Only available within 24 hours of user-initiated conversation
   - Use sparingly for testing

### Testing Steps

#### 1. Test API Connection

```python
# Test connection through Odoo interface
# Go to WhatsApp → Configuration → WhatsApp Configurations
# Click "Test Connection" on your configuration
```

#### 2. Test Template Message

1. **Create a Test Visitor**:
   ```
   Visitor Name: Test User
   Mobile Phone: +************ (your test number)
   WhatsApp Phone: +************
   Send WhatsApp Notification: ✓
   ```

2. **Approve Visitor Request**:
   - The system should automatically send WhatsApp notification
   - Check message status in WhatsApp Messages view
   - Verify message delivery on your phone

#### 3. Test Webhook Reception

1. **Send a message to your WhatsApp Business number**
2. **Check webhook logs**:
   - Go to WhatsApp → Monitoring → Webhook Logs
   - Verify incoming message is logged
   - Check message status updates

#### 4. Test Bulk Operations

1. **Create multiple test visitors**
2. **Use bulk send WhatsApp action**:
   - Select multiple visitors
   - Actions → Send WhatsApp Messages
   - Monitor success/failure rates

### Testing Best Practices

1. **Use Test Phone Numbers**
   - Use your own phone numbers for testing
   - Don't send test messages to real customers

2. **Monitor Message Quotas**
   - Track your monthly conversation usage
   - Stay within the 1,000 free conversation limit

3. **Test Different Scenarios**
   - Successful message delivery
   - Failed message delivery (invalid phone numbers)
   - Webhook status updates
   - Template parameter validation

4. **Error Handling Testing**
   - Test with invalid phone numbers
   - Test with missing templates
   - Test API connection failures

## Common Issues and Troubleshooting

### 1. Template Not Approved

**Error**: Template status shows "PENDING" or "REJECTED"

**Solution**:
- Check template status in Meta Business Manager
- Ensure template follows Meta's guidelines
- Wait for approval (24-48 hours)
- Resubmit if rejected with corrections

### 2. Webhook Verification Failed

**Error**: Webhook verification returns 403 error

**Solution**:
- Verify webhook verify token matches configuration
- Check webhook URL is accessible
- Ensure HTTPS is properly configured
- Check server logs for detailed error messages

### 3. Message Sending Failed

**Error**: Messages show "Failed" status

**Solution**:
- Verify phone number format (include country code)
- Check access token permissions
- Verify template is approved
- Check API rate limits
- Review error logs in WhatsApp Messages

### 4. Invalid Phone Number Format

**Error**: "Invalid phone number" error

**Solution**:
- Use international format: +[country code][number]
- Remove spaces, dashes, or special characters
- Example: +************ (not 0501234567)

### 5. API Rate Limit Exceeded

**Error**: "Rate limit exceeded" error

**Solution**:
- Reduce message sending frequency
- Implement proper retry mechanisms
- Check rate limit settings in service configuration
- Consider upgrading to paid plan for higher limits

## Security Considerations

### 1. Access Token Security

- Store access tokens securely
- Use system user tokens, not personal tokens
- Regularly rotate access tokens
- Limit token permissions to required scopes

### 2. Webhook Security

- Always verify webhook signatures
- Use HTTPS for webhook URLs
- Implement proper authentication
- Log and monitor webhook requests

### 3. Phone Number Privacy

- Validate phone numbers before storing
- Implement opt-out mechanisms
- Comply with local privacy regulations
- Secure phone number data storage

## Production Deployment

### 1. Environment Setup

- Use separate configurations for development/production
- Implement proper backup procedures
- Set up monitoring and alerting
- Configure log rotation

### 2. Scaling Considerations

- Monitor API rate limits
- Implement message queuing for high volume
- Set up load balancing for webhooks
- Plan for template approval lead times

### 3. Monitoring and Maintenance

- Monitor message delivery rates
- Track API success rates
- Regular template audits
- Update API versions as needed

## API Reference

### Key Models and Methods

#### WhatsApp Service (`whatsapp.service`)

```python
# Send template message
service.send_template_message(
    recipient_phone='+************',
    template_id='visitor_approval_ar',
    parameters=['John Doe', 'ABC Company', '2024-01-15', '1234'],
    language='ar'
)

# Send text message (within 24-hour window)
service.send_text_message(
    recipient_phone='+************',
    text_content='Hello, this is a test message'
)

# Test connection
result = service.test_connection()
```

#### WhatsApp Template (`whatsapp.template`)

```python
# Format message with parameters
formatted_message = template.format_message([
    'John Doe',
    'ABC Company', 
    '2024-01-15',
    '1234'
])

# Get WhatsApp API payload
payload = template.get_whatsapp_payload(
    recipient_phone='+************',
    parameters=['John Doe', 'ABC Company', '2024-01-15', '1234']
)
```

### Webhook Endpoints

- **Webhook Handler**: `POST /whatsapp/webhook`
- **Webhook Verification**: `GET /whatsapp/webhook`
- **Test Webhook**: `GET /whatsapp/webhook/test`
- **Webhook Status**: `POST /whatsapp/webhook/status`
- **Send Test Message**: `POST /whatsapp/send/test`

## Support and Resources

### Documentation Links

- [Meta WhatsApp Business API Documentation](https://developers.facebook.com/docs/whatsapp)
- [WhatsApp Business Management API](https://developers.facebook.com/docs/whatsapp/business-management-api)
- [Message Templates Guidelines](https://developers.facebook.com/docs/whatsapp/message-templates)

### Getting Help

1. **Check Logs**: Always check Odoo logs and WhatsApp message logs first
2. **Test Connection**: Use built-in connection test tools
3. **Webhook Logs**: Monitor webhook logs for delivery issues
4. **Meta Support**: Use Meta Business Support for API-related issues

### Development Tips

1. **Start Small**: Begin with simple template messages
2. **Test Thoroughly**: Use the free message allowance for comprehensive testing
3. **Monitor Quotas**: Keep track of your message usage
4. **Plan Templates**: Design and submit templates early in development
5. **Error Handling**: Implement robust error handling and retry mechanisms

---

**Last Updated**: January 2024
**Version**: 1.0
**Modules**: lp_whatsapp v18.*******, ams_vm_whatsapp v18.*******