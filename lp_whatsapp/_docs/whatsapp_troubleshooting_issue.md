# WhatsApp Integration Troubleshooting Issue

## Problem Description

The WhatsApp module `lp_whatsapp` is experiencing a discrepancy between direct API calls and Odoo API calls:

- **Direct curl test**: Successfully sends and delivers messages to WhatsApp
- **Odoo API test**: Returns success response but messages are not actually delivered

## Environment Details

- **Odoo Version**: 18.0
- **Database**: AMS_18_202411
- **Port**: 8020
- **Module**: lp_whatsapp
- **WhatsApp Business API**: Facebook Graph API v22.0/v23.0

## Test Results

### ✅ Working Direct API Call (curl)

```bash
curl -i -X POST \
  https://graph.facebook.com/v22.0/***************/messages \
  -H 'Authorization: Bearer [ACCESS_TOKEN]' \
  -H 'Content-Type: application/json' \
  -d '{
    "messaging_product": "whatsapp",
    "to": "************",
    "type": "template",
    "template": {
      "name": "hello_world",
      "language": {
        "code": "en_US"
      }
    }
  }'
```

**Response**: HTTP/2 200 - Message successfully delivered to WhatsApp

### ❌ Failing Odoo API Call

**Endpoint**: `http://localhost:8020/whatsapp/send/test`

**Request Body**:
```json
{
  "params": {
    "phone_number": "+************",
    "message_text": "Hello from Postman test abadr!"
  }
}
```

**Response**:
```json
{
  "jsonrpc": "2.0",
  "id": null,
  "result": {
    "status": "success",
    "message": "Test message sent successfully",
    "message_id": 1,
    "whatsapp_message_id": "wamid.HBgMOTY2NTkyNzcyMzM5FQIAERgSRjY2MUZBRDAwN0I5MTU3NUY4AA=="
  }
}
```

**Issue**: Despite success response, no message is delivered to WhatsApp.

## Configuration Details

- **Phone Number ID**: ***************
- **WhatsApp Business Account**: Active
- **API Version**: v22.0 (auto-upgraded to v23.0)
- **Access Token**: EAASbZBcMQpv8BPnLB599fPQr0dlb0lNoyyUPXld51Bj1QAZAjNG57BtxEkzVH1F9nJLAa0YI0upnfEFJEJvFHbydvoAKlTzR5aEb3BZATAha7HoaVCP0M1das0BSAmmQBCT9RwppZAcKXxmZCTRwDOecvAfGZBH6onrkQ0dPFZBZCz5vRPxs8psRDoVM1iVqPQ88jDyw2beORLYQxDVlw1LbPK8ZBbrZB5CD4WvwQ6sbm1MhgZAmPvavajYi6kPocIZD

## Files to Examine

1. `/lp_whatsapp/models/whatsapp_service.py` - Core service implementation
2. `/lp_whatsapp/controllers/whatsapp_webhook.py` - Test endpoint controller
3. `/lp_whatsapp/models/whatsapp_config.py` - Configuration model

## Key Methods to Debug

- `whatsapp_service._make_api_request()`
- `whatsapp_service._send_message_with_tracking()`
- `whatsapp_service.send_text_message()`
- `whatsapp_service._get_api_url()`
- `whatsapp_service._get_api_headers()`

## Suspected Issues

1. **Token Expiration**: Access token may be expired in database
2. **API Version Mismatch**: Hardcoded v22.0 vs dynamic version
3. **Request Format**: Difference between curl and Odoo request structure
4. **Response Handling**: Controller may not properly handle API errors
5. **Configuration Caching**: Odoo may be using cached expired configuration

## ✅ RESOLUTION

### Root Causes Identified
1. **Expired Access Token**: The primary issue was an expired access token in the database
2. **Phone Number Format Issue**: WhatsApp API expects phone numbers WITHOUT the + sign, but Odoo was sending them WITH the + sign
3. **Controller Response Issue**: The controller was returning raw response objects instead of proper JSON

### Solutions Applied
1. **Updated Access Token**: Replaced expired token with fresh token in database
2. **Fixed Phone Number Formatting**: Added `_format_phone_number()` method to remove + sign from phone numbers before sending to API
3. **Fixed API Version**: Updated default API version from v19.0 to v22.0
4. **Fixed Controller Response**: Modified controller to return proper JSON instead of raw response objects
5. **Restarted Server**: Cleared cached configuration by restarting Odoo

### Key Technical Fix - Phone Number Formatting
**Before (Failed)**:
```json
{
  "messaging_product": "whatsapp",
  "recipient_type": "individual",
  "to": "+************",  // ← WITH + SIGN (WRONG)
  "type": "text",
  "text": {"body": "Test message"}
}
```

**After (Working)**:
```json
{
  "messaging_product": "whatsapp",
  "recipient_type": "individual",
  "to": "************",  // ← WITHOUT + SIGN (CORRECT)
  "type": "text",
  "text": {"body": "Test message"}
}
```

### Test Results After Fix
- **✅ Curl Test**: Still working (HTTP 200, message delivered)
- **✅ Odoo API Test**: Now working (HTTP 200, message delivered, proper JSON response)
- **✅ Message Delivery**: Both tests now successfully deliver messages to WhatsApp

### Final Working Response
```json
{
  "jsonrpc": "2.0",
  "id": null,
  "result": {
    "status": "success",
    "message": "Test message sent successfully",
    "message_id": 13,
    "whatsapp_message_id": "wamid.HBgMOTY2NTkyNzcyMzM5FQIAERgSNUY3M0FGRUI5Q0E2OENFMUFEAA=="
  }
}
```

### Key Logs After Fix
```
Status Code: 200
Response Text: {"messaging_product":"whatsapp","contacts":[{"input":"************","wa_id":"************"}],"messages":[{"id":"wamid.HBgMOTY2NTkyNzcyMzM5FQIAERgSNUY3M0FGRUI5Q0E2OENFMUFEAA=="}]}
Text message sent successfully to +************
```

### Prevention
- Implement token refresh mechanism
- Add token expiration monitoring
- Set up alerts for API authentication failures
- Regular testing of both direct API and Odoo endpoints
- Ensure phone number formatting consistency across all message types

## Run Command

```bash
/Users/<USER>/Documents/Laplace/Projects/odoo18/.venv/bin/python \
/Users/<USER>/Documents/Laplace/Projects/odoo18/odoo-bin \
-c /Users/<USER>/Documents/Laplace/Projects/odoo18/config/ams18.conf \
-d AMS_18_202411 \
-u lp_whatsapp \
--http-port=8020 \
--dev xml,reload
```
