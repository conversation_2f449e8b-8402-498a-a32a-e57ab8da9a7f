# -*- coding: utf-8 -*-

import json
import logging
import hmac
import hashlib
from odoo import http, _
from odoo.http import request
from werkzeug.wrappers import Response

_logger = logging.getLogger(__name__)


class WhatsAppWebhook(http.Controller):

    @http.route('/whatsapp/webhook', type='http', auth='none', methods=['GET', 'POST'], csrf=False)
    def whatsapp_webhook(self, **kwargs):
        """Handle WhatsApp webhook requests"""

        if request.httprequest.method == 'GET':
            return self._handle_webhook_verification(**kwargs)
        elif request.httprequest.method == 'POST':
            return self._handle_webhook_notification()
        else:
            return self._error_response('Method not allowed', 405)

    def _handle_webhook_verification(self, **kwargs):
        """Handle webhook verification challenge from WhatsApp"""
        try:
            # Get parameters from query string
            mode = kwargs.get('hub.mode')
            token = kwargs.get('hub.verify_token')
            challenge = kwargs.get('hub.challenge')

            _logger.info(f"Webhook verification request: mode={mode}, token={token}, challenge={challenge}")

            if mode == 'subscribe':
                # هنا ممكن تعمل تحقق من config زي ما عندك
                config = request.env['whatsapp.config'].sudo().search([
                    ('is_active', '=', True)
                ], limit=1)

                if not config:
                    _logger.error("No active WhatsApp configuration found")
                    return Response("Configuration not found", status=404, content_type='text/plain')

                if config.webhook_verify_token == token:
                    _logger.info("Webhook verification successful")
                    # WhatsApp expects ONLY the challenge string
                    return challenge
                else:
                    _logger.error(f"Invalid verify token. Expected: {config.webhook_verify_token}, Got: {token}")
                    return Response("Invalid verify token", status=403, content_type='text/plain')
            else:
                _logger.error(f"Invalid mode: {mode}")
                return Response("Invalid mode", status=400, content_type='text/plain')

        except Exception as e:
            _logger.error(f"Webhook verification failed: {str(e)}")
            return Response("Verification failed", status=500, content_type='text/plain')

    def _handle_webhook_notification(self):
        """Handle webhook notification from WhatsApp"""
        try:
            # Get request data
            data = request.httprequest.get_data()

            if not data:
                _logger.error("Empty webhook payload")
                return self._error_response('Empty payload', 400)

            # Parse JSON data
            try:
                webhook_data = json.loads(data.decode('utf-8'))
            except json.JSONDecodeError as e:
                _logger.error(f"Invalid JSON in webhook payload: {str(e)}")
                return self._error_response('Invalid JSON', 400)

            _logger.info(f"Received webhook notification: {json.dumps(webhook_data, indent=2)}")

            # Verify webhook signature if configured
            if not self._verify_webhook_signature(data):
                _logger.error("Webhook signature verification failed")
                return self._error_response('Invalid signature', 403)

            # Process the webhook data
            success = self._process_webhook_data(webhook_data)

            if success:
                return self._success_response('Webhook processed successfully')
            else:
                return self._error_response('Webhook processing failed', 500)

        except Exception as e:
            _logger.error(f"Webhook notification handling failed: {str(e)}")
            return self._error_response('Internal server error', 500)

    def _verify_webhook_signature(self, payload):
        """Verify webhook signature using app secret"""
        try:
            # Get signature from headers
            signature_header = request.httprequest.headers.get('X-Hub-Signature-256')

            if not signature_header:
                _logger.warning("No signature header found, skipping verification")
                return True  # Allow if no signature is provided

            # Get WhatsApp configuration
            config = request.env['whatsapp.config'].sudo().search([
                ('is_active', '=', True)
            ], limit=1)

            if not config or not config.app_secret:
                _logger.warning("No app secret configured, skipping signature verification")
                return True

            # Extract signature from header (format: sha256=<signature>)
            if not signature_header.startswith('sha256='):
                _logger.error("Invalid signature format")
                return False

            provided_signature = signature_header[7:]  # Remove 'sha256=' prefix

            # Calculate expected signature
            expected_signature = hmac.new(
                config.app_secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()

            # Compare signatures
            if hmac.compare_digest(provided_signature, expected_signature):
                _logger.info("Webhook signature verification successful")
                return True
            else:
                _logger.error("Webhook signature mismatch")
                return False

        except Exception as e:
            _logger.error(f"Signature verification error: {str(e)}")
            return False

    def _process_webhook_data(self, webhook_data):
        """Process webhook data and update message statuses"""
        try:
            # Get WhatsApp service
            service = request.env['whatsapp.service'].sudo().search([
                ('is_active', '=', True)
            ], limit=1)

            if not service:
                _logger.error("No active WhatsApp service found")
                return False

            # Process the webhook data
            return service.process_webhook_message(webhook_data)

        except Exception as e:
            _logger.error(f"Webhook data processing failed: {str(e)}")
            return False

    def _success_response(self, message, status_code=200):
        """Return success response"""
        response = request.make_response(
            json.dumps({'status': 'success', 'message': message}),
            status=status_code
        )
        response.headers['Content-Type'] = 'application/json'
        return response

    def _error_response(self, message, status_code=400):
        """Return error response"""
        response = request.make_response(
            json.dumps({'status': 'error', 'message': message}),
            status=status_code
        )
        response.headers['Content-Type'] = 'application/json'
        return response

    @http.route('/whatsapp/webhook/test', type='http', auth='user', methods=['GET'], csrf=False)
    def test_webhook(self):
        """Test endpoint to verify webhook is working"""
        try:
            # Get WhatsApp configuration
            config = request.env['whatsapp.config'].search([
                ('is_active', '=', True),
                ('company_id', '=', request.env.company.id)
            ], limit=1)

            if not config:
                return self._error_response('No WhatsApp configuration found', 404)

            webhook_url = config.get_webhook_url()

            response_data = {
                'status': 'success',
                'message': 'Webhook endpoint is working',
                'webhook_url': webhook_url,
                'timestamp': str(request.env.cr.now()),
                'company': request.env.company.name
            }

            return self._success_response(response_data)

        except Exception as e:
            _logger.error(f"Webhook test failed: {str(e)}")
            return self._error_response(f'Test failed: {str(e)}', 500)

    @http.route('/whatsapp/webhook/status', type='json', auth='user', methods=['POST'])
    def webhook_status(self):
        """Get webhook status and statistics"""
        try:
            # Get WhatsApp service
            service = request.env['whatsapp.service'].search([
                ('is_active', '=', True),
                ('company_id', '=', request.env.company.id)
            ], limit=1)

            if not service:
                return {'error': 'No WhatsApp service found'}

            # Get message statistics
            messages = request.env['whatsapp.message'].search([
                ('company_id', '=', request.env.company.id)
            ])

            stats = {
                'total_messages': len(messages),
                'sent_messages': len(messages.filtered(lambda m: m.status == 'sent')),
                'delivered_messages': len(messages.filtered(lambda m: m.status == 'delivered')),
                'read_messages': len(messages.filtered(lambda m: m.status == 'read')),
                'failed_messages': len(messages.filtered(lambda m: m.status == 'failed')),
                'pending_messages': len(messages.filtered(lambda m: m.status == 'pending')),
            }

            # Calculate success rate
            if stats['total_messages'] > 0:
                success_count = stats['sent_messages'] + stats['delivered_messages'] + stats['read_messages']
                stats['success_rate'] = (success_count / stats['total_messages']) * 100
            else:
                stats['success_rate'] = 0

            return {
                'status': 'success',
                'webhook_url': service.whatsapp_config_id.get_webhook_url(),
                'service_stats': {
                    'api_calls': service.total_api_calls,
                    'failed_calls': service.failed_api_calls,
                    'success_rate': service.message_success_rate,
                    'last_api_call': str(service.last_api_call_date) if service.last_api_call_date else None
                },
                'message_stats': stats
            }

        except Exception as e:
            _logger.error(f"Webhook status check failed: {str(e)}")
            return {'error': str(e)}

    @http.route('/whatsapp/webhook/logs', type='json', auth='user', methods=['POST'])
    def webhook_logs(self, limit=50):
        """Get recent webhook logs"""
        try:
            # Get recent WhatsApp messages with webhook updates
            messages = request.env['whatsapp.message'].search([
                ('company_id', '=', request.env.company.id),
                ('webhook_data', '!=', False)
            ], order='write_date desc', limit=limit)

            logs = []
            for message in messages:
                logs.append({
                    'id': message.id,
                    'recipient_phone': message.recipient_phone,
                    'status': message.status,
                    'sent_date': str(message.sent_date) if message.sent_date else None,
                    'delivered_date': str(message.delivered_date) if message.delivered_date else None,
                    'read_date': str(message.read_date) if message.read_date else None,
                    'last_webhook_update': str(message.write_date),
                    'error_message': message.error_message
                })

            return {
                'status': 'success',
                'logs': logs,
                'total_count': len(logs)
            }

        except Exception as e:
            _logger.error(f"Webhook logs retrieval failed: {str(e)}")
            return {'error': str(e)}

    @http.route('/whatsapp/send/test', type='json', auth='public', methods=['POST'], csrf=False)
    def send_test_message(self, **kwargs):
        """Test endpoint for sending WhatsApp messages"""
        try:
            # Enhanced logging for debugging
            _logger.info("=== WhatsApp Test Endpoint Called ===")
            _logger.info(f"Request kwargs: {kwargs}")
            _logger.info(f"Request params: {kwargs.get('params', {})}")
            
            # Handle both direct kwargs and params structure
            params = kwargs.get('params', {})
            phone_number = params.get('phone_number') or kwargs.get('phone_number')
            message_text = params.get('message_text') or kwargs.get('message_text', 'Test message from Odoo')
            
            _logger.info(f"Phone number: {phone_number}")
            _logger.info(f"Message text: {message_text}")
            
            if not phone_number:
                _logger.error("Phone number is required")
                return {
                    'status': 'error',
                    'message': 'Phone number is required'
                }
            
            # Get WhatsApp service
            whatsapp_service = request.env['whatsapp.service'].sudo().search([
                ('is_active', '=', True)
            ], limit=1)
            
            if not whatsapp_service:
                _logger.error("No active WhatsApp service found")
                return {
                    'status': 'error',
                    'message': 'No active WhatsApp service configured'
                }
            
            _logger.info(f"Using WhatsApp service: {whatsapp_service.name}")
            _logger.info(f"Service config: {whatsapp_service.whatsapp_config_id.name}")
            _logger.info(f"Config phone_number_id: {whatsapp_service.whatsapp_config_id.phone_number_id}")
            _logger.info(f"Config access_token (first 20 chars): {whatsapp_service.whatsapp_config_id.access_token[:20]}...")
            
            # Send message
            result = whatsapp_service.send_text_message(phone_number, message_text)
            
            _logger.info(f"WhatsApp API Response: {result}")
            _logger.info(f"Phone Number ID: {whatsapp_service.whatsapp_config_id.phone_number_id}")
            _logger.info(f"Access Token: {whatsapp_service.whatsapp_config_id.access_token[:20]}...")
            
            # Check if message was sent successfully
            if result and result.status == 'sent':
                return {
                    'status': 'success',
                    'message': 'Test message sent successfully',
                    'message_id': result.id,
                    'whatsapp_message_id': result.whatsapp_message_id
                }
            else:
                return {
                    'status': 'error',
                    'message': f"Failed to send message. Status: {result.status if result else 'No result'}"
                }
        
        except Exception as e:
            _logger.error(f"Error in send_test_message: {str(e)}")
            return {
                'status': 'error',
                'message': f"Error: {str(e)}"
            }
