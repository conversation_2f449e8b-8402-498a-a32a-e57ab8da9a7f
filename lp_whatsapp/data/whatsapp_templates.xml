<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Default WhatsApp Templates -->
        
        <!-- Visitor-specific templates moved to ams_vm_whatsapp module -->
        
        <!-- Emergency Alert Template -->
        <record id="template_emergency_alert" model="whatsapp.template">
            <field name="name">Emergency Alert</field>
            <field name="template_id">emergency_alert_001</field>
            <field name="language_code">en</field>
            <field name="category">UTILITY</field>
            <field name="status">APPROVED</field>
            <field name="header_type">TEXT</field>
            <field name="header_text">🚨 EMERGENCY ALERT</field>
            <field name="body_text">URGENT NOTIFICATION

🚨 *Emergency Situation:*
{{1}}

📍 *Location:* {{2}}
⏰ *Time:* {{3}}

⚠️ *Instructions:*
{{4}}

Please follow all emergency procedures and contact security immediately if needed.

Stay safe!</field>
            <field name="footer_text">Emergency Alert - Visitor Management System</field>
            <field name="parameter_count">4</field>
            <field name="is_active">True</field>
        </record>
        
        <!-- System Maintenance Template -->
        <record id="template_system_maintenance" model="whatsapp.template">
            <field name="name">System Maintenance Notification</field>
            <field name="template_id">system_maintenance_001</field>
            <field name="language_code">en</field>
            <field name="category">UTILITY</field>
            <field name="status">APPROVED</field>
            <field name="header_type">TEXT</field>
            <field name="header_text">🔧 System Maintenance</field>
            <field name="body_text">Hello {{1}},

We will be performing scheduled maintenance on our visitor management system:

🔧 *Maintenance Details:*
• Start Time: {{2}}
• End Time: {{3}}
• Duration: {{4}}

⚠️ During this time, some services may be temporarily unavailable. We apologize for any inconvenience.

Thank you for your patience!</field>
            <field name="footer_text">System Notification - Visitor Management</field>
            <field name="parameter_count">4</field>
            <field name="is_active">True</field>
        </record>
        
        <!-- General Welcome Template -->
        <record id="template_welcome_message" model="whatsapp.template">
            <field name="name">General Welcome Message</field>
            <field name="template_id">welcome_message_001</field>
            <field name="language_code">en</field>
            <field name="category">UTILITY</field>
            <field name="status">APPROVED</field>
            <field name="header_type">TEXT</field>
            <field name="header_text">👋 Welcome!</field>
            <field name="body_text">Hello {{1}},

Welcome to our WhatsApp notification system! 🎉

You will receive important notifications and updates through WhatsApp.

If you need assistance, feel free to contact our support team.

Thank you for choosing our services!</field>
            <field name="footer_text">WhatsApp Notification System</field>
            <field name="parameter_count">1</field>
            <field name="is_active">True</field>
        </record>
        
        <!-- Default WhatsApp Configuration -->
        <record id="default_whatsapp_config" model="whatsapp.config">
            <field name="name">Default WhatsApp Configuration</field>
            <field name="phone_number_id">YOUR_PHONE_NUMBER_ID</field>
            <field name="access_token">YOUR_ACCESS_TOKEN</field>
            <field name="webhook_verify_token">YOUR_WEBHOOK_VERIFY_TOKEN</field>
            <field name="webhook_url">https://yourdomain.com/whatsapp/webhook</field>
            <field name="api_version">v18.0</field>
            <field name="is_active">False</field>
            <field name="description">Default WhatsApp configuration. Please update with your actual Meta API credentials.</field>
        </record>
        
        <!-- Default WhatsApp Service -->
        <record id="default_whatsapp_service" model="whatsapp.service">
            <field name="name">Default WhatsApp Service</field>
            <field name="whatsapp_config_id" ref="default_whatsapp_config"/>
            <field name="service_type">messaging</field>
            <field name="max_retry_attempts">3</field>
            <field name="retry_delay_minutes">300</field>
            <field name="rate_limit_per_minute">80</field>
            <field name="is_active">False</field>
            <field name="description">Default WhatsApp messaging service for visitor notifications.</field>
        </record>
        
        <!-- System Parameters for WhatsApp Configuration -->
        <record id="param_whatsapp_default_template" model="ir.config_parameter">
            <field name="key">whatsapp.default_template_id</field>
            <field name="value" ref="template_welcome_message"/>
        </record>
        
        <record id="param_whatsapp_webhook_timeout" model="ir.config_parameter">
            <field name="key">whatsapp.webhook_timeout</field>
            <field name="value">30</field>
        </record>
        
        <record id="param_whatsapp_message_retention_days" model="ir.config_parameter">
            <field name="key">whatsapp.message_retention_days</field>
            <field name="value">90</field>
        </record>
        
        <record id="param_whatsapp_rate_limit_enabled" model="ir.config_parameter">
            <field name="key">whatsapp.rate_limit_enabled</field>
            <field name="value">True</field>
        </record>
        
        <record id="param_whatsapp_debug_mode" model="ir.config_parameter">
            <field name="key">whatsapp.debug_mode</field>
            <field name="value">False</field>
        </record>
        
        <record id="param_whatsapp_auto_retry_failed" model="ir.config_parameter">
            <field name="key">whatsapp.auto_retry_failed</field>
            <field name="value">True</field>
        </record>
        
<!--        &lt;!&ndash; Cron Jobs for WhatsApp Operations &ndash;&gt;-->
<!--        <record id="cron_whatsapp_retry_failed_messages" model="ir.cron">-->
<!--            <field name="name">WhatsApp: Retry Failed Messages</field>-->
<!--            <field name="model_id" ref="model_whatsapp_message"/>-->
<!--            <field name="state">code</field>-->
<!--            <field name="code">-->
<!--# Retry failed WhatsApp messages-->
<!--failed_messages = env['whatsapp.message'].search([-->
<!--    ('status', '=', 'failed'),-->
<!--    ('retry_count', '&lt;', 3),-->
<!--    ('create_date', '&gt;=', fields.Datetime.now() - timedelta(hours=24))-->
<!--])-->

<!--for message in failed_messages:-->
<!--    try:-->
<!--        message.action_retry_send()-->
<!--    except Exception as e:-->
<!--        # Log error and continue-->
<!--        message.message_post(-->
<!--            body=f"Retry failed: {str(e)}",-->
<!--            message_type='comment'-->
<!--        )-->
<!--            </field>-->
<!--            <field name="interval_number">30</field>-->
<!--            <field name="interval_type">minutes</field>-->
<!--            <field name="numbercall">-1</field>-->
<!--            <field name="is_active">True</field>-->
<!--        </record>-->
<!--        -->
<!--        <record id="cron_whatsapp_cleanup_old_messages" model="ir.cron">-->
<!--            <field name="name">WhatsApp: Cleanup Old Messages</field>-->
<!--            <field name="model_id" ref="model_whatsapp_service"/>-->
<!--            <field name="state">code</field>-->
<!--            <field name="code">-->
<!--# Cleanup old WhatsApp messages-->
<!--services = env['whatsapp.service'].search([('is_active', '=', True)])-->
<!--for service in services:-->
<!--    try:-->
<!--        service.action_cleanup_old_messages()-->
<!--    except Exception as e:-->
<!--        # Log error and continue-->
<!--        service.message_post(-->
<!--            body=f"Cleanup failed: {str(e)}",-->
<!--            message_type='comment'-->
<!--        )-->
<!--            </field>-->
<!--            <field name="interval_number">1</field>-->
<!--            <field name="interval_type">days</field>-->
<!--            <field name="numbercall">-1</field>-->
<!--            <field name="is_active">True</field>-->
<!--        </record>-->
<!--        -->
<!--        <record id="cron_whatsapp_health_check" model="ir.cron">-->
<!--            <field name="name">WhatsApp: Service Health Check</field>-->
<!--            <field name="model_id" ref="model_whatsapp_config"/>-->
<!--            <field name="state">code</field>-->
<!--            <field name="code">-->
<!--# Perform health check on WhatsApp configurations-->
<!--configs = env['whatsapp.config'].search([('is_active', '=', True)])-->
<!--for config in configs:-->
<!--    try:-->
<!--        result = config.test_connection()-->
<!--        if not result.get('success'):-->
<!--            # Log health check failure-->
<!--            config.message_post(-->
<!--                body=f"Health check failed: {result.get('error', 'Unknown error')}",-->
<!--                message_type='comment'-->
<!--            )-->
<!--    except Exception as e:-->
<!--        # Log error-->
<!--        config.message_post(-->
<!--            body=f"Health check error: {str(e)}",-->
<!--            message_type='comment'-->
<!--        )-->
<!--            </field>-->
<!--            <field name="interval_number">1</field>-->
<!--            <field name="interval_type">hours</field>-->
<!--            <field name="numbercall">-1</field>-->
<!--            <field name="is_active">True</field>-->
<!--        </record>-->
    </data>
</odoo>