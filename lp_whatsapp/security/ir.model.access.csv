id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_whatsapp_config_user,whatsapp.config.user,model_whatsapp_config,base.group_user,1,1,1,0
access_whatsapp_message_user,whatsapp.message.user,model_whatsapp_message,base.group_user,1,1,1,0
access_whatsapp_template_user,whatsapp.template.user,model_whatsapp_template,base.group_user,1,1,1,0
access_whatsapp_service_user,whatsapp.service.user,model_whatsapp_service,base.group_user,1,1,1,0
access_whatsapp_api_call_user,whatsapp.api.call.user,model_whatsapp_api_call,base.group_user,1,0,0,0
access_whatsapp_log_entry_user,whatsapp.log.entry.user,model_whatsapp_log_entry,base.group_user,1,0,0,0
access_whatsapp_config_system,whatsapp.config.system,model_whatsapp_config,base.group_system,1,1,1,1
access_whatsapp_message_system,whatsapp.message.system,model_whatsapp_message,base.group_system,1,1,1,1
access_whatsapp_template_system,whatsapp.template.system,model_whatsapp_template,base.group_system,1,1,1,1
access_whatsapp_service_system,whatsapp.service.system,model_whatsapp_service,base.group_system,1,1,1,1
access_whatsapp_api_call_system,whatsapp.api.call.system,model_whatsapp_api_call,base.group_system,1,1,1,1
access_whatsapp_log_entry_system,whatsapp.log.entry.system,model_whatsapp_log_entry,base.group_system,1,1,1,1