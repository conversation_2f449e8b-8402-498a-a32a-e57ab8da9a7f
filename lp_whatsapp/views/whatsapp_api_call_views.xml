<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- WhatsApp API Call Tree View -->
        <record id="view_whatsapp_api_call_list" model="ir.ui.view">
            <field name="name">whatsapp.api.call.list</field>
            <field name="model">whatsapp.api.call</field>
            <field name="arch" type="xml">
                <list string="WhatsApp API Calls" default_order="call_date desc">
                    <field name="call_date"/>
                    <field name="endpoint"/>
                    <field name="method"/>
                    <field name="status_code"/>
                    <field name="response_time"/>
                    <field name="success" widget="boolean_toggle"/>
                    <field name="error_message"/>
                </list>
            </field>
        </record>

        <!-- WhatsApp API Call Form View -->
        <record id="view_whatsapp_api_call_form" model="ir.ui.view">
            <field name="name">whatsapp.api.call.form</field>
            <field name="model">whatsapp.api.call</field>
            <field name="arch" type="xml">
                <form string="WhatsApp API Call">
                    <sheet>
                        <group>
                            <group>
                                <field name="service_id"/>
                                <field name="call_date"/>
                                <field name="endpoint"/>
                                <field name="method"/>
                            </group>
                            <group>
                                <field name="status_code"/>
                                <field name="response_time"/>
                                <field name="success"/>
                                <field name="company_id"/>
                            </group>
                        </group>
                        <group string="Error Information" invisible="success">
                            <field name="error_message" nolabel="1"/>
                        </group>
                        <notebook>
                            <page string="Request Data">
                                <field name="request_data" widget="ace" options="{'mode': 'json'}"/>
                            </page>
                            <page string="Response Data">
                                <field name="response_data" widget="ace" options="{'mode': 'json'}"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- WhatsApp API Call Search View -->
        <record id="view_whatsapp_api_call_search" model="ir.ui.view">
            <field name="name">whatsapp.api.call.search</field>
            <field name="model">whatsapp.api.call</field>
            <field name="arch" type="xml">
                <search string="Search API Calls">
                    <field name="endpoint"/>
                    <field name="method"/>
                    <field name="service_id"/>
                    <field name="company_id"/>
                    <separator/>
                    <filter name="successful" string="Successful" domain="[('success', '=', True)]"/>
                    <filter name="failed" string="Failed" domain="[('success', '=', False)]"/>
                    <separator/>
                    <filter name="today" string="Today" domain="[('call_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                    <filter name="this_week" string="This Week" domain="[('call_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter name="group_by_service" string="Service" context="{'group_by': 'service_id'}"/>
                        <filter name="group_by_method" string="Method" context="{'group_by': 'method'}"/>
                        <filter name="group_by_success" string="Success" context="{'group_by': 'success'}"/>
                        <filter name="group_by_date" string="Date" context="{'group_by': 'call_date:day'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- WhatsApp API Call Action -->
        <record id="action_whatsapp_api_call" model="ir.actions.act_window">
            <field name="name">WhatsApp API Calls</field>
            <field name="res_model">whatsapp.api.call</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="view_whatsapp_api_call_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No API calls found!
                </p>
                <p>
                    API calls will appear here automatically when WhatsApp services make requests to the Meta API.
                </p>
            </field>
        </record>
    </data>
</odoo>