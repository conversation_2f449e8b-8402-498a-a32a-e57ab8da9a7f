<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- WhatsApp Configuration Form View -->
        <record id="view_whatsapp_config_form" model="ir.ui.view">
            <field name="name">whatsapp.config.form</field>
            <field name="model">whatsapp.config</field>
            <field name="arch" type="xml">
                <form string="WhatsApp Configuration">
                    <header>
                        <button name="test_connection" type="object" string="Test Connection" 
                                class="btn-primary" icon="fa-plug"/>
                        <button name="setup_webhook" type="object" string="Setup Webhook" 
                                class="btn-secondary" icon="fa-link"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_messages" type="object" class="oe_stat_button" icon="fa-comments">
                                <field name="message_count" widget="statinfo" string="Messages"/>
                            </button>
                            <button name="action_view_templates" type="object" class="oe_stat_button" icon="fa-file-text">
                                <field name="template_count" widget="statinfo" string="Templates"/>
                            </button>
                        </div>
                        
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                                invisible="is_active"/>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Configuration Name..."/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_info">
                                <field name="is_active" widget="boolean_toggle"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                                <field name="phone_number_id" required="1"/>
                                <field name="test_phone_number" />
                                <field name="access_token" password="True" required="1"/>
                            </group>
                            <group name="webhook_info">
                                <field name="webhook_verify_token" password="True"/>
                                <field name="app_secret" password="True"/>
                                <field name="webhook_url" readonly="1" widget="url"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="API Settings" name="api_settings">
                                <group>
                                    <group name="api_config">
                                        <field name="api_version"/>
                                        <field name="timeout"/>
                                        <field name="retry_attempts"/>
                                    </group>
                                    <group name="status_info">
                                        <field name="last_connection_test" readonly="1"/>
                                        <field name="connection_status" readonly="1"/>
                                        <field name="error_message" readonly="1" invisible="not error_message"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Webhook Configuration" name="webhook_config">
                                <group>
                                    <div class="alert alert-info" role="alert">
                                        <strong>Webhook Setup Instructions:</strong>
                                        <ol>
                                            <li>Copy the webhook URL below</li>
                                            <li>Go to your Meta Developer Console</li>
                                            <li>Navigate to WhatsApp &gt; Configuration &gt; Webhooks</li>
                                            <li>Add the webhook URL and verify token</li>
                                            <li>Subscribe to message events</li>
                                        </ol>
                                    </div>
                                    
                                    <group name="webhook_details">
                                        <field name="webhook_url" readonly="1" widget="url"/>
                                        <field name="webhook_verify_token" password="True"/>
                                        <field name="app_secret" password="True"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Statistics" name="statistics">
                                <group>
                                    <group name="message_stats">
                                        <field name="message_count" readonly="1"/>
                                        <field name="template_count" readonly="1"/>
                                    </group>
                                    <group name="usage_stats">
                                        <field name="create_date" readonly="1"/>
                                        <field name="write_date" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                  <chatter/>
                </form>
            </field>
        </record>
        
        <!-- WhatsApp Configuration Tree View -->
        <record id="view_whatsapp_config_list" model="ir.ui.view">
            <field name="name">whatsapp.config.list</field>
            <field name="model">whatsapp.config</field>
            <field name="arch" type="xml">
                <list string="WhatsApp Configurations"
                      decoration-success="connection_status == 'connected'"
                      decoration-danger="connection_status == 'error'"
                      decoration-warning="connection_status == 'not_tested'">
                    <field name="name"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="phone_number_id"/>
                    <field name="connection_status" widget="badge"/>
                    <field name="message_count"/>
                    <field name="template_count"/>
                    <field name="is_active" widget="boolean_toggle"/>
                    <field name="last_connection_test"/>
                </list>
            </field>
        </record>
        
        <!-- WhatsApp Configuration Search View -->
        <record id="view_whatsapp_config_search" model="ir.ui.view">
            <field name="name">whatsapp.config.search</field>
            <field name="model">whatsapp.config</field>
            <field name="arch" type="xml">
                <search string="Search WhatsApp Configurations">
                    <field name="name" string="Configuration Name"/>
                    <field name="phone_number_id" string="Phone Number ID"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    
                    <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                    <separator/>
                    <filter string="Connected" name="connected" domain="[('connection_status', '=', 'connected')]"/>
                    <filter string="Connection Error" name="error" domain="[('connection_status', '=', 'error')]"/>
                    <filter string="Not Tested" name="not_tested" domain="[('connection_status', '=', 'not_tested')]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Company" name="group_company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Connection Status" name="group_status" domain="[]" context="{'group_by': 'connection_status'}"/>
                        <filter string="Active Status" name="group_active" domain="[]" context="{'group_by': 'is_active'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- WhatsApp Configuration Action -->
        <record id="action_whatsapp_config" model="ir.actions.act_window">
            <field name="name">WhatsApp Configuration</field>
            <field name="res_model">whatsapp.config</field>
            <field name="view_mode">list,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first WhatsApp configuration!
                </p>
                <p>
                    Configure your WhatsApp Business API credentials to start sending messages.
                    You'll need your Phone Number ID and Access Token from Meta Developer Console.
                </p>
            </field>
        </record>
    </data>
</odoo>