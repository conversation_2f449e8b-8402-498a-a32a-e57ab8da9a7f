<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- WhatsApp Log Entry Tree View -->
        <record id="view_whatsapp_log_entry_list" model="ir.ui.view">
            <field name="name">whatsapp.log.entry.list</field>
            <field name="model">whatsapp.log.entry</field>
            <field name="arch" type="xml">
                <list string="WhatsApp Service Logs" default_order="create_date desc" decoration-danger="level == 'error'" decoration-warning="level == 'warning'" decoration-info="level == 'info'">
                    <field name="create_date"/>
                    <field name="level"/>
                    <field name="message"/>
                    <field name="details"/>
                    <field name="module"/>
                    <field name="service_id"/>
                </list>
            </field>
        </record>

        <!-- WhatsApp Log Entry Form View -->
        <record id="view_whatsapp_log_entry_form" model="ir.ui.view">
            <field name="name">whatsapp.log.entry.form</field>
            <field name="model">whatsapp.log.entry</field>
            <field name="arch" type="xml">
                <form string="WhatsApp Log Entry">
                    <sheet>
                        <group>
                            <group>
                                <field name="service_id"/>
                                <field name="create_date"/>
                                <field name="level"/>
                                <field name="message"/>
                            </group>
                            <group>
                                <field name="module"/>
                                <field name="function"/>
                                <field name="company_id"/>
                            </group>
                        </group>
                        <group string="Details" invisible="not details">
                            <field name="details" nolabel="1"/>
                        </group>
                        <group string="Exception Information" invisible="not exception_info">
                            <field name="exception_info" nolabel="1" widget="ace" options="{'mode': 'text'}"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- WhatsApp Log Entry Search View -->
        <record id="view_whatsapp_log_entry_search" model="ir.ui.view">
            <field name="name">whatsapp.log.entry.search</field>
            <field name="model">whatsapp.log.entry</field>
            <field name="arch" type="xml">
                <search string="Search Log Entries">
                    <field name="message"/>
                    <field name="level"/>
                    <field name="module"/>
                    <field name="service_id"/>
                    <field name="company_id"/>
                    <separator/>
                    <filter name="debug" string="Debug" domain="[('level', '=', 'debug')]"/>
                    <filter name="info" string="Info" domain="[('level', '=', 'info')]"/>
                    <filter name="warning" string="Warning" domain="[('level', '=', 'warning')]"/>
                    <filter name="error" string="Error" domain="[('level', '=', 'error')]"/>
                    <filter name="critical" string="Critical" domain="[('level', '=', 'critical')]"/>
                    <separator/>
                    <filter name="today" string="Today" domain="[('create_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                    <filter name="this_week" string="This Week" domain="[('create_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter name="group_by_service" string="Service" context="{'group_by': 'service_id'}"/>
                        <filter name="group_by_level" string="Level" context="{'group_by': 'level'}"/>
                        <filter name="group_by_module" string="Module" context="{'group_by': 'module'}"/>
                        <filter name="group_by_date" string="Date" context="{'group_by': 'create_date:day'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- WhatsApp Log Entry Action -->
        <record id="action_whatsapp_log_entry" model="ir.actions.act_window">
            <field name="name">WhatsApp Service Logs</field>
            <field name="res_model">whatsapp.log.entry</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="view_whatsapp_log_entry_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No log entries found!
                </p>
                <p>
                    Service logs will appear here automatically when WhatsApp services generate log entries.
                </p>
            </field>
        </record>
    </data>
</odoo>