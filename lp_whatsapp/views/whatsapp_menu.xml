<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- WhatsApp Main Menu - Custom Top Level Menu -->
        <menuitem id="whatsapp_top_menu"
                  name="WhatsApp"
                  sequence="50"
                  groups="base.group_user" web_icon="lp_whatsapp,static/description/icon.png"/>
        
        <!-- Configuration Submenu -->
        <menuitem id="menu_whatsapp_config"
                  name="Configuration"
                  parent="whatsapp_top_menu"
                  sequence="10"
                  groups="base.group_system"/>
        
        <!-- WhatsApp Configuration Menu Item -->
        <menuitem id="menu_whatsapp_config_item"
                  name="WhatsApp Configuration"
                  parent="menu_whatsapp_config"
                  action="action_whatsapp_config"
                  sequence="10"
                  groups="base.group_system"/>
        
        <!-- WhatsApp Services Menu Item -->
        <menuitem id="menu_whatsapp_service_item"
                  name="WhatsApp Services"
                  parent="menu_whatsapp_config"
                  action="action_whatsapp_service"
                  sequence="20"
                  groups="base.group_system"/>
        
        <!-- WhatsApp Templates Menu Item -->
        <menuitem id="menu_whatsapp_template_item"
                  name="Message Templates"
                  parent="menu_whatsapp_config"
                  action="action_whatsapp_template"
                  sequence="30"
                  groups="base.group_system"/>
        
        <!-- Messages Submenu -->
        <menuitem id="menu_whatsapp_messages"
                  name="Messages"
                  parent="whatsapp_top_menu"
                  sequence="20"
                  groups="base.group_user"/>
        
        <!-- WhatsApp Messages Menu Item -->
        <menuitem id="menu_whatsapp_message_item"
                  name="All Messages"
                  parent="menu_whatsapp_messages"
                  action="action_whatsapp_message"
                  sequence="10"
                  groups="base.group_user"/>
        
        <!-- Reports Submenu -->
        <menuitem id="menu_whatsapp_reports"
                  name="Reports"
                  parent="whatsapp_top_menu"
                  sequence="30"
                  groups="base.group_system"/>
        
        <!-- WhatsApp Analytics Action -->
        <record id="action_whatsapp_analytics" model="ir.actions.act_window">
            <field name="name">WhatsApp Analytics</field>
            <field name="res_model">whatsapp.message</field>
            <field name="view_mode">graph,pivot</field>
            <field name="context">{
                'search_default_group_status': 1,
                'search_default_group_date': 1,
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No WhatsApp message data to analyze yet!
                </p>
                <p>
                    This view will show analytics and statistics about your WhatsApp
                    message delivery, success rates, and usage patterns once you
                    start sending messages.
                </p>
            </field>
        </record>
        
        <!-- WhatsApp Analytics Menu Item -->
        <menuitem id="menu_whatsapp_analytics"
                  name="Message Analytics"
                  parent="menu_whatsapp_reports"
                  action="action_whatsapp_analytics"
                  sequence="10"
                  groups="base.group_system"/>
        
        <!-- WhatsApp Template Usage Report Action -->
        <record id="action_whatsapp_template_usage" model="ir.actions.act_window">
            <field name="name">Template Usage Report</field>
            <field name="res_model">whatsapp.template</field>
            <field name="view_mode">list,graph,pivot</field>
            <field name="context">{
                'search_default_group_category': 1,
                'search_default_active': 1,
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No template usage data available yet!
                </p>
                <p>
                    This report shows which templates are most frequently used,
                    their success rates, and performance metrics to help you
                    optimize your WhatsApp messaging strategy.
                </p>
            </field>
        </record>
        
        <!-- Template Usage Report Menu Item -->
        <menuitem id="menu_whatsapp_template_usage"
                  name="Template Usage"
                  parent="menu_whatsapp_reports"
                  action="action_whatsapp_template_usage"
                  sequence="20"
                  groups="base.group_system"/>
        
        <!-- WhatsApp Service Health Action -->
        <record id="action_whatsapp_service_health" model="ir.actions.act_window">
            <field name="name">Service Health Dashboard</field>
            <field name="res_model">whatsapp.service</field>
            <field name="view_mode">kanban,list,graph</field>
            <field name="context">{
                'search_default_active': 1,
                'search_default_group_status': 1,
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No WhatsApp services configured yet!
                </p>
                <p>
                    This dashboard shows the health and performance of your WhatsApp
                    services, including API call statistics, success rates, and
                    system status monitoring.
                </p>
            </field>
        </record>
        
        <!-- Service Health Menu Item -->
        <menuitem id="menu_whatsapp_service_health"
                  name="Service Health"
                  parent="menu_whatsapp_reports"
                  action="action_whatsapp_service_health"
                  sequence="30"
                  groups="base.group_system"/>
        
        <!-- Settings Submenu -->
        <menuitem id="menu_whatsapp_settings"
                  name="Settings"
                  parent="whatsapp_top_menu"
                  sequence="90"
                  groups="base.group_system"/>
        
        <!-- WhatsApp Settings Action -->
        <record id="action_whatsapp_settings" model="ir.actions.act_window">
            <field name="name">WhatsApp Settings</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context">{'module': 'lp_whatsapp'}</field>
        </record>
        
        <!-- Settings Menu Item -->
        <menuitem id="menu_whatsapp_settings_item"
                  name="General Settings"
                  parent="menu_whatsapp_settings"
                  action="action_whatsapp_settings"
                  sequence="10"
                  groups="base.group_system"/>
        
        <!-- Quick Actions Submenu -->
        <menuitem id="menu_whatsapp_actions"
                  name="Quick Actions"
                  parent="whatsapp_top_menu"
                  sequence="80"
                  groups="base.group_system"/>
        
        <!-- Test Connection Action -->
        <record id="action_test_whatsapp_connection" model="ir.actions.server">
            <field name="name">Test WhatsApp Connection</field>
            <field name="model_id" ref="model_whatsapp_config"/>
            <field name="state">code</field>
            <field name="code">action = env['whatsapp.config'].action_test_whatsapp_connection()</field>
        </record>
        
        <!-- Test Connection Menu Item -->
        <menuitem id="menu_test_whatsapp_connection"
                  name="Test Connection"
                  parent="menu_whatsapp_actions"
                  action="action_test_whatsapp_connection"
                  sequence="10"
                  groups="base.group_system"/>
        
        <!-- Cleanup Messages Action -->
        <record id="action_cleanup_old_messages" model="ir.actions.server">
            <field name="name">Cleanup Old Messages</field>
            <field name="model_id" ref="model_whatsapp_service"/>
            <field name="state">code</field>
            <field name="code">action = env['whatsapp.service'].action_cleanup_old_messages()</field>
        </record>
        
        <!-- Cleanup Messages Menu Item -->
        <menuitem id="menu_cleanup_whatsapp_messages" 
                      name="Cleanup Old Messages" 
                      parent="menu_whatsapp_actions" 
                      action="action_cleanup_old_messages" 
                      sequence="20" 
                      groups="base.group_system"/>
        
        <!-- Sync Templates Action -->
        <record id="action_sync_templates_from_meta" model="ir.actions.server">
            <field name="name">Sync Templates from Meta</field>
            <field name="model_id" ref="model_whatsapp_template"/>
            <field name="state">code</field>
            <field name="code">action = env['whatsapp.template'].action_sync_templates_from_meta()</field>
        </record>
        
        <!-- Sync Templates Menu Item -->
        <menuitem id="menu_sync_whatsapp_templates" 
                      name="Sync Templates" 
                      parent="menu_whatsapp_actions" 
                      action="action_sync_templates_from_meta" 
                      sequence="30" 
                      groups="base.group_system"/>
    </data>
</odoo>