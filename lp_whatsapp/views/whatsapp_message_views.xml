<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- WhatsApp Message Form View -->
        <record id="view_whatsapp_message_form" model="ir.ui.view">
            <field name="name">whatsapp.message.form</field>
            <field name="model">whatsapp.message</field>
            <field name="arch" type="xml">
                <form string="WhatsApp Message" create="false">
                    <header>
                        <button name="action_retry_send" type="object" string="Retry Send"                                class="btn-primary" icon="fa-refresh"
                                invisible="status not in ['failed']"/>
                        <button name="action_mark_as_read" type="object" string="Mark as Read" 
                                class="btn-secondary" icon="fa-check"
                                invisible="status == 'read'"/>
                        <field name="status" widget="statusbar" statusbar_visible="pending,sent,delivered,read"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_visitor" type="object" class="oe_stat_button" icon="fa-user"
                                    invisible="not visitor_id">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">View</span>
                                    <span class="o_stat_text">Visitor</span>
                                </div>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="display_name" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="recipient_info">
                                <field name="recipient_phone" readonly="1"/>
                                <field name="recipient_name" readonly="1"/>
                                <field name="visitor_id" readonly="1"/>
                                <field name="company_id" groups="base.group_multi_company" readonly="1"/>
                            </group>
                            <group name="message_info">
                                <field name="whatsapp_message_id" readonly="1"/>
                                <field name="message_type" readonly="1"/>
                                <field name="template_id" readonly="1" invisible="message_type != 'template'"/>
                                <field name="retry_count" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Message Content" name="content">
                                <group>
                                    <field name="content" readonly="1" widget="text" nolabel="1"/>
                                </group>
                            </page>
                            
                            <page string="Delivery Status" name="delivery">
                                <group>
                                    <group name="timestamps">
                                        <field name="sent_date" readonly="1"/>
                                        <field name="delivered_date" readonly="1"/>
                                        <field name="read_date" readonly="1"/>
                                        <field name="failed_date" readonly="1" invisible="not failed_date"/>
                                    </group>
                                    <group name="status_info">
                                        <field name="status" readonly="1"/>
                                        <field name="error_message" readonly="1" invisible="not error_message"/>
                                        <field name="error_code" readonly="1" invisible="not error_code"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Webhook Data" name="webhook" invisible="not webhook_data">
                                <group>
                                    <field name="webhook_data" readonly="1" widget="ace" options="{'mode': 'json'}"/>
                                </group>
                            </page>
                            
                            <page string="Technical Info" name="technical">
                                <group>
                                    <group name="creation_info">
                                        <field name="create_date" readonly="1"/>
                                        <field name="create_uid" readonly="1"/>
                                        <field name="write_date" readonly="1"/>
                                        <field name="write_uid" readonly="1"/>
                                    </group>
                                    <group name="retry_info">
                                        <field name="retry_count" readonly="1"/>
                                        <field name="last_retry_date" readonly="1" invisible="not last_retry_date"/>
                                        <field name="next_retry_date" readonly="1" invisible="not next_retry_date"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                   <chatter/>
                </form>
            </field>
        </record>
        
        <!-- WhatsApp Message Tree View -->
        <record id="view_whatsapp_message_list" model="ir.ui.view">
            <field name="name">whatsapp.message.list</field>
            <field name="model">whatsapp.message</field>
            <field name="arch" type="xml">
                <list string="WhatsApp Messages" default_order="create_date desc"
                      decoration-success="status in ['sent', 'delivered', 'read']"
                      decoration-danger="status in ['failed']"
                      decoration-warning="status == 'pending'">
                    <field name="create_date"/>
                    <field name="recipient_phone"/>
                    <field name="recipient_name"/>
                    <field name="message_type"/>
                    <field name="status" widget="badge"/>
                    <field name="sent_date"/>
                    <field name="delivered_date"/>
                    <field name="read_date"/>
                    <field name="retry_count"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="error_message" optional="hide"/>
                </list>
            </field>
        </record>
        
        <!-- WhatsApp Message Kanban View -->
        <record id="view_whatsapp_message_kanban" model="ir.ui.view">
            <field name="name">whatsapp.message.kanban</field>
            <field name="model">whatsapp.message</field>
            <field name="arch" type="xml">
                <kanban default_group_by="status" class="o_kanban_small_column">
                    <field name="id"/>
                    <field name="recipient_phone"/>
                    <field name="recipient_name"/>
                    <field name="status"/>
                    <field name="message_type"/>
                    <field name="sent_date"/>
                    <field name="error_message"/>
                    <field name="retry_count"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="recipient_phone"/>
                                        </strong>
                                        <br/>
                                        <span class="o_kanban_record_subtitle text-muted">
                                            <field name="recipient_name"/>
                                        </span>
                                    </div>
                                    <div class="o_kanban_record_top_right">
                                        <span class="badge badge-pill" t-attf-class="badge-#{record.status.raw_value == 'sent' or record.status.raw_value == 'delivered' or record.status.raw_value == 'read' ? 'success' : record.status.raw_value == 'failed' or record.status.raw_value == 'error' ? 'danger' : 'warning'}">
                                            <field name="status"/>
                                        </span>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="o_kanban_record_bottom">
                                        <div class="oe_kanban_bottom_left">
                                            <span class="o_kanban_record_subtitle">
                                                Type: <field name="message_type"/>
                                            </span>
                                            <br/>
                                            <span class="o_kanban_record_subtitle" t-if="record.sent_date.raw_value">
                                                Sent: <field name="sent_date"/>
                                            </span>
                                        </div>
                                        <div class="oe_kanban_bottom_right" t-if="record.retry_count.raw_value > 0">
                                            <span class="badge badge-info">
                                                Retries: <field name="retry_count"/>
                                            </span>
                                        </div>
                                    </div>
                                    <div t-if="record.error_message.raw_value" class="text-danger small">
                                        <field name="error_message"/>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- WhatsApp Message Search View -->
        <record id="view_whatsapp_message_search" model="ir.ui.view">
            <field name="name">whatsapp.message.search</field>
            <field name="model">whatsapp.message</field>
            <field name="arch" type="xml">
                <search string="Search WhatsApp Messages">
                    <field name="recipient_phone" string="Phone Number"/>
                    <field name="recipient_name" string="Recipient Name"/>
                    <field name="whatsapp_message_id" string="WhatsApp Message ID"/>
                    <field name="visitor_id" string="Visitor"/>
                    <field name="template_id" string="Template"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    
                    <filter string="Pending" name="pending" domain="[('status', '=', 'pending')]"/>
                    <filter string="Sent" name="sent" domain="[('status', '=', 'sent')]"/>
                    <filter string="Delivered" name="delivered" domain="[('status', '=', 'delivered')]"/>
                    <filter string="Read" name="read" domain="[('status', '=', 'read')]"/>
                    <filter string="Failed" name="failed" domain="[('status', 'in', ['failed'])]"/>
                    <separator/>
                    <filter string="Text Messages" name="text" domain="[('message_type', '=', 'text')]"/>
                    <filter string="Template Messages" name="template" domain="[('message_type', '=', 'template')]"/>
                    <separator/>
                    <filter string="Today" name="today" domain="[('create_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="week" domain="[('create_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <filter string="This Month" name="month" domain="[('create_date', '>=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <filter string="With Errors" name="with_errors" domain="[('error_message', '!=', False)]"/>
                    <filter string="Retried Messages" name="retried" domain="[('retry_count', '>', 0)]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_status" domain="[]" context="{'group_by': 'status'}"/>
                        <filter string="Message Type" name="group_type" domain="[]" context="{'group_by': 'message_type'}"/>
                        <filter string="Recipient" name="group_recipient" domain="[]" context="{'group_by': 'recipient_phone'}"/>
                        <filter string="Template" name="group_template" domain="[]" context="{'group_by': 'template_id'}"/>
                        <filter string="Company" name="group_company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Creation Date" name="group_create_date" domain="[]" context="{'group_by': 'create_date:day'}"/>
                        <filter string="Sent Date" name="group_sent_date" domain="[]" context="{'group_by': 'sent_date:day'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- WhatsApp Message Action -->
        <record id="action_whatsapp_message" model="ir.actions.act_window">
            <field name="name">WhatsApp Messages</field>
            <field name="res_model">whatsapp.message</field>
            <field name="view_mode">list,kanban,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No WhatsApp messages found!
                </p>
                <p>
                    WhatsApp messages will appear here once you start sending notifications
                    to visitors through the visitor management system.
                </p>
            </field>
        </record>
    </data>
</odoo>