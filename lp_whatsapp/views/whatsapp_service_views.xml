<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- WhatsApp Service Form View -->
        <record id="view_whatsapp_service_form" model="ir.ui.view">
            <field name="name">whatsapp.service.form</field>
            <field name="model">whatsapp.service</field>
            <field name="arch" type="xml">
                <form string="WhatsApp Service">
                    <header>
                        <button name="action_test_connection" type="object" string="Test Connection" 
                                class="btn-primary" icon="fa-plug"/>
                        <button name="action_cleanup_old_messages" type="object" string="Cleanup Old Messages" 
                                class="btn-secondary" icon="fa-trash"/>
                        <button name="action_retry_failed_messages" type="object" string="Retry Failed" 
                                class="btn-warning" icon="fa-refresh"/>
                        <field name="status" widget="statusbar" statusbar_visible="active,inactive,error"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_api_calls" type="object" class="oe_stat_button" icon="fa-exchange">
                                <field name="total_api_calls" widget="statinfo" string="API Calls"/>
                            </button>
                            <button name="action_view_messages" type="object" class="oe_stat_button" icon="fa-comments">
                                <field name="total_messages_sent" widget="statinfo" string="Messages Sent"/>
                            </button>
                            <button name="action_view_failed_messages" type="object" class="oe_stat_button" icon="fa-exclamation-triangle">
                                <field name="failed_messages_count" widget="statinfo" string="Failed Messages"/>
                            </button>
                        </div>
                        
                        <widget name="web_ribbon" title="Inactive" bg_color="bg-danger" 
                                invisible="is_active"/>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Service Name..."/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_info">
                                <field name="is_active" widget="boolean_toggle"/>
                                <field name="whatsapp_config_id" required="1"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                                <field name="status" readonly="1"/>
                            </group>
                            <group name="statistics">
                                <field name="total_api_calls" readonly="1"/>
                                <field name="total_messages_sent" readonly="1"/>
                                <field name="failed_messages_count" readonly="1"/>
                                <field name="last_api_call_date" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Configuration" name="configuration">
                                <group>
                                    <group name="service_config">
                                        <field name="max_retry_attempts"/>
                                        <field name="retry_delay_minutes"/>
                                        <field name="cleanup_days"/>
                                        <field name="rate_limit_per_minute"/>
                                    </group>
                                    <group name="webhook_config">
                                        <field name="webhook_verify_token"/>
                                        <field name="webhook_secret"/>
                                        <field name="enable_webhook_logging"/>
                                        <field name="webhook_timeout_seconds"/>
                                    </group>
                                </group>
                                
                                <group string="Default Templates">
                                    <field name="default_approval_template_id"/>
                                    <field name="default_rejection_template_id"/>
                                    <field name="default_notification_template_id"/>
                                </group>
                            </page>
                            
                            <page string="API Monitoring" name="monitoring">
                                <group>
                                    <group name="api_stats">
                                        <field name="total_api_calls" readonly="1"/>
                                        <field name="successful_api_calls" readonly="1"/>
                                        <field name="failed_api_calls" readonly="1"/>
                                        <field name="api_success_rate" readonly="1" widget="percentage"/>
                                    </group>
                                    <group name="message_stats">
                                        <field name="total_messages_sent" readonly="1"/>
                                        <field name="successful_messages" readonly="1"/>
                                        <field name="failed_messages_count" readonly="1"/>
                                        <field name="message_success_rate" readonly="1" widget="percentage"/>
                                    </group>
                                </group>
                                
                                <group>
                                    <group name="timing_stats">
                                        <field name="last_api_call_date" readonly="1"/>
                                        <field name="average_response_time" readonly="1"/>
                                        <field name="last_cleanup_date" readonly="1"/>
                                    </group>
                                    <group name="error_stats">
                                        <field name="last_error_date" readonly="1"/>
                                        <field name="last_error_message" readonly="1" widget="text"/>
                                        <field name="consecutive_failures" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Recent API Calls" name="api_calls">
                                <field name="recent_api_calls_ids" readonly="1">
                                    <list string="Recent API Calls" limit="20">
                                        <field name="call_date"/>
                                        <field name="endpoint"/>
                                        <field name="method"/>
                                        <field name="status_code"/>
                                        <field name="response_time"/>
                                        <field name="success" widget="boolean_toggle"/>
                                        <field name="error_message"/>
                                    </list>
                                </field>
                            </page>
                            
                            <page string="System Health" name="health">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5>Service Status</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-sm-6">
                                                        <strong>Status:</strong>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <field name="status" readonly="1" widget="badge"/>
                                                    </div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-sm-6">
                                                        <strong>Last Check:</strong>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <field name="last_health_check" readonly="1"/>
                                                    </div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-sm-6">
                                                        <strong>Uptime:</strong>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <field name="uptime_percentage" readonly="1" widget="percentage"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5>Performance Metrics</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-sm-6">
                                                        <strong>Avg Response:</strong>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <field name="average_response_time" readonly="1"/> ms
                                                    </div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-sm-6">
                                                        <strong>Success Rate:</strong>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <field name="api_success_rate" readonly="1" widget="percentage"/>
                                                    </div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-sm-6">
                                                        <strong>Rate Limit:</strong>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <field name="rate_limit_per_minute" readonly="1"/> /min
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="alert alert-info" role="alert">
                                            <h6><i class="fa fa-info-circle"/> Health Check Information</h6>
                                            <ul class="mb-0">
                                                <li>Service health is automatically monitored every 15 minutes</li>
                                                <li>Failed API calls are automatically retried based on configuration</li>
                                                <li>Old messages are cleaned up automatically based on cleanup_days setting</li>
                                                <li>Rate limiting prevents API quota exhaustion</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </page>
                            
                            <page string="Logs" name="logs">
                                <field name="log_entries_ids" readonly="1">
                                    <list string="Service Logs" limit="50">
                                        <field name="create_date"/>
                                        <field name="level"/>
                                        <field name="message"/>
                                        <field name="details"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                   <chatter/>
                </form>
            </field>
        </record>
        
        <!-- WhatsApp Service Tree View -->
        <record id="view_whatsapp_service_list" model="ir.ui.view">
            <field name="name">whatsapp.service.list</field>
            <field name="model">whatsapp.service</field>
            <field name="arch" type="xml">
                <list string="WhatsApp Services"
                      decoration-success="status == 'active'"
                      decoration-danger="status == 'error'"
                      decoration-warning="status == 'inactive'">
                    <field name="name"/>
                    <field name="whatsapp_config_id"/>
                    <field name="status" widget="badge"/>
                    <field name="total_api_calls"/>
                    <field name="total_messages_sent"/>
                    <field name="failed_messages_count"/>
                    <field name="api_success_rate" widget="percentage"/>
                    <field name="last_api_call_date"/>
                    <field name="is_active" widget="boolean_toggle"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </list>
            </field>
        </record>

        
        <!-- WhatsApp Service Search View -->
        <record id="view_whatsapp_service_search" model="ir.ui.view">
            <field name="name">whatsapp.service.search</field>
            <field name="model">whatsapp.service</field>
            <field name="arch" type="xml">
                <search string="Search WhatsApp Services">
                    <field name="name" string="Service Name"/>
                    <field name="whatsapp_config_id" string="Configuration"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    
                    <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                    <separator/>
                    <filter string="Active Status" name="status_active" domain="[('status', '=', 'active')]"/>
                    <filter string="Inactive Status" name="status_inactive" domain="[('status', '=', 'inactive')]"/>
                    <filter string="Error Status" name="status_error" domain="[('status', '=', 'error')]"/>
                    <separator/>
<!--                    <filter string="High Success Rate" name="high_success" domain="[('api_success_rate', '>=', 95)]"/>-->
<!--                    <filter string="Low Success Rate" name="low_success" domain="[('api_success_rate', '&lt;', 80)]"/>-->
<!--                    <filter string="Has Failed Messages" name="has_failures" domain="[('failed_messages_count', '>', 0)]"/>-->
                    <separator/>
<!--                    <filter string="Recent Activity" name="recent_activity" domain="[('last_api_call_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>-->
<!--                    <filter string="No Recent Activity" name="no_recent_activity" domain="[('last_api_call_date', '&lt;', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>-->
<!--                    -->
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_status" domain="[]" context="{'group_by': 'status'}"/>
                        <filter string="Configuration" name="group_config" domain="[]" context="{'group_by': 'whatsapp_config_id'}"/>
                        <filter string="Company" name="group_company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Active Status" name="group_active" domain="[]" context="{'group_by': 'is_active'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- WhatsApp Service Action -->
        <record id="action_whatsapp_service" model="ir.actions.act_window">
            <field name="name">WhatsApp Services</field>
            <field name="res_model">whatsapp.service</field>
            <field name="view_mode">list,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first WhatsApp service!
                </p>
                <p>
                    Services manage the connection between your Odoo system and the
                    WhatsApp Business API. Configure services to handle message sending,
                    webhook processing, and API monitoring.
                </p>
            </field>
        </record>
    </data>
</odoo>