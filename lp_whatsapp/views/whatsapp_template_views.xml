<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- WhatsApp Template Form View -->
        <record id="view_whatsapp_template_form" model="ir.ui.view">
            <field name="name">whatsapp.template.form</field>
            <field name="model">whatsapp.template</field>
            <field name="arch" type="xml">
                <form string="WhatsApp Template">
                    <header>
                        <button name="action_test_template" type="object" string="Test Template" 
                                class="btn-primary" icon="fa-paper-plane"/>
                        <button name="action_sync_from_meta" type="object" string="Sync from Meta" 
                                class="btn-secondary" icon="fa-refresh"/>
                        <field name="status" widget="statusbar" />
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_messages" type="object" class="oe_stat_button" icon="fa-comments">
                                <field name="usage_count" widget="statinfo" string="Messages Sent"/>
                            </button>
                        </div>
                        
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                                invisible="not is_active"/>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Template Name..."/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_info">
                                <field name="is_active" widget="boolean_toggle"/>
                                <field name="template_id" required="1"/>
                                <field name="language_code" required="1"/>
                                <field name="category" required="1"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group name="status_info">
                                <field name="status" readonly="1"/>
                                <field name="parameter_count" readonly="1"/>
                                <field name="usage_count" readonly="1"/>
                                <field name="last_used_date" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Template Content" name="content">
                                <group>
                                    <group name="header_content">
                                        <field name="header_type"/>
                                        <field name="header_text" invisible="header_type != 'TEXT'" required="header_type == 'TEXT'"/>
                                        <field name="header_media_url" invisible="header_type not in ['IMAGE', 'VIDEO', 'DOCUMENT']" required="header_type in ['IMAGE', 'VIDEO', 'DOCUMENT']"/>
                                    </group>
                                    <group name="body_content">
                                        <field name="body_text" required="1" widget="text"/>
                                        <div class="alert alert-info" role="alert" invisible="parameter_count == 0">
                                            <strong>Parameters detected:</strong> <field name="parameter_count" readonly="1"/> parameter(s)
                                            <br/>Use {{1}}, {{2}}, etc. for dynamic content
                                        </div>
                                    </group>
                                </group>
                                
                                <group name="footer_content">
                                    <field name="footer_text" widget="text"/>
                                </group>
                                
                                <group name="buttons" string="Action Buttons">
                                    <field name="button_text_1"/>
                                    <field name="button_url_1" invisible="not button_text_1"/>
                                    <field name="button_text_2"/>
                                    <field name="button_url_2" invisible="not button_text_2"/>
                                </group>
                            </page>
                            
                            <page string="Preview" name="preview">
                                <div class="o_whatsapp_template_preview">
                                    <div class="card" style="max-width: 400px; margin: 20px auto;">
                                        <div class="card-header bg-success text-white">
                                            <i class="fa fa-whatsapp"/> WhatsApp Template Preview
                                        </div>
                                        <div class="card-body">
                                            <!-- Header Preview -->
                                            <div  class="mb-2">
                                                <strong><field name="header_text" readonly="1"/></strong>
                                            </div>
                                            <div class="mb-2">
                                                <div class="alert alert-secondary">
                                                    <i class="fa fa-file"/> Media: <field name="header_media_url" readonly="1"/>
                                                </div>
                                            </div>
                                            
                                            <!-- Body Preview -->
                                            <div class="mb-2">
                                                <field name="body_text" readonly="1" widget="text"/>
                                            </div>
                                            
                                            <!-- Footer Preview -->
                                            <div class="mb-2 text-muted small">
                                                <field name="footer_text" readonly="1"/>
                                            </div>
                                            
                                            <!-- Buttons Preview -->
                                            <div  class="mt-2">
                                                <div class="mb-1">
                                                    <button class="btn btn-outline-primary btn-sm btn-block" disabled="1">
                                                        <field name="button_text_1" readonly="1"/>
                                                    </button>
                                                </div>
                                                <div >
                                                    <button class="btn btn-outline-primary btn-sm btn-block" disabled="1">
                                                        <field name="button_text_2" readonly="1"/>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </page>
                            
                            <page string="Usage Statistics" name="statistics">
                                <group>
                                    <group name="usage_stats">
                                        <field name="usage_count" readonly="1"/>
                                        <field name="last_used_date" readonly="1"/>
                                        <field name="success_rate" readonly="1" widget="percentage"/>
                                    </group>
                                    <group name="template_info">
                                        <field name="create_date" readonly="1"/>
                                        <field name="write_date" readonly="1"/>
                                        <field name="create_uid" readonly="1"/>
                                        <field name="write_uid" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Technical Details" name="technical">
                                <group>
                                    <group name="meta_info">
                                        <field name="template_id" readonly="1"/>
                                        <field name="language_code" readonly="1"/>
                                        <field name="category" readonly="1"/>
                                        <field name="status" readonly="1"/>
                                    </group>
                                    <group name="parameters">
                                        <field name="parameter_count" readonly="1"/>
                                        <div class="alert alert-info" role="alert">
                                            <strong>Parameter Usage:</strong>
                                            <ul>
                                                <li>Use {{1}}, {{2}}, {{3}}, etc. in your template text</li>
                                                <li>Parameters will be replaced with actual values when sending</li>
                                                <li>Parameter count is automatically calculated</li>
                                            </ul>
                                        </div>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                   <chatter/>
                </form>
            </field>
        </record>
        
        <!-- WhatsApp Template Tree View -->
        <record id="view_whatsapp_template_list" model="ir.ui.view">
            <field name="name">whatsapp.template.list</field>
            <field name="model">whatsapp.template</field>
            <field name="arch" type="xml">
                <list string="WhatsApp Templates">
                    <field name="name"/>
                    <field name="template_id"/>
                    <field name="language_code"/>
                    <field name="category"/>
                    <field name="status" widget="badge"
                           decoration-success="status == 'APPROVED'"
                           decoration-danger="status == 'REJECTED'"
                           decoration-warning="status == 'PENDING'"/>
                    <field name="usage_count"/>
                    <field name="parameter_count"/>
                    <field name="last_used_date"/>
                    <field name="is_active" widget="boolean_toggle"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </list>
            </field>
        </record>
        

        
        <!-- WhatsApp Template Search View -->
        <record id="view_whatsapp_template_search" model="ir.ui.view">
            <field name="name">whatsapp.template.search</field>
            <field name="model">whatsapp.template</field>
            <field name="arch" type="xml">
                <search string="Search WhatsApp Templates">
                    <field name="name" string="Template Name"/>
                    <field name="template_id" string="Template ID"/>
                    <field name="body_text" string="Body Text"/>
                    <field name="language_code" string="Language"/>
                    <field name="category" string="Category"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    
                    <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                    <separator/>
                    <filter string="Approved" name="approved" domain="[('status', '=', 'APPROVED')]"/>
                    <filter string="Pending" name="pending" domain="[('status', '=', 'PENDING')]"/>
                    <filter string="Rejected" name="rejected" domain="[('status', '=', 'REJECTED')]"/>
                    <separator/>
                    <filter string="Marketing" name="marketing" domain="[('category', '=', 'MARKETING')]"/>
                    <filter string="Utility" name="utility" domain="[('category', '=', 'UTILITY')]"/>
                    <filter string="Authentication" name="authentication" domain="[('category', '=', 'AUTHENTICATION')]"/>
                    <separator/>
                    <filter string="Recently Used" name="recently_used" domain="[('last_used_date', '>=', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                    <filter string="Never Used" name="never_used" domain="[('usage_count', '=', 0)]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_status" domain="[]" context="{'group_by': 'status'}"/>
                        <filter string="Category" name="group_category" domain="[]" context="{'group_by': 'category'}"/>
                        <filter string="Language" name="group_language" domain="[]" context="{'group_by': 'language_code'}"/>
                        <filter string="Company" name="group_company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Active Status" name="group_active" domain="[]" context="{'group_by': 'is_active'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- WhatsApp Template Action -->
        <record id="action_whatsapp_template" model="ir.actions.act_window">
            <field name="name">WhatsApp Templates</field>
            <field name="res_model">whatsapp.template</field>
            <field name="view_mode">list,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first WhatsApp template!
                </p>
                <p>
                    Templates are pre-approved message formats that you can use to send
                    structured messages to your visitors. Create templates for common
                    scenarios like visitor approvals, rejections, or information requests.
                </p>
            </field>
        </record>
    </data>
</odoo>