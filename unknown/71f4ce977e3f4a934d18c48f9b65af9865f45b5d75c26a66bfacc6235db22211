# Custom serialize_exception Implementation

## Overview

This implementation provides custom logic for the `serialize_exception` method in the `ams_mep` module to restrict debug information disclosure based on user admin status and system configuration.

## Problem Statement

The original `serialize_exception` method in `odoo/http.py` returns full debug information (including traceback) for all users, which can lead to information disclosure vulnerabilities. This implementation restricts debug information to admin users only when the security parameter is enabled.

## Solution

### Files Modified/Created

1. **`ams_mep/models/http_override.py`** - Main implementation file
2. **`ams_mep/models/__init__.py`** - Updated to import the new module
3. **`ams_mep/tests/test_serialize_exception.py`** - Comprehensive test suite
4. **`ams_mep/controllers/security_controllers.py`** - Added test endpoint

### Implementation Details

#### Core Logic

The custom `serialize_exception` function:

1. **Checks system parameter**: `ams_mep.debug_admin_only`
2. **Validates user status**: Checks if current user is admin
3. **Returns debug info conditionally**:
   - Admin user + debug_admin_only=True → Full debug info
   - Non-admin user + debug_admin_only=True → Empty debug info
   - Any user + debug_admin_only=False → Full debug info
   - No user context → Empty debug info

#### Key Features

- **Graceful error handling**: Never breaks original functionality
- **Configurable**: Can be enabled/disabled via system parameters
- **Secure by default**: Defaults to restricting debug info
- **Comprehensive logging**: Logs access attempts for audit
- **Backward compatible**: Maintains original API structure

### Configuration

#### System Parameter

- **Parameter**: `ams_mep.debug_admin_only`
- **Type**: Boolean
- **Default**: `True`
- **Description**: When enabled, restricts debug information to admin users only

#### Setting via UI

1. Go to Settings → Security Configuration
2. Find "Restrict Debug to Admins Only" option
3. Enable/disable as needed
4. Save configuration

#### Setting via Code

```python
# Enable debug restriction
self.env['ir.config_parameter'].sudo().set_param('ams_mep.debug_admin_only', 'True')

# Disable debug restriction
self.env['ir.config_parameter'].sudo().set_param('ams_mep.debug_admin_only', 'False')
```

### Usage Examples

#### Normal Exception Handling

```python
from odoo import http
from odoo.exceptions import UserError

try:
    # Some operation that might fail
    raise UserError("Something went wrong")
except Exception as e:
    # This will use our custom serialize_exception
    error_data = http.serialize_exception(e)
    # error_data['debug'] will be empty for non-admin users
    return error_data
```

#### Testing the Implementation

```python
# Test endpoint available at: /test/serialize_exception
# Returns detailed information about the serialization behavior
```

### Testing

#### Running Tests

```bash
# Run all tests for the module
python -m pytest ams_mep/tests/

# Run specific test file
python -m pytest ams_mep/tests/test_serialize_exception.py

# Run with verbose output
python -m pytest ams_mep/tests/test_serialize_exception.py -v
```

#### Test Coverage

The test suite covers:

- Admin users with debug restriction enabled
- Non-admin users with debug restriction enabled
- All users with debug restriction disabled
- Unauthenticated requests
- Missing request context
- Configuration errors
- Response structure validation

### Security Benefits

1. **Information Disclosure Prevention**: Prevents sensitive debug information from being exposed to non-admin users
2. **Configurable Security**: Allows administrators to control debug information exposure
3. **Audit Trail**: Logs debug access attempts for security monitoring
4. **Defense in Depth**: Adds an additional layer of security to exception handling

### Monitoring and Logging

The implementation logs the following events:

- Debug info provided to admin users
- Debug info restricted for non-admin users
- Configuration errors
- Missing request context scenarios

### Troubleshooting

#### Common Issues

1. **Debug info still showing for non-admin users**
   - Check if `ams_mep.debug_admin_only` parameter is set to `True`
   - Verify the user is not an admin
   - Check server logs for any errors

2. **No debug info for admin users**
   - Verify the user has admin privileges
   - Check if the parameter is correctly set
   - Review server logs for configuration errors

3. **Module not loading**
   - Ensure `http_override` is imported in `models/__init__.py`
   - Check for syntax errors in the implementation
   - Restart the Odoo server

#### Debug Commands

```python
# Check current configuration
config = request.env['res.config.settings'].get_security_config()
print(config.get('debug_admin_only'))

# Check user admin status
user = request.env.user
print(f"User {user.login} is admin: {user._is_admin()}")

# Test serialize_exception directly
from odoo.exceptions import UserError
import odoo.http as http
test_exception = UserError("Test")
result = http.serialize_exception(test_exception)
print(f"Debug info length: {len(result['debug'])}")
```

### Performance Considerations

- **Minimal overhead**: The custom logic adds minimal processing time
- **Caching**: Configuration values could be cached for better performance
- **Error handling**: Graceful fallbacks ensure no performance degradation

### Future Enhancements

1. **Rate limiting**: Add rate limiting for debug access attempts
2. **Enhanced logging**: More detailed audit logs
3. **User group support**: Allow specific user groups to access debug info
4. **Time-based restrictions**: Temporary debug access for specific users

## Conclusion

This implementation provides a secure, configurable, and maintainable solution for restricting debug information disclosure in Odoo applications. It follows security best practices while maintaining backward compatibility and providing comprehensive testing coverage.
